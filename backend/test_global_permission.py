#!/usr/bin/env python
"""
全局权限验证测试脚本
"""

import os
import sys
import django
import requests
import json

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

from zhi_oauth.models import OAuthApplication, OAuthAccessToken
from django.contrib.auth import get_user_model

User = get_user_model()

# API 基础URL
BASE_URL = "http://127.0.0.1:8001"

def test_whitelist_apis():
    """测试白名单 API（不需要认证）"""
    print("=== 测试白名单 API ===")
    
    whitelist_apis = [
        "/api/oauth/login",
        "/api/docs",
        "/health",
        "/ping",
    ]
    
    for api_path in whitelist_apis:
        try:
            response = requests.get(f"{BASE_URL}{api_path}")
            print(f"  {api_path}: 状态码 {response.status_code} {'✓' if response.status_code != 401 else '✗'}")
        except requests.exceptions.ConnectionError:
            print(f"  {api_path}: 连接失败 ✗")
        except Exception as e:
            print(f"  {api_path}: 异常 {e} ✗")

def test_protected_apis_without_token():
    """测试受保护的 API（无 Token）"""
    print("\n=== 测试受保护的 API（无 Token）===")
    
    protected_apis = [
        "/api/v1/example-products/list_pagination",
        "/api/oauth/user/info",
        "/api/oauth/applications",
    ]
    
    for api_path in protected_apis:
        try:
            response = requests.get(f"{BASE_URL}{api_path}")
            result = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            
            if response.status_code == 401:
                print(f"  {api_path}: 正确返回401未授权 ✓")
            else:
                print(f"  {api_path}: 状态码 {response.status_code} ✗")
                print(f"    响应: {result}")
                
        except requests.exceptions.ConnectionError:
            print(f"  {api_path}: 连接失败 ✗")
        except Exception as e:
            print(f"  {api_path}: 异常 {e} ✗")

def test_protected_apis_with_invalid_token():
    """测试受保护的 API（无效 Token）"""
    print("\n=== 测试受保护的 API（无效 Token）===")
    
    protected_apis = [
        "/api/v1/example-products/list_pagination",
        "/api/oauth/user/info",
    ]
    
    invalid_token = "invalid_token_12345"
    headers = {"Authorization": f"Bearer {invalid_token}"}
    
    for api_path in protected_apis:
        try:
            response = requests.get(f"{BASE_URL}{api_path}", headers=headers)
            result = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
            
            if response.status_code == 401:
                print(f"  {api_path}: 正确返回401无效令牌 ✓")
            else:
                print(f"  {api_path}: 状态码 {response.status_code} ✗")
                print(f"    响应: {result}")
                
        except requests.exceptions.ConnectionError:
            print(f"  {api_path}: 连接失败 ✗")
        except Exception as e:
            print(f"  {api_path}: 异常 {e} ✗")

def get_valid_access_token():
    """获取有效的 access_token"""
    print("\n=== 获取有效的 access_token ===")
    
    # 1. 创建测试用户
    username = "permission_test_user"
    password = "test_password_123"
    
    user, created = User.objects.get_or_create(
        username=username,
        defaults={'email': '<EMAIL>', 'is_active': True}
    )
    if created:
        user.set_password(password)
        user.save()
        print(f"  创建测试用户: {username}")
    else:
        user.set_password(password)
        user.save()
        print(f"  使用现有用户: {username}")
    
    # 2. 获取 OAuth 应用
    app = OAuthApplication.objects.filter(client_id="test_debug_client").first()
    if not app:
        print("  ❌ 找不到测试 OAuth 应用，请先运行 test_oauth_debug.py")
        return None
    
    # 3. 密码登录
    login_data = {
        "username": username,
        "password": password,
        "remember_me": False
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/oauth/login", json=login_data)
        if response.status_code != 200:
            print(f"  ❌ 登录失败: {response.text}")
            return None
        
        result = response.json()
        if not result.get('success'):
            print(f"  ❌ 登录失败: {result.get('message')}")
            return None
        
        session_token = result.get('data', {}).get('session_token')
        print(f"  ✓ 登录成功，获得 session_token")
        
        # 4. OAuth2 授权
        authorize_data = {
            "session_token": session_token,
            "client_id": app.client_id,
            "redirect_uri": app.redirect_uris[0],
            "response_type": "code",
            "scope": "read write"
        }
        
        response = requests.post(f"{BASE_URL}/api/oauth/authorize", json=authorize_data)
        if response.status_code != 200:
            print(f"  ❌ 授权失败: {response.text}")
            return None
        
        result = response.json()
        if not result.get('success'):
            print(f"  ❌ 授权失败: {result.get('message')}")
            return None
        
        auth_code = result.get('data', {}).get('authorization_code')
        print(f"  ✓ 授权成功，获得 authorization_code")
        
        # 5. 令牌交换
        token_data = {
            "grant_type": "authorization_code",
            "code": auth_code,
            "client_id": app.client_id,
            "client_secret": app.client_secret,
            "redirect_uri": app.redirect_uris[0]
        }
        
        response = requests.post(f"{BASE_URL}/api/oauth/token", json=token_data)
        if response.status_code != 200:
            print(f"  ❌ 令牌交换失败: {response.text}")
            return None
        
        result = response.json()
        if not result.get('success'):
            print(f"  ❌ 令牌交换失败: {result.get('message')}")
            return None
        
        access_token = result.get('data', {}).get('access_token')
        print(f"  ✓ 令牌交换成功，获得 access_token")
        
        return access_token
        
    except Exception as e:
        print(f"  ❌ 获取 access_token 异常: {e}")
        return None

def test_protected_apis_with_valid_token(access_token):
    """测试受保护的 API（有效 Token）"""
    print("\n=== 测试受保护的 API（有效 Token）===")
    
    protected_apis = [
        "/api/v1/example-products/list_pagination",
        "/api/oauth/user/info",
    ]
    
    headers = {"Authorization": f"Bearer {access_token}"}
    
    for api_path in protected_apis:
        try:
            response = requests.get(f"{BASE_URL}{api_path}", headers=headers)
            
            if response.status_code == 200:
                print(f"  {api_path}: 访问成功 ✓")
            elif response.status_code == 403:
                print(f"  {api_path}: 权限不足 ⚠️")
            else:
                result = response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text
                print(f"  {api_path}: 状态码 {response.status_code} ✗")
                print(f"    响应: {result}")
                
        except requests.exceptions.ConnectionError:
            print(f"  {api_path}: 连接失败 ✗")
        except Exception as e:
            print(f"  {api_path}: 异常 {e} ✗")

def main():
    """主测试流程"""
    print("全局权限验证测试")
    print("=" * 60)
    
    # 测试白名单 API
    test_whitelist_apis()
    
    # 测试无 Token 的受保护 API
    test_protected_apis_without_token()
    
    # 测试无效 Token 的受保护 API
    test_protected_apis_with_invalid_token()
    
    # 获取有效的 access_token
    access_token = get_valid_access_token()
    
    if access_token:
        # 测试有效 Token 的受保护 API
        test_protected_apis_with_valid_token(access_token)
    
    print("\n" + "=" * 60)
    print("全局权限验证测试完成！")
    
    print("\n测试说明:")
    print("✓ - 测试通过")
    print("✗ - 测试失败") 
    print("⚠️ - 权限不足（正常，需要配置具体权限）")

if __name__ == "__main__":
    main()
