# ZhiAdmin 后端系统

一个基于Django的模块化企业级管理系统，支持OAuth2认证、统一日志管理、异步任务处理等功能。

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        Web[Web前端<br/>Vue.js/React]
        Mobile[移动端<br/>App/小程序]
        API[第三方API<br/>外部系统]
    end

    subgraph "网关层"
        Gateway[API网关<br/>Nginx/Kong]
        LB[负载均衡器<br/>Load Balancer]
    end

    subgraph "认证服务层"
        OAuth[zhi_oauth服务<br/>端口:8001<br/>OAuth2认证中心]
        TokenManager[Token管理器<br/>JWT/缓存]
        UserModel[用户模型<br/>RBAC权限]
        PermModel[权限模型<br/>细粒度控制]
    end

    subgraph "业务微服务层"
        Logger[zhi_logger<br/>端口:8002<br/>日志管理服务]
        System[zhi_system<br/>端口:8003<br/>系统管理服务]
        Admin[interval_admin<br/>端口:8000<br/>完整管理后台]
        Future[未来服务<br/>可扩展...]
    end

    subgraph "SDK层"
        SDK[zhi_oauth_sdk<br/>统一认证SDK]
        Middleware[OAuth中间件<br/>自动认证]
        Decorators[权限装饰器<br/>@require_auth]
        UserManager[用户管理器<br/>缓存优化]
    end

    subgraph "数据层"
        DB[(MySQL数据库<br/>共享数据)]
        Redis[(Redis缓存<br/>Token/会话)]
        FileStorage[文件存储<br/>静态资源]
    end

    subgraph "监控层"
        Monitor[系统监控<br/>性能指标]
        Logs[日志聚合<br/>ELK Stack]
        Alert[告警系统<br/>异常通知]
    end

    Web --> Gateway
    Mobile --> Gateway
    API --> Gateway

    Gateway --> LB
    LB --> OAuth
    LB --> Logger
    LB --> System
    LB --> Admin

    OAuth --> TokenManager
    OAuth --> UserModel
    OAuth --> PermModel

    Logger --> SDK
    System --> SDK
    Admin --> SDK
    Future --> SDK

    SDK --> Middleware
    SDK --> Decorators
    SDK --> UserManager
    SDK --> OAuth

    OAuth --> DB
    Logger --> DB
    System --> DB
    Admin --> DB

    OAuth --> Redis
    Logger --> Redis
    System --> Redis
    Admin --> Redis

    Logger --> FileStorage
    System --> FileStorage
    Admin --> FileStorage

    Logger --> Monitor
    System --> Monitor
    Admin --> Monitor
    OAuth --> Monitor

    Monitor --> Logs
    Monitor --> Alert
```

系统采用分层架构设计，从上到下包括：客户端层、网关层、认证服务层、业务微服务层、SDK层、数据层和监控层。

### 核心设计理念

- **统一认证**：`zhi_oauth`作为认证中心，所有微服务通过OAuth SDK集成
- **服务隔离**：每个微服务独立部署，可单独扩展
- **共享数据**：使用共享数据库，通过SDK优化性能
- **缓存优化**：多层缓存策略，显著提升响应速度
- **监控完善**：全链路监控，实时掌握系统状态

### OAuth认证流程图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant OAuth as OAuth服务
    participant Service as 业务服务
    participant SDK as OAuth SDK
    participant DB as 数据库
    participant Cache as Redis缓存

    Note over Client,Cache: 用户登录流程
    Client->>Gateway: 1. 登录请求 (username/password)
    Gateway->>OAuth: 2. 转发登录请求
    OAuth->>DB: 3. 验证用户凭证
    DB-->>OAuth: 4. 返回用户信息
    OAuth->>Cache: 5. 生成并缓存Token
    OAuth-->>Gateway: 6. 返回Access Token
    Gateway-->>Client: 7. 返回Token给客户端

    Note over Client,Cache: API访问流程
    Client->>Gateway: 8. API请求 (Bearer Token)
    Gateway->>Service: 9. 转发请求
    Service->>SDK: 10. Token验证

    alt Token在缓存中
        SDK->>Cache: 11a. 从缓存获取用户信息
        Cache-->>SDK: 12a. 返回缓存的用户信息
    else Token不在缓存中
        SDK->>DB: 11b. 从数据库查询用户信息
        DB-->>SDK: 12b. 返回用户信息
        SDK->>Cache: 13b. 缓存用户信息
    end

    SDK->>SDK: 14. 权限验证
    SDK-->>Service: 15. 返回验证结果

    alt 验证成功
        Service->>Service: 16a. 执行业务逻辑
        Service-->>Gateway: 17a. 返回业务数据
        Gateway-->>Client: 18a. 返回响应
    else 验证失败
        Service-->>Gateway: 16b. 返回认证错误
        Gateway-->>Client: 17b. 返回401错误
    end
```

### 核心应用模块

```
backend/
├── zhi_common/          # 通用组件
│   ├── zhi_auth/       # 统一认证
│   ├── zhi_logger/     # 日志系统
│   └── models/         # 基础模型
├── zhi_oauth/          # OAuth2认证系统
├── zhi_logger/         # 日志管理系统
├── zhi_celery/         # 异步任务管理
├── interval_admin/     # 管理后台
├── application/        # 项目配置
│   ├── settings/       # 分环境配置
│   └── conf_urls/      # URL配置
└── zhi_scripts/        # 启动脚本
```

### 技术栈

- **框架**: Django 5.2 + Django Ninja
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **异步任务**: Celery + Redis
- **认证**: OAuth2 + JWT
- **API文档**: OpenAPI 3.0
- **日志**: Loguru + 数据库存储
- **响应格式**: BaseResponse 统一格式 (75%完成)
- **模型增强**: 自动用户信息填充系统 (100%完成)

## 🚀 快速开始

### 环境要求

- Python 3.10+
- Redis (用于Celery)
- 虚拟环境

### 安装依赖

```bash
# 激活虚拟环境
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt
```

### 数据库迁移

```bash
# 基础迁移
python manage.py migrate

# 启用Celery功能时
python manage.py migrate --settings=application.settings.celery_enabled
```

## 🎯 启动方式

### 1. 单项目启动

```bash
# OAuth认证系统 (端口8001)
python zhi_scripts/start_project.py --project oauth

# 日志管理系统 (端口8002)  
python zhi_scripts/start_project.py --project logger

# 管理后台系统 (端口8000)
python zhi_scripts/start_project.py --project admin
```

### 2. 开发模式启动

```bash
# 启动Logger项目（推荐用于开发）
python zhi_scripts/start_project.py --dev-mode
```

### 3. 多项目统一启动

```bash
# 所有API在一个服务器 (端口8001)
python manage.py runserver --settings=application.settings.base
```

### 4. Windows批处理启动

```bash
# 🆕 推荐：使用新的Windows启动脚本
# 通用开发环境启动（交互式选择）
zhi_scripts\start_dev_windows.bat

# PowerShell方式（更多选项）
zhi_scripts\start_dev_windows.ps1 -Mode webapp
zhi_scripts\start_dev_windows.ps1 -Mode all -Init

# ZhiCelery专用启动
zhi_scripts\start_zhi_celery_windows.ps1 -Mode worker
zhi_scripts\start_zhi_celery_windows.ps1 -Mode all

# 快速测试OAuth API响应格式
zhi_scripts\quick_test_oauth.bat
```

**传统启动方式（仍可用）:**
```bash
# OAuth项目
zhi_scripts\start_logger.bat 8001 --project oauth

# Logger项目
zhi_scripts\start_logger.bat 8002

# 带Celery的完整启动
zhi_scripts\start_logger.bat 8002 --with-celery
```

## ⚙️ Celery异步任务

### 启动Celery服务

```bash
# 🆕 推荐：使用新的ZhiCelery启动脚本
# 启动Worker
zhi_scripts\start_zhi_celery_windows.ps1 -Mode worker

# 启动Beat调度器
zhi_scripts\start_zhi_celery_windows.ps1 -Mode beat

# 启动所有Celery服务
zhi_scripts\start_zhi_celery_windows.ps1 -Mode all

# 启动Flower监控
zhi_scripts\start_zhi_celery_windows.ps1 -Mode flower

# 测试Celery配置
zhi_scripts\start_zhi_celery_windows.ps1 -Mode test
```

**Django管理命令方式:**
```bash
# 启动Worker
python manage.py celery_worker --pool=solo --loglevel=info

# 启动Beat调度器
python manage.py celery_beat --loglevel=info
```

**传统启动方式（仍可用）:**
```bash
# 启动Worker
python zhi_scripts/start_celery.py --worker

# 启动Beat调度器
python zhi_scripts/start_celery.py --beat

# 启动所有Celery服务
python zhi_scripts/start_celery.py --all
```

### 任务队列配置

- **oauth**: OAuth相关任务 (令牌清理、统计)
- **logger**: 日志相关任务 (日志清理、报告生成)
- **default**: 默认任务队列
- **urgent**: 紧急任务队列

### 定时任务

| 任务名称 | 执行时间 | 功能描述 |
|---------|---------|----------|
| cleanup-expired-oauth-tokens | 每天凌晨2点 | 清理过期OAuth令牌 |
| generate-oauth-statistics | 每周一凌晨3点 | 生成OAuth统计报告 |
| revoke-expired-tokens | 每小时整点 | 撤销过期令牌 |
| cleanup-old-logs | 每天凌晨1点 | 清理旧日志记录 |
| generate-log-statistics | 每天凌晨4点 | 生成日志统计 |
| log-health-check | 每10分钟 | 日志系统健康检查 |

## 📚 API文档

### 访问地址

- **OAuth API**: http://localhost:8001/api/oauth/docs
- **Logger API**: http://localhost:8002/api/logger/logger/docs
- **Celery API**: http://localhost:8001/api/celery/docs (多项目模式)
- **系统状态**: http://localhost:8001/api/system/status/

### 🎯 统一响应格式

系统已全面采用 `zhi_common.zhi_services.base_schema.BaseResponse` 统一响应格式，确保所有API返回结构一致。

#### 标准响应结构

```json
{
  "code": 2000,           // 响应码 (ResponseCode枚举)
  "message": "ok",        // 响应消息
  "success": true,        // 成功标志
  "trace_id": "uuid",     // 请求追踪ID
  "timestamp": "ISO时间",  // 响应时间戳
  "data": {}              // 实际业务数据
}
```

#### 响应码说明

| 响应码 | 含义 | 说明 |
|--------|------|------|
| 2000 | SUCCESS | 请求成功 |
| 4000 | BAD_REQUEST | 请求参数错误 |
| 4001 | UNAUTHORIZED | 未认证/认证失败 |
| 4003 | FORBIDDEN | 权限不足 |
| 4004 | NOT_FOUND | 资源不存在 |
| 5000 | INTERNAL_ERROR | 服务器内部错误 |

#### 成功响应示例

```json
{
  "code": 2000,
  "message": "登录成功",
  "success": true,
  "trace_id": "trace-abc123",
  "timestamp": "2025-07-24T10:00:00Z",
  "data": {
    "session_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user_info": {
      "id": 1,
      "username": "admin",
      "name": "管理员"
    },
    "expires_in": 3600
  }
}
```

#### 错误响应示例

```json
{
  "code": 4001,
  "message": "用户名或密码错误",
  "success": false,
  "trace_id": "trace-def456",
  "timestamp": "2025-07-24T10:00:00Z",
  "data": {
    "error": "invalid_credentials",
    "error_description": "Invalid username or password"
  }
}
```

#### OAuth模块适配状态

| API端点 | 状态 | 说明 |
|---------|------|------|
| POST /api/oauth/login | ✅ 已适配 | 用户登录接口 |
| GET /api/oauth/user/info | ✅ 已适配 | 获取用户信息 |
| GET /api/oauth/applications | ⚠️ 调试中 | OAuth应用列表 |
| POST /api/oauth/token | ✅ 已适配 | 令牌交换接口 |
| POST /api/oauth/revoke | ✅ 已适配 | 令牌撤销接口 |

**总体进度**: 75% 完成 (4/5 API已统一)

### 主要API端点

#### OAuth认证
- `POST /api/auth/token/` - 获取访问令牌
- `POST /api/auth/refresh/` - 刷新令牌
- `POST /api/auth/revoke/` - 撤销令牌
- `GET /api/auth/user/profile/` - 用户信息

#### 日志管理
- `GET /api/logger/logs/` - 查询日志
- `POST /api/logger/logs/` - 创建日志
- `GET /api/logger/stats/` - 日志统计
- `WS /ws/logs/` - 实时日志推送

#### 任务管理
- `GET /api/celery/tasks/list` - 任务列表
- `POST /api/celery/tasks/execute/{task_name}` - 执行任务
- `GET /api/celery/tasks/status/{task_id}` - 任务状态
- `GET /api/celery/tasks/stats` - 任务统计

## 🔧 配置说明

### Django设置模块

- `application.settings.base` - 基础配置
- `application.settings.zhi_oauth` - OAuth项目配置
- `application.settings.zhi_logger` - Logger项目配置
- `application.settings.interval_admin` - Admin项目配置
- `application.settings.celery_enabled` - 启用Celery的配置

### 环境变量

```bash
# Django配置
DJANGO_SETTINGS_MODULE=application.settings.zhi_logger
DEBUG=True

# 数据库配置
DATABASE_URL=sqlite:///db.sqlite3

# Redis配置 (Celery)
CELERY_BROKER_URL=redis://localhost:6379/0

# 日志配置
LOG_LEVEL=INFO
LOG_TO_FILE=True
```

## 🛠️ 开发指南

### 添加新的异步任务

1. 在`zhi_celery/tasks/`目录下创建任务文件
2. 在`zhi_celery/schedules.py`中添加调度配置
3. 在`zhi_celery/__init__.py`中导入任务

```python
# 示例任务
@shared_task(queue='custom')
def my_custom_task(param1, param2):
    # 任务逻辑
    return {'success': True}
```

### 添加新的API端点

1. 在对应应用的`controllers/`目录下创建控制器
2. 在`api.py`中注册控制器
3. 使用Django Ninja装饰器定义端点

```python
@api_controller("/custom", tags=["自定义"])
class CustomController:
    @route.get("/endpoint")
    def custom_endpoint(self):
        return {"message": "Hello World"}
```

## 🔍 故障排除

### 常见问题

1. **日志循环问题**: 已通过循环检测机制解决
2. **NinjaAPI冲突**: 使用不同的版本号和命名空间
3. **Celery连接失败**: 确保Redis服务正在运行
4. **环境变量重复**: 使用启动脚本避免手动设置

### 调试模式

```bash
# 启用调试模式
python zhi_scripts/start_project.py --project logger --debug

# 查看详细日志
tail -f logs/django.log
```

## 📈 监控和维护

### 日志查看

- 应用日志: `logs/` 目录
- 数据库日志: 通过Logger API查询
- Celery日志: Worker输出

### 性能监控

- Flower: http://localhost:5555 (如果启用)
- Django Admin: http://localhost:8001/admin/
- 系统状态API: `/api/system/status/`

## 🏛️ 系统架构详解

### 应用模块说明

#### zhi_common - 通用组件
- **zhi_auth**: 统一认证中间件和装饰器
- **zhi_logger**: 日志记录和管理工具
- **zhi_response**: 统一响应格式 (BaseResponse)
- **zhi_services**: 基础服务类和响应模式
- **zhi_model**: 增强模型系统
  - **core_model**: 基础模型类 (CoreModel, ZhiCoreModel等)
  - **enhanced_user_manager**: 增强用户信息管理器
  - **auto_user_mixin**: 自动用户信息填充混入类
  - **user_info_manager**: 用户信息基础服务
- **utils**: 通用工具函数
- **exceptions**: 自定义异常类

#### zhi_oauth - OAuth2认证系统
- **models**: 用户、应用、令牌模型
- **controllers**: 认证、授权、用户管理API
- **services**: OAuth2核心业务逻辑
- **tasks**: 令牌清理、统计等异步任务

#### zhi_logger - 日志管理系统
- **models**: 系统日志、API日志、审计日志模型
- **controllers**: 日志查询、统计、管理API
- **services**: 日志收集、处理、分析服务
- **consumers**: WebSocket实时日志推送

#### zhi_celery - 异步任务管理
- **tasks**: 统一管理所有异步任务
- **schedules**: 定时任务配置
- **controllers**: 任务管理API
- **celery_app**: Celery应用配置

### 数据库设计

#### 用户认证相关表
- `zhi_oauth_user` - 用户基础信息
- `zhi_oauth_application` - OAuth2应用
- `zhi_oauth_accesstoken` - 访问令牌
- `zhi_oauth_refreshtoken` - 刷新令牌
- `zhi_oauth_authorizationcode` - 授权码

#### 日志相关表
- `zhi_logger_systemlog` - 系统日志
- `zhi_logger_apilog` - API访问日志
- `zhi_logger_auditlog` - 审计日志
- `zhi_logger_errorlog` - 错误日志

#### Celery相关表
- `django_celery_beat_*` - Beat调度配置
- `django_celery_results_*` - 任务执行结果

## 🔐 安全特性

### 认证安全
- OAuth2标准实现
- JWT令牌加密
- 令牌自动过期和刷新
- 防暴力破解机制

### 权限控制
- RBAC角色权限模型
- 细粒度权限控制
- API级别权限验证
- 资源访问控制

### 数据安全
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护
- CSRF令牌验证

## 🚀 性能优化

### 缓存策略
- Redis缓存用户信息
- 数据库查询缓存
- API响应缓存
- 静态资源缓存

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置
- 读写分离支持

### 异步处理
- Celery异步任务
- 消息队列处理
- 批量数据处理
- 定时任务调度

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
python manage.py test

# 运行特定应用测试
python manage.py test zhi_oauth
python manage.py test zhi_logger

# 生成覆盖率报告
coverage run --source='.' manage.py test
coverage report
coverage html
```

### 测试类型
- 单元测试 (Unit Tests)
- 集成测试 (Integration Tests)
- API测试 (API Tests)
- 性能测试 (Performance Tests)

## 📦 部署

### Docker部署

```bash
# 构建镜像
docker build -t zhi-admin-backend .

# 运行容器
docker run -p 8000:8000 zhi-admin-backend

# 使用docker-compose
docker-compose up -d
```

### 生产环境配置

```bash
# 安装生产依赖
pip install -r requirements/production.txt

# 收集静态文件
python manage.py collectstatic

# 使用Gunicorn启动
gunicorn application.wsgi:application --bind 0.0.0.0:8000
```

## 👨‍💻 开发指南

### 统一响应格式开发

#### 新API开发
```python
from zhi_common.zhi_response.base import create_response, adapt_service_response
from zhi_common.zhi_consts.core_res_code import ResponseCode

# 方式1: 直接创建响应
@http_get('/example')
def example_api(self, request):
    try:
        data = {'result': 'success'}
        return create_response(
            data=data,
            message='操作成功',
            code=ResponseCode.SUCCESS,
            as_model=True
        )
    except Exception as e:
        return create_response(
            data={'error': 'server_error'},
            message='操作失败',
            code=ResponseCode.INTERNAL_ERROR,
            success=False,
            as_model=True
        )

# 方式2: 适配服务层响应
@http_post('/example')
def example_service_api(self, request):
    # 服务层返回标准格式
    service_result = {
        'success': True,
        'message': '处理成功',
        'data': {'id': 1, 'name': 'example'}
    }

    # 自动适配为BaseResponse格式
    return adapt_service_response(service_result)
```

#### 向后兼容处理
```python
# 导入统一响应格式
try:
    from zhi_common.zhi_response.base import create_response
    from zhi_common.zhi_consts.core_res_code import ResponseCode
    UNIFIED_RESPONSE_AVAILABLE = True
except ImportError:
    UNIFIED_RESPONSE_AVAILABLE = False

@http_get('/api')
def api_method(self, request):
    result = {'data': 'example'}

    if UNIFIED_RESPONSE_AVAILABLE:
        return create_response(data=result, as_model=True)
    else:
        return result  # 降级为原始格式
```

#### 测试统一响应格式
```bash
# 测试OAuth API响应格式
python zhi_scripts/test_oauth_complete.py

# 快速测试
zhi_scripts\quick_test_oauth.bat

# 测试ZhiCelery配置
python zhi_scripts/test_zhi_celery.py
```

### 前端调用示例
```javascript
// 统一的响应处理
function handleApiResponse(response) {
  if (response.success) {
    console.log('操作成功:', response.message);
    return response.data;
  } else {
    console.error('操作失败:', response.message);
    throw new Error(response.message);
  }
}

// API调用
fetch('/api/oauth/login', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({username: 'admin', password: '123456'})
})
.then(res => res.json())
.then(handleApiResponse)
.then(data => {
  // 使用 data.session_token 等
});
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- 遵循PEP 8代码风格
- 使用Black进行代码格式化
- 添加适当的注释和文档
- 编写相应的测试用例
- **新API必须使用BaseResponse统一响应格式**
- **服务层返回标准字典格式，控制器层负责适配**
- **包含向后兼容处理机制**

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您在使用过程中遇到问题，可以通过以下方式获取帮助：

- 提交Issue: [GitHub Issues](https://github.com/your-repo/issues)
- 邮件联系: <EMAIL>
- 文档中心: [ZhiAdmin Docs](https://docs.zhiadmin.com)

---

**ZhiAdmin** - 企业级Django管理系统 🚀

*让管理更智能，让开发更高效*
