#!/usr/bin/env python
"""
OAuth2 调试测试脚本
用于诊断 invalid_client 错误
"""

import os
import sys
import django
import requests
import json

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

from zhi_oauth.models import OAuthApplication
from zhi_oauth.controllers.auth_service import auth_service
from django.contrib.auth import get_user_model

User = get_user_model()

def test_oauth_client_validation():
    """测试OAuth客户端验证"""
    print("=== OAuth2 客户端验证测试 ===")
    
    # 获取数据库中的应用
    apps = OAuthApplication.objects.all()
    print(f"数据库中的OAuth应用数量: {apps.count()}")
    
    for app in apps:
        print(f"\n应用信息:")
        print(f"  名称: {app.name}")
        print(f"  client_id: {app.client_id}")
        print(f"  client_secret: {app.client_secret}")
        print(f"  redirect_uris: {app.redirect_uris}")
        print(f"  is_active: {app.is_active}")
        print(f"  is_deleted: {app.is_deleted}")
        
        # 测试客户端验证
        for redirect_uri in app.redirect_uris:
            result = auth_service._validate_oauth_client(
                client_id=app.client_id,
                redirect_uri=redirect_uri
            )
            print(f"  验证结果 (redirect_uri={redirect_uri}): {'✓ 成功' if result else '✗ 失败'}")

def test_password_login():
    """测试密码登录"""
    print("\n=== 密码登录测试 ===")
    
    # 检查是否有用户
    users = User.objects.filter(is_active=True)
    if not users.exists():
        print("❌ 没有活跃用户，请先创建用户")
        return None
    
    user = users.first()
    print(f"测试用户: {user.username}")
    
    # 模拟密码登录（这里需要你提供正确的密码）
    # 注意：这里只是演示，实际使用时需要正确的密码
    print("⚠️  需要手动测试密码登录，请使用正确的用户名和密码")
    return user

def test_oauth_authorize():
    """测试OAuth授权"""
    print("\n=== OAuth2 授权测试 ===")
    
    app = OAuthApplication.objects.first()
    if not app:
        print("❌ 没有OAuth应用")
        return
    
    user = User.objects.filter(is_active=True).first()
    if not user:
        print("❌ 没有活跃用户")
        return
    
    # 创建一个模拟的session_token（实际应该通过密码登录获得）
    print("⚠️  需要真实的session_token进行测试")
    print(f"建议的测试参数:")
    print(f"  client_id: {app.client_id}")
    print(f"  redirect_uri: {app.redirect_uris[0] if app.redirect_uris else 'http://localhost:3000/callback'}")
    print(f"  response_type: code")

def test_api_endpoint():
    """测试API端点"""
    print("\n=== API 端点测试 ===")
    
    app = OAuthApplication.objects.first()
    if not app:
        print("❌ 没有OAuth应用")
        return
    
    # 测试数据
    test_data = {
        "session_token": "test_session_token",  # 需要真实的session_token
        "client_id": app.client_id,
        "redirect_uri": app.redirect_uris[0] if app.redirect_uris else "http://localhost:3000/callback",
        "response_type": "code",
        "scope": "read write",
        "state": "test_state"
    }
    
    print("测试数据:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    # 发送请求测试
    try:
        response = requests.post(
            "http://127.0.0.1:8001/api/oauth/authorize",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def create_test_oauth_app():
    """创建测试OAuth应用"""
    print("\n=== 创建测试OAuth应用 ===")
    
    app, created = OAuthApplication.objects.get_or_create(
        client_id="test_debug_client",
        defaults={
            'name': 'Debug测试应用',
            'client_secret': 'test_debug_secret_123456',
            'app_type': 'web',
            'redirect_uris': [
                'http://localhost:3000/callback',
                'http://127.0.0.1:3000/callback',
                'http://localhost:8080/callback'
            ],
            'grant_types': ['authorization_code', 'refresh_token'],
            'allowed_scopes': ['read', 'write'],
            'is_active': True
        }
    )
    
    if created:
        print(f"✓ 创建新的测试应用: {app.name}")
    else:
        print(f"- 测试应用已存在: {app.name}")
    
    print(f"  client_id: {app.client_id}")
    print(f"  client_secret: {app.client_secret}")
    print(f"  redirect_uris: {app.redirect_uris}")

if __name__ == "__main__":
    print("OAuth2 调试工具")
    print("=" * 50)
    
    # 运行所有测试
    create_test_oauth_app()
    test_oauth_client_validation()
    test_password_login()
    test_oauth_authorize()
    test_api_endpoint()
    
    print("\n" + "=" * 50)
    print("调试完成！")
    print("\n建议的解决步骤:")
    print("1. 确保使用正确的 client_id")
    print("2. 确保 redirect_uri 在应用的 redirect_uris 列表中")
    print("3. 先通过 /api/oauth/login 获取有效的 session_token")
    print("4. 使用获得的 session_token 调用 /api/oauth/authorize")
