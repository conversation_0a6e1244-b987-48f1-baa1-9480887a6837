#!/usr/bin/env python
"""
API 文档访问测试脚本
验证 API 文档是否可以正常访问
"""

import requests
import json

# API 基础URL
BASE_URL = "http://127.0.0.1:8001"

def test_api_docs_access():
    """测试 API 文档访问"""
    print("=== 测试 API 文档访问 ===")
    
    # 需要测试的文档路径 - 包含所有子项目
    docs_paths = [
        # 通用文档入口
        "/docs",                        # 项目文档入口
        "/api/docs",                    # 通用文档入口
        "/api/openapi.json",            # 通用 OpenAPI 规范
        "/api/redoc",                   # 通用 ReDoc 文档

        # OAuth2 认证系统
        "/api/oauth/docs",              # OAuth API 文档
        "/api/oauth/openapi.json",      # OAuth OpenAPI 规范
        "/api/oauth/redoc",             # OAuth ReDoc 文档

        # 日志管理系统
        "/api/logger/docs",             # Logger API 文档
        "/api/logger/openapi.json",     # Logger OpenAPI 规范
        "/api/logger/redoc",            # Logger ReDoc 文档

        # 系统管理模块
        "/api/system/docs",             # System API 文档
        "/api/system/openapi.json",     # System OpenAPI 规范
        "/api/system/redoc",            # System ReDoc 文档

        # Celery 任务系统
        "/api/celery/docs",             # Celery API 文档
        "/api/celery/openapi.json",     # Celery OpenAPI 规范
        "/api/celery/redoc",            # Celery ReDoc 文档

        # 完整管理后台
        "/api/admin/docs",              # Admin API 文档
        "/api/admin/openapi.json",      # Admin OpenAPI 规范
        "/api/admin/redoc",             # Admin ReDoc 文档
    ]
    
    results = {}
    
    for path in docs_paths:
        try:
            response = requests.get(f"{BASE_URL}{path}", timeout=5)
            status = response.status_code
            
            if status == 200:
                result = "✅ 可访问"
                # 检查内容类型
                content_type = response.headers.get('content-type', '')
                if 'text/html' in content_type:
                    result += " (HTML文档)"
                elif 'application/json' in content_type:
                    result += " (JSON规范)"
                else:
                    result += f" ({content_type})"
            elif status == 404:
                result = "❌ 未找到"
            elif status == 401:
                result = "🔒 需要认证 (被中间件拦截)"
            elif status == 403:
                result = "🚫 权限不足"
            elif status == 500:
                result = "💥 服务器错误"
            else:
                result = f"⚠️ 状态码: {status}"
            
            results[path] = result
            print(f"  {path}: {result}")
            
        except requests.exceptions.ConnectionError:
            results[path] = "❌ 连接失败 (服务器未启动)"
            print(f"  {path}: ❌ 连接失败 (服务器未启动)")
        except requests.exceptions.Timeout:
            results[path] = "⏰ 请求超时"
            print(f"  {path}: ⏰ 请求超时")
        except Exception as e:
            results[path] = f"❌ 异常: {e}"
            print(f"  {path}: ❌ 异常: {e}")
    
    return results

def test_protected_endpoints():
    """测试受保护的端点是否正确拦截"""
    print("\n=== 测试受保护端点 ===")
    
    protected_paths = [
        "/api/v1/example-products/list_pagination",
        "/api/oauth/user/info",
        "/api/oauth/applications",
    ]
    
    for path in protected_paths:
        try:
            response = requests.get(f"{BASE_URL}{path}", timeout=5)
            status = response.status_code
            
            if status == 401:
                print(f"  {path}: ✅ 正确拦截 (401)")
            else:
                print(f"  {path}: ⚠️ 状态码: {status} (应该是401)")
                
        except requests.exceptions.ConnectionError:
            print(f"  {path}: ❌ 连接失败")
        except Exception as e:
            print(f"  {path}: ❌ 异常: {e}")

def analyze_results(results):
    """分析测试结果"""
    print("\n=== 结果分析 ===")
    
    accessible_docs = [path for path, result in results.items() if "✅" in result]
    blocked_docs = [path for path, result in results.items() if "🔒" in result]
    missing_docs = [path for path, result in results.items() if "❌" in result]
    
    print(f"可访问的文档: {len(accessible_docs)}")
    for path in accessible_docs:
        print(f"  ✅ {path}")
    
    if blocked_docs:
        print(f"\n被拦截的文档: {len(blocked_docs)}")
        for path in blocked_docs:
            print(f"  🔒 {path}")
        print("  💡 这些路径可能需要添加到白名单中")
    
    if missing_docs:
        print(f"\n未找到的文档: {len(missing_docs)}")
        for path in missing_docs:
            print(f"  ❌ {path}")

def provide_solutions():
    """提供解决方案"""
    print("\n=== 解决方案 ===")
    
    print("如果 API 文档被拦截，请检查以下配置：")
    print()
    print("1. 检查白名单配置 (settings/base.py):")
    print("   GLOBAL_PERMISSION_CONFIG['WHITELIST_PATTERNS'] 中是否包含文档路径")
    print()
    print("2. 重启 Django 服务器:")
    print("   python manage.py runserver")
    print()
    print("3. 检查中间件日志:")
    print("   查看是否有权限验证相关的错误信息")
    print()
    print("4. 临时禁用中间件进行测试:")
    print("   在 settings.py 中注释掉 GlobalPermissionMiddleware")

def main():
    """主测试流程"""
    print("API 文档访问测试")
    print("=" * 50)
    
    # 测试文档访问
    results = test_api_docs_access()
    
    # 测试受保护端点
    test_protected_endpoints()
    
    # 分析结果
    analyze_results(results)
    
    # 提供解决方案
    provide_solutions()
    
    print("\n" + "=" * 50)
    print("测试完成！")

if __name__ == "__main__":
    main()
