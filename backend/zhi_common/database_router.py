"""
Django 微服务数据库路由器
支持多数据库的微服务架构
"""
from django.conf import settings


class MicroserviceRouter:
    """
    微服务数据库路由器
    
    根据应用和模型自动路由到对应的数据库
    支持用户中心的统一管理和各微服务的数据隔离
    """
    
    # 用户相关应用，统一路由到用户数据库
    user_apps = {
        'zhi_oauth',           # 用户认证中心
        'auth',                # Django认证
        'contenttypes',        # 内容类型
        'sessions',            # 会话
        'admin',               # 管理后台
    }

    # 日志相关应用，统一路由到日志数据库
    logger_apps = {
        'zhi_logger',          # 日志系统
    }
    
    # 公共应用，可以访问多个数据库
    common_apps = {
        'zhi_common',          # 公共组件
    }
    
    # 各微服务的数据库映射
    service_db_mapping = {
        # 用户中心
        'zhi_oauth': 'user_db',
        'auth': 'user_db',
        'contenttypes': 'user_db',
        'sessions': 'user_db',
        'admin': 'user_db',

        # 日志系统
        'zhi_logger': 'logger_db',

        # 业务微服务（示例）
        'order_service': 'order_db',
        'product_service': 'product_db',
        'payment_service': 'payment_db',
        'inventory_service': 'inventory_db',
        'notification_service': 'notification_db',

        # 公共服务
        'zhi_common': 'default',
    }
    
    def db_for_read(self, model, **hints):
        """
        读取操作的数据库路由
        
        Args:
            model: Django模型类
            **hints: 路由提示
            
        Returns:
            str: 数据库别名
        """
        app_label = model._meta.app_label
        
        # 用户相关应用路由到用户数据库
        if app_label in self.user_apps:
            return 'user_db'

        # 日志相关应用路由到日志数据库
        if app_label in self.logger_apps:
            return 'logger_db'

        # 根据应用映射路由
        if app_label in self.service_db_mapping:
            return self.service_db_mapping[app_label]

        # 默认数据库
        return 'default'
    
    def db_for_write(self, model, **hints):
        """
        写入操作的数据库路由
        
        Args:
            model: Django模型类
            **hints: 路由提示
            
        Returns:
            str: 数据库别名
        """
        # 写入路由与读取路由保持一致
        return self.db_for_read(model, **hints)
    
    def allow_relation(self, obj1, obj2, **hints):
        """
        检查两个对象之间是否允许建立关系
        
        Args:
            obj1: 第一个对象
            obj2: 第二个对象
            **hints: 路由提示
            
        Returns:
            bool: 是否允许关系
        """
        # 获取两个对象的数据库
        db1 = obj1._state.db
        db2 = obj2._state.db
        
        # 如果在同一个数据库，允许关系
        if db1 == db2:
            return True
        
        # 定义允许跨数据库关系的数据库集合
        allowed_dbs = {
            'default', 'user_db', 'order_db', 'product_db', 
            'payment_db', 'inventory_db', 'notification_db'
        }
        
        # 如果两个数据库都在允许的集合中，允许关系
        if db1 in allowed_dbs and db2 in allowed_dbs:
            return True
        
        # 特殊情况：允许业务数据库与用户数据库的关系
        cross_db_allowed = {
            ('user_db', 'order_db'),
            ('user_db', 'product_db'),
            ('user_db', 'payment_db'),
            ('user_db', 'inventory_db'),
            ('user_db', 'notification_db'),
        }
        
        if (db1, db2) in cross_db_allowed or (db2, db1) in cross_db_allowed:
            return True
        
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        控制迁移操作的数据库路由
        
        Args:
            db: 数据库别名
            app_label: 应用标签
            model_name: 模型名称
            **hints: 路由提示
            
        Returns:
            bool: 是否允许在指定数据库执行迁移
        """
        # 用户相关应用只能在用户数据库迁移
        if app_label in self.user_apps:
            return db == 'user_db'

        # 日志相关应用只能在日志数据库迁移
        if app_label in self.logger_apps:
            return db == 'logger_db'

        # 根据应用映射控制迁移
        if app_label in self.service_db_mapping:
            target_db = self.service_db_mapping[app_label]
            return db == target_db

        # 公共应用可以在默认数据库迁移
        if app_label in self.common_apps:
            return db == 'default'

        # 其他应用在默认数据库迁移
        return db == 'default'


class ReadWriteSplitRouter:
    """
    读写分离路由器
    
    支持主从数据库的读写分离
    """
    
    # 读写分离配置
    read_db_mapping = {
        'user_db': 'user_db_read',
        'order_db': 'order_db_read',
        'product_db': 'product_db_read',
        'payment_db': 'payment_db_read',
    }
    
    def db_for_read(self, model, **hints):
        """读操作路由到从数据库"""
        app_label = model._meta.app_label
        
        # 先通过微服务路由器获取主数据库
        main_router = MicroserviceRouter()
        main_db = main_router.db_for_read(model, **hints)
        
        # 如果有对应的读数据库，使用读数据库
        if main_db in self.read_db_mapping:
            read_db = self.read_db_mapping[main_db]
            # 检查读数据库是否在配置中
            if read_db in getattr(settings, 'DATABASES', {}):
                return read_db
        
        # 否则使用主数据库
        return main_db
    
    def db_for_write(self, model, **hints):
        """写操作路由到主数据库"""
        main_router = MicroserviceRouter()
        return main_router.db_for_write(model, **hints)
    
    def allow_relation(self, obj1, obj2, **hints):
        """委托给主路由器"""
        main_router = MicroserviceRouter()
        return main_router.allow_relation(obj1, obj2, **hints)
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """委托给主路由器"""
        main_router = MicroserviceRouter()
        return main_router.allow_migrate(db, app_label, model_name, **hints)


class TenantRouter:
    """
    多租户路由器
    
    根据租户ID路由到不同的数据库
    """
    
    def __init__(self):
        self.tenant_db_mapping = getattr(settings, 'TENANT_DB_MAPPING', {})
    
    def db_for_read(self, model, **hints):
        """根据租户路由读操作"""
        # 从hints中获取租户信息
        tenant_id = hints.get('tenant_id')
        if tenant_id and tenant_id in self.tenant_db_mapping:
            return self.tenant_db_mapping[tenant_id]
        
        # 委托给微服务路由器
        main_router = MicroserviceRouter()
        return main_router.db_for_read(model, **hints)
    
    def db_for_write(self, model, **hints):
        """根据租户路由写操作"""
        return self.db_for_read(model, **hints)
    
    def allow_relation(self, obj1, obj2, **hints):
        """委托给主路由器"""
        main_router = MicroserviceRouter()
        return main_router.allow_relation(obj1, obj2, **hints)
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """委托给主路由器"""
        main_router = MicroserviceRouter()
        return main_router.allow_migrate(db, app_label, model_name, **hints)


def get_database_for_model(model_class):
    """
    获取模型对应的数据库
    
    Args:
        model_class: Django模型类
        
    Returns:
        str: 数据库别名
    """
    router = MicroserviceRouter()
    return router.db_for_read(model_class)


def get_user_database():
    """获取用户数据库别名"""
    return 'user_db'


def execute_on_user_db(queryset_or_model):
    """
    在用户数据库上执行查询
    
    Args:
        queryset_or_model: QuerySet或Model类
        
    Returns:
        QuerySet: 指定数据库的查询集
    """
    if hasattr(queryset_or_model, 'using'):
        # 如果是QuerySet
        return queryset_or_model.using('user_db')
    else:
        # 如果是Model类
        return queryset_or_model.objects.using('user_db')


def execute_on_service_db(queryset_or_model, service_name):
    """
    在指定服务数据库上执行查询
    
    Args:
        queryset_or_model: QuerySet或Model类
        service_name: 服务名称
        
    Returns:
        QuerySet: 指定数据库的查询集
    """
    router = MicroserviceRouter()
    db_mapping = router.service_db_mapping
    
    db_name = db_mapping.get(f"{service_name}_service", 'default')
    
    if hasattr(queryset_or_model, 'using'):
        return queryset_or_model.using(db_name)
    else:
        return queryset_or_model.objects.using(db_name)
