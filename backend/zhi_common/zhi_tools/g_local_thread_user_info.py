"""
独立出从 Request 请求中获取用户，防止 出现 `most likely due to a circular import` 问题
"""


import logging
from typing import Any, Dict, Union

from django.contrib.auth.models import AbstractBaseUser, AnonymousUser
from django.http import HttpRequest

logger = logging.getLogger(__name__)
from zhi_common.zhi_middlewares.core_req_preprocessor import g_thread_locals


def get_request_user_info(request: HttpRequest = None) -> Union[AbstractBaseUser, AnonymousUser, Dict[str, Any]]:
    """
    获取请求用户（优化版）
    1. 优先从request.user 获取已认证用户
    2. 未认证时尝试从token解析用户
    3. 添加类型注解和完整错误处理
    """
    if not request:
        request = getattr(g_thread_locals, 'request', None)
    try:
        user = getattr(request, 'user', None)
        if user and user.is_authenticated:
            # 检查用户是否有 to_dict 属性
            if hasattr(user, 'to_dict'):
                return user.to_dict
            else:
                # 如果没有 to_dict 属性，手动构建用户信息字典
                return {
                    'id': getattr(user, 'id', None),
                    'username': getattr(user, 'username', ''),
                    'name': getattr(user, 'name', '') or getattr(user, 'get_full_name', lambda: '')() or getattr(user, 'username', ''),
                    'email': getattr(user, 'email', ''),
                    'is_superuser': getattr(user, 'is_superuser', False),
                    'is_staff': getattr(user, 'is_staff', False),
                    'org_id': getattr(user, 'org_id', None),
                }
        else:
            # 用户未认证或为匿名用户
            return {}

    except Exception as e:
        logger.debug(f" 获取用户信息失败: {str(e)}", exc_info=True)

    return {}
