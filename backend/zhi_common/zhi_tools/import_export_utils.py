import csv
import io
import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Type
from decimal import Decimal

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from django.db.models import Model
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile

try:
    from zhi_common.zhi_logger.core_logger import zhi_logger
except ImportError:
    # 如果导入失败，使用标准logging
    import logging
    zhi_logger = logging.getLogger(__name__)


class ImportExportUtils:
    """导入导出工具类"""
    
    # 支持的文件格式
    SUPPORTED_EXPORT_FORMATS = ['csv', 'excel', 'json']
    SUPPORTED_IMPORT_FORMATS = ['csv', 'excel']
    
    # Excel样式配置
    HEADER_STYLE = {
        'font': Font(bold=True, color='FFFFFF'),
        'fill': <PERSON><PERSON><PERSON>ill(start_color='366092', end_color='366092', fill_type='solid'),
        'alignment': Alignment(horizontal='center', vertical='center')
    }
    
    @staticmethod
    def export_to_csv(data: List[Dict], filename: str = None, field_mapping: Dict[str, str] = None) -> HttpResponse:
        """导出为CSV格式"""
        try:
            if not data:
                return HttpResponse("没有数据可导出", content_type="text/plain; charset=utf-8")
            
            # 创建CSV响应
            response = HttpResponse(content_type='text/csv; charset=utf-8')
            response['Content-Disposition'] = f'attachment; filename="{filename or "export.csv"}"'
            response.write('\ufeff')  # 添加BOM以支持Excel中文显示
            
            writer = csv.writer(response)
            
            # 获取字段名和表头
            fields = list(data[0].keys())
            headers = []
            for field in fields:
                if field_mapping and field in field_mapping:
                    headers.append(field_mapping[field])
                else:
                    headers.append(field)
            
            # 写入表头
            writer.writerow(headers)
            
            # 写入数据
            for row in data:
                row_data = []
                for field in fields:
                    value = row.get(field, '')
                    # 处理特殊数据类型
                    if isinstance(value, (list, dict)):
                        value = json.dumps(value, ensure_ascii=False)
                    elif value is None:
                        value = ''
                    row_data.append(str(value))
                writer.writerow(row_data)
            
            return response
            
        except Exception as e:
            zhi_logger.error(f"CSV导出失败: {str(e)}")
            return HttpResponse(f"导出失败: {str(e)}", status=500)
    
    @staticmethod
    def export_to_excel(data: List[Dict], filename: str = None, field_mapping: Dict[str, str] = None) -> HttpResponse:
        """导出为Excel格式"""
        try:
            if not data:
                return HttpResponse("没有数据可导出", content_type="text/plain; charset=utf-8")
            
            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "数据导出"
            
            # 获取字段名和表头
            fields = list(data[0].keys())
            headers = []
            for field in fields:
                if field_mapping and field in field_mapping:
                    headers.append(field_mapping[field])
                else:
                    headers.append(field)
            
            # 写入表头并设置样式
            for col_num, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_num, value=header)
                cell.font = ImportExportUtils.HEADER_STYLE['font']
                cell.fill = ImportExportUtils.HEADER_STYLE['fill']
                cell.alignment = ImportExportUtils.HEADER_STYLE['alignment']
            
            # 写入数据
            for row_num, row in enumerate(data, 2):
                for col_num, field in enumerate(fields, 1):
                    value = row.get(field, '')
                    # 处理特殊数据类型
                    if isinstance(value, (list, dict)):
                        value = json.dumps(value, ensure_ascii=False)
                    elif value is None:
                        value = ''
                    ws.cell(row=row_num, column=col_num, value=value)
            
            # 自动调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width
            
            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename or "export.xlsx"}"'
            
            # 保存到响应
            wb.save(response)
            return response
            
        except Exception as e:
            zhi_logger.error(f"Excel导出失败: {str(e)}")
            return HttpResponse(f"导出失败: {str(e)}", status=500)
    
    @staticmethod
    def export_to_json(data: List[Dict], filename: str = None) -> HttpResponse:
        """导出为JSON格式"""
        try:
            response = HttpResponse(
                json.dumps(data, ensure_ascii=False, indent=2),
                content_type='application/json; charset=utf-8'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename or "export.json"}"'
            return response

        except Exception as e:
            zhi_logger.error(f"JSON导出失败: {str(e)}")
            return HttpResponse(f"导出失败: {str(e)}", status=500)

    @staticmethod
    def import_from_csv(file: UploadedFile, field_mapping: Dict[str, str] = None) -> Dict[str, Any]:
        """从CSV文件导入数据"""
        try:
            # 读取文件内容
            content = file.read()
            if isinstance(content, bytes):
                # 尝试不同的编码
                for encoding in ['utf-8', 'gbk', 'gb2312']:
                    try:
                        content = content.decode(encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise ValueError("无法识别文件编码")

            # 解析CSV
            csv_reader = csv.DictReader(io.StringIO(content))
            data = []
            errors = []

            for row_num, row in enumerate(csv_reader, 1):
                try:
                    # 应用字段映射
                    if field_mapping:
                        mapped_row = {}
                        for csv_field, db_field in field_mapping.items():
                            if csv_field in row:
                                mapped_row[db_field] = row[csv_field]
                        row = mapped_row

                    # 清理空值
                    cleaned_row = {k: v.strip() if isinstance(v, str) else v
                                 for k, v in row.items() if v is not None and v != ''}

                    if cleaned_row:  # 只添加非空行
                        data.append(cleaned_row)

                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")

            return {
                'success': True,
                'data': data,
                'total_rows': len(data),
                'errors': errors,
                'message': f"成功导入{len(data)}条记录" + (f"，{len(errors)}条错误" if errors else "")
            }

        except Exception as e:
            zhi_logger.error(f"CSV导入失败: {str(e)}")
            return {
                'success': False,
                'data': [],
                'total_rows': 0,
                'errors': [str(e)],
                'message': f"导入失败: {str(e)}"
            }

    @staticmethod
    def import_from_excel(file: UploadedFile, field_mapping: Dict[str, str] = None, sheet_name: str = None) -> Dict[str, Any]:
        """从Excel文件导入数据"""
        try:
            # 读取Excel文件
            wb = openpyxl.load_workbook(file, data_only=True)

            # 选择工作表
            if sheet_name and sheet_name in wb.sheetnames:
                ws = wb[sheet_name]
            else:
                ws = wb.active

            # 获取表头
            headers = []
            for cell in ws[1]:
                headers.append(cell.value)

            data = []
            errors = []

            # 读取数据行
            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), 2):
                try:
                    # 构建行数据字典
                    row_dict = {}
                    for col_num, value in enumerate(row):
                        if col_num < len(headers) and headers[col_num]:
                            row_dict[headers[col_num]] = value

                    # 应用字段映射
                    if field_mapping:
                        mapped_row = {}
                        for excel_field, db_field in field_mapping.items():
                            if excel_field in row_dict:
                                mapped_row[db_field] = row_dict[excel_field]
                        row_dict = mapped_row

                    # 清理空值和处理数据类型
                    cleaned_row = {}
                    for k, v in row_dict.items():
                        if v is not None and v != '':
                            # 处理特殊数据类型
                            if isinstance(v, str):
                                v = v.strip()
                            cleaned_row[k] = v

                    if cleaned_row:  # 只添加非空行
                        data.append(cleaned_row)

                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")

            return {
                'success': True,
                'data': data,
                'total_rows': len(data),
                'errors': errors,
                'message': f"成功导入{len(data)}条记录" + (f"，{len(errors)}条错误" if errors else "")
            }

        except Exception as e:
            zhi_logger.error(f"Excel导入失败: {str(e)}")
            return {
                'success': False,
                'data': [],
                'total_rows': 0,
                'errors': [str(e)],
                'message': f"导入失败: {str(e)}"
            }

    @staticmethod
    def validate_and_convert_data(data: List[Dict], model_class: Type[Model],
                                field_validators: Dict[str, callable] = None) -> Dict[str, Any]:
        """验证和转换导入数据"""
        try:
            validated_data = []
            errors = []

            for row_num, row in enumerate(data, 1):
                try:
                    validated_row = {}

                    # 遍历模型字段进行验证和转换
                    for field in model_class._meta.fields:
                        field_name = field.name
                        if field_name in row:
                            value = row[field_name]

                            # 跳过空值（除非字段不允许为空）
                            if value is None or value == '':
                                if not field.null and not field.blank and not hasattr(field, 'default'):
                                    raise ValueError(f"字段 {field_name} 不能为空")
                                continue

                            # 根据字段类型转换数据
                            try:
                                if hasattr(field, 'choices') and field.choices:
                                    # 枚举字段验证
                                    valid_values = [choice[0] for choice in field.choices]
                                    if value not in valid_values:
                                        raise ValueError(f"字段 {field_name} 的值 '{value}' 不在允许的选项中: {valid_values}")

                                elif field.__class__.__name__ == 'IntegerField':
                                    validated_row[field_name] = int(value)
                                elif field.__class__.__name__ == 'FloatField':
                                    validated_row[field_name] = float(value)
                                elif field.__class__.__name__ == 'DecimalField':
                                    validated_row[field_name] = Decimal(str(value))
                                elif field.__class__.__name__ == 'BooleanField':
                                    if isinstance(value, str):
                                        validated_row[field_name] = value.lower() in ('true', '1', 'yes', '是', '真')
                                    else:
                                        validated_row[field_name] = bool(value)
                                elif field.__class__.__name__ in ('DateField', 'DateTimeField'):
                                    if isinstance(value, str):
                                        # 尝试多种日期格式
                                        date_formats = ['%Y-%m-%d', '%Y/%m/%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S']
                                        for fmt in date_formats:
                                            try:
                                                validated_row[field_name] = datetime.strptime(value, fmt)
                                                break
                                            except ValueError:
                                                continue
                                        else:
                                            raise ValueError(f"无法解析日期格式: {value}")
                                    else:
                                        validated_row[field_name] = value
                                elif field.__class__.__name__ == 'JSONField':
                                    if isinstance(value, str):
                                        try:
                                            validated_row[field_name] = json.loads(value)
                                        except json.JSONDecodeError:
                                            validated_row[field_name] = value
                                    else:
                                        validated_row[field_name] = value
                                else:
                                    validated_row[field_name] = str(value)

                                # 应用自定义验证器
                                if field_validators and field_name in field_validators:
                                    validator = field_validators[field_name]
                                    validated_row[field_name] = validator(validated_row[field_name])

                            except (ValueError, TypeError, json.JSONDecodeError) as e:
                                raise ValueError(f"字段 {field_name} 数据转换失败: {str(e)}")

                    validated_data.append(validated_row)

                except Exception as e:
                    errors.append(f"第{row_num}行: {str(e)}")

            return {
                'success': len(errors) == 0,
                'data': validated_data,
                'total_rows': len(validated_data),
                'errors': errors,
                'message': f"验证完成，成功{len(validated_data)}条" + (f"，失败{len(errors)}条" if errors else "")
            }

        except Exception as e:
            zhi_logger.error(f"数据验证失败: {str(e)}")
            return {
                'success': False,
                'data': [],
                'total_rows': 0,
                'errors': [str(e)],
                'message': f"验证失败: {str(e)}"
            }

    @staticmethod
    def get_file_format(filename: str) -> str:
        """根据文件名获取文件格式"""
        if filename.lower().endswith('.csv'):
            return 'csv'
        elif filename.lower().endswith(('.xlsx', '.xls')):
            return 'excel'
        elif filename.lower().endswith('.json'):
            return 'json'
        else:
            return 'unknown'

    @staticmethod
    def generate_import_template(model_class: Type[Model], format_type: str = 'excel',
                               field_mapping: Dict[str, str] = None) -> HttpResponse:
        """生成导入模板"""
        try:
            # 获取模型字段
            fields = []
            headers = []

            for field in model_class._meta.fields:
                # 跳过自动字段和系统字段
                if (field.auto_created or
                    field.name in ['id', 'created_at', 'updated_at', 'is_deleted', 'creator_id', 'modifier_id']):
                    continue

                fields.append(field.name)

                # 使用字段映射或字段名作为表头
                if field_mapping and field.name in field_mapping:
                    headers.append(field_mapping[field.name])
                else:
                    # 尝试获取字段的verbose_name
                    header = getattr(field, 'verbose_name', field.name)
                    headers.append(header)

            # 创建示例数据
            sample_data = [dict(zip(fields, [''] * len(fields)))]

            # 根据格式生成模板
            if format_type == 'csv':
                return ImportExportUtils.export_to_csv(
                    sample_data,
                    f"{model_class.__name__}_import_template.csv",
                    dict(zip(fields, headers))
                )
            elif format_type == 'excel':
                return ImportExportUtils.export_to_excel(
                    sample_data,
                    f"{model_class.__name__}_import_template.xlsx",
                    dict(zip(fields, headers))
                )
            else:
                return HttpResponse("不支持的模板格式", status=400)

        except Exception as e:
            zhi_logger.error(f"生成导入模板失败: {str(e)}")
            return HttpResponse(f"生成模板失败: {str(e)}", status=500)
