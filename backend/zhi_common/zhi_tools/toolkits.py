import json
import logging
from datetime import datetime

from django.http import HttpRequest
from zhi_common.zhi_logger import zhi_logger

# 修复导入路径
try:
    from zhi_common.zhi_middlewares.core_req_preprocessor import g_thread_locals
except ImportError:
    # 如果导入失败，创建一个空的对象
    class MockThreadLocals:
        request = None
    g_thread_locals = MockThreadLocals()

try:
    from zhi_oauth.models import User
except ImportError:
    from django.contrib.auth import get_user_model
    User = get_user_model()

# 暂时注释掉不存在的导入
# from utils.base.auth_jwt.core_toolkits import get_user_from_token
# from system.models import Organization


logger = logging.getLogger(__name__)

DEFAULT_DATETIME_FORMAT  = "%Y-%m-%d %H:%M:%S"


def get_user_info_from_token(request: HttpRequest = None):
    """
    从token获取用户信息（兼容版本）
    """
    try:
        if not request:
            request = getattr(g_thread_locals, 'request', None)

        if not request:
            return None

        # 尝试从request.user获取用户信息
        if hasattr(request, 'user') and request.user.is_authenticated:
            user = request.user
            if user:
                return user.to_dict
            else:
                return None

        # 如果没有认证用户，尝试从Authorization头解析
        token = request.headers.get("Authorization", "").replace("Bearer ", "")
        if token:
            # 这里应该实现token解析逻辑，暂时返回None
            # user_info = get_user_from_token(token)
            # return user_info
            pass

        return None
    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")
        return None


def get_org(org_id: str, org_all_list=None, org_list=None):
    """
    递归获取部门的所有下级部门（暂时禁用，需要Organization模型）
    :param org_id: 需要获取的部门id
    :param org_all_list: 所有部门列表
    :param org_list: 递归部门list
    :return:
    """
    # 暂时返回空列表，等Organization模型可用后再实现
    logger.warning("get_org 函数暂时不可用，需要 Organization 模型")
    return [org_id] if org_id else []

    # 原始实现（暂时注释）
    # if not org_all_list:
    #     org_all_list = Organization.objects.filter(
    #         is_delete=False,
    #         is_active=True,
    #         ).all().values('id', 'parent')
    # if org_list is None:
    #     org_list = [org_id]
    # for ele in org_all_list:
    #     if ele.get('parent') == org_id:
    #         org_list.append(ele.get('id'))
    #         get_org(ele.get('id'), org_all_list, org_list)
    # return list(set(org_list))


def datetime_to_str(dt: datetime) -> str:
    """将datetime对象格式化为'YYYY-mm-dd HH:MM:SS'字符串"""
    return dt.strftime(DEFAULT_DATETIME_FORMAT) if dt else None


class DateEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.strftime("%Y-%m-%d %H:%M:%S")
        else:
            return json.JSONEncoder.default(self, obj)
