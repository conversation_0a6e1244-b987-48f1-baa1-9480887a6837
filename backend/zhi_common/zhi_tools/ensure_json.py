from datetime import datetime
from django.conf import settings


def ensure_json(data):
    """
    递归处理数据使其可JSON序列化
    返回处理后的安全数据副本
    """
    if isinstance(data, (str, int, float, bool)) or data is None:
        return data
    elif isinstance(data, dict):
        return {k: ensure_json(v) for k, v in data.items()}
    elif isinstance(data, (list, tuple, set)):
        return [ensure_json(item) for item in data]
    elif isinstance(data, datetime):
        return data.strftime(settings.DEFAULT_DATETIME_FORMAT)
    elif hasattr(data, '__dict__'):  # 处理模型实例
        return ensure_json(data.__dict__)
    else:
        return str(data)  # 兜底处理


if __name__ == '__main__':
    aa = {
        "a": 'b',
        "c": "c",
        "d": 'd'
        }
    dd = ensure_json(aa)
    print(dd)
