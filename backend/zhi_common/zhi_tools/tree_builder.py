from typing import List, Dict, Optional, Any, Callable


class TreeBuilder:
    """
    树形结构构建工具类（优化版）
    提供将扁平列表转换为树形结构的方法，确保子元素 choices 默认为 0
    """

    @staticmethod
    def list_to_tree(
            data: List[Dict], parent_key: str = 'parent_id',
            id_key: str = 'id', children_key: str = 'children'
            ) -> List[Dict]:
        """
        将扁平列表转换为树形结构（确保每个节点都有 choices 字段）

        参数:
            data: 原始数据列表
            parent_key: 父节点ID字段名 (默认 'parent_id')
            id_key: 节点ID字段名 (默认'id')
            children_key: 子节点字段名 (默认 'children')

        返回:
            树形结构数据（每个节点都有 choices 字段）
        """
        if not data:
            return []

        # 创建ID到节点的映射（确保每个节点都有 children 和 choices）
        id_map = {}
        for item in data:
            item[children_key] = item.get(children_key, [])
            item['choices'] = item.get('choices', 0)
            id_map[item[id_key]] = item

            # 初始化树
        tree = []

        for item in data:
            pid = item.get(parent_key)

            # 如果是根节点
            if pid is None or pid not in id_map:
                tree.append(item)
            else:
                # 找到父节点并添加当前节点为子节点
                parent = id_map.get(pid)
                if parent:
                    parent[children_key].append(item)
                    parent['choices'] = len(parent[children_key])

        return tree

    @staticmethod
    def list_to_tree_with_order(
            data: List[Dict], parent_key: str = 'parent_id',
            id_key: str = 'id', children_key: str = 'children',
            order_key: str = 'sort'
            ) -> List[Dict]:
        """
        带排序功能的树形结构构建（确保 choices 正确计数）

        参数:
            data: 原始数据列表
            parent_key: 父节点ID字段名
            id_key: 节点ID字段名
            children_key: 子节点字段名
            order_key: 排序字段名

        返回:
            排序后的树形结构数据（每个节点都有正确的 choices 计数）
        """
        # 先构建基础树结构
        tree = TreeBuilder.list_to_tree(data, parent_key, id_key, children_key)

        # 递归排序函数（同时更新 choices）
        def _sort_and_count(node: Dict) -> None:
            if children_key in node and node[children_key]:
                # 排序子节点
                node[children_key].sort(key=lambda x: x.get(order_key, 0))
                # 更新 choices 计数
                node['choices'] = len(node[children_key])
                # 递归处理子节点
                for child in node[children_key]:
                    _sort_and_count(child)

        # 对每棵子树进行排序
        for root in tree:
            _sort_and_count(root)

        return tree

    @staticmethod
    def find_node(
            tree: List[Dict], node_id: Any,
            id_key: str = 'id', children_key: str = 'children'
            ) -> Optional[Dict]:
        """
        在树中查找特定节点（带 choices 字段）

        参数:
            tree: 树形结构数据
            node_id: 要查找的节点ID
            id_key: 节点ID字段名
            children_key: 子节点字段名

        返回:
            找到的节点（包含 choices 字段）或 None
        """
        for node in tree:
            if node.get(id_key) == node_id:
                return node
            if children_key in node:
                found = TreeBuilder.find_node(node[children_key], node_id, id_key, children_key)
                if found:
                    return found
        return None

    @staticmethod
    def flatten_tree(tree: List[Dict], children_key: str = 'children') -> List[Dict]:
        """
        将树形结构扁平化为列表（保留 choices 字段）

        参数:
            tree: 树形结构数据
            children_key: 子节点字段名

        返回:
            扁平化后的列表（每个节点保留 choices 字段）
        """
        result = []
        for node in tree:
            # 复制节点但排除 children
            node_copy = {k: v for k, v in node.items() if k != children_key}
            result.append(node_copy)
            if children_key in node and node[children_key]:
                result.extend(TreeBuilder.flatten_tree(node[children_key], children_key))
        return result

    @staticmethod
    def filter_tree(
            tree: List[Dict], filter_func: Callable[[Dict], bool],
            children_key: str = 'children'
            ) -> List[Dict]:
        """
        过滤树形结构（保持 choices 计数准确）

        参数:
            tree: 树形结构数据
            filter_func: 过滤函数，接收节点返回布尔值
            children_key: 子节点字段名

        返回:
            过滤后的新树（每个节点有正确的 choices 计数）
        """
        filtered = []
        for node in tree:
            # 先过滤子节点
            if children_key in node:
                filtered_children = TreeBuilder.filter_tree(
                    node[children_key], filter_func, children_key
                    )
                # 复制节点并更新子节点和 choices
                node_copy = node.copy()
                node_copy[children_key] = filtered_children
                node_copy['choices'] = len(filtered_children)
            else:
                node_copy = node.copy()
                node_copy['choices'] = 0

                # 如果节点本身或子节点满足条件则保留
            if filter_func(node) or (children_key in node_copy and node_copy[children_key]):
                filtered.append(node_copy)

        return filtered

    @staticmethod
    def count_children(tree: List[Dict], children_key: str = 'children') -> int:
        """
        计算树中所有子节点总数（包括 choices 字段）

        参数:
            tree: 树形结构数据
            children_key: 子节点字段名

        返回:
            所有子节点的总数
        """
        count = 0
        for node in tree:
            if children_key in node:
                count += len(node[children_key])
                count += TreeBuilder.count_children(node[children_key], children_key)
        return count


if __name__ == '__main__':
    temp_data = [
        {'id': 1, 'name': 'Root', 'parent_id': None, 'sort': 1},
        {'id': 2, 'name': 'Child 1', 'parent_id': 1, 'sort': 2},
        {'id': 3, 'name': 'Child 2', 'parent_id': 1, 'sort': 1},
        {'id': 4, 'name': 'Grandchild', 'parent_id': 2, 'sort': 1}
        ]

    # 构建带排序的树（自动添加 choices）
    temp_tree = TreeBuilder.list_to_tree_with_order(temp_data)
    # 输出: [{'id':1, 'name':'Root', 'children':[...], 'choices':2}]

    # 查找节点
    temp_node = TreeBuilder.find_node(temp_tree, 2)
    # 输出: {'id':2, 'name':'Child 1', 'children':[...], 'choices':1}

    # 过滤树
    filtered_tree = TreeBuilder.filter_tree(temp_tree, lambda x: x['id'] != 3)
    # 输出: [{'id':1, 'name':'Root', 'children':[...], 'choices':1}]

    # 计算子节点总数
    total_children = TreeBuilder.count_children(temp_tree)
    # 输出: 3 'id': 2, 'name': 'Node 2', 'parent_id': 1}
