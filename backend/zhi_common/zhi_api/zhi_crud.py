import json
from typing import Any, Dict, List, Optional, Type, Union, Set, cast
from decimal import Decimal
from datetime import datetime
from django.db.models import Model, Q, QuerySet
from django.db  import models
from django.core.paginator import Paginator
from django.conf import settings
from django.utils import timezone
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile
from ninja import Schema
from zhi_common.zhi_response.base import ZhiResponse, ZhiModelResponse, create_response, ResponseCode
from zhi_common.zhi_logger.core_logger import zhi_logger
from zhi_common.zhi_tools.import_export_utils import ImportExportUtils
from .core_filters.user_filters import get_user_filters
from .base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from .base_config import QuerySecurityManager


class BaseModelService:
    """完整版服务基类，适配自定义响应码"""

    RELATION_LOOKUPS = {
        'contains': 'icontains',
        'startswith': 'istartswith',
        'endswith': 'iendswith',
        'gt': 'gt',
        'gte': 'gte',
        'lt': 'lt',
        'lte': 'lte',
        'in': 'in',
        'range': 'range'
        }

    def __init__(
            self,
            model_class: Type[Model],
            schema_out: Type[Schema] = None,
            export_settings: dict = None,
            import_settings: dict = None,
            mappings_settings: dict = None,
            allowed_query_fields: Set[str] = None,
            model_exclude: List[str] = None,
            **kwargs,
            ):
        self.model = model_class
        self.schema_out = schema_out
        self.model_exclude = model_exclude or BASE_SCHEMA_IN_EXCLUDE_FIELDS
        self.export_settings = export_settings or {}
        self.import_settings = import_settings or {}
        self.mappings_settings = mappings_settings or {}
        self.security_mgr = QuerySecurityManager(
            model_class=model_class,
            allowed_fields=allowed_query_fields
            )

    @staticmethod
    def _handle_response(data=None, code=ResponseCode.SUCCESS, message='ok', success=True, **kwargs):
        """统一响应处理方法"""
        return create_response(
            data=data,
            code=code,
            message=message,
            success=success,
            **kwargs
            )

    @staticmethod
    def serialize_data(queryset: Union[Model, List[Model], QuerySet], schema_out=None):
        """统一序列化数据，多对多关系默认只返回ID列表"""

        def model_to_dict(instance: Model) -> Model | dict[Any, list[Any] | None | str | Any]:
            """模型转字典方法，处理多对多关系只返回ID列表"""
            if not isinstance(instance, Model):
                return instance

            data = {}
            # 使用类型断言告诉类型检查器instance是Model类型
            model_meta = cast(Model, instance)._meta

            for field in model_meta.get_fields():
                # 跳过反向关系
                if getattr(field, 'auto_created', False) and not getattr(field, 'concrete', False):
                    zhi_logger.info(f'Continued: filed_name: {field.name}')
                    continue

                field_name = field.name
                # 处理外键关系 - 优先使用_id后缀字段
                if field.many_to_one or field.one_to_one:
                    # 检查是否存在对应的_id字段（如org_id）
                    id_field_name = f"{field_name}_id"
                    if hasattr(instance, id_field_name):
                        id_value = getattr(instance, id_field_name)
                        data[field_name] = str(id_value) if id_value is not None else None
                    else:
                        # 回退到对象方式
                        try:
                            value = getattr(instance, field_name)
                            data[field_name] = str(value.id) if value else None
                        except Exception as exc:
                            zhi_logger.warning(f" 获取外键字段失败: {field_name}, 错误: {str(exc)}")
                            data[field_name] = None
                            # 处理多对多关系
                elif field.many_to_many:
                    try:
                        related_manager = getattr(instance, field_name)
                        data[field_name] = [str(obj.id) for obj in related_manager.all()]
                    except Exception as e:
                        zhi_logger.warning(f" 获取多对多关系失败: {field_name}, 错误: {str(e)}")
                        data[field_name] = []
                # 处理普通字段
                else:
                    try:
                        value = getattr(instance, field_name)
                        if value is None:
                            data[field_name] = None
                        elif isinstance(value, (datetime, timezone.datetime)):
                            data[field_name] = value.isoformat()
                        else:
                            data[field_name] = value
                    except Exception as e:
                        zhi_logger.warning(f" 获取字段失败: {field_name}, 错误: {str(e)}")
                        continue
            return data

                        # 处理Schema输出
        if schema_out is not None:
            try:
                if isinstance(queryset, (QuerySet, list)):
                    return [schema_out.model_validate(model_to_dict(obj)).model_dump()
                            for obj in queryset]
                return schema_out.model_validate(model_to_dict(queryset)).model_dump()
            except Exception as e:
                zhi_logger.error(f"Schema 序列化失败: {str(e)}")
                # 回退到基础序列化
                if isinstance(queryset, (QuerySet, list)):
                    return [model_to_dict(obj) for obj in queryset]
                return model_to_dict(queryset)

        # 默认处理
        if hasattr(queryset, 'to_dict'):
            if isinstance(queryset, (list, QuerySet)):
                return [item.to_dict for item in queryset]
            return queryset.to_dict

            # 基础序列化
        if isinstance(queryset, (QuerySet, list)):
            return [model_to_dict(obj) for obj in queryset]
        return model_to_dict(queryset)

    def _build_query_conditions(self, filters: Dict) -> Dict:
        """构建查询条件字典"""
        query = {}
        # 只保留必要的默认条件
        for key, value in filters.items():
            # 1. 跳过空值、None值和分页参数
            if value is None or value == '' or key in ['page', 'page_size', 'ordering', 'is_all']:
                continue

            # 2. 安全验证
            try:
                self.security_mgr.validate_field_access(key)
                self.security_mgr.sanitize_operator(key)
            except ValueError as e:
                zhi_logger.warning(f" 查询安全拦截: {key}={value}, 原因: {str(e)}")
                continue

            # 3. 处理特殊条件
            if key.endswith('_start'):
                field = key[:-6]
                query[f"{field}__gte"] = self._convert_value(field, value)
            elif key.endswith('_end'):
                field = key[:-4]
                query[f"{field}__lte"] = self._convert_value(field, value)
            elif '__' in key:
                parts = key.split('__')
                if self._is_valid_lookup(parts):
                    # 特殊处理数组参数（兼容逗号分隔和直接数组两种形式）
                    if '__in' in key and not isinstance(value, (list, tuple)):
                        if isinstance(value, str):
                            value = [v.strip() for v in value.split(',') if v.strip()]
                        else:
                            value = [value]
                    query[key] = self._convert_value(parts[0], value)
            else:
                query[key] = self._convert_value(key, value)
        # 4. 注入强制安全条件
        query.update(self._get_mandatory_conditions())
        return query

    def _get_mandatory_conditions(self) -> Dict:
        """获取强制注入的安全条件"""
        conditions = {'is_deleted': False}

        # 示例：自动注入租户隔离
        if hasattr(self, 'current_tenant_id'):
            conditions['tenant_id'] = self.current_tenant_id

        return conditions

    def _convert_value(self, field_name: str, value):
        """安全增强版值转换"""
        if value is None:
            return None

        try:
            base_field = field_name.split('__')[0]
            field = self.model._meta.get_field(base_field)

            # 枚举字段验证
            if hasattr(field, 'choices') and field.choices:
                valid_values = {str(k) for k, v in field.choices}
                if str(value) not in valid_values:
                    raise ValueError(f"无效枚举值: {value} (允许值: {valid_values})")

            # 类型转换
            if isinstance(field, (models.IntegerField, models.ForeignKey)):
                return int(value)
            if isinstance(field, models.BooleanField):
                return str(value).lower() in ('true', '1', 'yes')
            if isinstance(field, (models.DateField, models.DateTimeField)):
                return datetime.fromisoformat(value) if value else None
            if isinstance(field, models.DecimalField):
                return Decimal(str(value))
            if isinstance(field, models.JSONField):
                if isinstance(value, str):
                    return json.loads(value)
                return value

        except (ValueError, json.JSONDecodeError) as e:
            zhi_logger.warning(f" 值转换失败: {field_name}={value}, 错误: {str(e)}")
            raise ValueError(f"字段[{field_name}]值格式非法") from e

        return value

    def _is_valid_lookup(self, parts: List[str]) -> bool:
        """验证查询条件是否有效"""
        for part in parts[1:-1]:
            if part not in self.RELATION_LOOKUPS:
                return False
        return True

    def list_id_mappings(self, **kwargs):
        """安全版批量创建方法，支持复合主键"""
        if not self.mappings_settings.get('is_enabled', False):
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message="未配置列表映射功能",
                success=False,
                )
        try:
            is_all = kwargs.get('is_all', False)
            if is_all:
                data = self.model.objects.filter(
                    is_deleted=False,
                    ).values(
                    self.mappings_settings.get('id_key', 'id'), self.mappings_settings.get('value_key', 'name')
                    )
            else:
                query_conditions = self._build_query_conditions(kwargs)
                data = self.model.objects.filter(
                    **query_conditions,
                    ).values(self.mappings_settings.get('id_key', 'id'), self.mappings_settings.get('value_key', 'name'))
            return self._handle_response(
                data=[{
                    "id": row[self.mappings_settings.get('id_key', 'id')],
                    "value": row[self.mappings_settings.get('value_key', 'name')],
                    } for row in data],
                code=ResponseCode.SUCCESS,
                message="查询成功",
                )
        except Exception as e:
            zhi_logger.warning(f"获取Key-Value错误: {type(e)} - {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message="获取Key-Value失败",
                success=False,
                )

    def list(
            self,
            filters: Optional[Dict] = None,
            ordering: Optional[List[str]] = None,
            page: int = 1,
            page_size: int = 10,
            pagination: bool = True,
            **kwargs
            ) -> Union[ZhiResponse, ZhiModelResponse]:
        try:
            zhi_logger.info(f'list:filters: {filters}, kwargs: {kwargs}')
            perm_filter = get_user_filters(filters, model_class=self.model)
            zhi_logger.info(f'page: {page}, page_size: {page_size}, pagination: {pagination}')

            zhi_logger.info(f'list:perm_filter: {perm_filter}')
            queryset = self.model.objects.filter(**perm_filter)
            # 处理查询条件
            if filters:
                query_conditions = self._build_query_conditions(filters)
                zhi_logger.info(f'list:query_conditions: {query_conditions}')

                queryset = queryset.filter(**query_conditions)

                # 处理排序
            if ordering:
                queryset = queryset.order_by(*ordering)

                # 处理分页
            if pagination and not kwargs.get('is_all', False):
                paginator = Paginator(queryset, page_size)
                page_obj = paginator.get_page(page)
                return self._handle_response(
                    data={
                        "items": self.serialize_data(page_obj.object_list, self.schema_out),
                        "total": paginator.count,
                        "page": page_obj.number,
                        "page_size": page_size,
                        "pages": paginator.num_pages
                        }
                    )
            print(queryset)
            return self._handle_response(
                data=[obj.to_dict for obj in queryset],
                message="查询成功"
                )

        except Exception as e:
            zhi_logger.warning(f" 查询失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message="查询失败",
                success=False
                )

    def retrieve(self, id: Any) -> Union[ZhiResponse, ZhiModelResponse]:
        try:
            instance = self.model.objects.get(id=id, is_deleted=False)
            return self._handle_response(
                data=instance.to_dict,
                message="查询成功"
                )
        except self.model.DoesNotExist:
            zhi_logger.info(f'retrieve: {id} not found')
            return self._handle_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="记录不存在",
                success=False
                )
        except Exception as e:
            zhi_logger.warning(f"查询详情失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"查询详情失败",
                success=False
                )

    def create(self, data: Dict, **kwargs) -> Union[ZhiResponse, ZhiModelResponse]:
        try:
            create_data = {k: v for k, v in data.items() if k not in self.model_exclude}
            print(create_data)
            instance = self.model.objects.create(**create_data)
            return self._handle_response(
                data=self.serialize_data(instance, self.schema_out),
                message="创建成功",
                **kwargs
                )
        except Exception as e:
            zhi_logger.warning(f"创建失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.UNPROCESSABLE_ENTITY,
                message=f"创建失败",
                success=False
                )

    def update(
            self,
            id: Any,
            data: Dict,
            **kwargs
            ) -> Union[ZhiResponse, ZhiModelResponse]:
        try:
            instance = self.model.objects.get(id=id, is_deleted=False)

            model_fields = {f.name for f in instance._meta.fields}
            updatable_fields = model_fields - self.model_exclude

            update_data = {}
            for field, value in data.items():
                if field in updatable_fields:
                    update_data[field] = value

            for field, value in update_data.items():
                setattr(instance, field, value)

            instance.save()
            return self._handle_response(
                data=instance.to_dict,
                message="更新成功",
                **kwargs
                )

        except self.model.DoesNotExist:
            zhi_logger.info(f'update: {id} not found')
            return self._handle_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="记录不存在",
                success=False
                )
        except Exception as e:
            zhi_logger.warning(f"更新失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.UNPROCESSABLE_ENTITY,
                message=f"更新失败",
                success=False
                )

    def delete(self, id: Any, **kwargs) -> Union[ZhiResponse, ZhiModelResponse]:
        try:
            instance = self.model.objects.get(id=id, is_deleted=False)
            instance.delete()
            return self._handle_response(
                data={},
                code=ResponseCode.SUCCESS,
                message="删除成功",
                **kwargs
                )
        except self.model.DoesNotExist:
            zhi_logger.info(f'deleted {id} not Found')
            return self._handle_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="记录不存在",
                success=False
                )
        except Exception as e:
            zhi_logger.warning(f"删除失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"删除失败",
                success=False
                )

    def batch_create(self, data: List[dict]):
        """安全版批量创建方法，支持复合主键"""
        try:
            model = self._model if hasattr(self, '_model') else self.model

            # 预处理数据：确保不包含主键字段
            cleaned_data = []
            for _data in data:
                update_data = {k: v for k, v in _data.items() if k not in self.model_exclude}
                cleaned_data.append(update_data)

            # 批量创建
            res_data = []
            for data in cleaned_data:
                obj = self.model.objects.create(**data)
                res_data.append(obj.to_dict)
            return self._handle_response(data=res_data, message='批量创建成功')
            # 返回创建结果（包含系统生成的主键）
        except Exception as e:
            zhi_logger.warning(f"批量创建错误详情: {type(e)} - {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message="批量创建失败",
                success=False
                )

    def export_data(
            self,
            format_type: str = 'excel',
            filename: str = None,
            filters: Optional[Dict] = None,
            fields: Optional[List[str]] = None,
            **kwargs
    ) -> Union[HttpResponse, ZhiResponse]:
        """
        导出数据

        Args:
            format_type: 导出格式 (csv, excel, json)
            filename: 文件名
            filters: 过滤条件
            fields: 要导出的字段列表
            **kwargs: 其他参数

        Returns:
            HttpResponse: 文件下载响应 或 ZhiResponse: 错误响应
        """
        try:
            # 检查导出配置
            if not self.export_settings.get('enabled', True):
                return self._handle_response(
                    data=None,
                    code=ResponseCode.FORBIDDEN,
                    message="导出功能未启用",
                    success=False
                )

            # 验证导出格式
            if format_type not in ImportExportUtils.SUPPORTED_EXPORT_FORMATS:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message=f"不支持的导出格式: {format_type}",
                    success=False
                )

            # 构建查询条件
            perm_filter = get_user_filters(filters, model_class=self.model)
            queryset = self.model.objects.filter(**perm_filter)

            if filters:
                query_conditions = self._build_query_conditions(filters)
                queryset = queryset.filter(**query_conditions)

            # 应用导出字段限制
            export_fields = fields or self.export_settings.get('fields', None)
            if export_fields:
                # 验证字段权限
                allowed_fields = set(export_fields) - set(self.model_exclude)
                if not allowed_fields:
                    return self._handle_response(
                        data=None,
                        code=ResponseCode.FORBIDDEN,
                        message="没有可导出的字段",
                        success=False
                    )

            # 限制导出数量
            max_export_rows = self.export_settings.get('max_rows', 10000)
            if queryset.count() > max_export_rows:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message=f"导出数据量超过限制({max_export_rows}条)",
                    success=False
                )

            # 序列化数据
            data = self.serialize_data(queryset, self.schema_out)

            # 应用字段过滤
            if export_fields:
                filtered_data = []
                for item in data:
                    filtered_item = {k: v for k, v in item.items() if k in export_fields}
                    filtered_data.append(filtered_item)
                data = filtered_data

            # 获取字段映射
            field_mapping = self.export_settings.get('field_mapping', {})

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{self.model.__name__}_export_{timestamp}"

            # 根据格式导出
            if format_type == 'csv':
                return ImportExportUtils.export_to_csv(data, f"{filename}.csv", field_mapping)
            elif format_type == 'excel':
                return ImportExportUtils.export_to_excel(data, f"{filename}.xlsx", field_mapping)
            elif format_type == 'json':
                return ImportExportUtils.export_to_json(data, f"{filename}.json")

        except Exception as e:
            zhi_logger.error(f"数据导出失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"导出失败: {str(e)}",
                success=False
            )

    def import_data(
            self,
            file: UploadedFile,
            preview_only: bool = False,
            field_mapping: Optional[Dict[str, str]] = None,
            **kwargs
    ) -> Union[ZhiResponse, ZhiModelResponse]:
        """
        导入数据

        Args:
            file: 上传的文件
            preview_only: 是否仅预览，不实际导入
            field_mapping: 字段映射 {文件字段名: 模型字段名}
            **kwargs: 其他参数

        Returns:
            ZhiResponse: 导入结果
        """
        try:
            # 检查导入配置
            if not self.import_settings.get('enabled', True):
                return self._handle_response(
                    data=None,
                    code=ResponseCode.FORBIDDEN,
                    message="导入功能未启用",
                    success=False
                )

            # 验证文件格式
            file_format = ImportExportUtils.get_file_format(file.name)
            if file_format not in ImportExportUtils.SUPPORTED_IMPORT_FORMATS:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message=f"不支持的文件格式: {file_format}",
                    success=False
                )

            # 验证文件大小
            max_file_size = self.import_settings.get('max_file_size', 10 * 1024 * 1024)  # 默认10MB
            if file.size > max_file_size:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message=f"文件大小超过限制({max_file_size / 1024 / 1024:.1f}MB)",
                    success=False
                )

            # 使用配置的字段映射或传入的映射
            field_mapping = field_mapping or self.import_settings.get('field_mapping', {})

            # 解析文件
            if file_format == 'csv':
                import_result = ImportExportUtils.import_from_csv(file, field_mapping)
            elif file_format == 'excel':
                sheet_name = self.import_settings.get('sheet_name', None)
                import_result = ImportExportUtils.import_from_excel(file, field_mapping, sheet_name)
            else:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message="不支持的导入格式",
                    success=False
                )

            if not import_result['success']:
                return self._handle_response(
                    data=import_result,
                    code=ResponseCode.BAD_REQUEST,
                    message=import_result['message'],
                    success=False
                )

            # 如果只是预览，返回解析结果
            if preview_only:
                preview_data = {
                    'total_rows': import_result['total_rows'],
                    'sample_data': import_result['data'][:5],  # 只显示前5行
                    'headers': list(import_result['data'][0].keys()) if import_result['data'] else [],
                    'field_mapping': field_mapping,
                    'errors': import_result['errors']
                }
                return self._handle_response(
                    data=preview_data,
                    message="预览成功"
                )

            # 验证和转换数据
            field_validators = self.import_settings.get('field_validators', {})
            validation_result = ImportExportUtils.validate_and_convert_data(
                import_result['data'],
                self.model,
                field_validators
            )

            if not validation_result['success']:
                return self._handle_response(
                    data=validation_result,
                    code=ResponseCode.BAD_REQUEST,
                    message=validation_result['message'],
                    success=False
                )

            # 限制导入数量
            max_import_rows = self.import_settings.get('max_rows', 1000)
            if len(validation_result['data']) > max_import_rows:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message=f"导入数据量超过限制({max_import_rows}条)",
                    success=False
                )

            # 执行批量导入
            batch_result = self.batch_create(validation_result['data'])

            # 合并结果
            final_result = {
                'success': batch_result.data is not None,
                'total_rows': import_result['total_rows'],
                'success_rows': len(batch_result.data) if batch_result.data else 0,
                'error_rows': len(validation_result['errors']),
                'errors': validation_result['errors'],
                'message': f"导入完成，成功{len(batch_result.data) if batch_result.data else 0}条"
            }

            return self._handle_response(
                data=final_result,
                message=final_result['message']
            )

        except Exception as e:
            zhi_logger.error(f"数据导入失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"导入失败: {str(e)}",
                success=False
            )

    def get_import_template(
            self,
            format_type: str = 'excel',
            **kwargs
    ) -> Union[HttpResponse, ZhiResponse]:
        """
        获取导入模板

        Args:
            format_type: 模板格式 (csv, excel)
            **kwargs: 其他参数

        Returns:
            HttpResponse: 模板文件下载响应 或 ZhiResponse: 错误响应
        """
        try:
            # 检查导入配置
            if not self.import_settings.get('enabled', True):
                return self._handle_response(
                    data=None,
                    code=ResponseCode.FORBIDDEN,
                    message="导入功能未启用",
                    success=False
                )

            # 验证格式
            if format_type not in ['csv', 'excel']:
                return self._handle_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message=f"不支持的模板格式: {format_type}",
                    success=False
                )

            # 获取字段映射（用于生成中文表头）
            field_mapping = self.import_settings.get('field_mapping', {})

            # 生成模板
            return ImportExportUtils.generate_import_template(
                self.model,
                format_type,
                field_mapping
            )

        except Exception as e:
            zhi_logger.error(f"生成导入模板失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"生成模板失败: {str(e)}",
                success=False
            )

    def get_export_fields(self) -> Union[ZhiResponse, ZhiModelResponse]:
        """
        获取可导出的字段列表

        Returns:
            ZhiResponse: 字段列表
        """
        try:
            fields_info = []

            for field in self.model._meta.fields:
                # 跳过排除字段
                if field.name in self.model_exclude:
                    continue

                field_info = {
                    'name': field.name,
                    'verbose_name': getattr(field, 'verbose_name', field.name),
                    'type': field.__class__.__name__,
                    'required': not field.null and not field.blank and not hasattr(field, 'default'),
                    'choices': [{'value': k, 'label': v} for k, v in field.choices] if hasattr(field, 'choices') and field.choices else None
                }
                fields_info.append(field_info)

            return self._handle_response(
                data=fields_info,
                message="获取字段列表成功"
            )

        except Exception as e:
            zhi_logger.error(f"获取导出字段失败: {str(e)}")
            return self._handle_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取字段列表失败: {str(e)}",
                success=False
            )
