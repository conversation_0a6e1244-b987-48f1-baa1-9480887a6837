from functools import wraps
from typing import Any, Callable, Dict, List, Type, cast
from inspect import getmembers, isfunction

from django.db.models import Model
from ninja import Schema
from ninja_extra import api_controller
from ninja_extra.permissions.base import BasePermission
from ninja_extra.permissions.common import IsAuthenticated

from zhi_common.zhi_logger import zhi_logger
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from .base_config import (HTTPMethod, TCallable, _ensure_throttle)
from .base_config import CRUDEndpointConfig


def api_route(
        http_method: HTTPMethod,
        path: str,
        **kwargs: Any
        ) -> Callable[[TCallable], TCallable]:
    """Enhanced route decorator with automatic query param handling and docs generation"""

    def decorator(func: TCallable) -> TCallable:
        path_clean = path.strip('/')
        final_path = f'/{path_clean}' if path_clean else '/'

        # Process throttle
        throttle = _ensure_throttle(kwargs.pop('throttle', None))

        # Process query params
        query_params = kwargs.pop('query_params', {})
        extra_query_fields = kwargs.pop('extra_query_fields', {})

        # Build OpenAPI extras
        openapi_extra = kwargs.get('openapi_extra', {})

        # Add query parameters to OpenAPI docs
        if query_params or extra_query_fields:
            parameters = []

            # In api_route decorator, modify the parameter processing:
            for param_name, param_config in query_params.items():
                if isinstance(param_config, set):
                    # Convert set to tuple with default description
                    param_config = (f"Filter by {param_name}", next(iter(param_config)))

                param_desc = param_config[0] if isinstance(param_config, tuple) else param_config.get(
                    'description', f"Filter by {param_name}"
                    )

                if isinstance(param_config, dict) and 'values' in param_config:
                    enum_type = 'integer' if all(isinstance(v, int) for v in param_config['values']) else 'string'
                    parameters.append(
                        {
                            'name': param_name,
                            'in': 'query',
                            'required': False,
                            'schema': {
                                'type': 'array',
                                'items': {
                                    'type': enum_type,
                                    'enum': param_config['values']
                                    }
                                },
                            'description': param_desc
                            }
                        )
                else:
                    parameters.append(
                        {
                            'name': param_name,
                            'in': 'query',
                            'required': False,
                            'schema': {'type': 'string'},
                            'description': param_desc
                            }
                        )

            openapi_extra['parameters'] = parameters
            kwargs['openapi_extra'] = openapi_extra

            # Route configuration
        route_kwargs = {
            'auth': kwargs.get('auth', GlobalOAuth2()),
            'throttle': throttle,
            'response': kwargs.get('response'),
            'summary': kwargs.get('summary'),
            'description': kwargs.get('description'),
            'tags': kwargs.get('tags'),
            'deprecated': kwargs.get('deprecated'),
            'by_alias': kwargs.get('by_alias', False),
            'exclude_unset': kwargs.get('exclude_unset', False),
            'exclude_defaults': kwargs.get('exclude_defaults', False),
            'exclude_none': kwargs.get('exclude_none', False),
            'url_name': kwargs.get('url_name', f"custom_{func.__name__}"),
            'include_in_schema': kwargs.get('include_in_schema', True),
            'openapi_extra': openapi_extra,
            'permissions': kwargs.get('permissions', [IsAuthenticated])
            }

        # Mark route info
        func._is_custom_route = True
        func._route_path = final_path
        func._http_method = http_method.__name__.upper()
        func._query_params = query_params

        @wraps(func)
        def wrapped(self: Any, request: Any, *args, **kwargs) -> Any:
            try:
                # Process query params
                if hasattr(func, '_query_params'):
                    for param_name in func._query_params:
                        if param_name not in kwargs:
                            param_value = request.GET.get(param_name)
                            if param_value is not None:
                                kwargs[param_name] = param_value
                return func(self, request, *args, **kwargs)
            except Exception as e:
                zhi_logger.error(f'❌  请求处理错误: {e}')
                raise

        # Ensure wrapped function has correct attributes
        wrapped._is_custom_route = True
        wrapped._route_path = final_path
        wrapped._http_method = http_method.__name__.upper()
        return cast(TCallable, http_method(final_path, **route_kwargs)(wrapped))

    return decorator


def auto_crud_api(
        model_class: Type[Model],
        prefix: str = None,
        tags: List[str] = None,
        schema_in: Type[Schema] = None,
        schema_out: Type[Schema] = None,
        pagination: bool = True,
        exclude: List[str] = None,
        auth: Any = GlobalOAuth2(),
        permissions: List[BasePermission] = None,
        extra_query_fields: Dict[str, Any] = None,
        enum_fields_config: Dict[str, Dict] = None,
        ) -> Callable[[Type], Type]:
    """
    自动API装饰器，生成标准CRUD接口

    参数:
        model_class: 数据模型类
        prefix: API路径前缀
        tags: OpenAPI标签
        schema_in: 输入Schema
        schema_out: 输出Schema
        pagination: 是否启用分页
        exclude: 排除的端点列表
        auth: 认证配置
        permissions: 权限控制
        extra_query_fields: 额外查询字段配置
        enum_fields_config: 枚举字段配置
    """

    def decorator(service_class: Type) -> Type:
        _service_instance = None

        class Controller:
            def __init__(self) -> None:
                nonlocal _service_instance
                self._model = model_class
                if _service_instance is None:
                    # 从服务类中提取配置参数
                    service_kwargs = {
                        'model_class': model_class,
                        'schema_out': schema_out
                    }

                    # 提取类属性中的配置
                    if hasattr(service_class, 'mappings_settings'):
                        service_kwargs['mappings_settings'] = service_class.mappings_settings
                    if hasattr(service_class, 'export_settings'):
                        service_kwargs['export_settings'] = service_class.export_settings
                    if hasattr(service_class, 'import_settings'):
                        service_kwargs['import_settings'] = service_class.import_settings
                    if hasattr(service_class, 'model_exclude'):
                        service_kwargs['model_exclude'] = service_class.model_exclude
                    if hasattr(service_class, 'allowed_query_fields'):
                        service_kwargs['allowed_query_fields'] = service_class.allowed_query_fields

                    _service_instance = service_class(**service_kwargs)
                self._service = _service_instance

                # 继承服务类的所有公共方法
                if isinstance(service_class, type):  # 确保是类对象
                    for name, method in getmembers(service_class, lambda x: isfunction(x) or isinstance(x, property)):
                        if not name.startswith('_'):
                            setattr(self.__class__, name, method)

            @property
            def model(self) -> Type[Model]:
                return self._model

            @property
            def service(self) -> Any:
                return self._service

            def __getattr__(self, name):
                """将未找到的属性委托给服务实例"""
                return getattr(self._service, name)

        # 安全地注册自定义路由
        if isinstance(service_class, type):  # 确保是类对象
            for name, method in getmembers(service_class, isfunction):
                if hasattr(method, '_is_custom_route'):
                    # 使用闭包避免循环引用问题
                    def make_wrapper(method):
                        @wraps(method)
                        def wrapper(self, request, *args, **kwargs):
                            return method(self, request, *args, **kwargs)

                        return wrapper

                    wrapped_func = make_wrapper(method)

                    # 复制所有自定义属性
                    for attr in ['_is_custom_route', '_route_path', '_http_method', '_query_params']:
                        if hasattr(method, attr):
                            setattr(wrapped_func, attr, getattr(method, attr))

                    setattr(Controller, name, wrapped_func)
                    zhi_logger.info(f"✅ 注册自定义路由: {name}")

        # 添加CRUD端点
        if schema_in and schema_out:
            _add_crud_endpoints(
                Controller,
                lambda: Controller().service,
                schema_in,
                schema_out,
                pagination,
                exclude,
                auth,
                permissions or [IsAuthenticated],
                extra_query_fields,
                enum_fields_config
                )

        return api_controller(
            f"/{prefix or model_class.__name__.lower()}",
            tags=tags or [f"{model_class.__name__} Management"],
            auth=auth,
            permissions=permissions or [IsAuthenticated]
            )(Controller)

    return decorator


def _add_crud_endpoints(
        cls: Type,
        service_factory: Callable,
        schema_in: Type[Schema],
        schema_out: Type[Schema],
        pagination: bool = True,
        exclude: List[str] = None,
        auth: Any = GlobalOAuth2(),
        permissions: List[BasePermission] = None,
        extra_query_fields: Dict[str, Any] = None,
        enum_fields_config: Dict[str, Dict] = None,
        ) -> None:
    """Register CRUD endpoints"""

    exclude = exclude or []

    try:
        # 安全获取配置
        config = CRUDEndpointConfig(getattr(cls(), 'model'), exclude=exclude).get_config()
    except Exception as e:
        zhi_logger.warning(f"获取CRUD配置失败: {str(e)}")
        return

        # 获取Key-Value映射端点 - 必须在retrieve之前注册，避免路径冲突
    if 'list_id_mappings' not in exclude and 'list_id_mappings' in config:
        @api_route(
            config['list_id_mappings']['method'],
            config['list_id_mappings']['path'],
            response=config['list_id_mappings']['response'](schema_out),
            summary=config['list_id_mappings']['summary'],
            description=config['list_id_mappings']['description'],
            auth=auth,
            permissions=permissions,
            query_params=config['list_id_mappings'].get('query_params', {}),
            extra_query_fields=extra_query_fields,
            enum_fields_config=enum_fields_config,
            )
        def list_id_mappings(self: Any, request: Any, **kwargs: Any) -> Any:
            service = service_factory()
            return service.list_id_mappings(**kwargs)

        cls.list_id_mappings = list_id_mappings

        # 批量创建端点
    if 'batch_create' not in exclude and 'batch_create' in config:
        @api_route(
            config['batch_create']['method'],
            config['batch_create']['path'],
            response=config['batch_create']['response'](schema_out),
            summary=config['batch_create']['summary'],
            description=config['batch_create']['description'],
            auth=auth,
            permissions=permissions
            )
        def batch_create(self: Any, request: Any, data: List[schema_in]) -> Any:
            service = service_factory()
            data_list = [item.dict() for item in data]
            return service.batch_create(data_list)

        cls.batch_create = batch_create

        # 列表分页查询端点
    if 'list_pagination' not in exclude and 'list_pagination' in config:
        @api_route(
            config['list_pagination']['method'],
            config['list_pagination']['path'],
            response=config['list_pagination']['response'](schema_out),
            summary=config['list_pagination']['summary'],
            description=config['list_pagination']['description'],
            auth=auth,
            permissions=permissions,
            query_params=config['list_pagination'].get(
                'query_params', {
                    'page': ("页码", 1),
                    'page_size': ("每页数量", 10)
                    }
                ),
            enum_fields_config=enum_fields_config
            )
        def list_pagination(self: Any, request: Any, **kwargs: Any) -> Any:
            service = service_factory()
            return service.list(filters=kwargs)

        cls.list_pagination = list_pagination

        # 所有列表查询端点
    if 'list_all' not in exclude and 'list_all' in config:
        @api_route(
            config['list_all']['method'],
            config['list_all']['path'],
            response=config['list_all']['response'](schema_out),
            summary=config['list_all']['summary'],
            description=config['list_all']['description'],
            auth=auth,
            permissions=permissions,
            query_params=config['list_all'].get('query_params'),
            extra_query_fields=extra_query_fields,
            )
        def list_all(self: Any, request: Any, **kwargs: Any) -> Any:
            service = service_factory()
            return service.list(pagination=False, **kwargs)

        cls.list_all = list_all

        # 创建资源端点
    if 'create' not in exclude and 'create' in config:
        @api_route(
            config['create']['method'],
            config['create']['path'],
            response=config['create']['response'](schema_out),
            summary=config['create']['summary'],
            description=config['create']['description'],
            auth=auth,
            permissions=permissions,
            )
        def create(self: Any, request: Any, data: schema_in) -> Any:
            service = service_factory()
            return service.create(data.dict())

        cls.create = create

    # 全量更新端点
    if 'update' not in exclude and 'update' in config:
        @api_route(
            config['update']['method'],
            config['update']['path'],
            response=config['update']['response'](schema_out),
            summary=config['update']['summary'],
            description=config['update']['description'],
            auth=auth,
            permissions=permissions,
            )
        def update(self: Any, request: Any, id: str | int, data: schema_in) -> Any:
            service = service_factory()
            return service.update(id, data.dict())

        cls.update = update

    # 删除资源端点
    if 'delete' not in exclude and 'delete' in config:
        @api_route(
            config['delete']['method'],
            config['delete']['path'],
            response=config['delete']['response'],
            summary=config['delete']['summary'],
            description=config['delete']['description'],
            auth=auth,
            permissions=permissions
            )
        def delete(self: Any, request: Any, id: str | int) -> Any:
            service = service_factory()
            return service.delete(id)

        cls.delete = delete

    # 获取资源详情端点 - 放在具体路径之后，避免通配符冲突
    if 'retrieve' not in exclude and 'retrieve' in config:
        @api_route(
            config['retrieve']['method'],
            config['retrieve']['path'],
            response=config['retrieve']['response'](schema_out),
            summary=config['retrieve']['summary'],
            description=config['retrieve']['description'],
            auth=auth,
            permissions=permissions,
            )
        def retrieve(self: Any, request: Any, id: str | int) -> Any:
            print(f'In retrieve =====================> base_api.py')
            service = service_factory()
            return service.retrieve(id)

        cls.retrieve = retrieve
