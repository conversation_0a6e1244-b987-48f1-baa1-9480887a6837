from datetime import datetime
from decimal import Decimal
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar

from django.db.models import Model
from ninja import Schema
from ninja_extra import http_delete, http_get, http_post, http_put, http_patch
from ninja_extra.controllers.base import BaseThrottle

from zhi_common.zhi_response.base import ZhiModelResponse
from zhi_common.zhi_response.schemas.base import ListIDValueMappingSchema, BaseResponseSchema, PaginatedResponseSchema


BASE_QUERY_EXCLUDE_FIELDS = {
    'seq',
    'creator_id',
    'modifier_id',
    'creator_name',
    'modifier_name',
    'org_id',
    'is_deleted',
    'deleted_at'
    }

BASE_SCHEMA_IN_EXCLUDE_FIELDS = [
    'seq',
    'id',
    'creator_id',
    'modifier_id',
    'creator_name',
    'modifier_name',
    'org_id',
    'is_deleted',
    'deleted_at',
    'updated_at',
    'created_at',
    ]




# 修复 NOT_SET_TYPE 问题
try:
    from ninja_extra.constants import NOT_SET_TYPE
except ImportError:
    NOT_SET_TYPE = object()  # 回退实现

# 类型定义
TCallable = TypeVar('TCallable', bound=Callable[..., Any])
HTTPMethod = Callable[..., Callable[[TCallable], TCallable]]


def _ensure_throttle(throttle: Any) -> Any:
    """确保 throttle 参数格式正确"""
    if throttle is None or throttle is NOT_SET_TYPE:
        return []  # 返回空列表而不是None
    if isinstance(throttle, (list, BaseThrottle)):
        return throttle
    if isinstance(throttle, type) and issubclass(throttle, BaseThrottle):
        return [throttle()]
    return []


class CRUDEndpointConfig(object):
    """标准CRUD端点配置类，支持枚举字段"""

    def __init__(self, model_class: Type[Model] = None, exclude: list = None):
        if exclude is None:
            exclude = []
        self.exclude = exclude
        if isinstance(model_class, property):
            self._model = model_class.fget.__self__.model
        else:
            self._model = model_class
        self.default_extent_fields = BASE_QUERY_EXCLUDE_FIELDS.union(exclude)

        # 添加枚举字段配置
        self._enum_config = {
            # 示例配置格式：
            # 'field_name': {
            #     'source': 'choices|config|function',
            #     'data': [...]  # 或 callable
            # }
            'page': {
                'source': 'config',
                'data': [10, 20, 50, 100]
                }
            }
        self._field_type_mapping = {
            'AutoField': None,
            'BigAutoField': None,
            'BooleanField': None,
            'CharField': None,
            'DateField': None,
            'DateTimeField': None,
            'DecimalField': None,
            'EmailField': None,
            'FloatField': None,
            'IntegerField': None,
            'PositiveIntegerField': None,
            'SmallIntegerField': None,
            'TextField': None,
            'TimeField': None,
            'UUIDField': None,
            }

    def configure_enum_field(self, field_name: str, source: str, data: Any):
        """配置枚举字段数据获取方式

        参数:
            field_name: 字段名
            source: 数据来源类型 ('choices'|'config'|'function')
            data: 枚举数据(列表)或获取函数
        """
        field_key = f'{self._model._meta.app_label}:{self._model._meta.model_name}:{field_name}'
        self._enum_config[field_key] = {
            'source': source,
            'data': data
            }

    def _get_enum_values(self, field_name: str) -> List[Any]:
        """获取枚举字段的可选值"""
        config = self._enum_config.get(
            f'{self._model._meta.app_label}:{self._model._meta.model_name}:{field_name}'
            )
        field = self._model._meta.get_field(field_name)

        # 1. 检查是否有自定义配置
        if config:
            if config['source'] == 'config':
                return config['data']
            elif config['source'] == 'function':
                return getattr(config, 'data')()

        # 2. 检查模型字段的choices
        if hasattr(field, 'choices') and field.choices:
            return [choice[0] for choice in field.choices]

        # 3. 默认返回空列表
        return []

    def get_default_dynamic_search_fields(self) -> Dict[str, Any]:
        """动态生成查询参数配置，支持枚举字段"""
        if self._model is None:
            return {}

        query_params = {}
        for field in self._model._meta.fields:
            self._process_field(field, query_params)

        return query_params

    def _process_field(self, field, params: dict, param_name: str = None):
        """处理单个字段，添加枚举字段支持"""
        if field.name in self.default_extent_fields:
            return

        field_name = param_name or field.name
        field_type = field.get_internal_type()

        # 检查是否是枚举字段
        is_enum = (
                field_name in self._enum_config or
                (hasattr(field, 'choices') and field.choices)
        )

        if is_enum:
            enum_values = self._get_enum_values(field_name)
            if enum_values:
                params[f"{field_name}__in"] = {
                    'description': f"筛选{field_name}，可选值: {', '.join(str(v) for v in enum_values)}",
                    'values': enum_values
                    }
                return

            # 原有字段处理逻辑
        default_value = self._get_field_default_value(field_type, field_name)

        if field_type in ['DateTimeField', 'DateField']:
            params[f"{field_name}_start"] = ("开始时间", "")
            params[f"{field_name}_end"] = ("结束时间", "")
        elif field_type == 'BooleanField':
            params[field_name] = ("是否", False)
        elif field.is_relation:
            params[f"{field_name}__id"] = ("关联ID", "")
        else:
            params[field_name] = ("筛选值", default_value)

            # 添加常用查询后缀
            if field_type in ['CharField', 'TextField']:
                if not field_name.startswith('id'):
                    params[f"{field_name}__contains"] = ("包含文本", "")
                params[f"{field_name}__in"] = ("多值筛选", "")
            elif field_type in ['IntegerField', 'DecimalField', 'FloatField', 'DateField', 'DateTimeField']:
                params[f"{field_name}__gt"] = ("大于", default_value)
                params[f"{field_name}__lt"] = ("小于", default_value)

    @staticmethod
    def _get_field_default_value(field_type: str, field_name: str) -> Any:
        """所有字段默认返回None"""
        return None

    def get_config(self) -> Dict[str, Dict[str, Any]]:
        dynamic_fields = self.get_default_dynamic_search_fields()

        return {
            'list_pagination': {
                'method': http_get,
                'path': "/list_pagination",
                'response': lambda schema_out: {200: PaginatedResponseSchema[List[schema_out]] if schema_out else Any},
                'summary': "分页列表查询",
                'description': "获取分页列表数据",
                'throttle': NOT_SET_TYPE,
                'query_params': {
                    "page": {'e'
                             "页码", 1
                             },
                    **dynamic_fields
                    },
                },
            'list_all': {
                'method': http_get,
                'path': "/list_all",
                'response': lambda schema_out: {200: BaseResponseSchema[List[schema_out]] if schema_out else Any},
                'summary': "全部列表查询(不分页)，请请谨慎使用",
                'description': "获取全部列表数据(不分页)，请谨慎使用",
                'query_params': {
                    **dynamic_fields
                    },
                'throttle': NOT_SET_TYPE
                },
            'list_id_mappings': {
                'method': http_get,
                'path': "/list_id_mappings",
                'response': lambda schema_out: {200: ListIDValueMappingSchema},
                'summary': "ID-名称映射列表",
                'description': "获取以ID为键的映射列表",
                'query_params': {
                    "is_all": ("是否获取全部", True),
                    **dynamic_fields,
                    },
                'throttle': NOT_SET_TYPE
                },
            'create': {
                'method': http_post,
                'path': "/create",
                'response': lambda schema_out: {200: BaseResponseSchema[schema_out] if schema_out else Any},
                'summary': "创建资源",
                'description': "创建新资源实例",
                'throttle': NOT_SET_TYPE
                },
            'batch_create': {
                'method': http_post,
                'path': "/batch",
                'response': lambda schema_out: {200: BaseResponseSchema[List[schema_out]] if schema_out else Any},
                'summary': "批量创建",
                'description': "批量创建资源",
                'throttle': NOT_SET_TYPE
                },
            'update': {
                'method': http_put,
                'path': "/update/{id}",
                'response': lambda schema_out: {200: BaseResponseSchema[schema_out] if schema_out else Any},
                'summary': "全量更新",
                'description': "替换整个资源",
                'throttle': NOT_SET_TYPE
                },
            'delete': {
                'method': http_delete,
                'path': "/delete/{id}",
                'response': lambda schema_out: {200: BaseResponseSchema[schema_out] if schema_out else Any},
                'summary': "删除资源",
                'description': "删除指定资源",
                'throttle': NOT_SET_TYPE
                },
            'retrieve': {
                'method': http_get,
                'path': "/detail/{id}",
                'response': lambda schema_out: {200: BaseResponseSchema[schema_out] if schema_out else Any},
                'summary': "详情查询",
                'description': "获取单个资源详情",
                'throttle': NOT_SET_TYPE
                },
            }



def _build_query_schema(model_class: Type[Model], extra_fields: Dict[str, Any], enum_config: Dict = None) -> Any:
    """构建查询Schema，支持枚举字段和额外字段"""
    if not model_class:
        return Schema

    fields = {}
    config = CRUDEndpointConfig(model_class)

    # 应用枚举配置
    if enum_config:
        for field_name, field_config in enum_config.items():
            config.configure_enum_field(field_name, **field_config)

    dynamic_fields = config.get_default_dynamic_search_fields()

    # 处理模型字段
    for field in model_class._meta.fields:
        if field.name in config.default_extent_fields:
            continue

            # 根据blank属性决定是否必选
        is_required = not field.blank if hasattr(field, 'blank') else True
        field_type = field.get_internal_type()

        # 处理不同类型字段
        if field_type in ['CharField', 'TextField']:
            fields[field.name] = (str if is_required else Optional[str], None)
        elif field_type in ['IntegerField', 'PositiveIntegerField']:
            fields[field.name] = (int if is_required else Optional[int], None)
        elif field_type == 'BooleanField':
            fields[field.name] = (bool if is_required else Optional[bool], None)
        elif field_type in ['DateField', 'DateTimeField']:
            fields[field.name] = (datetime if is_required else Optional[datetime], None)
        elif field_type == 'DecimalField':
            fields[field.name] = (Decimal if is_required else Optional[Decimal], None)
        else:
            fields[field.name] = (Any if is_required else Optional[Any], None)

    # 处理动态查询字段
    for field_name, field_info in dynamic_fields.items():
        if '__in' in field_name and isinstance(field_info, dict) and 'values' in field_info:
            fields[field_name] = (Optional[List[str]], None)
        else:
            field_type = type(field_info[1] if isinstance(field_info, tuple) else field_info)
            fields[field_name] = (Optional[field_type], None)

    # 处理额外字段（优先级高于模型字段）
    if extra_fields:
        for field_name, field_config in extra_fields.items():
            if isinstance(field_config, tuple):
                field_type, default_value = field_config
            else:
                field_type = type(field_config)
                default_value = field_config

            fields[field_name] = (Optional[field_type], default_value)

    # 添加分页字段
    fields.update(
        {
            'ordering': (Optional[str], None)
            }
        )

    return type(f"{model_class.__name__}QuerySchema", (Schema,), fields)


def build_update_optional_schema(model_class: Type[Model], exclude_fields: List[str] = None) -> Any:
    """
    构建可选更新Schema，用于PATCH端点（部分更新）

    Args:
        model_class: Django模型类
        exclude_fields: 需要排除的字段列表

    Returns:
        动态生成的Schema类，所有字段都是可选的
    """
    if not model_class:
        return Schema

    # 默认排除字段
    default_exclude = set(BASE_SCHEMA_IN_EXCLUDE_FIELDS)
    if exclude_fields:
        default_exclude.update(exclude_fields)

    # 使用字典来存储字段注解和默认值
    annotations = {}
    field_defaults = {}

    # 遍历模型字段
    for field in model_class._meta.fields:
        if field.name in default_exclude:
            continue

        # 跳过自动生成的字段
        if getattr(field, 'auto_created', False):
            continue

        field_type = field.get_internal_type()
        field_name = field.name

        # 根据字段类型构建Schema字段，所有字段都设为可选
        if field_type == 'CharField':
            annotations[field_name] = Optional[str]
            field_defaults[field_name] = None

        elif field_type == 'TextField':
            annotations[field_name] = Optional[str]
            field_defaults[field_name] = None

        elif field_type in ['IntegerField', 'PositiveIntegerField', 'SmallIntegerField', 'BigIntegerField']:
            annotations[field_name] = Optional[int]
            field_defaults[field_name] = None

        elif field_type == 'BooleanField':
            annotations[field_name] = Optional[bool]
            field_defaults[field_name] = None

        elif field_type in ['DateField', 'DateTimeField']:
            annotations[field_name] = Optional[datetime]
            field_defaults[field_name] = None

        elif field_type == 'TimeField':
            from datetime import time
            annotations[field_name] = Optional[time]
            field_defaults[field_name] = None

        elif field_type == 'DecimalField':
            annotations[field_name] = Optional[Decimal]
            field_defaults[field_name] = None

        elif field_type == 'FloatField':
            annotations[field_name] = Optional[float]
            field_defaults[field_name] = None

        elif field_type == 'EmailField':
            annotations[field_name] = Optional[str]
            field_defaults[field_name] = None

        elif field_type == 'UUIDField':
            from uuid import UUID
            annotations[field_name] = Optional[UUID]
            field_defaults[field_name] = None

        elif field_type == 'JSONField':
            annotations[field_name] = Optional[Dict[str, Any]]
            field_defaults[field_name] = None

        # 处理外键关系
        elif field.many_to_one or field.one_to_one:
            # 外键字段通常接受ID值
            annotations[field_name] = Optional[str]
            field_defaults[field_name] = None

        # 处理选择字段
        elif hasattr(field, 'choices') and field.choices:
            # 获取选择项的值类型
            choice_values = [choice[0] for choice in field.choices]
            if choice_values:
                choice_type = type(choice_values[0])
                annotations[field_name] = Optional[choice_type]
            else:
                annotations[field_name] = Optional[str]
            field_defaults[field_name] = None

        else:
            # 默认处理为Any类型
            annotations[field_name] = Optional[Any]
            field_defaults[field_name] = None

    # 处理多对多关系字段
    for field in model_class._meta.many_to_many:
        if field.name in default_exclude:
            continue

        # 多对多字段通常接受ID列表
        annotations[field.name] = Optional[List[str]]
        field_defaults[field.name] = None

    # 动态创建Schema类
    schema_name = f"{model_class.__name__}UpdateOptionalSchema"

    # 创建类的命名空间
    namespace = {
        '__annotations__': annotations,
        **field_defaults  # 将默认值添加到命名空间
    }

    return type(schema_name, (Schema,), namespace)



class QuerySecurityManager:
    """查询安全验证管理器"""

    BANNED_OPERATORS = {'__iregex', '__regex', '__isnull', '__raw'}
    SENSITIVE_FIELDS = {'password', 'secret', 'token'}

    def __init__(self, model_class: Type[Model], allowed_fields: Set[str] = None):
        self.model = model_class
        self.allowed_fields = allowed_fields or self._get_default_allowed_fields()

    def _get_default_allowed_fields(self) -> Set[str]:
        """获取模型默认允许查询的字段"""
        return {
            f.name for f in self.model._meta.fields
            if not f.name.startswith('_') and f.name not in self.SENSITIVE_FIELDS
            }

    def validate_field_access(self, field_name: str) -> bool:
        """验证字段是否允许查询"""
        base_field = field_name.split('__')[0]
        return base_field in self.allowed_fields

    def sanitize_operator(self, lookup: str) -> str:
        """清理危险操作符"""
        for op in self.BANNED_OPERATORS:
            if op in lookup:
                raise ValueError(f"禁止使用的查询操作符: {op}")
        return lookup
