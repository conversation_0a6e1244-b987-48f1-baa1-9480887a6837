"""
文件管理API
基于 django-ninja 和 django-ninja-extra 的文件上传下载接口
"""

from typing import List, Optional, Dict, Any
from django.http import FileResponse, Http404
from django.core.files.uploadedfile import UploadedFile
from ninja import Router, File, Form, Query
from ninja_extra import NinjaExtraAPI, api_controller, route
from ninja_extra.controllers import ControllerBase
from ninja_extra.permissions import IsAuthenticated

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_services.file_manager import (
    FileManager, 
    FileUploadConfig, 
    FileUploadSchema, 
    FileInfoSchema
)
from zhi_common.zhi_logger.core_logger import zhi_logger


# 移除有问题的Schema类，直接在方法中使用参数


@api_controller('/files', tags=['文件管理'], permissions=[IsAuthenticated])
class FileAPIController(ControllerBase):
    """文件管理API控制器"""
    
    def __init__(self):
        """初始化文件管理器"""
        # 默认配置
        self.default_config = FileUploadConfig(
            max_file_size=10 * 1024 * 1024,  # 10MB
            allowed_types=['image', 'document', 'archive'],
            upload_path='uploads',
            organize_by_date=True,
            generate_unique_name=True
        )
        self.file_manager = FileManager(self.default_config)
    
    def _get_file_manager(self, config: Dict[str, Any] = None) -> FileManager:
        """获取文件管理器实例"""
        if config:
            custom_config = FileUploadConfig(**config)
            return FileManager(custom_config)
        return self.file_manager
    
    @route.post('/upload', response={200: BaseResponseSchema[FileUploadSchema]})
    def upload_file(self, request, 
                   file: UploadedFile = File(..., description="上传的文件"),
                   custom_path: Optional[str] = Form(None, description="自定义存储路径"),
                   file_id: Optional[str] = Form(None, description="自定义文件ID"),
                   max_size: Optional[int] = Form(None, description="最大文件大小(字节)"),
                   allowed_types: Optional[str] = Form(None, description="允许的文件类型,逗号分隔")):
        """
        上传单个文件
        
        支持的文件类型：
        - image: jpg, jpeg, png, gif, bmp, webp
        - document: pdf, doc, docx, xls, xlsx, ppt, pptx, txt
        - archive: zip, rar, 7z, tar, gz
        - video: mp4, avi, mov, wmv, flv, mkv
        - audio: mp3, wav, flac, aac, ogg
        """
        try:
            # 构建自定义配置
            config = {}
            if max_size:
                config['max_file_size'] = max_size
            if allowed_types:
                config['allowed_types'] = [t.strip() for t in allowed_types.split(',')]
            
            # 获取文件管理器
            file_manager = self._get_file_manager(config)
            
            # 上传文件
            result = file_manager.upload_file(file, custom_path, file_id)
            
            return create_response(
                data=result,
                message="文件上传成功"
            )
            
        except ValueError as e:
            zhi_logger.warning(f"文件上传验证失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message=str(e),
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"文件上传失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"文件上传失败: {str(e)}",
                success=False
            )
    
    @route.post('/upload/batch', response={200: BaseResponseSchema[List[FileUploadSchema]]})
    def upload_multiple_files(self, request,
                             files: List[UploadedFile] = File(..., description="上传的文件列表"),
                             max_size: Optional[int] = Form(None, description="最大文件大小(字节)"),
                             allowed_types: Optional[str] = Form(None, description="允许的文件类型,逗号分隔")):
        """
        批量上传文件
        """
        try:
            # 构建自定义配置
            config = {}
            if max_size:
                config['max_file_size'] = max_size
            if allowed_types:
                config['allowed_types'] = [t.strip() for t in allowed_types.split(',')]
            
            # 获取文件管理器
            file_manager = self._get_file_manager(config)
            
            # 批量上传文件
            results = file_manager.upload_multiple_files(files)
            
            return create_response(
                data=results,
                message=f"批量上传成功，共上传 {len(results)} 个文件"
            )
            
        except ValueError as e:
            zhi_logger.warning(f"批量文件上传验证失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message=str(e),
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"批量文件上传失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"批量文件上传失败: {str(e)}",
                success=False
            )
    
    @route.get('/download', response=FileResponse)
    def download_file(self, request,
                     file_path: str = Query(..., description="文件路径"),
                     as_attachment: bool = Query(True, description="是否作为附件下载"),
                     custom_filename: Optional[str] = Query(None, description="自定义下载文件名")):
        """
        下载文件
        """
        try:
            return self.file_manager.download_file(file_path, as_attachment, custom_filename)
            
        except Http404:
            zhi_logger.warning(f"下载文件不存在: {file_path}")
            raise
        except Exception as e:
            zhi_logger.error(f"文件下载失败: {str(e)}")
            raise Http404("文件下载失败")
    
    @route.get('/info', response={200: BaseResponseSchema[FileInfoSchema]})
    def get_file_info(self, request,
                     file_path: str = Query(..., description="文件路径")):
        """
        获取文件信息
        """
        try:
            file_info = self.file_manager.get_file_info(file_path)
            
            return create_response(
                data=file_info,
                message="获取文件信息成功"
            )
            
        except Exception as e:
            zhi_logger.error(f"获取文件信息失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取文件信息失败: {str(e)}",
                success=False
            )
    
    @route.delete('/delete', response={200: BaseResponseSchema[Dict[str, bool]]})
    def delete_file(self, request,
                   file_path: str = Query(..., description="文件路径")):
        """
        删除文件
        """
        try:
            success = self.file_manager.delete_file(file_path)
            
            return create_response(
                data={"deleted": success},
                message="文件删除成功" if success else "文件不存在或删除失败"
            )
            
        except Exception as e:
            zhi_logger.error(f"文件删除失败: {str(e)}")
            return create_response(
                data={"deleted": False},
                code=ResponseCode.INTERNAL_ERROR,
                message=f"文件删除失败: {str(e)}",
                success=False
            )
    
    @route.get('/list', response={200: BaseResponseSchema[List[FileInfoSchema]]})
    def list_files(self, request,
                  directory: Optional[str] = Query(None, description="目录路径"),
                  recursive: bool = Query(False, description="是否递归列出子目录")):
        """
        列出文件
        """
        try:
            files = self.file_manager.list_files(directory, recursive)
            
            return create_response(
                data=files,
                message=f"获取文件列表成功，共 {len(files)} 个文件"
            )
            
        except Exception as e:
            zhi_logger.error(f"获取文件列表失败: {str(e)}")
            return create_response(
                data=[],
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取文件列表失败: {str(e)}",
                success=False
            )


# 创建路由器
file_router = Router(tags=['文件管理'])

# 不需要认证的文件下载接口（用于公共文件访问）
@file_router.get('/public/download', response=FileResponse)
def download_public_file(request,
                        file_path: str = Query(..., description="文件路径"),
                        as_attachment: bool = Query(True, description="是否作为附件下载"),
                        custom_filename: Optional[str] = Query(None, description="自定义下载文件名")):
    """
    下载公共文件（无需认证）
    """
    try:
        file_manager = FileManager()
        return file_manager.download_file(file_path, as_attachment, custom_filename)
        
    except Http404:
        zhi_logger.warning(f"下载公共文件不存在: {file_path}")
        raise
    except Exception as e:
        zhi_logger.error(f"公共文件下载失败: {str(e)}")
        raise Http404("文件下载失败")
