from typing import Dict, Type
from django.db.models import Model
from zhi_common.zhi_logger import zhi_logger
from zhi_common.zhi_tools.toolkits import get_user_info_from_token


def get_user_filters(
        filters: Dict = None,
        model_class: Type[Model] = None,
        is_filter_deleted: bool = True
        ) -> Dict:
    """
    统一权限过滤入口
    :param filters: 基础查询条件
    :param model_class: 模型类(用于行级权限)
    :param is_filter_deleted: 是否过滤已删除
    :return: 合并后的权限条件
    """
    if filters is None:
        filters = {}

    # 1. 获取用户信息
    user_info = get_user_info_from_token()
    zhi_logger.info(f'Permission  filters for user: {user_info["id"]}')

    # 2. 超级管理员直接返回
    if user_info['is_superuser']:
        return {**filters, 'is_deleted': False} if is_filter_deleted else filters

    # 3. 获取完整权限条件
    # final_filters = filters
    # permission_filter = PermissionFilter(user_info, model_class)
    # final_filters = permission_filter.get_filters(filters, is_filter_deleted)

    zhi_logger.debug(f'Final permission filters: {filters}')
    return filters
