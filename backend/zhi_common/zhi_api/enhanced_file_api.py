"""
增强的文件管理API
集成数据库记录功能的文件上传下载接口
"""

from typing import List, Optional, Dict, Any
from django.http import FileResponse, Http404
from django.core.files.uploadedfile import UploadedFile
from ninja import Router, File, Form, Query
from ninja_extra import NinjaExtraAPI, api_controller, route
from ninja_extra.controllers import ControllerBase
from ninja_extra.permissions import IsAuthenticated

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_services.enhanced_file_manager import (
    EnhancedFileManager, 
    FileUploadConfig, 
    EnhancedFileUploadSchema, 
    EnhancedFileInfoSchema
)
from zhi_common.zhi_logger.core_logger import zhi_logger


@api_controller('/enhanced-files', tags=['增强文件管理'], permissions=[IsAuthenticated])
class EnhancedFileAPIController(ControllerBase):
    """增强文件管理API控制器"""
    
    def __init__(self):
        """初始化增强文件管理器"""
        # 默认配置
        self.default_config = FileUploadConfig(
            max_file_size=50 * 1024 * 1024,  # 50MB
            allowed_types=['image', 'document', 'archive', 'video', 'audio'],
            upload_path='enhanced_uploads',
            organize_by_date=True,
            generate_unique_name=True
        )
        self.file_manager = EnhancedFileManager(self.default_config)
    
    def _get_file_manager(self, config: Dict[str, Any] = None) -> EnhancedFileManager:
        """获取文件管理器实例"""
        if config:
            custom_config = FileUploadConfig(**config)
            return EnhancedFileManager(custom_config)
        return self.file_manager
    
    @route.post('/upload', response={200: BaseResponseSchema[EnhancedFileUploadSchema]})
    def upload_file(self, request, 
                   file: UploadedFile = File(..., description="上传的文件"),
                   custom_path: Optional[str] = Form(None, description="自定义存储路径"),
                   file_id: Optional[str] = Form(None, description="自定义文件ID"),
                   tags: Optional[str] = Form(None, description="文件标签,逗号分隔"),
                   description: Optional[str] = Form("", description="文件描述"),
                   expires_in_days: Optional[int] = Form(None, description="过期天数"),
                   max_size: Optional[int] = Form(None, description="最大文件大小(字节)"),
                   allowed_types: Optional[str] = Form(None, description="允许的文件类型,逗号分隔")):
        """
        上传单个文件（增强版）
        
        支持的功能：
        - 文件标签管理
        - 文件描述
        - 过期时间设置
        - 访问日志记录
        - 下载统计
        """
        try:
            # 构建自定义配置
            config = {}
            if max_size:
                config['max_file_size'] = max_size
            if allowed_types:
                config['allowed_types'] = [t.strip() for t in allowed_types.split(',')]
            
            # 处理标签
            file_tags = []
            if tags:
                file_tags = [tag.strip() for tag in tags.split(',') if tag.strip()]
            
            # 获取文件管理器
            file_manager = self._get_file_manager(config)
            
            # 上传文件
            result = file_manager.upload_file(
                file=file,
                custom_path=custom_path,
                file_id=file_id,
                tags=file_tags,
                description=description,
                expires_in_days=expires_in_days,
                request=request
            )
            
            return create_response(
                data=result,
                message="增强文件上传成功"
            )
            
        except ValueError as e:
            zhi_logger.warning(f"增强文件上传验证失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message=str(e),
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"增强文件上传失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"增强文件上传失败: {str(e)}",
                success=False
            )
    
    @route.post('/upload/batch', response={200: BaseResponseSchema[List[EnhancedFileUploadSchema]]})
    def upload_multiple_files(self, request,
                             files: List[UploadedFile] = File(..., description="上传的文件列表"),
                             tags: Optional[str] = Form(None, description="文件标签,逗号分隔"),
                             description: Optional[str] = Form("", description="文件描述"),
                             expires_in_days: Optional[int] = Form(None, description="过期天数"),
                             max_size: Optional[int] = Form(None, description="最大文件大小(字节)"),
                             allowed_types: Optional[str] = Form(None, description="允许的文件类型,逗号分隔")):
        """
        批量上传文件（增强版）
        """
        try:
            # 构建自定义配置
            config = {}
            if max_size:
                config['max_file_size'] = max_size
            if allowed_types:
                config['allowed_types'] = [t.strip() for t in allowed_types.split(',')]
            
            # 处理标签
            file_tags = []
            if tags:
                file_tags = [tag.strip() for tag in tags.split(',') if tag.strip()]
            
            # 获取文件管理器
            file_manager = self._get_file_manager(config)
            
            # 批量上传文件
            results = file_manager.upload_multiple_files(
                files=files,
                tags=file_tags,
                description=description,
                expires_in_days=expires_in_days,
                request=request
            )
            
            return create_response(
                data=results,
                message=f"批量上传成功，共上传 {len(results)} 个文件"
            )
            
        except ValueError as e:
            zhi_logger.warning(f"批量文件上传验证失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message=str(e),
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"批量文件上传失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"批量文件上传失败: {str(e)}",
                success=False
            )
    
    @route.get('/download', response=FileResponse)
    def download_file(self, request,
                     file_id: Optional[str] = Query(None, description="文件ID"),
                     file_path: Optional[str] = Query(None, description="文件路径"),
                     as_attachment: bool = Query(True, description="是否作为附件下载"),
                     custom_filename: Optional[str] = Query(None, description="自定义下载文件名")):
        """
        下载文件（增强版）
        
        支持通过文件ID或文件路径下载，自动记录下载日志和统计
        """
        try:
            if not file_id and not file_path:
                raise ValueError("必须提供 file_id 或 file_path")
            
            return self.file_manager.download_file(
                file_id=file_id,
                file_path=file_path,
                as_attachment=as_attachment,
                custom_filename=custom_filename,
                request=request
            )
            
        except Http404:
            zhi_logger.warning(f"下载文件不存在: file_id={file_id}, file_path={file_path}")
            raise
        except Exception as e:
            zhi_logger.error(f"增强文件下载失败: {str(e)}")
            raise Http404("文件下载失败")
    
    @route.get('/info', response={200: BaseResponseSchema[EnhancedFileInfoSchema]})
    def get_file_info(self, request,
                     file_id: Optional[str] = Query(None, description="文件ID"),
                     file_path: Optional[str] = Query(None, description="文件路径")):
        """
        获取文件信息（增强版）
        
        包含标签、描述、下载统计等增强信息
        """
        try:
            if not file_id and not file_path:
                raise ValueError("必须提供 file_id 或 file_path")
            
            file_info = self.file_manager.get_file_info(file_id=file_id, file_path=file_path)
            
            return create_response(
                data=file_info,
                message="获取增强文件信息成功"
            )
            
        except Http404:
            return create_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="文件不存在",
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"获取增强文件信息失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取文件信息失败: {str(e)}",
                success=False
            )
    
    @route.delete('/delete', response={200: BaseResponseSchema[Dict[str, bool]]})
    def delete_file(self, request,
                   file_id: Optional[str] = Query(None, description="文件ID"),
                   file_path: Optional[str] = Query(None, description="文件路径"),
                   physical_delete: bool = Query(False, description="是否物理删除文件")):
        """
        删除文件（增强版）
        
        支持逻辑删除和物理删除
        """
        try:
            if not file_id and not file_path:
                raise ValueError("必须提供 file_id 或 file_path")
            
            success = self.file_manager.delete_file(
                file_id=file_id,
                file_path=file_path,
                physical_delete=physical_delete,
                request=request
            )
            
            return create_response(
                data={"deleted": success},
                message="文件删除成功" if success else "文件删除失败"
            )
            
        except Exception as e:
            zhi_logger.error(f"增强文件删除失败: {str(e)}")
            return create_response(
                data={"deleted": False},
                code=ResponseCode.INTERNAL_ERROR,
                message=f"文件删除失败: {str(e)}",
                success=False
            )
    
    @route.get('/list', response={200: BaseResponseSchema[Dict[str, Any]]})
    def list_files(self, request,
                  file_type: Optional[str] = Query(None, description="文件类型过滤"),
                  tags: Optional[str] = Query(None, description="标签过滤,逗号分隔"),
                  status: str = Query('active', description="状态过滤"),
                  page: int = Query(1, description="页码"),
                  page_size: int = Query(20, description="每页大小")):
        """
        列出文件（增强版）
        
        支持分页、过滤和搜索
        """
        try:
            # 处理标签过滤
            tag_list = []
            if tags:
                tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            
            # 获取当前用户ID（如果需要按用户过滤）
            creator_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
            
            result = self.file_manager.list_files(
                file_type=file_type,
                tags=tag_list,
                creator_id=creator_id,
                status=status,
                page=page,
                page_size=page_size
            )
            
            return create_response(
                data=result,
                message=f"获取文件列表成功，共 {result['total']} 个文件"
            )
            
        except Exception as e:
            zhi_logger.error(f"获取增强文件列表失败: {str(e)}")
            return create_response(
                data={'files': [], 'total': 0, 'page': page, 'page_size': page_size, 'total_pages': 0},
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取文件列表失败: {str(e)}",
                success=False
            )
