# ZhiLogger - 智能日志系统

ZhiLogger 是 ZhiAdmin 系统的核心日志组件，提供了统一的日志记录、管理和分析功能。支持同步/异步日志记录、数据库存储、WebSocket实时推送、精确位置定位等高级特性。

## 🏗️ 系统架构

```
zhi_common/zhi_logger/
├── loguru_logger.py              # 核心日志记录器（基于Loguru）
├── logger_config.py              # 日志配置管理
├── log_models.py                 # 日志数据模型
├── websocket_handler.py          # WebSocket日志推送
├── celery_tasks.py               # 异步日志任务
├── test_logger_location.py       # 日志位置显示测试
└── README.md                     # 本文件
```

### 架构层次图

```
┌─────────────────────────────────────────────────────────────┐
│                      ZhiLogger 智能日志系统                   │
├─────────────────────────────────────────────────────────────┤
│  get_logger()      │  LoguruLogger    │  便捷方法            │
│  (统一入口)        │  (核心记录器)     │  (快速使用)          │
├─────────────────────────────────────────────────────────────┤
│                    日志处理层                                │
│  同步日志方法      │  异步日志方法     │  位置优化            │
├─────────────────────────────────────────────────────────────┤
│                    输出处理层                                │
│  控制台输出        │  文件输出        │  数据库存储          │
├─────────────────────────────────────────────────────────────┤
│                    扩展功能层                                │
│  WebSocket推送     │  Celery异步      │  统计分析            │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 核心功能

### 1. 精确位置定位 ⭐ (新增优化)

**问题解决**：修复了日志显示 `zhi_logger` 内部位置而不是真实调用位置的问题

**修复前**：
```
INFO | zhi_common.zhi_logger.loguru_logger:_log:142 - 用户登录成功
```

**修复后**：
```
INFO | zhi_oauth.apis.auth:password_login:590 - 用户登录成功
```

**技术实现**：
- 使用 `loguru.opt(depth=2)` 跳过内部调用栈
- 支持同步和异步日志方法
- 精确定位到文件、函数和行号

### 2. 统一日志接口

**核心入口**：
```python
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)
logger.info("这是一条信息日志")
```

**自动模块名获取**：
```python
logger = get_logger()  # 自动获取调用者模块名
```

### 3. 多级日志支持

**支持的日志级别**：
- `TRACE` - 最详细的调试信息
- `DEBUG` - 调试信息
- `INFO` - 一般信息
- `SUCCESS` - 成功操作
- `WARNING` - 警告信息
- `ERROR` - 错误信息
- `CRITICAL` - 严重错误

### 4. 同步/异步日志记录

**同步方法**：
```python
logger.info("同步日志")
logger.debug("调试信息")
logger.error("错误信息")
```

**异步方法**：
```python
await logger.ainfo("异步日志")
await logger.adebug("异步调试信息")
await logger.aerror("异步错误信息")
```

### 5. 丰富的上下文信息

**自动添加的上下文**：
- `trace_id` - 请求追踪ID
- `user_id` - 当前用户ID
- `module_name` - 模块名称
- `timestamp` - 时间戳
- `request_id` - 请求ID

**自定义上下文**：
```python
logger.info("业务操作", user_id="123", category="business", extra_data={"key": "value"})
```

### 6. 多输出目标

**控制台输出**：
- 彩色格式化显示
- 支持不同日志级别的颜色区分
- 包含完整的上下文信息

**文件输出**：
- 按日期轮转
- 支持压缩存储
- 可配置保留天数

**数据库存储**：
- 异步写入数据库
- 支持结构化查询
- 提供统计分析功能

**WebSocket推送**：
- 实时日志推送
- 支持日志级别过滤
- 前端实时监控

## 📖 使用方法

### 基本使用

```python
from zhi_common.zhi_logger import get_logger

# 获取logger实例
logger = get_logger(__name__)

# 记录不同级别的日志
logger.debug("调试信息")
logger.info("一般信息")
logger.success("操作成功")
logger.warning("警告信息")
logger.error("错误信息")
logger.critical("严重错误")
```

### 带上下文的日志

```python
# 添加用户信息
logger.info("用户登录", user_id="123", username="admin")

# 添加业务分类
logger.error("支付失败", category="payment", order_id="ORDER123")

# 添加复杂数据
logger.debug("API调用", extra_data={
    "method": "POST",
    "url": "/api/users",
    "status_code": 200,
    "response_time": 0.5
})
```

### 异步日志使用

```python
import asyncio
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)

async def async_operation():
    await logger.ainfo("开始异步操作")
    
    try:
        # 执行异步业务逻辑
        result = await some_async_function()
        await logger.asuccess("异步操作成功", result=result)
    except Exception as e:
        await logger.aerror("异步操作失败", error=str(e))
```

### API访问日志

```python
# 记录API访问
logger.log_api_request(
    method="POST",
    path="/api/users",
    status_code=201,
    response_time=0.3,
    user_id="123"
)
```

### 审计日志

```python
# 记录审计事件
logger.log_audit_event(
    action="CREATE_USER",
    resource_type="User",
    resource_id="user123",
    user_id="admin",
    extra_data={"username": "newuser"}
)
```

## ⚙️ 配置管理

### 日志级别配置

```python
# 在settings.py中配置
LOGGING_CONFIG = {
    'level': 'INFO',  # 全局日志级别
    'console_level': 'DEBUG',  # 控制台日志级别
    'file_level': 'INFO',  # 文件日志级别
    'database_level': 'WARNING',  # 数据库日志级别
}
```

### 输出目标配置

```python
LOGGING_CONFIG = {
    'handlers': {
        'console': True,  # 启用控制台输出
        'file': True,     # 启用文件输出
        'database': True, # 启用数据库存储
        'websocket': True # 启用WebSocket推送
    }
}
```

### 文件轮转配置

```python
LOGGING_CONFIG = {
    'file': {
        'path': 'logs/app.log',
        'rotation': '1 day',      # 每天轮转
        'retention': '30 days',   # 保留30天
        'compression': 'zip'      # 压缩格式
    }
}
```

## 🔧 高级特性

### 1. 防循环日志

系统内置防循环机制，避免日志记录过程中产生的日志导致无限循环：

```python
# 内部实现
with _disable_logging_loop():
    # 安全的日志记录
    bound_logger.info(message)
```

### 2. 异步处理优化

- **数据库写入异步化**：避免阻塞主线程
- **WebSocket推送异步化**：提高响应性能
- **批量处理**：减少数据库访问次数

### 3. 上下文自动获取

- **请求上下文**：自动获取当前请求信息
- **用户上下文**：自动获取当前用户信息
- **追踪ID**：自动生成和传递追踪ID

### 4. 智能格式化

```python
# 自动格式化复杂对象
logger.info("用户数据", user_obj=user_instance)

# 自动处理异常信息
try:
    risky_operation()
except Exception as e:
    logger.error("操作失败", exc_info=True)  # 自动包含异常堆栈
```

## 📊 性能优化

### 1. 异步写入

```python
# 数据库写入异步化
if self.enable_async:
    asyncio.create_task(self._async_log_handlers(level, message, extra))
```

### 2. 批量处理

```python
# 批量写入数据库
@periodic_task(run_every=crontab(minute='*/5'))
def batch_write_logs():
    # 每5分钟批量写入累积的日志
    pass
```

### 3. 缓存优化

- **用户信息缓存**：减少用户信息查询
- **配置缓存**：避免重复读取配置
- **模板缓存**：缓存日志格式模板

## 🧪 测试验证

### 运行位置显示测试

```bash
# 测试日志位置显示是否正确
python backend/zhi_common/zhi_logger/test_logger_location.py
```

**期望结果**：
```
INFO | __main__:test_function_a:28 - 这是来自test_function_a的日志
DEBUG | __main__:test_method_a:47 - 这是来自TestClass.test_method_a的日志
```

### 功能测试

```python
from zhi_common.zhi_logger import get_logger

def test_logger_functionality():
    logger = get_logger(__name__)
    
    # 测试基本功能
    logger.info("测试信息日志")
    logger.debug("测试调试日志")
    logger.warning("测试警告日志")
    logger.error("测试错误日志")
    
    # 测试上下文
    logger.info("测试上下文", user_id="test", category="test")
    
    print("✅ 日志功能测试完成")

if __name__ == "__main__":
    test_logger_functionality()
```

## 🔍 故障排查

### 常见问题

1. **日志位置显示不正确**
   - 检查是否使用了正确的 `opt(depth=2)` 设置
   - 验证调用栈层级是否正确

2. **异步日志不工作**
   - 确保在异步上下文中调用
   - 检查事件循环是否正常运行

3. **数据库写入失败**
   - 检查数据库连接
   - 验证Celery任务是否正常

4. **WebSocket推送不工作**
   - 检查WebSocket连接状态
   - 验证推送任务是否启动

### 调试模式

```python
# 启用详细调试
import logging
logging.getLogger('zhi_logger').setLevel(logging.DEBUG)

# 查看内部日志
logger = get_logger(__name__)
logger.debug("调试信息", _internal_debug=True)
```

## 📚 相关文档

- **日志模型**: [log_models.py](./log_models.py)
- **配置管理**: [logger_config.py](./logger_config.py)
- **WebSocket处理**: [websocket_handler.py](./websocket_handler.py)
- **异步任务**: [celery_tasks.py](./celery_tasks.py)
- **位置测试**: [test_logger_location.py](./test_logger_location.py)

## 🎉 系统特性总结

ZhiLogger 智能日志系统为您提供：

1. **🎯 精确定位** - 准确显示日志调用的文件、函数和行号
2. **⚡ 高性能** - 异步处理、批量写入、缓存优化
3. **🔧 易于使用** - 统一接口、自动配置、智能格式化
4. **📊 功能丰富** - 多输出目标、实时推送、统计分析
5. **🛡️ 稳定可靠** - 防循环机制、异常处理、容错设计
6. **📈 可扩展** - 插件化架构、自定义处理器、灵活配置

通过 ZhiLogger，您可以轻松实现企业级的日志管理，提高系统的可观测性和调试效率！🚀

## 🔄 日志流程图

```
用户调用 → get_logger() → LoguruLogger → 日志处理 → 多输出目标
   ↓           ↓             ↓            ↓           ↓
业务代码 → 统一入口 → 核心记录器 → 位置优化 → 控制台/文件/数据库/WebSocket
   ↓           ↓             ↓            ↓           ↓
自动上下文 → 模块识别 → 异步处理 → 格式化 → 实时推送/持久化存储
```

## 💡 最佳实践

### 1. 日志级别使用建议

```python
# ✅ 正确使用
logger.debug("详细的调试信息，仅开发环境使用")
logger.info("重要的业务流程信息")
logger.success("操作成功的确认信息")
logger.warning("需要注意但不影响功能的问题")
logger.error("影响功能的错误，需要处理")
logger.critical("系统级严重错误，需要立即处理")

# ❌ 避免滥用
logger.info("进入函数")  # 过于详细，应使用debug
logger.error("用户输入错误")  # 用户错误，应使用warning
```

### 2. 上下文信息添加

```python
# ✅ 推荐方式
logger.info("用户登录成功",
    user_id=user.id,
    username=user.username,
    ip_address=request.META.get('REMOTE_ADDR'),
    user_agent=request.META.get('HTTP_USER_AGENT')
)

# ✅ 业务分类
logger.error("支付处理失败",
    category="payment",
    order_id=order.id,
    amount=order.amount,
    error_code="PAYMENT_GATEWAY_ERROR"
)
```

### 3. 异常处理日志

```python
# ✅ 完整的异常信息
try:
    result = risky_operation()
    logger.success("操作成功", result=result)
except ValidationError as e:
    logger.warning("数据验证失败", error=str(e), data=input_data)
except DatabaseError as e:
    logger.error("数据库操作失败", error=str(e), exc_info=True)
except Exception as e:
    logger.critical("未知错误", error=str(e), exc_info=True)
```

### 4. 性能敏感场景

```python
# ✅ 异步日志用于高频操作
async def high_frequency_operation():
    await logger.adebug("开始高频操作")
    # 业务逻辑
    await logger.ainfo("高频操作完成")

# ✅ 条件日志记录
if logger.isEnabledFor(logging.DEBUG):
    expensive_debug_info = generate_debug_info()
    logger.debug("详细调试信息", debug_info=expensive_debug_info)
```

## 🔧 自定义扩展

### 1. 自定义日志处理器

```python
from zhi_common.zhi_logger.loguru_logger import LoguruLogger

class CustomLogger(LoguruLogger):
    def custom_business_log(self, message: str, **kwargs):
        """自定义业务日志方法"""
        kwargs['category'] = 'business'
        kwargs['custom_field'] = 'custom_value'
        self.info(message, **kwargs)

# 使用自定义logger
logger = CustomLogger("custom_module")
logger.custom_business_log("自定义业务日志")
```

### 2. 自定义格式化器

```python
def custom_formatter(record):
    """自定义日志格式"""
    return "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} - {message}\n"

# 应用自定义格式
from loguru import logger
logger.remove()
logger.add(sys.stdout, format=custom_formatter)
```

### 3. 自定义过滤器

```python
def sensitive_data_filter(record):
    """过滤敏感数据"""
    if 'password' in record['message']:
        record['message'] = record['message'].replace(record['extra'].get('password', ''), '***')
    return record

# 应用过滤器
logger.add(sys.stdout, filter=sensitive_data_filter)
```

## 📈 监控和统计

### 1. 日志统计查询

```python
from zhi_common.zhi_logger.log_models import SystemLog

# 查询错误日志统计
error_count = SystemLog.objects.filter(
    level='ERROR',
    created_at__gte=timezone.now() - timedelta(hours=24)
).count()

# 查询用户操作日志
user_logs = SystemLog.objects.filter(
    user_id='user123',
    category='business'
).order_by('-created_at')[:100]
```

### 2. 实时监控

```python
# WebSocket实时日志推送
from zhi_common.zhi_logger.websocket_handler import LogWebSocketHandler

class LogMonitor:
    def __init__(self):
        self.handler = LogWebSocketHandler()

    def start_monitoring(self, log_level='INFO'):
        """开始实时日志监控"""
        self.handler.start_push(level=log_level)
```

### 3. 日志分析

```python
# 生成日志报告
def generate_log_report(days=7):
    """生成日志分析报告"""
    end_date = timezone.now()
    start_date = end_date - timedelta(days=days)

    report = {
        'total_logs': SystemLog.objects.filter(
            created_at__range=[start_date, end_date]
        ).count(),
        'error_logs': SystemLog.objects.filter(
            level='ERROR',
            created_at__range=[start_date, end_date]
        ).count(),
        'top_modules': SystemLog.objects.filter(
            created_at__range=[start_date, end_date]
        ).values('module_name').annotate(
            count=Count('id')
        ).order_by('-count')[:10]
    }

    return report
```

## 🚨 安全考虑

### 1. 敏感信息过滤

```python
# 自动过滤敏感字段
SENSITIVE_FIELDS = ['password', 'token', 'secret', 'key']

def filter_sensitive_data(data):
    """过滤敏感数据"""
    if isinstance(data, dict):
        return {
            k: '***' if k.lower() in SENSITIVE_FIELDS else v
            for k, v in data.items()
        }
    return data

# 使用过滤器
logger.info("用户数据", user_data=filter_sensitive_data(user_dict))
```

### 2. 访问控制

```python
# 基于角色的日志访问控制
def can_access_logs(user, log_level):
    """检查用户是否可以访问特定级别的日志"""
    if user.is_superuser:
        return True
    if user.has_perm('zhi_logger.view_systemlog'):
        return log_level in ['INFO', 'WARNING', 'ERROR']
    return False
```

### 3. 数据脱敏

```python
import re

def mask_sensitive_info(message):
    """脱敏处理"""
    # 脱敏手机号
    message = re.sub(r'(\d{3})\d{4}(\d{4})', r'\1****\2', message)
    # 脱敏邮箱
    message = re.sub(r'(\w+)@(\w+)', r'***@\2', message)
    return message
```

## 🔄 版本更新记录

### v2.1.0 (2025-07-27)
- ✅ **重大优化**：修复日志位置显示问题，现在能准确显示调用位置
- ✅ **技术改进**：使用 `loguru.opt(depth=2)` 优化调用栈跳转
- ✅ **测试完善**：添加完整的位置显示测试用例
- ✅ **文档更新**：完善系统架构和使用文档

### v2.0.0 (2025-07-20)
- ✅ **架构重构**：基于Loguru重新设计日志系统
- ✅ **异步支持**：添加完整的异步日志记录功能
- ✅ **多输出目标**：支持控制台、文件、数据库、WebSocket
- ✅ **上下文增强**：自动获取请求和用户上下文

### v1.0.0 (2025-07-01)
- ✅ **基础功能**：实现基本的日志记录功能
- ✅ **数据库存储**：支持日志持久化存储
- ✅ **配置管理**：灵活的配置系统

---

**ZhiLogger** - 让日志记录更智能，让问题定位更精准 ✨
