"""
基于Loguru的ZhiAdmin日志系统

提供统一的、高性能的日志记录接口，专为开发者体验优化
"""

import sys
import json
import asyncio
import threading
from enum import Enum
from typing import Any, Dict, Optional, Union, Callable
from pathlib import Path
from datetime import datetime
from contextlib import contextmanager

from loguru import logger
from django.conf import settings
from django.utils import timezone

from ..core_context import get_trace_id, set_trace_id

# 线程本地存储，用于防止循环日志
_thread_local = threading.local()


@contextmanager
def _disable_audit_logging():
    """临时禁用审计日志的上下文管理器"""
    old_value = getattr(_thread_local, 'disable_audit', False)
    _thread_local.disable_audit = True
    try:
        yield
    finally:
        _thread_local.disable_audit = old_value


def _is_audit_disabled():
    """检查是否禁用了审计日志"""
    return getattr(_thread_local, 'disable_audit', False)


@contextmanager
def _disable_logging_loop():
    """临时禁用日志循环检测的上下文管理器"""
    old_value = getattr(_thread_local, 'logging_in_progress', False)
    _thread_local.logging_in_progress = True
    try:
        yield
    finally:
        _thread_local.logging_in_progress = old_value


def _is_logging_in_progress():
    """检查是否正在进行日志记录（防止循环）"""
    return getattr(_thread_local, 'logging_in_progress', False)


class LogLevel(str, Enum):
    """日志级别枚举"""
    TRACE = "TRACE"
    DEBUG = "DEBUG"
    INFO = "INFO"
    SUCCESS = "SUCCESS"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(str, Enum):
    """日志分类枚举"""
    SYSTEM = "system"
    API = "api"
    AUTH = "auth"
    DATABASE = "database"
    BUSINESS = "business"
    SECURITY = "security"
    PERFORMANCE = "performance"
    OAUTH = "oauth"
    CELERY = "celery"
    WEBSOCKET = "websocket"


class ZhiLoguruLogger:
    """
    基于Loguru的ZhiAdmin日志记录器
    
    特性:
    - 自动trace_id绑定
    - 结构化日志记录
    - 异步数据库写入
    - WebSocket实时推送
    - 模块化管理
    """
    
    def __init__(self, module_name: str = None, enable_async: bool = True):
        """
        初始化日志记录器
        
        Args:
            module_name: 模块名称，用于日志分类
            enable_async: 是否启用异步功能
        """
        self.module_name = module_name or "unknown"
        self.enable_async = enable_async
        
        # 绑定模块信息到logger
        self._logger = logger.bind(
            module=self.module_name,
            trace_id=get_trace_id()
        )
    
    def _prepare_extra(self, **kwargs) -> Dict[str, Any]:
        """准备额外的日志字段"""
        extra = {
            'module': self.module_name,
            'trace_id': get_trace_id(),
            'timestamp': timezone.now().isoformat(),
        }

        # 添加用户提供的额外字段
        # 排除内部参数，避免冲突
        excluded_keys = {'message', 'level', 'self'}
        for key, value in kwargs.items():
            if key not in excluded_keys:
                extra[key] = value

        return extra
    
    def _log(self, log_level: str, message: str, **kwargs):
        """内部日志记录方法"""
        # 防止日志循环：如果正在记录日志，直接返回
        if _is_logging_in_progress():
            return

        extra = self._prepare_extra(**kwargs)

        # 使用上下文管理器防止循环
        with _disable_logging_loop():
            # 使用loguru记录日志，跳过当前调用栈层级以显示真实的调用位置
            # depth=2: 跳过 _log 方法和调用它的便捷方法（如info/debug等）
            bound_logger = logger.bind(**extra).opt(depth=2)
            getattr(bound_logger, log_level.lower())(message)

            # 异步处理数据库写入和WebSocket推送
            if self.enable_async:
                try:
                    # 检查是否在异步上下文中
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 在异步上下文中，创建任务
                        asyncio.create_task(self._async_log_handlers(log_level, message, extra))
                    else:
                        # 不在异步上下文中，使用同步方式
                        self._sync_log_handlers(log_level, message, extra)
                except RuntimeError:
                    # 没有事件循环，使用同步方式
                    self._sync_log_handlers(log_level, message, extra)
    
    async def _async_log_handlers(self, level: str, message: str, extra: Dict[str, Any]):
        """异步日志处理器"""
        try:
            # 检查是否是日志系统内部的日志，避免循环
            if extra.get('module') == 'zhi_common.zhi_logger.loguru_logger':
                return

            # 异步写入数据库
            await async_log_to_database(level, message, extra)

            # 异步WebSocket推送
            await async_log_to_websocket(level, message, extra)

        except Exception as e:
            # 异步处理失败时，静默处理，避免循环日志
            pass
    
    def _sync_log_handlers(self, level: str, message: str, extra: Dict[str, Any]):
        """同步日志处理器（备用）"""
        try:
            # 检查是否是日志系统内部的日志，避免循环
            if extra.get('module') == 'zhi_common.zhi_logger.loguru_logger':
                return

            # 直接调用同步版本的数据库写入，避免异步问题
            self._sync_log_to_database(level, message, extra)
        except Exception as e:
            # 避免循环日志，不记录错误
            pass

    def _sync_log_to_database(self, level: str, message: str, extra: Dict[str, Any]):
        """同步版本的数据库写入"""
        try:
            from django.apps import apps
            if apps.ready:
                from zhi_logger.models import SystemLog, LogLevel as ModelLogLevel, LogCategory as ModelLogCategory

                # 临时禁用审计日志，避免循环
                with _disable_audit_logging():
                    SystemLog.objects.create(
                        level=getattr(ModelLogLevel, level, ModelLogLevel.INFO),
                        message=message,
                        category=extra.get('category', ModelLogCategory.SYSTEM),
                        module_name=extra.get('module', 'unknown'),
                        trace_id=extra.get('trace_id', ''),
                        user_id=extra.get('user_id', ''),
                        ip_address=extra.get('ip_address', ''),
                        user_agent=extra.get('user_agent', ''),
                        extra_data=extra
                    )
        except Exception:
            # 数据库写入失败时，静默处理
            pass
    
    # 标准日志方法
    def trace(self, message: str, **kwargs):
        """记录TRACE级别日志"""
        self._log("TRACE", message, **kwargs)

    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self._log("DEBUG", message, **kwargs)

    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        self._log("INFO", message, **kwargs)

    def success(self, message: str, **kwargs):
        """记录SUCCESS级别日志"""
        self._log("SUCCESS", message, **kwargs)

    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        self._log("WARNING", message, **kwargs)

    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        self._log("ERROR", message, **kwargs)

    def critical(self, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self._log("CRITICAL", message, **kwargs)
    
    # 异步日志方法
    async def atrace(self, message: str, **kwargs):
        """异步记录TRACE级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra)
        bound_logger.trace(message)
        await self._async_log_handlers("TRACE", message, extra)
    
    async def adebug(self, message: str, **kwargs):
        """异步记录DEBUG级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra).opt(depth=1)
        bound_logger.debug(message)
        await self._async_log_handlers("DEBUG", message, extra)
    
    async def ainfo(self, message: str, **kwargs):
        """异步记录INFO级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra).opt(depth=1)
        bound_logger.info(message)
        await self._async_log_handlers("INFO", message, extra)
    
    async def asuccess(self, message: str, **kwargs):
        """异步记录SUCCESS级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra).opt(depth=1)
        bound_logger.success(message)
        await self._async_log_handlers("SUCCESS", message, extra)
    
    async def awarning(self, message: str, **kwargs):
        """异步记录WARNING级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra).opt(depth=1)
        bound_logger.warning(message)
        await self._async_log_handlers("WARNING", message, extra)
    
    async def aerror(self, message: str, **kwargs):
        """异步记录ERROR级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra).opt(depth=1)
        bound_logger.error(message)
        await self._async_log_handlers("ERROR", message, extra)
    
    async def acritical(self, message: str, **kwargs):
        """异步记录CRITICAL级别日志"""
        extra = self._prepare_extra(**kwargs)
        bound_logger = logger.bind(**extra).opt(depth=1)
        bound_logger.critical(message)
        await self._async_log_handlers("CRITICAL", message, extra)
    
    # 便捷方法
    def log_api_request(self, method: str, path: str, status_code: int, 
                       response_time: float = None, **kwargs):
        """记录API请求日志"""
        self.info(
            f"API请求: {method} {path} -> {status_code}",
            category=LogCategory.API,
            method=method,
            path=path,
            status_code=status_code,
            response_time=response_time,
            **kwargs
        )
    
    def log_auth_event(self, event: str, user_id: str = None, success: bool = True, **kwargs):
        """记录认证事件日志"""
        level = "info" if success else "warning"
        getattr(self, level)(
            f"认证事件: {event}",
            category=LogCategory.AUTH,
            event=event,
            user_id=user_id,
            success=success,
            **kwargs
        )
    
    def log_database_operation(self, operation: str, table: str = None, 
                              duration: float = None, **kwargs):
        """记录数据库操作日志"""
        self.debug(
            f"数据库操作: {operation}",
            category=LogCategory.DATABASE,
            operation=operation,
            table=table,
            duration=duration,
            **kwargs
        )
    
    def log_performance(self, operation: str, duration: float, **kwargs):
        """记录性能日志"""
        level = "warning" if duration > 1.0 else "info"
        getattr(self, level)(
            f"性能监控: {operation} 耗时 {duration:.3f}s",
            category=LogCategory.PERFORMANCE,
            operation=operation,
            duration=duration,
            **kwargs
        )


# 全局日志记录器实例（向后兼容）
zhi_logger = ZhiLoguruLogger(module_name="zhi_admin")


def get_logger(module_name: str = None, enable_async: bool = True) -> ZhiLoguruLogger:
    """
    获取模块级日志记录器
    
    Args:
        module_name: 模块名称，通常使用 __name__
        enable_async: 是否启用异步功能
        
    Returns:
        ZhiLoguruLogger: 日志记录器实例
        
    Example:
        ```python
        from zhi_common.zhi_logger import get_logger
        
        logger = get_logger(__name__)
        logger.info("这是一条信息日志", user_id="123", category="business")
        ```
    """
    if module_name is None:
        # 自动获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        module_name = frame.f_globals.get('__name__', 'unknown')
    
    return ZhiLoguruLogger(module_name=module_name, enable_async=enable_async)


def configure_logging(
    level: str = "INFO",
    format_type: str = "standard",
    log_file: str = None,
    enable_json: bool = False,
    enable_colors: bool = True
):
    """
    配置Loguru日志系统
    
    Args:
        level: 日志级别
        format_type: 格式类型 ("standard", "detailed", "json")
        log_file: 日志文件路径
        enable_json: 是否启用JSON格式
        enable_colors: 是否启用颜色输出
    """
    # 移除默认处理器
    logger.remove()
    
    # 根据格式类型选择格式
    if format_type == "json" or enable_json:
        format_str = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {extra[trace_id]} | {extra[module]}:{function}:{line} - {message}"
        serialize = True
    elif format_type == "detailed":
        format_str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{extra[trace_id]}</cyan> | <cyan>{extra[module]}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        serialize = False
    else:  # standard
        format_str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{extra[trace_id]}</cyan> | <cyan>{extra[module]}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        serialize = False
    
    # 添加控制台处理器
    logger.add(
        sys.stderr,
        format=format_str,
        level=level,
        colorize=enable_colors,
        serialize=serialize
    )
    
    # 添加文件处理器
    if log_file:
        logger.add(
            log_file,
            format=format_str,
            level=level,
            rotation="100 MB",
            retention="30 days",
            compression="zip",
            serialize=serialize
        )
    
    # 设置默认的extra字段
    logger.configure(extra={"trace_id": "", "module": "unknown"})


# 异步处理函数
async def async_log_to_database(level: str, message: str, extra: Dict[str, Any]):
    """异步写入日志到数据库"""
    # 防止循环日志：跳过日志系统内部的日志
    if extra.get('module') == 'zhi_common.zhi_logger.loguru_logger':
        return

    try:
        # 优先使用zhi_celery的异步任务
        from zhi_celery.tasks.logger_tasks import log_to_database_task

        # 创建Celery任务
        log_to_database_task.delay(
            level=level,
            message=message,
            extra_data=extra
        )

    except ImportError:
        # 如果zhi_celery不可用，直接写入数据库
        try:
            from django.apps import apps
            if apps.ready:
                from zhi_logger.models import SystemLog, LogLevel as ModelLogLevel, LogCategory as ModelLogCategory

                # 临时禁用审计日志，避免循环
                with _disable_audit_logging():
                    SystemLog.objects.create(
                        level=getattr(ModelLogLevel, level, ModelLogLevel.INFO),
                        message=message,
                        category=extra.get('category', ModelLogCategory.SYSTEM),
                        module_name=extra.get('module', 'unknown'),
                        trace_id=extra.get('trace_id', ''),
                        user_id=extra.get('user_id', ''),
                        ip_address=extra.get('ip_address', ''),
                        user_agent=extra.get('user_agent', ''),
                        extra_data=extra
                    )
            else:
                # Django应用未就绪，跳过数据库写入
                pass
        except Exception as db_error:
            # 数据库写入失败时，静默处理（避免循环日志）
            pass

    except Exception as e:
        # 异步任务创建失败时，静默处理（避免循环日志）
        pass


async def async_log_to_websocket(level: str, message: str, extra: Dict[str, Any]):
    """异步推送日志到WebSocket"""
    # 防止循环日志：跳过日志系统内部的日志
    if extra.get('module') == 'zhi_common.zhi_logger.loguru_logger':
        return

    try:
        # 优先使用zhi_celery的异步任务
        from zhi_celery.tasks.logger_tasks import websocket_log_push_task

        log_data = {
            'level': level,
            'message': message,
            'extra': extra
        }

        # 创建Celery任务
        websocket_log_push_task.delay(log_data)

    except ImportError:
        # 如果zhi_celery不可用，直接推送
        try:
            from channels.layers import get_channel_layer

            channel_layer = get_channel_layer()
            if channel_layer:
                await channel_layer.group_send(
                    "logs",
                    {
                        "type": "log_message",
                        "level": level,
                        "message": message,
                        "extra": extra,
                        "timestamp": timezone.now().isoformat()
                    }
                )
        except Exception as ws_error:
            # 静默处理WebSocket推送失败，避免循环日志
            pass

    except Exception as e:
        # 静默处理异常，避免循环日志
        pass


# 便捷的异步任务调用函数
def log_api_access_async(request_data: Dict[str, Any]):
    """异步记录API访问日志"""
    try:
        from zhi_celery.tasks.logger_tasks import log_api_access_task
        log_api_access_task.delay(request_data)
    except ImportError:
        # 静默处理，避免循环日志
        pass
    except Exception as e:
        # 静默处理，避免循环日志
        pass


def log_audit_event_async(audit_data: Dict[str, Any]):
    """异步记录审计日志"""
    try:
        from zhi_celery.tasks.logger_tasks import log_audit_event_task
        log_audit_event_task.delay(audit_data)
    except ImportError:
        # 静默处理，避免循环日志
        pass
    except Exception as e:
        # 静默处理，避免循环日志
        pass
