"""
ZhiAdmin 日志SDK - 基于Loguru的统一日志系统

设计理念:
- 统一使用loguru作为底层日志引擎，提供更好的开发体验
- 模块级记录器：每个模块使用自己的记录器实例
- 结构化日志：支持trace_id、分类、模块等结构化字段
- 异步日志：支持异步写入，提高性能
- WebSocket实时推送：支持实时日志推送到前端
- 多环境支持：开发、测试、生产环境的不同配置

特性:
- 🚀 基于loguru，更好的开发体验和性能
- 📊 结构化日志记录，支持JSON格式
- 🔄 异步日志写入，不阻塞主线程
- 📡 WebSocket实时推送
- 🏷️ 自动trace_id追踪
- 📁 模块化日志管理
- 🎯 多级别日志过滤
- 📈 日志统计和分析
- 🔧 灵活的配置系统

使用方式:
```python
from zhi_common.zhi_logger import get_logger

# 创建模块级记录器
logger = get_logger(__name__)

# 记录日志
logger.info("用户登录成功", category="auth", user_id="user123")
logger.error("数据库连接失败", category="database", error="timeout")

# 异步日志记录
await logger.ainfo("异步操作完成", operation="data_sync")
```
"""

try:
    from .loguru_logger import (
        # 主要接口
        get_logger,
        configure_logging,

        # 日志记录器类
        ZhiLoguruLogger,

        # 常量和枚举
        LogLevel,
        LogCategory,

        # 异步任务支持
        async_log_to_database,
        async_log_to_websocket,
        log_api_access_async,
        log_audit_event_async,

        # 兼容性接口
        zhi_logger,
    )

    LOGURU_AVAILABLE = True

except ImportError as e:
    # 如果loguru_logger导入失败，回退到原有系统
    import warnings
    warnings.warn(f"Loguru日志系统导入失败，回退到原有系统: {e}", UserWarning)

    from .core_logger import zhi_logger
    from .base import get_logger

    LOGURU_AVAILABLE = False

# 向后兼容的导入
try:
    from .base import (
        ZhiLogger,
        ZhiLogFormatter,
        LogCallbackHandler,
        PrintingCallbackHandler,
        FileCallbackHandler,
        ZHI_LOGGER_ROOT
    )
except ImportError:
    # 如果base模块导入失败，忽略
    pass

# 导出主要类和函数
if LOGURU_AVAILABLE:
    __all__ = [
        # 新的Loguru接口（推荐使用）
        'get_logger',
        'configure_logging',
        'ZhiLoguruLogger',

        # 常量和枚举
        'LogLevel',
        'LogCategory',

        # 异步任务支持
        'async_log_to_database',
        'async_log_to_websocket',
        'log_api_access_async',
        'log_audit_event_async',

        # 兼容性接口
        'zhi_logger',

        # 传统接口（向后兼容）
        'ZhiLogger',
        'ZhiLogFormatter',
        'LogCallbackHandler',
        'PrintingCallbackHandler',
        'FileCallbackHandler',
        'ZHI_LOGGER_ROOT',
    ]
else:
    __all__ = [
        # 传统接口
        'zhi_logger',
        'get_logger',
        'ZhiLogger',
        'ZhiLogFormatter',
        'LogCallbackHandler',
        'PrintingCallbackHandler',
        'FileCallbackHandler',
        'ZHI_LOGGER_ROOT',
    ]

# 版本信息
__version__ = "1.0.0"
__author__ = "ZhiAdmin Team"
