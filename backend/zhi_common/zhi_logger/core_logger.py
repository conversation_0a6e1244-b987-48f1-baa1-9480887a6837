import time
from typing import Any, Callable, Optional
from functools import wraps
from .base import ZhiLogger

# 全局日志实例
zhi_logger = ZhiLogger()


def logging_func_time(
        func: Optional[Callable] = None, *,
        log_start: bool = True,
        log_end: bool = True,
        log_time: bool = True
        ) -> Callable:
    """记录函数执行时间的装饰器

    Args:
        func: 被装饰函数
        log_start: 是否记录开始日志
        log_end: 是否记录结束日志
        log_time: 是否记录执行时间

    Returns:
        装饰后的函数
    """

    def decorator(f: Callable) -> Callable:
        @wraps(f)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            function_name = f.__name__
            start_msg = f"🚀 开始执行: {function_name}"
            end_msg = f"✅ 结束执行: {function_name}"

            if log_start:
                zhi_logger.info(start_msg.center(50, "="), depth=2)

            start_time = time.time()
            result = f(*args, **kwargs)
            run_time = time.time() - start_time

            if log_end:
                zhi_logger.info(end_msg.center(50, "="), depth=2)
            if log_time:
                time_msg = f"⏱️ 执行耗时: {run_time:.3f}s"
                zhi_logger.info(time_msg.center(50, "="), depth=2)

            return result

        return wrapper

    if func is not None:
        return decorator(func)
    return decorator
