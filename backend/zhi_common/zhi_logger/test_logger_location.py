"""
测试日志位置显示优化

验证修复后的日志能够正确显示调用位置而不是zhi_logger内部位置
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_common.zhi_logger import get_logger

# 获取当前模块的logger
logger = get_logger(__name__)


def test_function_a():
    """测试函数A - 验证日志位置显示"""
    logger.info("这是来自test_function_a的日志")
    logger.debug("这是来自test_function_a的调试日志")
    logger.warning("这是来自test_function_a的警告日志")


def test_function_b():
    """测试函数B - 验证日志位置显示"""
    logger.error("这是来自test_function_b的错误日志")
    logger.info("这是来自test_function_b的信息日志", user_id="test_user", category="test")


class TestClass:
    """测试类 - 验证类方法中的日志位置显示"""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def test_method_a(self):
        """测试方法A"""
        self.logger.info("这是来自TestClass.test_method_a的日志")
        self.logger.debug("这是来自TestClass.test_method_a的调试日志")
    
    def test_method_b(self):
        """测试方法B"""
        self.logger.warning("这是来自TestClass.test_method_b的警告日志")
        self.logger.error("这是来自TestClass.test_method_b的错误日志")


def test_nested_calls():
    """测试嵌套调用的日志位置显示"""
    
    def inner_function():
        logger.info("这是来自inner_function的日志")
        logger.debug("这是来自inner_function的调试日志")
    
    logger.info("这是来自test_nested_calls的日志")
    inner_function()


def test_different_modules():
    """测试不同模块的日志位置显示"""
    
    # 测试从其他模块调用
    try:
        from zhi_common.zhi_model.enhanced_user_manager import enhanced_user_manager
        
        # 这应该显示enhanced_user_manager模块的位置
        current_user = enhanced_user_manager.get_current_user_info()
        logger.info(f"获取到用户信息: {current_user}")
        
    except Exception as e:
        logger.error(f"测试其他模块调用失败: {e}")


async def test_async_logging():
    """测试异步日志的位置显示"""
    logger.info("开始异步日志测试")
    
    # 测试异步日志方法
    await logger.ainfo("这是异步info日志")
    await logger.adebug("这是异步debug日志")
    await logger.awarning("这是异步warning日志")
    await logger.aerror("这是异步error日志")
    
    logger.info("异步日志测试完成")


def main():
    """主测试函数"""
    print("🧪 开始测试日志位置显示优化...")
    print("🎯 目标：验证日志显示真实的调用位置而不是zhi_logger内部位置")
    print()
    
    # 测试1：基本函数调用
    print("📋 测试1：基本函数调用")
    test_function_a()
    test_function_b()
    print()
    
    # 测试2：类方法调用
    print("📋 测试2：类方法调用")
    test_obj = TestClass()
    test_obj.test_method_a()
    test_obj.test_method_b()
    print()
    
    # 测试3：嵌套调用
    print("📋 测试3：嵌套调用")
    test_nested_calls()
    print()
    
    # 测试4：不同模块调用
    print("📋 测试4：不同模块调用")
    test_different_modules()
    print()
    
    # 测试5：异步日志
    print("📋 测试5：异步日志")
    import asyncio
    try:
        asyncio.run(test_async_logging())
    except Exception as e:
        logger.error(f"异步日志测试失败: {e}")
    print()
    
    print("✅ 日志位置显示测试完成！")
    print()
    print("🔍 检查上面的日志输出：")
    print("   - 应该显示具体的函数名和行号（如：test_function_a:25）")
    print("   - 而不是显示 zhi_common.zhi_logger.loguru_logger:_log:142")
    print("   - 每个日志都应该准确指向调用日志的代码位置")
    print()
    print("📝 如果看到类似以下格式的日志，说明修复成功：")
    print("   INFO | zhi_common.zhi_logger.test_logger_location:test_function_a:25 - 这是来自test_function_a的日志")
    print("   而不是：")
    print("   INFO | zhi_common.zhi_logger.loguru_logger:_log:142 - 这是来自test_function_a的日志")


if __name__ == "__main__":
    main()
