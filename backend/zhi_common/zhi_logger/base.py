import json
import logging
import asyncio
from datetime import datetime
from typing import Any, Literal, Optional, Dict, Union, Callable
from functools import partial
from asgiref.sync import async_to_sync

from ..core_context import get_trace_id

# 安全导入 channels 相关模块
def get_safe_channel_layer():
    """
    安全获取 Channel Layer，支持多种配置方式

    优先级：
    1. channels_redis (推荐)
    2. django_redis 作为 channel layer
    3. 内存 channel layer (开发环境)
    4. 返回 None (禁用 WebSocket)
    """
    try:
        from channels.layers import get_channel_layer
        from django.conf import settings

        # 检查是否配置了 CHANNEL_LAYERS
        if not hasattr(settings, 'CHANNEL_LAYERS'):
            return None

        # 尝试获取 channel layer
        try:
            channel_layer = get_channel_layer()
            # 测试 channel layer 是否可用
            if hasattr(channel_layer, 'send'):
                return channel_layer
        except Exception as e:
            # 如果获取失败，尝试创建备用的 channel layer
            return _create_fallback_channel_layer()

    except ImportError:
        # channels 未安装
        pass

    return None

def _create_fallback_channel_layer():
    """创建备用的 Channel Layer"""
    try:
        from django.conf import settings

        # 尝试使用 django_redis 创建简单的 channel layer
        if hasattr(settings, 'CACHES') and 'default' in settings.CACHES:
            cache_config = settings.CACHES['default']

            # 如果使用的是 django_redis
            if 'django_redis' in cache_config.get('BACKEND', ''):
                return _create_redis_channel_layer_from_cache(cache_config)

        # 使用内存 channel layer (仅开发环境)
        if settings.DEBUG:
            return _create_memory_channel_layer()

    except Exception:
        pass

    return None

def _create_redis_channel_layer_from_cache(cache_config):
    """从 django_redis 缓存配置创建 Redis Channel Layer"""
    try:
        # 这里可以实现一个简单的基于 Redis 的消息传递
        # 但由于复杂性，建议安装 channels_redis
        return None
    except Exception:
        return None

def _create_memory_channel_layer():
    """创建内存 Channel Layer (仅用于开发)"""
    try:
        from channels.layers import InMemoryChannelLayer
        return InMemoryChannelLayer()
    except ImportError:
        return None


# ZhiAdmin 日志SDK根记录器名称
ZHI_LOGGER_ROOT = "zhi_admin"

class LogLevel:
    """日志级别常量"""
    DEBUG = logging.DEBUG
    INFO = logging.INFO
    WARNING = logging.WARNING
    ERROR = logging.ERROR
    CRITICAL = logging.CRITICAL


class LogCategory:
    """日志分类常量"""
    SYSTEM = "system"
    API = "api"
    AUTH = "auth"
    DATABASE = "database"
    BUSINESS = "business"
    SECURITY = "security"
    PERFORMANCE = "performance"


class ZhiLogFormatter(logging.Formatter):
    """ZhiAdmin 自定义日志格式化器"""

    def __init__(self, format_type: str = "standard"):
        self.format_type = format_type

        if format_type == "json":
            super().__init__()
        elif format_type == "structured":
            super().__init__(
                "%(levelname)s | %(name)s | %(module_name)s | %(category)s | %(message)s"
            )
        else:  # standard
            super().__init__(
                "%(asctime)s | %(levelname)s | %(name)s | %(message)s"
            )

    def format(self, record):
        if self.format_type == "json":
            return self._format_json(record)
        else:
            return super().format(record)

    def _format_json(self, record):
        """JSON格式化"""
        log_data = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": getattr(record, 'module_name', ''),
            "category": getattr(record, 'category', ''),
            "trace_id": getattr(record, 'trace_id', ''),
            "user_id": getattr(record, 'user_id', ''),
            "extra_data": getattr(record, 'extra_data', {}),
        }

        # 移除空值
        log_data = {k: v for k, v in log_data.items() if v}

        return json.dumps(log_data, ensure_ascii=False)


class ZhiLogger:
    """ZhiAdmin 日志记录器

    类似于 Strands Agents SDK 的设计模式:
    - 模块级记录器：每个模块使用自己的记录器
    - 分层日志记录：所有记录器都是 "zhi_admin" 根记录器的子级
    - 默认行为：不强制配置，允许集成到应用程序的日志配置中
    - 结构化日志：支持自定义格式化器和额外字段
    - 回调系统：区分内部日志和面向用户的输出
    """

    def __init__(self, name: str = None, module_name: str = None, enable_websocket: bool = True):
        """
        初始化日志记录器

        Args:
            name: 记录器名称，如果为None则使用调用模块的__name__
            module_name: 模块名称，用于日志分类
            enable_websocket: 是否启用WebSocket推送
        """
        if name is None:
            # 获取调用者的模块名
            import inspect
            frame = inspect.currentframe().f_back
            name = frame.f_globals.get('__name__', 'unknown')

        # 确保所有记录器都是 zhi_admin 的子级
        if not name.startswith(ZHI_LOGGER_ROOT):
            if name == '__main__':
                logger_name = ZHI_LOGGER_ROOT
            else:
                logger_name = f"{ZHI_LOGGER_ROOT}.{name}"
        else:
            logger_name = name

        self._logger = logging.getLogger(logger_name)
        self.module_name = module_name or self._extract_module_name(name)
        self.enable_websocket = enable_websocket
        self.channel_layer = get_safe_channel_layer() if enable_websocket else None

        # 如果启用 WebSocket 但无法获取 channel layer，记录警告
        if enable_websocket and not self.channel_layer:
            self._logger.warning(
                "WebSocket功能已启用但无法获取Channel Layer。"
                "请安装 channels_redis 或配置 CHANNEL_LAYERS。"
                "WebSocket日志推送将被禁用。"
            )

        # 回调处理器列表
        self._callback_handlers = []

    def _extract_module_name(self, name: str) -> str:
        """从记录器名称中提取模块名"""
        parts = name.split('.')
        if len(parts) > 2:
            return parts[-1]  # 取最后一部分作为模块名
        return parts[-1] if parts else 'unknown'

    def debug(self, message: str, **kwargs):
        """记录DEBUG级别日志"""
        self._log_with_context(logging.DEBUG, message, **kwargs)

    def info(self, message: str, **kwargs):
        """记录INFO级别日志"""
        self._log_with_context(logging.INFO, message, **kwargs)

    def warning(self, message: str, **kwargs):
        """记录WARNING级别日志"""
        self._log_with_context(logging.WARNING, message, **kwargs)

    def error(self, message: str, **kwargs):
        """记录ERROR级别日志"""
        self._log_with_context(logging.ERROR, message, **kwargs)

    def critical(self, message: str, **kwargs):
        """记录CRITICAL级别日志"""
        self._log_with_context(logging.CRITICAL, message, **kwargs)

    def exception(self, message: str, **kwargs):
        """记录异常日志（包含堆栈跟踪）"""
        kwargs['exc_info'] = True
        self._log_with_context(logging.ERROR, message, **kwargs)

    def _log_with_context(self, level: int, message: str, **kwargs) -> None:
        """
        带上下文的日志记录

        Args:
            level: 日志级别 (logging.DEBUG, logging.INFO, etc.)
            message: 日志消息
            **kwargs:
                category: 日志分类
                user_id: 用户ID
                module_name: 模块名称覆盖
                extra_data: 额外数据
                enable_websocket: 是否启用WebSocket推送
                exc_info: 是否包含异常信息
        """
        # 提取参数
        category = kwargs.pop('category', LogCategory.SYSTEM)
        user_id = kwargs.pop('user_id', None)
        module_name = kwargs.pop('module_name', self.module_name)
        extra_data = kwargs.pop('extra_data', {})
        enable_ws = kwargs.pop('enable_websocket', self.enable_websocket)
        exc_info = kwargs.pop('exc_info', False)

        # 获取trace_id
        try:
            trace_id = get_trace_id() or ""
        except Exception:
            trace_id = ""

        # 创建日志记录，添加额外字段
        extra = {
            'module_name': module_name,
            'category': category,
            'trace_id': trace_id,
            'user_id': user_id,
            'extra_data': extra_data,
        }

        # 记录日志
        self._logger.log(level, message, extra=extra, exc_info=exc_info)

        # 构建WebSocket推送数据
        if enable_ws and self.channel_layer:
            log_data = {
                'timestamp': datetime.now().isoformat(),
                'level': logging.getLevelName(level),
                'logger': self._logger.name,
                'message': message,
                'trace_id': trace_id,
                'module_name': module_name,
                'category': category,
                'user_id': user_id,
                'extra_data': extra_data
            }

            try:
                # 检查是否在异步上下文中
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 在异步上下文中，创建任务而不是使用async_to_sync
                        asyncio.create_task(self._send_websocket_log(log_data))
                    else:
                        # 不在异步上下文中，使用async_to_sync
                        async_to_sync(self._send_websocket_log)(log_data)
                except RuntimeError:
                    # 没有事件循环，使用async_to_sync
                    async_to_sync(self._send_websocket_log)(log_data)
            except Exception as e:
                # WebSocket推送失败不应影响正常日志记录
                self._logger.warning(f"WebSocket日志推送失败: {e}")

        # 调用回调处理器
        self._call_handlers(level, message, extra)

    def add_callback_handler(self, handler: Callable):
        """添加回调处理器"""
        self._callback_handlers.append(handler)

    def remove_callback_handler(self, handler: Callable):
        """移除回调处理器"""
        if handler in self._callback_handlers:
            self._callback_handlers.remove(handler)

    def _call_handlers(self, level: int, message: str, extra: Dict[str, Any]):
        """调用所有回调处理器"""
        for handler in self._callback_handlers:
            try:
                if callable(handler):
                    handler(level, message, extra)
                elif hasattr(handler, 'handle_log'):
                    handler.handle_log(level, message, extra)
            except Exception as e:
                # 回调处理器异常不应影响日志记录
                self._logger.warning(f"回调处理器异常: {e}")

    async def _send_websocket_log(self, log_data: Dict[str, Any]) -> None:
        """异步发送WebSocket日志"""
        if not self.channel_layer:
            return

        try:
            # 检查 channel layer 是否有效
            if not hasattr(self.channel_layer, 'group_send'):
                return

            # 发送到日志频道组
            await self.channel_layer.group_send(
                "logs",
                {
                    "type": "log_message",
                    "data": log_data
                }
            )

            # 根据日志级别发送到特定频道
            if log_data['level'] in ['ERROR', 'CRITICAL']:
                await self.channel_layer.group_send(
                    "logs_error",
                    {
                        "type": "log_message",
                        "data": log_data
                    }
                )

            # 根据模块发送到特定频道
            if log_data.get('module_name'):
                module_group = f"logs_{log_data['module_name']}"
                await self.channel_layer.group_send(
                    module_group,
                    {
                        "type": "log_message",
                        "data": log_data
                    }
                )

        except Exception as e:
            # WebSocket发送失败不应影响正常日志记录
            # 只在调试模式下记录详细错误
            try:
                from django.conf import settings
                if getattr(settings, 'DEBUG', False):
                    self._logger.debug(f"WebSocket日志发送失败: {e}")
            except Exception:
                pass

    def log_api_request(self, request, response=None, **kwargs):
        """记录API请求日志"""
        extra_data = {
            'method': getattr(request, 'method', 'UNKNOWN'),
            'path': getattr(request, 'path', ''),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'ip': request.META.get('REMOTE_ADDR', ''),
        }

        if response:
            extra_data.update({
                'status_code': getattr(response, 'status_code', 0),
                'response_size': len(getattr(response, 'content', b''))
            })

        extra_data.update(kwargs.get('extra_data', {}))

        self.info(
            f"API请求: {extra_data['method']} {extra_data['path']}",
            category=LogCategory.API,
            extra_data=extra_data,
            **kwargs
        )


# ============================================================================
# SDK 工厂函数和配置系统 (类似 Strands Agents SDK)
# ============================================================================

def get_logger(name: str = None, module_name: str = None, enable_websocket: bool = True) -> ZhiLogger:
    """
    获取 ZhiAdmin 日志记录器实例

    类似于 Strands SDK 的使用方式:
    ```python
    from zhi_common.zhi_logger import get_logger

    # 模块级记录器
    logger = get_logger(__name__)

    # 或者指定模块名
    logger = get_logger(module_name="user_service")
    ```

    Args:
        name: 记录器名称，通常传入 __name__
        module_name: 模块名称，用于日志分类
        enable_websocket: 是否启用WebSocket推送

    Returns:
        ZhiLogger实例
    """
    return ZhiLogger(name=name, module_name=module_name, enable_websocket=enable_websocket)


def configure_logging(
    level: int = logging.INFO,
    format_type: str = "standard",
    handlers: list = None,
    enable_console: bool = True
):
    """
    配置 ZhiAdmin 日志系统

    类似于 Strands SDK 的配置方式:
    ```python
    import logging
    from zhi_common.zhi_logger import configure_logging

    # 基础配置
    configure_logging(level=logging.DEBUG)

    # JSON格式配置
    configure_logging(
        level=logging.INFO,
        format_type="json",
        enable_console=True
    )
    ```

    Args:
        level: 日志级别
        format_type: 格式类型 ("standard", "structured", "json")
        handlers: 自定义处理器列表
        enable_console: 是否启用控制台输出
    """
    # 获取根记录器
    root_logger = logging.getLogger(ZHI_LOGGER_ROOT)
    root_logger.setLevel(level)

    # 清除现有处理器
    root_logger.handlers.clear()

    # 创建格式化器
    formatter = ZhiLogFormatter(format_type=format_type)

    # 添加控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # 添加自定义处理器
    if handlers:
        for handler in handlers:
            if not handler.formatter:
                handler.setFormatter(formatter)
            root_logger.addHandler(handler)


def configure_module_logging(module_name: str, level: int):
    """
    为特定模块配置日志级别

    类似于 Strands SDK 的模块级配置:
    ```python
    from zhi_common.zhi_logger import configure_module_logging
    import logging

    # 只为工具注册模块启用DEBUG日志
    configure_module_logging("zhi_admin.tools.registry", logging.DEBUG)

    # 为模型交互设置WARNING级别
    configure_module_logging("zhi_admin.models", logging.WARNING)
    ```

    Args:
        module_name: 模块名称
        level: 日志级别
    """
    logger_name = f"{ZHI_LOGGER_ROOT}.{module_name}"
    logger = logging.getLogger(logger_name)
    logger.setLevel(level)


# ============================================================================
# 回调处理器基类 (类似 Strands SDK 的回调系统)
# ============================================================================

class LogCallbackHandler:
    """日志回调处理器基类"""

    def handle_log(self, level: int, message: str, extra: Dict[str, Any]):
        """处理日志事件"""
        raise NotImplementedError


class PrintingCallbackHandler(LogCallbackHandler):
    """打印回调处理器 - 用于调试"""

    def handle_log(self, level: int, message: str, extra: Dict[str, Any]):
        level_name = logging.getLevelName(level)
        module_name = extra.get('module_name', '')
        category = extra.get('category', '')
        print(f"[{level_name}] {module_name}:{category} | {message}")


class FileCallbackHandler(LogCallbackHandler):
    """文件回调处理器"""

    def __init__(self, filename: str):
        self.filename = filename

    def handle_log(self, level: int, message: str, extra: Dict[str, Any]):
        import json
        log_data = {
            'timestamp': datetime.now().isoformat(),
            'level': logging.getLevelName(level),
            'message': message,
            **extra
        }

        with open(self.filename, 'a', encoding='utf-8') as f:
            f.write(json.dumps(log_data, ensure_ascii=False) + '\n')


# ============================================================================
# 便捷的模块级日志记录器创建函数
# ============================================================================

def create_module_logger(module_name: str) -> ZhiLogger:
    """
    为模块创建专用的日志记录器

    Args:
        module_name: 模块名称

    Returns:
        配置好的ZhiLogger实例
    """
    return get_logger(name=f"{ZHI_LOGGER_ROOT}.{module_name}", module_name=module_name)



