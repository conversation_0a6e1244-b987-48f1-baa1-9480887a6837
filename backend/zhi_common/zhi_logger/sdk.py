"""
ZhiAdmin 日志SDK
为各微服务提供统一的日志记录接口
"""
from typing import Dict, Any, Optional
from django.utils import timezone
from django.db import transaction

from zhi_logger.models import SystemLog, AuditLog, OAuthAuditLog, ApiAccessLog


class LoggerSDK:
    """
    日志SDK - 统一的日志记录接口
    """
    
    @staticmethod
    def log_system(
        level: str,
        message: str,
        category: str = 'system',
        module_name: str = '',
        trace_id: str = '',
        user_id: str = '',
        ip_address: str = '',
        user_agent: str = '',
        extra_data: Dict[str, Any] = None,
        exception_info: Dict[str, str] = None
    ) -> SystemLog:
        """
        记录系统日志
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            message: 日志消息
            category: 日志分类
            module_name: 模块名称
            trace_id: 追踪ID
            user_id: 用户ID
            ip_address: IP地址
            user_agent: 用户代理
            extra_data: 额外数据
            exception_info: 异常信息
            
        Returns:
            SystemLog: 创建的日志记录
        """
        log_data = {
            'level': level,
            'category': category,
            'module_name': module_name,
            'message': message,
            'trace_id': trace_id or '',
            'user_id': user_id or '',
            'ip_address': ip_address or None,
            'user_agent': user_agent or '',
            'extra_data': extra_data or {},
        }
        
        # 添加异常信息
        if exception_info:
            log_data.update({
                'exception_type': exception_info.get('type', ''),
                'exception_message': exception_info.get('message', ''),
                'stack_trace': exception_info.get('stack_trace', ''),
            })
        
        return SystemLog.objects.using('logger_db').create(**log_data)
    
    @staticmethod
    def log_audit(
        action_type: str,
        action_name: str,
        user_id: str = '',
        user_name: str = '',
        resource_type: str = '',
        resource_id: str = '',
        old_values: Dict[str, Any] = None,
        new_values: Dict[str, Any] = None,
        description: str = '',
        trace_id: str = '',
        ip_address: str = '',
        user_agent: str = '',
        is_success: bool = True,
        error_message: str = '',
        module: str = '',
        tenant_id: str = ''
    ) -> AuditLog:
        """
        记录审计日志
        
        Args:
            action_type: 操作类型
            action_name: 操作名称
            user_id: 用户ID
            user_name: 用户名
            resource_type: 资源类型
            resource_id: 资源ID
            old_values: 变更前数据
            new_values: 变更后数据
            description: 操作描述
            trace_id: 追踪ID
            ip_address: IP地址
            user_agent: 用户代理
            is_success: 是否成功
            error_message: 错误信息
            module: 业务模块
            tenant_id: 租户ID
            
        Returns:
            AuditLog: 创建的审计日志
        """
        return AuditLog.objects.using('logger_db').create(
            action_type=action_type,
            action_name=action_name,
            description=description,
            user_id=user_id,
            user_name=user_name,
            object_id=resource_id,
            old_values=old_values or {},
            new_values=new_values or {},
            trace_id=trace_id,
            ip_address=ip_address,
            user_agent=user_agent,
            is_success=is_success,
            error_message=error_message,
            module=module,
            tenant_id=tenant_id
        )
    
    @staticmethod
    def log_oauth_audit(
        action: str,
        user_id: str = '',
        user_name: str = '',
        application_id: str = '',
        application_name: str = '',
        resource_type: str = '',
        resource_id: str = '',
        ip_address: str = '',
        user_agent: str = '',
        trace_id: str = '',
        details: Dict[str, Any] = None,
        scopes: list = None,
        is_success: bool = True,
        error_message: str = ''
    ) -> OAuthAuditLog:
        """
        记录OAuth2审计日志
        
        Args:
            action: OAuth操作类型
            user_id: 用户ID
            user_name: 用户名
            application_id: 应用ID
            application_name: 应用名称
            resource_type: 资源类型
            resource_id: 资源ID
            ip_address: IP地址
            user_agent: 用户代理
            trace_id: 追踪ID
            details: 详细信息
            scopes: 权限范围
            is_success: 是否成功
            error_message: 错误信息
            
        Returns:
            OAuthAuditLog: 创建的OAuth审计日志
        """
        return OAuthAuditLog.objects.using('logger_db').create(
            action=action,
            user_id=user_id,
            user_name=user_name,
            application_id=application_id,
            application_name=application_name,
            resource_type=resource_type,
            resource_id=resource_id,
            ip_address=ip_address,
            user_agent=user_agent,
            trace_id=trace_id,
            details=details or {},
            scopes=scopes or [],
            is_success=is_success,
            error_message=error_message
        )
    
    @staticmethod
    def log_api_access(
        method: str,
        path: str,
        response_status: int,
        response_time: float = None,
        query_params: Dict[str, Any] = None,
        request_body: str = '',
        response_body: str = '',
        ip_address: str = '',
        user_agent: str = '',
        trace_id: str = '',
        user_id: str = ''
    ) -> ApiAccessLog:
        """
        记录API访问日志
        
        Args:
            method: HTTP方法
            path: 请求路径
            response_status: 响应状态码
            response_time: 响应时间
            query_params: 查询参数
            request_body: 请求体
            response_body: 响应体
            ip_address: IP地址
            user_agent: 用户代理
            trace_id: 追踪ID
            user_id: 用户ID
            
        Returns:
            ApiAccessLog: 创建的API访问日志
        """
        return ApiAccessLog.objects.using('logger_db').create(
            method=method,
            path=path,
            query_params=query_params or {},
            request_body=request_body,
            response_status=response_status,
            response_body=response_body,
            response_time=response_time,
            ip_address=ip_address,
            user_agent=user_agent,
            trace_id=trace_id,
            user_id=user_id
        )
    
    @staticmethod
    @transaction.atomic(using='logger_db')
    def batch_log_system(logs: list) -> list:
        """
        批量记录系统日志
        
        Args:
            logs: 日志数据列表
            
        Returns:
            list: 创建的日志记录列表
        """
        log_objects = []
        for log_data in logs:
            log_objects.append(SystemLog(**log_data))
        
        return SystemLog.objects.using('logger_db').bulk_create(log_objects)
    
    @staticmethod
    def get_user_audit_logs(user_id: str, limit: int = 100) -> list:
        """
        获取用户的审计日志
        
        Args:
            user_id: 用户ID
            limit: 限制数量
            
        Returns:
            list: 审计日志列表
        """
        return list(
            AuditLog.objects.using('logger_db')
            .filter(user_id=user_id)
            .order_by('-created_at')[:limit]
        )
    
    @staticmethod
    def get_resource_audit_logs(resource_id: str, limit: int = 100) -> list:
        """
        获取资源的审计日志
        
        Args:
            resource_id: 资源ID
            limit: 限制数量
            
        Returns:
            list: 审计日志列表
        """
        return list(
            AuditLog.objects.using('logger_db')
            .filter(object_id=resource_id)
            .order_by('-created_at')[:limit]
        )
    
    @staticmethod
    def get_oauth_audit_logs(
        user_id: str = '',
        application_id: str = '',
        action: str = '',
        limit: int = 100
    ) -> list:
        """
        获取OAuth审计日志
        
        Args:
            user_id: 用户ID
            application_id: 应用ID
            action: 操作类型
            limit: 限制数量
            
        Returns:
            list: OAuth审计日志列表
        """
        queryset = OAuthAuditLog.objects.using('logger_db').all()
        
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        if application_id:
            queryset = queryset.filter(application_id=application_id)
        if action:
            queryset = queryset.filter(action=action)
        
        return list(queryset.order_by('-created_at')[:limit])


# 全局日志SDK实例
logger_sdk = LoggerSDK()


# 便捷函数
def log_info(message: str, module_name: str = '', **kwargs):
    """记录INFO级别日志"""
    return logger_sdk.log_system('INFO', message, module_name=module_name, **kwargs)


def log_error(message: str, module_name: str = '', exception_info: Dict = None, **kwargs):
    """记录ERROR级别日志"""
    return logger_sdk.log_system('ERROR', message, module_name=module_name, exception_info=exception_info, **kwargs)


def log_warning(message: str, module_name: str = '', **kwargs):
    """记录WARNING级别日志"""
    return logger_sdk.log_system('WARNING', message, module_name=module_name, **kwargs)


def log_debug(message: str, module_name: str = '', **kwargs):
    """记录DEBUG级别日志"""
    return logger_sdk.log_system('DEBUG', message, module_name=module_name, **kwargs)


def audit_create(resource_type: str, resource_id: str, new_values: Dict, **kwargs):
    """记录创建操作审计日志"""
    return logger_sdk.log_audit('create', f'创建{resource_type}', resource_id=resource_id, new_values=new_values, **kwargs)


def audit_update(resource_type: str, resource_id: str, old_values: Dict, new_values: Dict, **kwargs):
    """记录更新操作审计日志"""
    return logger_sdk.log_audit('update', f'更新{resource_type}', resource_id=resource_id, old_values=old_values, new_values=new_values, **kwargs)


def audit_delete(resource_type: str, resource_id: str, old_values: Dict, **kwargs):
    """记录删除操作审计日志"""
    return logger_sdk.log_audit('delete', f'删除{resource_type}', resource_id=resource_id, old_values=old_values, **kwargs)
