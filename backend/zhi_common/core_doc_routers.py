from django.http import HttpResponse
from django.template.loader import render_to_string
from django.conf import settings


def get_project_docs_config():
    """获取项目文档配置 - 统一使用OAuth认证"""
    # 统一的OAuth登录接口
    oauth_login_url = '/api/oauth/login'

    return [
        {
            'name': 'OAuth认证系统',
            'description': 'OAuth2认证、用户管理、权限控制 - 核心认证模块',
            'api_url': oauth_login_url,
            'docs_url': '/api/oauth/docs',
            'status': '✅ 已适配BaseResponse (75%)',
            'color': '#1890ff',
            'icon': '🔐',
            'features': ['OAuth2认证', '用户管理', 'JWT令牌', '权限控制', '统一认证'],
            'is_auth_center': True
        },
        {
            'name': 'Logger日志系统',
            'description': '日志记录、审计追踪、统计分析',
            'api_url': oauth_login_url,  # 使用统一OAuth认证
            'docs_url': '/api/logger/docs',
            'status': '🔄 开发中',
            'color': '#52c41a',
            'icon': '📝',
            'features': ['日志记录', '审计追踪', '统计分析', 'WebSocket推送'],
            'auth_note': '使用OAuth统一认证'
        },
        {
            'name': 'Celery任务系统',
            'description': '异步任务、定时调度、任务监控',
            'api_url': oauth_login_url,  # 使用统一OAuth认证
            'docs_url': '/api/celery/docs',
            'status': '✅ ZhiCelery完整架构',
            'color': '#722ed1',
            'icon': '⚡',
            'features': ['异步任务', '定时调度', 'Flower监控', '任务队列'],
            'auth_note': '使用OAuth统一认证'
        },
        {
            'name': '系统管理',
            'description': '系统配置、健康检查、性能监控',
            'api_url': oauth_login_url,  # 使用统一OAuth认证
            'docs_url': '/api/system/docs',
            'status': '🔄 规划中',
            'color': '#fa8c16',
            'icon': '⚙️',
            'features': ['系统配置', '健康检查', '性能监控', '资源管理'],
            'auth_note': '使用OAuth统一认证'
        }
    ]


def render_login_docs(request):
    """多项目文档管理页面"""
    if not settings.DEBUG:  # 生产环境禁用
        from django.http import Http404
        raise Http404("测试页仅在调试模式可用")

    # 获取项目参数，默认显示所有项目
    project = request.GET.get('project', 'all')
    projects_config = get_project_docs_config()

    # 如果指定了特定项目，只显示该项目
    if project != 'all':
        projects_config = [p for p in projects_config if p['name'].lower().replace(' ', '') == project.lower()]
        if not projects_config:
            # 如果找不到指定项目，回退到OAuth项目
            projects_config = [p for p in get_project_docs_config() if 'oauth' in p['name'].lower()]

    return HttpResponse(
        render_to_string(
            'auth/multi_project_docs.html', {
                'projects': projects_config,
                'current_project': project,
                'debug_mode': settings.DEBUG,
                'total_projects': len(get_project_docs_config()),
                'show_all': project == 'all'
            }
        )
    )