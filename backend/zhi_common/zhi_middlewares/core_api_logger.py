"""
优化后的API日志中间件
"""
import json
from typing import Dict

from django.conf import settings
from django.http import HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin

from zhi_logger.models import OperationLog
from zhi_common.zhi_logger.core_logger import zhi_logger
from zhi_common.zhi_tools.req_util import (
    get_os,
    get_browser,
    get_request_ip,
    get_request_path,
    get_request_data,
    get_request_trace_id,
    )
from zhi_common.zhi_tools.g_local_thread_user_info import get_request_user_info


class ApiLoggingMiddleware(MiddlewareMixin):
    """
    优化后的API访问日志中间件
    主要改进：
    1. 更好的性能（减少不必要的处理）
    2. 更完善的错误处理
    3. 更清晰的代码结构
    4. 支持配置跳过特定路径
    """

    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.enable = getattr(settings, 'API_LOG_ENABLE', False)
        self.methods = set(getattr(settings, 'API_LOG_METHODS', []))
        self.skip_paths = set(getattr(settings, 'API_LOG_SKIP_PATHS', []))
        self.API_REQUEST_INFO_LOGGER = getattr(settings, 'API_REQUEST_INFO_LOGGER', False)

    def _should_log(self, request: HttpRequest) -> bool:
        """判断是否应该记录当前请求"""
        if not self.enable:
            return False

        if request.path in self.skip_paths:
            return False

        return 'ALL' in self.methods or request.method in self.methods

    def _prepare_request_info(self, request: HttpRequest) -> None:
        """准备请求信息"""
        try:
            # 添加常用请求属性
            request.request_ip = get_request_ip(request)
            request.request_path = get_request_path(request)
            request.request_data = self._sanitize_data(get_request_data(request))
        except Exception as e:
            zhi_logger.debug(f" 准备请求信息失败: {str(e)}", exc_info=True)

    @staticmethod
    def _sanitize_data(data: Dict) -> Dict:
        """清理敏感数据"""
        if isinstance(data, dict):
            # 敏感字段脱敏
            # 精准匹配敏感字段配置
            fixed_mi_fields = ['password', 'token', 'access_token', 'refresh_token']
            # 模糊匹配敏感字段配置
            like_mi_fields = ['secret']
            for key in data.keys():
                if key in fixed_mi_fields or any(like_field in key for like_field in like_mi_fields):
                    data[key] = '********'
        return data

    @staticmethod
    def _parse_response_data(response):
        """更健壮的响应解析"""
        try:
            if not response.content:
                return response

                # 尝试解析JSON
            try:
                data = json.loads(response.content.decode())
                if isinstance(data, dict):
                    return data
                return {'data': data}
            except json.JSONDecodeError:
                # 非JSON响应处理
                if response['Content-Type'].startswith('text/html'):
                    return {'html_content': '...'}
                return {'raw_content': str(response.content)[:500]}  # 截断长内容

        except Exception as e:
            zhi_logger.warning(f" 响应解析降级处理: {str(e)}")
            return {
                'status_code': response.status_code,
                'error': 'response_parse_failed'
                }

    def _create_operation_log(self, request: HttpRequest, response: HttpResponse) -> None:
        """创建操作日志记录"""
        try:
            user = get_request_user_info(request)

            response_data = self._parse_response_data(response)

            log_data = {
                'response_code': response_data.get('code', response.status_code),
                'status': response.status_code == 200,  # 根据实际成功码调整
                'json_result': response_data.get('data', ''),
                'response_msg': response_data.get('message', ''),
                'request_username': user.get('username'),
                'request_ip': getattr(request, 'request_ip', 'unknown'),
                'creator_id': user.get('id'),
                'org_id': user.get('dept'),
                'request_method': request.method,
                'request_path': getattr(request, 'request_path', request.path),
                'request_body': getattr(request, 'request_data', {}),
                'request_os': get_os(request),
                'request_browser': get_browser(request),
                'trace_id': get_request_trace_id(request),
                }
            OperationLog.objects.create(**log_data)

        except Exception as e:
            zhi_logger.warning(f" 创建操作日志失败: {str(e)}", exc_info=True)

    def process_request(self, request: HttpRequest) -> None:
        """处理请求"""
        if self.API_REQUEST_INFO_LOGGER:
            zhi_logger.info(
                f"Before Out ApiLoggingMiddleware.process_request: request_method: {request.method}, "
                f"request_path: {request.path}"
                )
        if self._should_log(request):
            self._prepare_request_info(request)

    def process_response(self, request: HttpRequest, response: HttpResponse) -> HttpResponse:
        """处理响应"""
        if self._should_log(request):
            self._create_operation_log(request, response)
        if self.API_REQUEST_INFO_LOGGER:
            if request.path == '/api/openapi.json':
                return response
            zhi_logger.info(
                f"After Out ApiLoggingMiddleware.process_response:"
                f"Response: {response}, request_text: {response.text}"
                )
        return response
