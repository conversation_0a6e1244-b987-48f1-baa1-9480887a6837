import json
from threading import local
from django.utils.deprecation import MiddlewareMixin
from zhi_common.core_context import set_trace_id, get_trace_id
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_logger.core_logger import zhi_logger
from zhi_common.zhi_response.base import ZhiResponse


g_thread_locals = local()


class RequestPreprocessorMiddleware(MiddlewareMixin):
    """
    修复后的请求预处理中间件
    1. 正确处理异常并返回JSON响应
    2. 更健壮的JSON验证
    """

    def process_request(self, request):
        """处理请求预处理"""
        self.__handle_trace_logger(request)
        g_thread_locals.request = request
        try:
            if request.method in ('POST', 'PUT', 'PATCH', 'DELETE'):
                pass
                # self._validate_json_request(request)
        except Exception as e:
            # 记录错误日志
            zhi_logger.error(
                f"请求预处理失败: {str(e)}",
                exc_info=True
                )
            # 直接返回JSON响应
            return ZhiResponse(
                data=None,
                message=str(e),
                code=ResponseCode.INTERNAL_ERROR,
                success=False
                )

    @staticmethod
    def __handle_trace_logger(request):
        """
        日志记录
        :param request:
        :return:
        """
        # 生成或获取 trace_id
        trace_id = request.headers.get('X-Trace-ID')
        if not trace_id:
            trace_id = get_trace_id()
        request.trace_id = trace_id
        set_trace_id(trace_id)


    @staticmethod
    def _validate_json_request(request):
        """验证JSON请求"""
        # 读取并解析请求体
        try:
            if not hasattr(request, '_body'):
                request._body = request.body
            zhi_logger.info(f'开始处理请求: {request.path}')
            body = request._body.decode('utf-8') if request._body else '{}'
            zhi_logger.info(f"body: {body}")
            request.json_data = json.loads(body)
        except json.JSONDecodeError as e:
            raise ValueError(f"无效的JSON格式: {str(e)}")
        except UnicodeDecodeError as e:
            raise ValueError(f"请求体编码错误: {str(e)}")
        except Exception as e:
            raise ValueError(f"请求处理异常: {str(e)}")

    @staticmethod
    def process_response(request, response):
        """处理响应"""
        """请求结束后清理线程局部变量"""
        if hasattr(g_thread_locals, 'request'):
            del g_thread_locals.request
        return response
