from ninja_extra import exceptions

from zhi_common.zhi_consts.core_res_code import ResponseCode


class ZhiAPIException(exceptions.APIException):
    def __init__(self, message='error', code=ResponseCode.BAD_REQUEST, success=False, status_code=400):
        self.code = code
        self.success = success
        self.status_code = status_code
        self.message = message
        super().__init__(message)


class ZhiTokenException(ZhiAPIException):
    def __init__(
            self, message='Token Error', code=ResponseCode.UNAUTHORIZED,
            success=False, status_code=401
            ):
        super().__init__(message, code, success, status_code)


class ZhiRequestDataException(ZhiAPIException):
    def __init__(
            self, message='Params Error', code=ResponseCode.BAD_REQUEST, success=False,
            status_code=401
            ):
        super().__init__(message, code, success, status_code)


class ZhiPermissionException(ZhiAPIException):
    def __init__(
            self, message='Permission Denied', code=ResponseCode.PERMISSION_ERROR, success=False,
            status_code=401
            ):
        super().__init__(message, code, success, status_code)
