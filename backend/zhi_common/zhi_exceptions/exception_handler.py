# api/exception_handlers.py
from ninja import NinjaAPI
from ninja.errors import HttpError
from django.http.request import HttpRequest
from .base import ZhiAPIException
from zhi_common.zhi_response.base import ZhiResponse
from zhi_common.zhi_consts.core_res_code import ResponseCode


def register_exception_handlers(api: NinjaAPI):
    """注册全局异常处理器"""

    @api.exception_handler(ZhiAPIException)
    def handle_api_exception(request: HttpRequest, exc: ZhiAPIException):
        """处理业务异常"""
        from zhi_common.base.core_logger import zhi_logger
        zhi_logger.warning(f"[API]handle_api_exception: {exc.message}")
        return ZhiResponse(
            message=exc.message,
            data=None,
            code=exc.code,
            success=False,
            status_code=exc.status_code
            )

    @api.exception_handler(HttpError)
    def handle_http_error(request: HttpRequest, exc: HttpError):
        """处理404未找到路由异常"""
        from utils.base.core_logger import zhi_logger
        zhi_logger.error(f"[Django]handle_http_error: {str(exc)}")
        return ZhiResponse(
            message=exc.message,
            data=None,
            code=ResponseCode.INTERNAL_ERROR,
            success=False,
            status_code=exc.status_code
            )

    @api.exception_handler(Exception)
    def handle_unexpected_exception(request: HttpRequest, exc: Exception):
        """处理未捕获异常"""

        from zhi_common.zhi_logger import zhi_logger
        zhi_logger.warning(request)
        zhi_logger.error(f"[Internal]handle_unexpected_exception]: {str(exc)}", depth=3)

        return ZhiResponse(
            message="服务器内部错误",
            data=None,
            code=ResponseCode.INTERNAL_ERROR,
            success=False,
            status_code=500
            )