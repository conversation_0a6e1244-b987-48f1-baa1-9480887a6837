"""
文件管理服务
基于 django-ninja 和 django-ninja-extra 的文件上传下载功能
"""

import os
import uuid
import hashlib
import mimetypes
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any, Union, BinaryIO
from django.conf import settings
from django.core.files.uploadedfile import UploadedFile
from django.http import HttpResponse, FileResponse, Http404
from django.utils.text import get_valid_filename
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from ninja import Schema
from pydantic import Field

from zhi_common.zhi_logger.core_logger import zhi_logger


class FileUploadSchema(Schema):
    """文件上传响应Schema"""
    file_id: str = Field(description="文件唯一标识")
    filename: str = Field(description="原始文件名")
    file_path: str = Field(description="文件存储路径")
    file_url: str = Field(description="文件访问URL")
    file_size: int = Field(description="文件大小(字节)")
    content_type: str = Field(description="文件MIME类型")
    upload_time: str = Field(description="上传时间")
    file_hash: str = Field(description="文件MD5哈希值")


class FileInfoSchema(Schema):
    """文件信息Schema"""
    file_id: str = Field(description="文件唯一标识")
    filename: str = Field(description="原始文件名")
    file_path: str = Field(description="文件存储路径")
    file_url: str = Field(description="文件访问URL")
    file_size: int = Field(description="文件大小(字节)")
    content_type: str = Field(description="文件MIME类型")
    upload_time: str = Field(description="上传时间")
    file_hash: str = Field(description="文件MD5哈希值")
    exists: bool = Field(description="文件是否存在")


class FileUploadConfig:
    """文件上传配置"""
    
    # 默认配置
    DEFAULT_MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    DEFAULT_ALLOWED_EXTENSIONS = {
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
        'document': ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'],
        'archive': ['.zip', '.rar', '.7z', '.tar', '.gz'],
        'video': ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'],
        'audio': ['.mp3', '.wav', '.flac', '.aac', '.ogg']
    }
    
    def __init__(self, 
                 max_file_size: int = None,
                 allowed_extensions: List[str] = None,
                 allowed_types: List[str] = None,
                 upload_path: str = 'uploads',
                 organize_by_date: bool = True,
                 generate_unique_name: bool = True):
        """
        初始化文件上传配置
        
        Args:
            max_file_size: 最大文件大小(字节)
            allowed_extensions: 允许的文件扩展名列表
            allowed_types: 允许的文件类型列表 ('image', 'document', 'archive', 'video', 'audio')
            upload_path: 上传路径
            organize_by_date: 是否按日期组织文件夹
            generate_unique_name: 是否生成唯一文件名
        """
        self.max_file_size = max_file_size or self.DEFAULT_MAX_FILE_SIZE
        self.upload_path = upload_path
        self.organize_by_date = organize_by_date
        self.generate_unique_name = generate_unique_name
        
        # 处理允许的扩展名
        if allowed_extensions:
            self.allowed_extensions = [ext.lower() for ext in allowed_extensions]
        elif allowed_types:
            self.allowed_extensions = []
            for file_type in allowed_types:
                if file_type in self.DEFAULT_ALLOWED_EXTENSIONS:
                    self.allowed_extensions.extend(self.DEFAULT_ALLOWED_EXTENSIONS[file_type])
        else:
            # 默认允许所有类型
            self.allowed_extensions = []
            for extensions in self.DEFAULT_ALLOWED_EXTENSIONS.values():
                self.allowed_extensions.extend(extensions)


class FileManager:
    """文件管理器"""
    
    def __init__(self, config: FileUploadConfig = None):
        """
        初始化文件管理器
        
        Args:
            config: 文件上传配置
        """
        self.config = config or FileUploadConfig()
        self.media_root = Path(settings.MEDIA_ROOT)
        self.media_url = settings.MEDIA_URL
        
        # 确保上传目录存在
        upload_dir = self.media_root / self.config.upload_path
        upload_dir.mkdir(parents=True, exist_ok=True)
    
    def _calculate_file_hash(self, file_content: bytes) -> str:
        """计算文件MD5哈希值"""
        return hashlib.md5(file_content).hexdigest()
    
    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名"""
        return Path(filename).suffix.lower()
    
    def _validate_file(self, file: UploadedFile) -> Dict[str, Any]:
        """
        验证上传文件
        
        Args:
            file: 上传的文件
            
        Returns:
            验证结果字典
            
        Raises:
            ValueError: 文件验证失败
        """
        # 检查文件大小
        if file.size > self.config.max_file_size:
            raise ValueError(f"文件大小超过限制 ({self.config.max_file_size / 1024 / 1024:.1f}MB)")
        
        # 检查文件扩展名
        file_ext = self._get_file_extension(file.name)
        if self.config.allowed_extensions and file_ext not in self.config.allowed_extensions:
            raise ValueError(f"不支持的文件类型: {file_ext}")
        
        # 获取MIME类型
        content_type = file.content_type or mimetypes.guess_type(file.name)[0] or 'application/octet-stream'
        
        return {
            'filename': file.name,
            'size': file.size,
            'content_type': content_type,
            'extension': file_ext
        }
    
    def _generate_file_path(self, filename: str, file_id: str = None) -> str:
        """
        生成文件存储路径
        
        Args:
            filename: 原始文件名
            file_id: 文件ID
            
        Returns:
            文件存储路径
        """
        # 基础路径
        path_parts = [self.config.upload_path]
        
        # 按日期组织
        if self.config.organize_by_date:
            now = datetime.now()
            path_parts.extend([str(now.year), f"{now.month:02d}", f"{now.day:02d}"])
        
        # 生成文件名
        if self.config.generate_unique_name:
            file_id = file_id or str(uuid.uuid4())
            file_ext = self._get_file_extension(filename)
            new_filename = f"{file_id}{file_ext}"
        else:
            new_filename = get_valid_filename(filename)
        
        path_parts.append(new_filename)
        return '/'.join(path_parts)
    
    def upload_file(self, file: UploadedFile, custom_path: str = None, 
                   file_id: str = None) -> FileUploadSchema:
        """
        上传文件
        
        Args:
            file: 上传的文件
            custom_path: 自定义存储路径
            file_id: 自定义文件ID
            
        Returns:
            文件上传结果
            
        Raises:
            ValueError: 文件验证失败
            Exception: 文件保存失败
        """
        try:
            # 验证文件
            file_info = self._validate_file(file)
            
            # 读取文件内容
            file_content = file.read()
            file_hash = self._calculate_file_hash(file_content)
            
            # 生成文件ID和路径
            file_id = file_id or str(uuid.uuid4())
            file_path = custom_path or self._generate_file_path(file_info['filename'], file_id)
            
            # 确保目录存在
            full_path = self.media_root / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 保存文件
            saved_path = default_storage.save(file_path, ContentFile(file_content))
            
            # 生成文件URL
            file_url = f"{self.media_url}{saved_path}"
            
            zhi_logger.info(f"文件上传成功: {file_info['filename']} -> {saved_path}")
            
            return FileUploadSchema(
                file_id=file_id,
                filename=file_info['filename'],
                file_path=saved_path,
                file_url=file_url,
                file_size=file_info['size'],
                content_type=file_info['content_type'],
                upload_time=datetime.now().isoformat(),
                file_hash=file_hash
            )
            
        except Exception as e:
            zhi_logger.error(f"文件上传失败: {str(e)}")
            raise
    
    def upload_multiple_files(self, files: List[UploadedFile], 
                            custom_paths: List[str] = None) -> List[FileUploadSchema]:
        """
        批量上传文件
        
        Args:
            files: 上传的文件列表
            custom_paths: 自定义存储路径列表
            
        Returns:
            文件上传结果列表
        """
        results = []
        custom_paths = custom_paths or [None] * len(files)
        
        for i, file in enumerate(files):
            try:
                result = self.upload_file(file, custom_paths[i])
                results.append(result)
            except Exception as e:
                zhi_logger.error(f"批量上传文件失败 [{i}]: {str(e)}")
                # 可以选择继续上传其他文件或者抛出异常
                raise
        
        return results

    def get_file_info(self, file_path: str) -> FileInfoSchema:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            文件信息
        """
        try:
            full_path = self.media_root / file_path
            exists = default_storage.exists(file_path)

            if exists:
                file_size = default_storage.size(file_path)
                # 尝试从路径中提取文件ID和原始文件名
                filename = Path(file_path).name
                file_id = Path(filename).stem

                # 获取MIME类型
                content_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'

                # 获取修改时间
                try:
                    modified_time = default_storage.get_modified_time(file_path)
                    upload_time = modified_time.isoformat()
                except:
                    upload_time = datetime.now().isoformat()

                # 计算文件哈希
                try:
                    with default_storage.open(file_path, 'rb') as f:
                        file_hash = self._calculate_file_hash(f.read())
                except:
                    file_hash = ""
            else:
                file_size = 0
                filename = Path(file_path).name
                file_id = Path(filename).stem
                content_type = 'application/octet-stream'
                upload_time = datetime.now().isoformat()
                file_hash = ""

            file_url = f"{self.media_url}{file_path}"

            return FileInfoSchema(
                file_id=file_id,
                filename=filename,
                file_path=file_path,
                file_url=file_url,
                file_size=file_size,
                content_type=content_type,
                upload_time=upload_time,
                file_hash=file_hash,
                exists=exists
            )

        except Exception as e:
            zhi_logger.error(f"获取文件信息失败: {str(e)}")
            raise

    def download_file(self, file_path: str, as_attachment: bool = True,
                     custom_filename: str = None) -> FileResponse:
        """
        下载文件

        Args:
            file_path: 文件路径
            as_attachment: 是否作为附件下载
            custom_filename: 自定义下载文件名

        Returns:
            文件响应

        Raises:
            Http404: 文件不存在
        """
        try:
            if not default_storage.exists(file_path):
                raise Http404("文件不存在")

            # 获取文件信息
            file_info = self.get_file_info(file_path)

            # 打开文件
            file_obj = default_storage.open(file_path, 'rb')

            # 创建响应
            response = FileResponse(
                file_obj,
                content_type=file_info.content_type,
                as_attachment=as_attachment
            )

            # 设置文件名
            filename = custom_filename or file_info.filename
            if as_attachment:
                response['Content-Disposition'] = f'attachment; filename="{filename}"'

            zhi_logger.info(f"文件下载: {file_path}")
            return response

        except Http404:
            raise
        except Exception as e:
            zhi_logger.error(f"文件下载失败: {str(e)}")
            raise

    def delete_file(self, file_path: str) -> bool:
        """
        删除文件

        Args:
            file_path: 文件路径

        Returns:
            是否删除成功
        """
        try:
            if default_storage.exists(file_path):
                default_storage.delete(file_path)
                zhi_logger.info(f"文件删除成功: {file_path}")
                return True
            else:
                zhi_logger.warning(f"文件不存在，无法删除: {file_path}")
                return False

        except Exception as e:
            zhi_logger.error(f"文件删除失败: {str(e)}")
            return False

    def list_files(self, directory: str = None, recursive: bool = False) -> List[FileInfoSchema]:
        """
        列出文件

        Args:
            directory: 目录路径，None表示根目录
            recursive: 是否递归列出子目录

        Returns:
            文件信息列表
        """
        try:
            base_path = directory or self.config.upload_path

            if recursive:
                # 递归列出所有文件
                dirs, files = default_storage.listdir(base_path)
                file_list = []

                # 添加当前目录的文件
                for filename in files:
                    file_path = f"{base_path}/{filename}"
                    try:
                        file_info = self.get_file_info(file_path)
                        file_list.append(file_info)
                    except:
                        continue

                # 递归处理子目录
                for dirname in dirs:
                    subdir_path = f"{base_path}/{dirname}"
                    try:
                        subdir_files = self.list_files(subdir_path, recursive=True)
                        file_list.extend(subdir_files)
                    except:
                        continue

                return file_list
            else:
                # 只列出当前目录的文件
                try:
                    dirs, files = default_storage.listdir(base_path)
                    file_list = []

                    for filename in files:
                        file_path = f"{base_path}/{filename}"
                        try:
                            file_info = self.get_file_info(file_path)
                            file_list.append(file_info)
                        except:
                            continue

                    return file_list
                except:
                    return []

        except Exception as e:
            zhi_logger.error(f"列出文件失败: {str(e)}")
            return []

    def get_file_content(self, file_path: str) -> bytes:
        """
        获取文件内容

        Args:
            file_path: 文件路径

        Returns:
            文件内容

        Raises:
            Http404: 文件不存在
        """
        try:
            if not default_storage.exists(file_path):
                raise Http404("文件不存在")

            with default_storage.open(file_path, 'rb') as f:
                return f.read()

        except Http404:
            raise
        except Exception as e:
            zhi_logger.error(f"读取文件内容失败: {str(e)}")
            raise
