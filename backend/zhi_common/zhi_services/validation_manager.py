"""
数据验证和转换管理器
"""

import re
from typing import Any, Dict, List, Optional, Callable, Union, Type
from datetime import datetime, date
from decimal import Decimal
from functools import wraps

from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from ninja import Schema
from pydantic import BaseModel, validator

from utils.base.core_logger import zhi_logger


class ValidationRule:
    """验证规则基类"""
    
    def __init__(self, message: str = None):
        self.message = message or "验证失败"
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        """验证值是否符合规则"""
        raise NotImplementedError
    
    def get_error_message(self, field_name: str = None) -> str:
        """获取错误消息"""
        return self.message.format(field=field_name) if field_name else self.message


class RequiredRule(ValidationRule):
    """必填验证规则"""
    
    def __init__(self, message: str = "{field}不能为空"):
        super().__init__(message)
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        return value is not None and value != ""


class LengthRule(ValidationRule):
    """长度验证规则"""
    
    def __init__(self, min_length: int = None, max_length: int = None, 
                 message: str = "{field}长度必须在{min}-{max}之间"):
        self.min_length = min_length
        self.max_length = max_length
        super().__init__(message)
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        if value is None:
            return True
        
        length = len(str(value))
        
        if self.min_length is not None and length < self.min_length:
            return False
        
        if self.max_length is not None and length > self.max_length:
            return False
        
        return True
    
    def get_error_message(self, field_name: str = None) -> str:
        return self.message.format(
            field=field_name or "字段",
            min=self.min_length or 0,
            max=self.max_length or "无限制"
        )


class RegexRule(ValidationRule):
    """正则表达式验证规则"""
    
    def __init__(self, pattern: str, message: str = "{field}格式不正确"):
        self.pattern = re.compile(pattern)
        super().__init__(message)
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        if value is None:
            return True
        return bool(self.pattern.match(str(value)))


class EmailRule(ValidationRule):
    """邮箱验证规则"""
    
    def __init__(self, message: str = "{field}邮箱格式不正确"):
        super().__init__(message)
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        if value is None:
            return True
        try:
            validate_email(str(value))
            return True
        except ValidationError:
            return False


class RangeRule(ValidationRule):
    """数值范围验证规则"""
    
    def __init__(self, min_value: Union[int, float] = None, 
                 max_value: Union[int, float] = None,
                 message: str = "{field}必须在{min}-{max}范围内"):
        self.min_value = min_value
        self.max_value = max_value
        super().__init__(message)
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        if value is None:
            return True
        
        try:
            num_value = float(value)
            
            if self.min_value is not None and num_value < self.min_value:
                return False
            
            if self.max_value is not None and num_value > self.max_value:
                return False
            
            return True
        except (ValueError, TypeError):
            return False
    
    def get_error_message(self, field_name: str = None) -> str:
        return self.message.format(
            field=field_name or "字段",
            min=self.min_value or "无限制",
            max=self.max_value or "无限制"
        )


class CustomRule(ValidationRule):
    """自定义验证规则"""
    
    def __init__(self, validator_func: Callable[[Any], bool], 
                 message: str = "{field}验证失败"):
        self.validator_func = validator_func
        super().__init__(message)
    
    def validate(self, value: Any, field_name: str = None) -> bool:
        if value is None:
            return True
        return self.validator_func(value)


class DataConverter:
    """数据转换器"""
    
    @staticmethod
    def to_int(value: Any, default: int = None) -> Optional[int]:
        """转换为整数"""
        if value is None:
            return default
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def to_float(value: Any, default: float = None) -> Optional[float]:
        """转换为浮点数"""
        if value is None:
            return default
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def to_decimal(value: Any, default: Decimal = None) -> Optional[Decimal]:
        """转换为Decimal"""
        if value is None:
            return default
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def to_bool(value: Any, default: bool = None) -> Optional[bool]:
        """转换为布尔值"""
        if value is None:
            return default
        
        if isinstance(value, bool):
            return value
        
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        
        try:
            return bool(int(value))
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def to_datetime(value: Any, format_str: str = None, 
                   default: datetime = None) -> Optional[datetime]:
        """转换为datetime"""
        if value is None:
            return default
        
        if isinstance(value, datetime):
            return value
        
        if isinstance(value, str):
            try:
                if format_str:
                    return datetime.strptime(value, format_str)
                else:
                    # 尝试常见格式
                    formats = [
                        '%Y-%m-%d %H:%M:%S',
                        '%Y-%m-%d',
                        '%Y/%m/%d %H:%M:%S',
                        '%Y/%m/%d',
                        '%d/%m/%Y %H:%M:%S',
                        '%d/%m/%Y'
                    ]
                    for fmt in formats:
                        try:
                            return datetime.strptime(value, fmt)
                        except ValueError:
                            continue
            except ValueError:
                pass
        
        return default
    
    @staticmethod
    def to_list(value: Any, separator: str = ',', 
               default: List = None) -> Optional[List]:
        """转换为列表"""
        if value is None:
            return default or []
        
        if isinstance(value, list):
            return value
        
        if isinstance(value, str):
            return [item.strip() for item in value.split(separator) if item.strip()]
        
        return default or []


class ValidationManager:
    """验证管理器"""
    
    def __init__(self):
        self.field_rules: Dict[str, List[ValidationRule]] = {}
        self.global_rules: List[ValidationRule] = []
        self.converters: Dict[str, Callable] = {}
    
    def add_field_rule(self, field_name: str, rule: ValidationRule):
        """为字段添加验证规则"""
        if field_name not in self.field_rules:
            self.field_rules[field_name] = []
        self.field_rules[field_name].append(rule)
    
    def add_global_rule(self, rule: ValidationRule):
        """添加全局验证规则"""
        self.global_rules.append(rule)
    
    def add_converter(self, field_name: str, converter: Callable):
        """为字段添加转换器"""
        self.converters[field_name] = converter
    
    def validate_data(self, data: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证数据，返回错误信息"""
        errors = {}
        
        # 应用转换器
        converted_data = self._apply_converters(data)
        
        # 字段级验证
        for field_name, rules in self.field_rules.items():
            field_errors = []
            value = converted_data.get(field_name)
            
            for rule in rules:
                if not rule.validate(value, field_name):
                    field_errors.append(rule.get_error_message(field_name))
            
            if field_errors:
                errors[field_name] = field_errors
        
        # 全局验证
        global_errors = []
        for rule in self.global_rules:
            if not rule.validate(converted_data):
                global_errors.append(rule.get_error_message())
        
        if global_errors:
            errors['__global__'] = global_errors
        
        return errors
    
    def _apply_converters(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """应用数据转换器"""
        converted_data = data.copy()
        
        for field_name, converter in self.converters.items():
            if field_name in converted_data:
                try:
                    converted_data[field_name] = converter(converted_data[field_name])
                except Exception as e:
                    zhi_logger.warning(f"字段转换失败: {field_name}, {e}")
        
        return converted_data
    
    def create_validator_decorator(self):
        """创建验证装饰器"""
        def validator_decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(self, request, *args, **kwargs):
                # 获取请求数据
                if hasattr(request, 'json') and request.json:
                    data = request.json
                elif hasattr(request, 'data') and request.data:
                    data = request.data
                else:
                    data = {}
                
                # 验证数据
                errors = self.validate_data(data)
                
                if errors:
                    zhi_logger.warning(f"数据验证失败: {errors}")
                    raise ValidationError(errors)
                
                return func(self, request, *args, **kwargs)
            return wrapper
        return validator_decorator


# 预定义的验证规则
COMMON_RULES = {
    'required': RequiredRule(),
    'email': EmailRule(),
    'phone': RegexRule(r'^1[3-9]\d{9}$', '{field}手机号格式不正确'),
    'id_card': RegexRule(r'^\d{17}[\dXx]$', '{field}身份证号格式不正确'),
    'positive_int': CustomRule(lambda x: isinstance(x, int) and x > 0, '{field}必须是正整数'),
    'non_negative_int': CustomRule(lambda x: isinstance(x, int) and x >= 0, '{field}必须是非负整数'),
}


def create_validation_manager(config: Dict[str, Any]) -> ValidationManager:
    """根据配置创建验证管理器"""
    manager = ValidationManager()
    
    # 添加字段规则
    field_rules = config.get('field_rules', {})
    for field_name, rules in field_rules.items():
        for rule_config in rules:
            rule_type = rule_config.get('type')
            rule_params = rule_config.get('params', {})
            
            if rule_type in COMMON_RULES:
                manager.add_field_rule(field_name, COMMON_RULES[rule_type])
            elif rule_type == 'length':
                rule = LengthRule(**rule_params)
                manager.add_field_rule(field_name, rule)
            elif rule_type == 'range':
                rule = RangeRule(**rule_params)
                manager.add_field_rule(field_name, rule)
            elif rule_type == 'regex':
                rule = RegexRule(**rule_params)
                manager.add_field_rule(field_name, rule)
    
    # 添加转换器
    converters = config.get('converters', {})
    for field_name, converter_type in converters.items():
        if hasattr(DataConverter, f'to_{converter_type}'):
            converter = getattr(DataConverter, f'to_{converter_type}')
            manager.add_converter(field_name, converter)
    
    return manager
