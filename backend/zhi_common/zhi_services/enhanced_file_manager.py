"""
增强的文件管理服务
集成数据库记录功能，提供完整的文件生命周期管理
"""

import os
import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
from django.core.files.uploadedfile import UploadedFile
from django.http import FileResponse, Http404
from django.utils import timezone
from django.db import transaction

from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig, FileUploadSchema, FileInfoSchema
from zhi_common.zhi_logger.core_logger import zhi_logger
from zhi_common.zhi_tools.req_util import get_client_ip, get_user_agent

# 延迟导入模型以避免循环导入
try:
    from zhi_common.zhi_model.file_model import FileStorage, FileAccessLog
except ImportError:
    # 如果模型不存在，创建占位符
    FileStorage = None
    FileAccessLog = None


class EnhancedFileUploadSchema(FileUploadSchema):
    """增强的文件上传响应Schema"""
    id: int = None  # 数据库记录ID
    tags: List[str] = []
    description: str = ""
    expires_at: Optional[str] = None


class EnhancedFileInfoSchema(FileInfoSchema):
    """增强的文件信息Schema"""
    id: int = None
    tags: List[str] = []
    description: str = ""
    download_count: int = 0
    last_download_at: Optional[str] = None
    expires_at: Optional[str] = None
    status: str = "active"
    creator_name: str = ""


class EnhancedFileManager:
    """增强的文件管理器"""
    
    def __init__(self, config: FileUploadConfig = None):
        """
        初始化增强文件管理器

        Args:
            config: 文件上传配置
        """
        self.config = config or FileUploadConfig()
        self.file_manager = FileManager(self.config)

        # 检查模型是否可用
        if FileStorage is None or FileAccessLog is None:
            zhi_logger.warning("文件存储模型不可用，增强功能将被禁用")
    
    def _determine_file_type(self, filename: str, content_type: str) -> str:
        """
        确定文件类型
        
        Args:
            filename: 文件名
            content_type: MIME类型
            
        Returns:
            文件类型
        """
        extension = os.path.splitext(filename)[1].lower()
        
        # 图片类型
        if extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'] or content_type.startswith('image/'):
            return 'image'
        
        # 文档类型
        elif extension in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']:
            return 'document'
        
        # 压缩包类型
        elif extension in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'archive'
        
        # 视频类型
        elif extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'] or content_type.startswith('video/'):
            return 'video'
        
        # 音频类型
        elif extension in ['.mp3', '.wav', '.flac', '.aac', '.ogg'] or content_type.startswith('audio/'):
            return 'audio'
        
        else:
            return 'other'
    
    def _log_file_access(self, file_record, action: str, request=None, extra_data: Dict = None):
        """
        记录文件访问日志

        Args:
            file_record: 文件记录
            action: 操作类型
            request: HTTP请求对象
            extra_data: 额外数据
        """
        try:
            if FileAccessLog is None:
                zhi_logger.debug("FileAccessLog模型不可用，跳过日志记录")
                return

            log_data = {
                'file': file_record,
                'action': action,
                'extra_data': extra_data or {}
            }

            if request:
                log_data.update({
                    'ip_address': get_client_ip(request),
                    'user_agent': get_user_agent(request),
                    'referer': request.META.get('HTTP_REFERER')
                })

            FileAccessLog.objects.create(**log_data)

        except Exception as e:
            zhi_logger.error(f"记录文件访问日志失败: {str(e)}")
    
    @transaction.atomic
    def upload_file(self, file: UploadedFile, 
                   custom_path: str = None,
                   file_id: str = None,
                   tags: List[str] = None,
                   description: str = "",
                   expires_in_days: int = None,
                   request=None) -> EnhancedFileUploadSchema:
        """
        上传文件并记录到数据库
        
        Args:
            file: 上传的文件
            custom_path: 自定义存储路径
            file_id: 自定义文件ID
            tags: 文件标签
            description: 文件描述
            expires_in_days: 过期天数
            request: HTTP请求对象
            
        Returns:
            增强的文件上传结果
        """
        try:
            # 使用基础文件管理器上传文件
            upload_result = self.file_manager.upload_file(file, custom_path, file_id)
            
            # 确定文件类型
            file_type = self._determine_file_type(upload_result.filename, upload_result.content_type)
            
            # 计算过期时间
            expires_at = None
            if expires_in_days:
                expires_at = timezone.now() + timedelta(days=expires_in_days)
            
            # 创建数据库记录
            file_record = FileStorage.objects.create(
                file_id=upload_result.file_id,
                original_name=upload_result.filename,
                file_name=os.path.basename(upload_result.file_path),
                file_path=upload_result.file_path,
                file_url=upload_result.file_url,
                file_size=upload_result.file_size,
                file_type=file_type,
                content_type=upload_result.content_type,
                file_extension=os.path.splitext(upload_result.filename)[1].lower(),
                file_hash=upload_result.file_hash,
                upload_ip=get_client_ip(request) if request else None,
                user_agent=get_user_agent(request) if request else None,
                tags=tags or [],
                description=description,
                expires_at=expires_at
            )
            
            # 记录访问日志
            self._log_file_access(file_record, 'upload', request)
            
            # 构建响应
            result = EnhancedFileUploadSchema(
                **upload_result.dict(),
                id=file_record.id,
                tags=file_record.tags,
                description=file_record.description,
                expires_at=expires_at.isoformat() if expires_at else None
            )
            
            zhi_logger.info(f"增强文件上传成功: {upload_result.filename} (ID: {file_record.id})")
            return result
            
        except Exception as e:
            zhi_logger.error(f"增强文件上传失败: {str(e)}")
            raise
    
    def upload_multiple_files(self, files: List[UploadedFile],
                            custom_paths: List[str] = None,
                            tags: List[str] = None,
                            description: str = "",
                            expires_in_days: int = None,
                            request=None) -> List[EnhancedFileUploadSchema]:
        """
        批量上传文件
        
        Args:
            files: 上传的文件列表
            custom_paths: 自定义存储路径列表
            tags: 文件标签
            description: 文件描述
            expires_in_days: 过期天数
            request: HTTP请求对象
            
        Returns:
            增强的文件上传结果列表
        """
        results = []
        custom_paths = custom_paths or [None] * len(files)
        
        for i, file in enumerate(files):
            try:
                result = self.upload_file(
                    file=file,
                    custom_path=custom_paths[i],
                    tags=tags,
                    description=description,
                    expires_in_days=expires_in_days,
                    request=request
                )
                results.append(result)
            except Exception as e:
                zhi_logger.error(f"批量上传文件失败 [{i}]: {str(e)}")
                raise
        
        return results
    
    def get_file_info(self, file_id: str = None, file_path: str = None) -> EnhancedFileInfoSchema:
        """
        获取增强的文件信息
        
        Args:
            file_id: 文件ID
            file_path: 文件路径
            
        Returns:
            增强的文件信息
        """
        try:
            # 从数据库获取文件记录
            if file_id:
                file_record = FileStorage.objects.get(file_id=file_id, status='active')
            elif file_path:
                file_record = FileStorage.objects.get(file_path=file_path, status='active')
            else:
                raise ValueError("必须提供 file_id 或 file_path")
            
            # 获取基础文件信息
            base_info = self.file_manager.get_file_info(file_record.file_path)
            
            # 构建增强信息
            result = EnhancedFileInfoSchema(
                **base_info.dict(),
                id=file_record.id,
                tags=file_record.tags,
                description=file_record.description,
                download_count=file_record.download_count,
                last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None,
                expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                status=file_record.status,
                creator_name=getattr(file_record.creator, 'username', '') if file_record.creator else ''
            )
            
            return result
            
        except FileStorage.DoesNotExist:
            raise Http404("文件记录不存在")
        except Exception as e:
            zhi_logger.error(f"获取增强文件信息失败: {str(e)}")
            raise
    
    def download_file(self, file_id: str = None, file_path: str = None,
                     as_attachment: bool = True, custom_filename: str = None,
                     request=None) -> FileResponse:
        """
        下载文件并记录访问日志
        
        Args:
            file_id: 文件ID
            file_path: 文件路径
            as_attachment: 是否作为附件下载
            custom_filename: 自定义下载文件名
            request: HTTP请求对象
            
        Returns:
            文件响应
        """
        try:
            # 获取文件记录
            if file_id:
                file_record = FileStorage.objects.get(file_id=file_id, status='active')
            elif file_path:
                file_record = FileStorage.objects.get(file_path=file_path, status='active')
            else:
                raise ValueError("必须提供 file_id 或 file_path")
            
            # 检查文件是否过期
            if file_record.expires_at and timezone.now() > file_record.expires_at:
                file_record.status = 'expired'
                file_record.save()
                raise Http404("文件已过期")
            
            # 下载文件
            response = self.file_manager.download_file(
                file_record.file_path, 
                as_attachment, 
                custom_filename or file_record.original_name
            )
            
            # 更新下载统计
            file_record.increment_download_count()
            
            # 记录访问日志
            self._log_file_access(file_record, 'download', request)
            
            return response
            
        except FileStorage.DoesNotExist:
            raise Http404("文件记录不存在")
        except Exception as e:
            zhi_logger.error(f"增强文件下载失败: {str(e)}")
            raise

    @transaction.atomic
    def delete_file(self, file_id: str = None, file_path: str = None,
                   physical_delete: bool = False, request=None) -> bool:
        """
        删除文件

        Args:
            file_id: 文件ID
            file_path: 文件路径
            physical_delete: 是否物理删除文件
            request: HTTP请求对象

        Returns:
            是否删除成功
        """
        try:
            # 获取文件记录
            if file_id:
                file_record = FileStorage.objects.get(file_id=file_id)
            elif file_path:
                file_record = FileStorage.objects.get(file_path=file_path)
            else:
                raise ValueError("必须提供 file_id 或 file_path")

            # 记录访问日志
            self._log_file_access(file_record, 'delete', request)

            if physical_delete:
                # 物理删除文件
                success = self.file_manager.delete_file(file_record.file_path)
                if success:
                    file_record.delete()  # 删除数据库记录
            else:
                # 逻辑删除
                file_record.mark_as_deleted()
                success = True

            zhi_logger.info(f"文件删除成功: {file_record.original_name} (物理删除: {physical_delete})")
            return success

        except FileStorage.DoesNotExist:
            zhi_logger.warning("要删除的文件记录不存在")
            return False
        except Exception as e:
            zhi_logger.error(f"增强文件删除失败: {str(e)}")
            return False
