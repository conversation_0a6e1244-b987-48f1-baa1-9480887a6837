"""
API缓存管理器 - 支持多种缓存策略
"""

import json
import hashlib
from typing import Any, Dict, Optional, Union, Callable
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from functools import wraps

from django.core.cache import cache
from django.conf import settings
from utils.base.core_logger import zhi_logger

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class CacheBackend(ABC):
    """缓存后端抽象基类"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """设置缓存"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        pass


class DjangoCacheBackend(CacheBackend):
    """Django缓存后端"""
    
    def get(self, key: str) -> Optional[Any]:
        try:
            return cache.get(key)
        except Exception as e:
            zhi_logger.warning(f"Django缓存获取失败: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        try:
            cache.set(key, value, ttl)
            return True
        except Exception as e:
            zhi_logger.warning(f"Django缓存设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        try:
            cache.delete(key)
            return True
        except Exception as e:
            zhi_logger.warning(f"Django缓存删除失败: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        # Django缓存不直接支持模式清除，需要自定义实现
        try:
            if hasattr(cache, 'delete_pattern'):
                return cache.delete_pattern(pattern)
            else:
                zhi_logger.warning("Django缓存不支持模式删除")
                return 0
        except Exception as e:
            zhi_logger.warning(f"Django缓存模式清除失败: {e}")
            return 0


class RedisCacheBackend(CacheBackend):
    """Redis缓存后端"""
    
    def __init__(self, redis_url: str = None):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis not available")
        
        self.redis_url = redis_url or getattr(settings, 'REDIS_URL', 'redis://localhost:6379/0')
        self.client = redis.from_url(self.redis_url)
    
    def get(self, key: str) -> Optional[Any]:
        try:
            value = self.client.get(key)
            if value:
                return json.loads(value.decode('utf-8'))
            return None
        except Exception as e:
            zhi_logger.warning(f"Redis缓存获取失败: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        try:
            serialized_value = json.dumps(value, default=str)
            self.client.setex(key, ttl, serialized_value)
            return True
        except Exception as e:
            zhi_logger.warning(f"Redis缓存设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        try:
            self.client.delete(key)
            return True
        except Exception as e:
            zhi_logger.warning(f"Redis缓存删除失败: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        try:
            keys = self.client.keys(pattern)
            if keys:
                return self.client.delete(*keys)
            return 0
        except Exception as e:
            zhi_logger.warning(f"Redis缓存模式清除失败: {e}")
            return 0


class MemoryCacheBackend(CacheBackend):
    """内存缓存后端"""
    
    def __init__(self):
        self._cache: Dict[str, Dict[str, Any]] = {}
    
    def get(self, key: str) -> Optional[Any]:
        try:
            if key in self._cache:
                item = self._cache[key]
                if datetime.now() < item['expires']:
                    return item['value']
                else:
                    del self._cache[key]
            return None
        except Exception as e:
            zhi_logger.warning(f"内存缓存获取失败: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        try:
            expires = datetime.now() + timedelta(seconds=ttl)
            self._cache[key] = {
                'value': value,
                'expires': expires
            }
            return True
        except Exception as e:
            zhi_logger.warning(f"内存缓存设置失败: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        try:
            if key in self._cache:
                del self._cache[key]
            return True
        except Exception as e:
            zhi_logger.warning(f"内存缓存删除失败: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        try:
            import fnmatch
            keys_to_delete = [key for key in self._cache.keys() if fnmatch.fnmatch(key, pattern)]
            for key in keys_to_delete:
                del self._cache[key]
            return len(keys_to_delete)
        except Exception as e:
            zhi_logger.warning(f"内存缓存模式清除失败: {e}")
            return 0


class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.backends: Dict[str, CacheBackend] = {
            'django': DjangoCacheBackend(),
            'memory': MemoryCacheBackend(),
        }
        
        # 如果Redis可用，添加Redis后端
        if REDIS_AVAILABLE:
            try:
                self.backends['redis'] = RedisCacheBackend()
            except Exception as e:
                zhi_logger.warning(f"Redis缓存后端初始化失败: {e}")
    
    def get_backend(self, backend_name: str) -> Optional[CacheBackend]:
        """获取缓存后端"""
        return self.backends.get(backend_name)
    
    def generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        key_hash = hashlib.md5(key_string.encode()).hexdigest()
        return f"{prefix}:{key_hash}"
    
    def cached_result(
        self,
        cache_key: str,
        backend_name: str = 'django',
        ttl: int = 300,
        force_refresh: bool = False
    ):
        """缓存结果装饰器"""
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            def wrapper(*args, **kwargs):
                backend = self.get_backend(backend_name)
                if not backend:
                    zhi_logger.warning(f"缓存后端不存在: {backend_name}")
                    return func(*args, **kwargs)
                
                # 生成完整的缓存键
                full_cache_key = self.generate_cache_key(cache_key, *args, **kwargs)
                
                # 如果不强制刷新，尝试从缓存获取
                if not force_refresh:
                    cached_result = backend.get(full_cache_key)
                    if cached_result is not None:
                        zhi_logger.debug(f"缓存命中: {full_cache_key}")
                        return cached_result
                
                # 执行原函数
                result = func(*args, **kwargs)
                
                # 将结果存入缓存
                if backend.set(full_cache_key, result, ttl):
                    zhi_logger.debug(f"缓存设置成功: {full_cache_key}")
                
                return result
            return wrapper
        return decorator
    
    def invalidate_cache(self, pattern: str, backend_name: str = 'django') -> int:
        """使缓存失效"""
        backend = self.get_backend(backend_name)
        if backend:
            return backend.clear_pattern(pattern)
        return 0
    
    def warm_cache(self, cache_configs: Dict[str, Dict]) -> Dict[str, bool]:
        """预热缓存"""
        results = {}
        
        for cache_key, config in cache_configs.items():
            try:
                backend_name = config.get('backend', 'django')
                backend = self.get_backend(backend_name)
                
                if backend and 'data' in config:
                    ttl = config.get('ttl', 300)
                    success = backend.set(cache_key, config['data'], ttl)
                    results[cache_key] = success
                    
                    if success:
                        zhi_logger.info(f"缓存预热成功: {cache_key}")
                    else:
                        zhi_logger.warning(f"缓存预热失败: {cache_key}")
                else:
                    results[cache_key] = False
                    zhi_logger.warning(f"缓存预热配置无效: {cache_key}")
                    
            except Exception as e:
                results[cache_key] = False
                zhi_logger.error(f"缓存预热异常: {cache_key}, {e}")
        
        return results


# 全局缓存管理器实例
cache_manager = CacheManager()


def api_cache(
    cache_key: str = None,
    backend: str = 'django',
    ttl: int = 300,
    vary_on: list = None,
    condition: Callable = None
):
    """
    API缓存装饰器
    
    Args:
        cache_key: 缓存键前缀
        backend: 缓存后端
        ttl: 缓存时间(秒)
        vary_on: 缓存变化依据字段列表
        condition: 缓存条件函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            # 检查缓存条件
            if condition and not condition(request, *args, **kwargs):
                return func(self, request, *args, **kwargs)
            
            # 构建缓存键
            key_prefix = cache_key or f"{func.__module__}.{func.__name__}"
            
            # 添加vary_on字段到缓存键
            vary_data = {}
            if vary_on:
                for field in vary_on:
                    if hasattr(request, field):
                        vary_data[field] = getattr(request, field)
                    elif field in kwargs:
                        vary_data[field] = kwargs[field]
            
            full_cache_key = cache_manager.generate_cache_key(
                key_prefix, 
                *args, 
                **kwargs, 
                **vary_data
            )
            
            # 尝试从缓存获取
            cache_backend = cache_manager.get_backend(backend)
            if cache_backend:
                cached_result = cache_backend.get(full_cache_key)
                if cached_result is not None:
                    zhi_logger.debug(f"API缓存命中: {full_cache_key}")
                    return cached_result
            
            # 执行原函数
            result = func(self, request, *args, **kwargs)
            
            # 存入缓存
            if cache_backend:
                cache_backend.set(full_cache_key, result, ttl)
                zhi_logger.debug(f"API缓存设置: {full_cache_key}")
            
            return result
        return wrapper
    return decorator
