class ResponseCode:
    """增强版响应状态码枚举"""
    SUCCESS = 2000  # 请求成功
    # 客户端错误 (4xxx)
    BAD_REQUEST = 4000  # 请求参数错误
    UNAUTHORIZED = 4001  # 未认证
    PAYMENT_REQUIRED = 4002  # 需要支付
    FORBIDDEN = 4003  # 权限不足
    NOT_FOUND = 4004  # 资源不存在
    METHOD_NOT_ALLOWED = 4005  # 方法不允许
    CONFLICT = 4009  # 资源冲突
    UNPROCESSABLE_ENTITY = 4022  #  请求数据格式错误
    TOO_MANY_REQUESTS = 4029  # 请求过于频繁
    PERMISSION_ERROR = 4030 # 权限不足或错误
    # 服务端错误 (5xxx)
    INTERNAL_ERROR = 5000  # 服务器内部错误
    NOT_IMPLEMENTED = 5001  # 功能未实现
    SERVICE_UNAVAILABLE = 5003  # 服务不可用
    GATEWAY_TIMEOUT = 5004  # 网关超时
    INTERNAL_API_LOG_REQUEST = 5005  # 服务器内部请求日志记录错误
