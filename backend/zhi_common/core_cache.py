from django.core.cache import cache

setting_key = {
    'oauth2_code': 'oauth2_code:{code}',
    'oauth2_state': 'oauth2_state:{state_type}:{state}'
    }


class CacheManager:
    @staticmethod
    def set_key(key, value, timeout=300):
        cache.set(key, value, timeout)

    @staticmethod
    def get_key(key, only_once=False):
        if only_once:
            data = cache.get(key)
            cache.delete(key)
        else:
            data = cache.get(key)
        return data
