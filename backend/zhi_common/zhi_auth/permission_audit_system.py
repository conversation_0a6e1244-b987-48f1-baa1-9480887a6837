"""
权限审计和监控系统
"""

import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from enum import Enum

from django.contrib.auth import get_user_model
from django.db.models import Count, Q
from django.utils import timezone
from django.core.serializers.json import DjangoJSONEncoder

from zhi_common.zhi_logger import zhi_logger

User = get_user_model()


class AuditEventType(Enum):
    """审计事件类型"""
    PERMISSION_CHECK = "permission_check"
    PERMISSION_GRANTED = "permission_granted"
    PERMISSION_DENIED = "permission_denied"
    ROLE_ASSIGNED = "role_assigned"
    ROLE_REMOVED = "role_removed"
    PERMISSION_ADDED = "permission_added"
    PERMISSION_REMOVED = "permission_removed"
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    LOGOUT = "logout"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"


class RiskLevel(Enum):
    """风险等级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """审计事件"""
    event_type: AuditEventType
    user_id: Optional[int]
    username: Optional[str]
    permission_code: Optional[str]
    resource_type: Optional[str]
    resource_id: Optional[str]
    action: Optional[str]
    result: bool
    risk_level: RiskLevel
    client_ip: Optional[str]
    user_agent: Optional[str]
    session_id: Optional[str]
    timestamp: datetime
    context: Dict[str, Any]
    message: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 转换枚举值
        data['event_type'] = self.event_type.value
        data['risk_level'] = self.risk_level.value
        data['timestamp'] = self.timestamp.isoformat()
        return data


class PermissionAuditLogger:
    """权限审计日志记录器"""
    
    def __init__(self):
        self.logger = zhi_logger
        self.risk_patterns = self._load_risk_patterns()
    
    def log_permission_check(self,
                           user: User,
                           permission_code: str,
                           result: bool,
                           resource_type: str = None,
                           resource_id: str = None,
                           context: Dict[str, Any] = None) -> AuditEvent:
        """记录权限检查事件"""
        
        risk_level = self._assess_risk_level(user, permission_code, result, context)
        
        event = AuditEvent(
            event_type=AuditEventType.PERMISSION_GRANTED if result else AuditEventType.PERMISSION_DENIED,
            user_id=user.id if user else None,
            username=user.username if user else None,
            permission_code=permission_code,
            resource_type=resource_type,
            resource_id=resource_id,
            action="check",
            result=result,
            risk_level=risk_level,
            client_ip=context.get('client_ip') if context else None,
            user_agent=context.get('user_agent') if context else None,
            session_id=context.get('session_id') if context else None,
            timestamp=timezone.now(),
            context=context or {},
            message=f"权限检查: {permission_code} - {'允许' if result else '拒绝'}"
        )
        
        self._write_audit_log(event)
        
        # 如果是高风险事件，触发告警
        if risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
            self._trigger_security_alert(event)
        
        return event
    
    def log_role_change(self,
                       target_user: User,
                       operator_user: User,
                       role_name: str,
                       action: str,  # 'assigned' or 'removed'
                       context: Dict[str, Any] = None) -> AuditEvent:
        """记录角色变更事件"""
        
        event = AuditEvent(
            event_type=AuditEventType.ROLE_ASSIGNED if action == 'assigned' else AuditEventType.ROLE_REMOVED,
            user_id=target_user.id,
            username=target_user.username,
            permission_code=None,
            resource_type="role",
            resource_id=role_name,
            action=action,
            result=True,
            risk_level=RiskLevel.MEDIUM,
            client_ip=context.get('client_ip') if context else None,
            user_agent=context.get('user_agent') if context else None,
            session_id=context.get('session_id') if context else None,
            timestamp=timezone.now(),
            context={
                'operator_user_id': operator_user.id,
                'operator_username': operator_user.username,
                **(context or {})
            },
            message=f"角色{action}: {role_name} -> {target_user.username}"
        )
        
        self._write_audit_log(event)
        return event
    
    def log_login_attempt(self,
                         username: str,
                         success: bool,
                         context: Dict[str, Any] = None) -> AuditEvent:
        """记录登录尝试事件"""
        
        risk_level = self._assess_login_risk(username, success, context)
        
        event = AuditEvent(
            event_type=AuditEventType.LOGIN_SUCCESS if success else AuditEventType.LOGIN_FAILED,
            user_id=None,
            username=username,
            permission_code=None,
            resource_type="authentication",
            resource_id=None,
            action="login",
            result=success,
            risk_level=risk_level,
            client_ip=context.get('client_ip') if context else None,
            user_agent=context.get('user_agent') if context else None,
            session_id=context.get('session_id') if context else None,
            timestamp=timezone.now(),
            context=context or {},
            message=f"登录尝试: {username} - {'成功' if success else '失败'}"
        )
        
        self._write_audit_log(event)
        
        if not success and risk_level == RiskLevel.HIGH:
            self._trigger_security_alert(event)
        
        return event
    
    def log_suspicious_activity(self,
                              user: User,
                              activity_type: str,
                              description: str,
                              context: Dict[str, Any] = None) -> AuditEvent:
        """记录可疑活动"""
        
        event = AuditEvent(
            event_type=AuditEventType.SUSPICIOUS_ACTIVITY,
            user_id=user.id if user else None,
            username=user.username if user else None,
            permission_code=None,
            resource_type="security",
            resource_id=activity_type,
            action="suspicious",
            result=False,
            risk_level=RiskLevel.HIGH,
            client_ip=context.get('client_ip') if context else None,
            user_agent=context.get('user_agent') if context else None,
            session_id=context.get('session_id') if context else None,
            timestamp=timezone.now(),
            context=context or {},
            message=f"可疑活动: {activity_type} - {description}"
        )
        
        self._write_audit_log(event)
        self._trigger_security_alert(event)
        
        return event
    
    def _assess_risk_level(self,
                          user: User,
                          permission_code: str,
                          result: bool,
                          context: Dict[str, Any] = None) -> RiskLevel:
        """评估风险等级"""
        
        # 权限被拒绝的情况
        if not result:
            # 检查是否是敏感权限
            if self._is_sensitive_permission(permission_code):
                return RiskLevel.HIGH
            return RiskLevel.MEDIUM
        
        # 权限被允许的情况
        if self._is_sensitive_permission(permission_code):
            # 检查是否在异常时间或地点
            if self._is_unusual_access(user, context):
                return RiskLevel.MEDIUM
            return RiskLevel.LOW
        
        return RiskLevel.LOW
    
    def _assess_login_risk(self,
                          username: str,
                          success: bool,
                          context: Dict[str, Any] = None) -> RiskLevel:
        """评估登录风险"""
        
        if not success:
            # 检查是否有频繁失败尝试
            if self._has_frequent_failures(username, context):
                return RiskLevel.HIGH
            return RiskLevel.MEDIUM
        
        # 成功登录的风险评估
        if self._is_unusual_login_location(username, context):
            return RiskLevel.MEDIUM
        
        return RiskLevel.LOW
    
    def _is_sensitive_permission(self, permission_code: str) -> bool:
        """检查是否是敏感权限"""
        sensitive_patterns = [
            'admin.*',
            '*.delete',
            '*.modify_permission',
            'system.*',
            'user.create',
            'role.assign'
        ]
        
        import re
        for pattern in sensitive_patterns:
            if re.match(pattern.replace('*', '.*'), permission_code):
                return True
        
        return False
    
    def _is_unusual_access(self, user: User, context: Dict[str, Any] = None) -> bool:
        """检查是否是异常访问"""
        if not context:
            return False
        
        # 检查访问时间
        current_hour = timezone.now().hour
        if current_hour < 6 or current_hour > 22:  # 非工作时间
            return True
        
        # 检查IP地址
        client_ip = context.get('client_ip')
        if client_ip and not self._is_known_ip(user, client_ip):
            return True
        
        return False
    
    def _has_frequent_failures(self, username: str, context: Dict[str, Any] = None) -> bool:
        """检查是否有频繁失败尝试"""
        # 这里应该查询最近的失败记录
        # 简化实现，返回False
        return False
    
    def _is_unusual_login_location(self, username: str, context: Dict[str, Any] = None) -> bool:
        """检查是否是异常登录地点"""
        # 这里应该检查用户的历史登录地点
        # 简化实现，返回False
        return False
    
    def _is_known_ip(self, user: User, ip: str) -> bool:
        """检查是否是已知IP"""
        # 这里应该查询用户的历史IP记录
        # 简化实现，返回True
        return True
    
    def _load_risk_patterns(self) -> Dict[str, Any]:
        """加载风险模式配置"""
        return {
            'sensitive_permissions': [
                'admin.*',
                '*.delete',
                'system.*'
            ],
            'unusual_hours': [0, 1, 2, 3, 4, 5, 22, 23],
            'max_failed_attempts': 5
        }
    
    def _write_audit_log(self, event: AuditEvent):
        """写入审计日志"""
        try:
            # 写入到日志文件
            self.logger.info(
                f"AUDIT: {event.message}",
                extra={
                    'audit_event': event.to_dict(),
                    'event_type': event.event_type.value,
                    'risk_level': event.risk_level.value
                }
            )
            
            # 这里可以添加写入数据库的逻辑
            # self._save_to_database(event)
            
        except Exception as e:
            self.logger.error(f"写入审计日志失败: {e}")
    
    def _trigger_security_alert(self, event: AuditEvent):
        """触发安全告警"""
        try:
            alert_message = f"安全告警: {event.message} (风险等级: {event.risk_level.value})"
            
            # 发送告警通知
            self.logger.warning(alert_message, extra={'audit_event': event.to_dict()})
            
            # 这里可以添加其他告警机制
            # - 发送邮件
            # - 发送短信
            # - 推送到监控系统
            
        except Exception as e:
            self.logger.error(f"触发安全告警失败: {e}")


class PermissionMonitor:
    """权限监控器"""
    
    def __init__(self):
        self.audit_logger = PermissionAuditLogger()
    
    def get_permission_usage_stats(self, 
                                 start_date: datetime = None,
                                 end_date: datetime = None) -> Dict[str, Any]:
        """获取权限使用统计"""
        if not start_date:
            start_date = timezone.now() - timedelta(days=7)
        if not end_date:
            end_date = timezone.now()
        
        # 这里应该从审计日志中统计数据
        # 简化实现，返回模拟数据
        return {
            'total_checks': 1000,
            'granted_count': 950,
            'denied_count': 50,
            'top_permissions': [
                {'permission': 'user.view', 'count': 200},
                {'permission': 'user.edit', 'count': 150},
                {'permission': 'admin.access', 'count': 100}
            ],
            'risk_events': {
                'low': 800,
                'medium': 150,
                'high': 40,
                'critical': 10
            }
        }
    
    def get_user_activity_report(self, user_id: int, days: int = 7) -> Dict[str, Any]:
        """获取用户活动报告"""
        # 这里应该从审计日志中查询用户活动
        # 简化实现，返回模拟数据
        return {
            'user_id': user_id,
            'period_days': days,
            'total_actions': 50,
            'permission_checks': 45,
            'login_attempts': 5,
            'failed_attempts': 0,
            'risk_events': 2,
            'most_used_permissions': [
                'user.view',
                'dashboard.access',
                'report.generate'
            ]
        }
    
    def detect_anomalies(self) -> List[Dict[str, Any]]:
        """检测异常行为"""
        anomalies = []
        
        # 检测频繁的权限拒绝
        # 检测异常时间的访问
        # 检测异常IP的访问
        # 检测权限提升尝试
        
        # 简化实现，返回模拟数据
        anomalies.append({
            'type': 'frequent_denials',
            'description': '用户频繁被拒绝访问敏感权限',
            'user_id': 123,
            'severity': 'high',
            'timestamp': timezone.now().isoformat()
        })
        
        return anomalies
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """生成合规报告"""
        return {
            'report_date': timezone.now().isoformat(),
            'audit_coverage': '100%',
            'policy_violations': 5,
            'security_incidents': 2,
            'recommendations': [
                '加强敏感权限的审批流程',
                '定期审查用户权限分配',
                '增强异常行为监控'
            ]
        }


# 全局审计系统实例
permission_audit_logger = PermissionAuditLogger()
permission_monitor = PermissionMonitor()


def audit_permission_check(func):
    """权限检查审计装饰器"""
    def wrapper(user, permission_code, *args, **kwargs):
        result = func(user, permission_code, *args, **kwargs)
        
        # 记录审计日志
        permission_audit_logger.log_permission_check(
            user=user,
            permission_code=permission_code,
            result=result,
            context=kwargs.get('context', {})
        )
        
        return result
    return wrapper
