from ninja.security import HttpBearer
from django.utils import timezone
from zhi_oauth.models import OAuthAccessToken
from zhi_common.zhi_exceptions.base import ZhiTokenException
from zhi_common.zhi_logger import zhi_logger

METHOD = {
    'GET': 0,
    'POST': 1,
    'PUT': 2,
    'DELETE': 3,
}


UUID_PATTERN = r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'


class GlobalOAuth2(HttpBearer):
    def __init__(self, *args, **kwargs):
        """初始化GlobalOAuth2认证器"""
        # 调用父类初始化，忽略额外参数
        super().__init__()

    def authenticate(self, request, token):
        # [TODO:优化认证][加入缓存]
        zhi_logger.info('In GlobalAuth...............')
        zhi_logger.info(f'token: {token}')
        if 'access' in token:
            # 获取token验证信息
            token_obj = OAuthAccessToken.objects.get(
                token=token,
                is_deleted=False,
                )
            if not token_obj:
                raise ZhiTokenException(message='无效Token')
            now_time = timezone.now()
            if token_obj.expires_at <= now_time:
                raise ZhiTokenException(message='token已过期')
        else:
            raise ZhiTokenException(message='无效Token')
        # 将用户信息添加到 request
        request.user = token_obj.user

        # 初始化数据权限上下文
        user_info = token_obj.user.to_dict
        request.user_info = user_info

        return user_info
