"""
统一权限管理接口 - 整合所有权限管理功能
"""

from typing import Dict, List, Optional, Any, Set, Union
from datetime import datetime
from functools import wraps

from django.contrib.auth import get_user_model
from django.db.models import QuerySet
from django.core.cache import cache

from .enhanced_permission_manager import enhanced_permission_manager
from .permission_cache_manager import permission_cache_manager
from .dynamic_permission_engine import dynamic_permission_engine
from .permission_audit_system import permission_audit_logger, permission_monitor
from zhi_common.zhi_logger import zhi_logger

User = get_user_model()


class UnifiedPermissionManager:
    """统一权限管理器"""
    
    def __init__(self):
        self.enhanced_manager = enhanced_permission_manager
        self.cache_manager = permission_cache_manager
        self.dynamic_engine = dynamic_permission_engine
        self.audit_logger = permission_audit_logger
        self.monitor = permission_monitor
        
        # 配置选项
        self.enable_caching = True
        self.enable_dynamic_policies = True
        self.enable_auditing = True
        self.enable_monitoring = True
    
    def check_permission(self,
                        user: User,
                        permission_code: str,
                        resource: Any = None,
                        context: Dict[str, Any] = None) -> bool:
        """
        统一权限检查接口
        
        Args:
            user: 用户对象
            permission_code: 权限代码
            resource: 资源对象
            context: 请求上下文
        """
        if not user or not permission_code:
            return False
        
        try:
            # 1. 尝试从缓存获取
            if self.enable_caching:
                resource_hash = None
                if resource:
                    resource_hash = self.cache_manager._generate_hash_key(resource)
                
                cached_result = self.cache_manager.get_permission_check(
                    user.id, permission_code, resource_hash
                )
                if cached_result is not None:
                    # 记录缓存命中的审计日志
                    if self.enable_auditing:
                        self.audit_logger.log_permission_check(
                            user, permission_code, cached_result, 
                            context=dict(context or {}, **{'cache_hit': True})
                        )
                    return cached_result
            
            # 2. 使用动态策略引擎检查
            result = False
            if self.enable_dynamic_policies:
                result = self.dynamic_engine.evaluate_permission(
                    user, permission_code, resource, context
                )
            else:
                # 3. 使用增强权限管理器检查
                result = self.enhanced_manager.check_permission(
                    user, permission_code, resource
                )
            
            # 4. 缓存结果
            if self.enable_caching:
                self.cache_manager.cache_permission_check(
                    user.id, permission_code, result, resource_hash
                )
            
            # 5. 记录审计日志
            if self.enable_auditing:
                self.audit_logger.log_permission_check(
                    user, permission_code, result, 
                    resource_type=type(resource).__name__ if resource else None,
                    resource_id=getattr(resource, 'id', None),
                    context=context
                )
            
            return result
            
        except Exception as e:
            zhi_logger.error(f"权限检查失败: {e}")
            
            # 记录异常审计日志
            if self.enable_auditing:
                self.audit_logger.log_suspicious_activity(
                    user, "permission_check_error", str(e), context
                )
            
            return False
    
    def filter_queryset(self,
                       user: User,
                       queryset: QuerySet,
                       action: str = 'view',
                       dimension: str = 'data') -> QuerySet:
        """
        根据权限过滤查询集
        
        Args:
            user: 用户对象
            queryset: 原始查询集
            action: 操作类型
            dimension: 权限维度
        """
        try:
            return self.enhanced_manager.filter_queryset(
                user, queryset, action, dimension
            )
        except Exception as e:
            zhi_logger.error(f"查询集过滤失败: {e}")
            return queryset.none()
    
    def get_user_permissions(self,
                           user: User,
                           permission_type: str = None,
                           use_cache: bool = True) -> List[str]:
        """
        获取用户所有权限
        
        Args:
            user: 用户对象
            permission_type: 权限类型过滤
            use_cache: 是否使用缓存
        """
        if not user:
            return []
        
        try:
            # 尝试从缓存获取
            if use_cache and self.enable_caching:
                cached_permissions = self.cache_manager.get_user_permissions(user.id)
                if cached_permissions is not None:
                    if permission_type:
                        # 过滤权限类型
                        return [p for p in cached_permissions if p.startswith(f"{permission_type}.")]
                    return cached_permissions
            
            # 从增强管理器获取
            permissions = self.enhanced_manager.get_user_permissions(user, permission_type)
            
            # 缓存结果
            if use_cache and self.enable_caching:
                self.cache_manager.cache_user_permissions(user.id, permissions)
            
            return permissions
            
        except Exception as e:
            zhi_logger.error(f"获取用户权限失败: {e}")
            return []
    
    def get_permitted_fields(self, user: User, model_class) -> Set[str]:
        """获取用户有权限的字段"""
        try:
            return self.enhanced_manager.get_permitted_fields(user, model_class)
        except Exception as e:
            zhi_logger.error(f"获取字段权限失败: {e}")
            return set()
    
    def batch_check_permissions(self,
                              user: User,
                              permission_codes: List[str],
                              context: Dict[str, Any] = None) -> Dict[str, bool]:
        """
        批量检查权限
        
        Args:
            user: 用户对象
            permission_codes: 权限代码列表
            context: 请求上下文
        """
        results = {}
        
        for perm_code in permission_codes:
            results[perm_code] = self.check_permission(user, perm_code, context=context)
        
        return results
    
    def assign_role(self,
                   target_user: User,
                   role_name: str,
                   operator_user: User,
                   context: Dict[str, Any] = None) -> bool:
        """
        分配角色
        
        Args:
            target_user: 目标用户
            role_name: 角色名称
            operator_user: 操作者
            context: 操作上下文
        """
        try:
            # 这里应该实现角色分配逻辑
            # 简化实现，假设分配成功
            success = True
            
            if success:
                # 清除用户缓存
                if self.enable_caching:
                    self.cache_manager.invalidate_user_cache(target_user.id)
                
                # 记录审计日志
                if self.enable_auditing:
                    self.audit_logger.log_role_change(
                        target_user, operator_user, role_name, 'assigned', context
                    )
            
            return success
            
        except Exception as e:
            zhi_logger.error(f"角色分配失败: {e}")
            return False
    
    def remove_role(self,
                   target_user: User,
                   role_name: str,
                   operator_user: User,
                   context: Dict[str, Any] = None) -> bool:
        """
        移除角色
        
        Args:
            target_user: 目标用户
            role_name: 角色名称
            operator_user: 操作者
            context: 操作上下文
        """
        try:
            # 这里应该实现角色移除逻辑
            # 简化实现，假设移除成功
            success = True
            
            if success:
                # 清除用户缓存
                if self.enable_caching:
                    self.cache_manager.invalidate_user_cache(target_user.id)
                
                # 记录审计日志
                if self.enable_auditing:
                    self.audit_logger.log_role_change(
                        target_user, operator_user, role_name, 'removed', context
                    )
            
            return success
            
        except Exception as e:
            zhi_logger.error(f"角色移除失败: {e}")
            return False
    
    def get_permission_stats(self, days: int = 7) -> Dict[str, Any]:
        """获取权限统计信息"""
        if not self.enable_monitoring:
            return {}
        
        try:
            return self.monitor.get_permission_usage_stats(days=days)
        except Exception as e:
            zhi_logger.error(f"获取权限统计失败: {e}")
            return {}
    
    def get_user_activity_report(self, user_id: int, days: int = 7) -> Dict[str, Any]:
        """获取用户活动报告"""
        if not self.enable_monitoring:
            return {}
        
        try:
            return self.monitor.get_user_activity_report(user_id, days)
        except Exception as e:
            zhi_logger.error(f"获取用户活动报告失败: {e}")
            return {}
    
    def detect_security_anomalies(self) -> List[Dict[str, Any]]:
        """检测安全异常"""
        if not self.enable_monitoring:
            return []
        
        try:
            return self.monitor.detect_anomalies()
        except Exception as e:
            zhi_logger.error(f"检测安全异常失败: {e}")
            return []
    
    def warm_up_cache(self, user_ids: List[int] = None) -> Dict[str, int]:
        """预热权限缓存"""
        if not self.enable_caching:
            return {'success': 0, 'failed': 0}
        
        try:
            return self.cache_manager.warm_up_cache(user_ids)
        except Exception as e:
            zhi_logger.error(f"缓存预热失败: {e}")
            return {'success': 0, 'failed': 0}
    
    def clear_user_cache(self, user_id: int) -> bool:
        """清除用户缓存"""
        if not self.enable_caching:
            return True
        
        try:
            return self.cache_manager.invalidate_user_cache(user_id)
        except Exception as e:
            zhi_logger.error(f"清除用户缓存失败: {e}")
            return False
    
    def configure(self, **options):
        """配置权限管理器"""
        self.enable_caching = options.get('enable_caching', self.enable_caching)
        self.enable_dynamic_policies = options.get('enable_dynamic_policies', self.enable_dynamic_policies)
        self.enable_auditing = options.get('enable_auditing', self.enable_auditing)
        self.enable_monitoring = options.get('enable_monitoring', self.enable_monitoring)
        
        zhi_logger.info(f"权限管理器配置更新: {options}")


# 全局统一权限管理器实例
unified_permission_manager = UnifiedPermissionManager()


# 便捷装饰器
def require_permission(permission_code: str, 
                      resource_getter: callable = None,
                      context_getter: callable = None):
    """
    权限检查装饰器
    
    Args:
        permission_code: 权限代码
        resource_getter: 资源获取函数
        context_getter: 上下文获取函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            user = getattr(request, 'user', None)
            if not user or not user.is_authenticated:
                from django.http import JsonResponse
                return JsonResponse({
                    'success': False,
                    'message': '用户未认证',
                    'code': 'NOT_AUTHENTICATED'
                }, status=401)
            
            # 获取资源对象
            resource = None
            if resource_getter:
                try:
                    resource = resource_getter(request, *args, **kwargs)
                except Exception as e:
                    zhi_logger.warning(f"获取资源对象失败: {e}")
            
            # 获取上下文
            context = {}
            if context_getter:
                try:
                    context = context_getter(request, *args, **kwargs)
                except Exception as e:
                    zhi_logger.warning(f"获取上下文失败: {e}")
            
            # 添加请求信息到上下文
            context.update({
                'client_ip': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT'),
                'session_id': request.session.session_key,
                'method': request.method,
                'path': request.path
            })
            
            # 检查权限
            if not unified_permission_manager.check_permission(
                user, permission_code, resource, context
            ):
                from django.http import JsonResponse
                return JsonResponse({
                    'success': False,
                    'message': f'权限不足，需要权限: {permission_code}',
                    'code': 'PERMISSION_DENIED'
                }, status=403)
            
            return func(self, request, *args, **kwargs)
        return wrapper
    return decorator


def filter_by_permission(dimension: str = 'data', action: str = 'view'):
    """
    查询集权限过滤装饰器
    
    Args:
        dimension: 权限维度
        action: 操作类型
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            # 执行原函数获取查询集
            queryset = func(self, request, *args, **kwargs)
            
            # 应用权限过滤
            user = getattr(request, 'user', None)
            if user and user.is_authenticated:
                queryset = unified_permission_manager.filter_queryset(
                    user, queryset, action, dimension
                )
            else:
                # 未认证用户返回空查询集
                queryset = queryset.none()
            
            return queryset
        return wrapper
    return decorator
