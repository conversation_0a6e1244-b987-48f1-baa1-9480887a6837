"""
增强权限管理器 - 支持多维度权限控制
"""

import json
from typing import Dict, List, Optional, Set, Any, Union
from datetime import datetime, timedelta
from functools import wraps
from abc import ABC, abstractmethod

from django.core.cache import cache
from django.db.models import Q, QuerySet
from django.contrib.auth import get_user_model
from django.conf import settings

from zhi_common.zhi_logger import zhi_logger

User = get_user_model()


class PermissionDimension(ABC):
    """权限维度抽象基类"""
    
    @abstractmethod
    def check_permission(self, user: User, resource: Any, action: str) -> bool:
        """检查权限"""
        pass
    
    @abstractmethod
    def filter_queryset(self, user: User, queryset: QuerySet, action: str = 'view') -> QuerySet:
        """过滤查询集"""
        pass


class MenuPermissionDimension(PermissionDimension):
    """菜单权限维度"""
    
    def check_permission(self, user: User, resource: Any, action: str) -> bool:
        """检查菜单权限"""
        if user.is_superuser:
            return True
        
        menu_code = resource if isinstance(resource, str) else getattr(resource, 'code', None)
        if not menu_code:
            return False
        
        # 检查用户角色是否有该菜单权限
        return user.roles.filter(
            is_active=True,
            permissions__code=menu_code,
            permissions__permission_type='menu',
            permissions__is_active=True
        ).exists()
    
    def filter_queryset(self, user: User, queryset: QuerySet, action: str = 'view') -> QuerySet:
        """过滤菜单查询集"""
        if user.is_superuser:
            return queryset
        
        # 获取用户有权限的菜单
        permitted_menu_ids = user.roles.filter(
            is_active=True
        ).values_list(
            'permissions__id', flat=True
        ).filter(
            permissions__permission_type='menu',
            permissions__is_active=True
        )
        
        return queryset.filter(id__in=permitted_menu_ids)


class DataPermissionDimension(PermissionDimension):
    """数据权限维度"""
    
    def check_permission(self, user: User, resource: Any, action: str) -> bool:
        """检查数据权限"""
        if user.is_superuser:
            return True
        
        # 获取用户的数据权限范围
        data_scopes = user.roles.filter(is_active=True).values_list('data_scope', flat=True)
        
        # 如果有全部数据权限，直接返回True
        if 'all' in data_scopes:
            return True
        
        # 根据资源类型和用户权限范围进行检查
        return self._check_data_scope_permission(user, resource, data_scopes, action)
    
    def filter_queryset(self, user: User, queryset: QuerySet, action: str = 'view') -> QuerySet:
        """根据数据权限过滤查询集"""
        if user.is_superuser:
            return queryset
        
        # 获取用户的数据权限范围
        data_scopes = set(user.roles.filter(is_active=True).values_list('data_scope', flat=True))
        
        if 'all' in data_scopes:
            return queryset
        
        # 构建数据权限过滤条件
        filters = Q()
        
        if 'tenant' in data_scopes and hasattr(user, 'tenant_id'):
            filters |= Q(tenant_id=user.tenant_id)
        
        if 'org' in data_scopes and hasattr(user, 'organization_id'):
            filters |= Q(organization_id=user.organization_id)
        
        if 'org_and_sub' in data_scopes and hasattr(user, 'organization'):
            # 获取用户部门及子部门的所有ID
            org_ids = self._get_org_and_sub_ids(user.organization)
            filters |= Q(organization_id__in=org_ids)
        
        if 'self' in data_scopes:
            filters |= Q(creator_id=user.id)
        
        return queryset.filter(filters) if filters else queryset.none()
    
    def _check_data_scope_permission(self, user: User, resource: Any, data_scopes: List[str], action: str) -> bool:
        """检查数据范围权限"""
        if 'all' in data_scopes:
            return True
        
        if 'tenant' in data_scopes:
            if hasattr(resource, 'tenant_id') and resource.tenant_id == getattr(user, 'tenant_id', None):
                return True
        
        if 'org' in data_scopes:
            if hasattr(resource, 'organization_id') and resource.organization_id == getattr(user, 'organization_id', None):
                return True
        
        if 'org_and_sub' in data_scopes and hasattr(user, 'organization'):
            org_ids = self._get_org_and_sub_ids(user.organization)
            if hasattr(resource, 'organization_id') and resource.organization_id in org_ids:
                return True
        
        if 'self' in data_scopes:
            if hasattr(resource, 'creator_id') and resource.creator_id == user.id:
                return True
        
        return False
    
    def _get_org_and_sub_ids(self, organization) -> List[int]:
        """获取组织及其子组织的所有ID"""
        if not organization:
            return []
        
        # 这里需要根据实际的组织模型实现
        # 假设组织模型有get_descendants方法
        if hasattr(organization, 'get_descendants'):
            return [org.id for org in organization.get_descendants(include_self=True)]
        else:
            return [organization.id]


class FieldPermissionDimension(PermissionDimension):
    """字段权限维度"""
    
    def check_permission(self, user: User, resource: Any, action: str) -> bool:
        """检查字段权限"""
        if user.is_superuser:
            return True
        
        field_code = resource if isinstance(resource, str) else getattr(resource, 'code', None)
        if not field_code:
            return False
        
        return user.roles.filter(
            is_active=True,
            permissions__code=field_code,
            permissions__permission_type='field',
            permissions__is_active=True
        ).exists()
    
    def filter_queryset(self, user: User, queryset: QuerySet, action: str = 'view') -> QuerySet:
        """字段权限不直接过滤查询集，而是在序列化时处理"""
        return queryset
    
    def get_permitted_fields(self, user: User, model_class) -> Set[str]:
        """获取用户有权限的字段列表"""
        if user.is_superuser:
            return set(field.name for field in model_class._meta.fields)
        
        permitted_fields = set()
        
        # 获取用户有权限的字段
        field_permissions = user.roles.filter(
            is_active=True
        ).values_list(
            'permissions__code', flat=True
        ).filter(
            permissions__permission_type='field',
            permissions__is_active=True
        )
        
        # 解析字段权限码，格式如：model_name.field_name
        model_name = model_class.__name__.lower()
        for perm_code in field_permissions:
            if perm_code.startswith(f"{model_name}."):
                field_name = perm_code.split('.', 1)[1]
                permitted_fields.add(field_name)
        
        return permitted_fields


class EnhancedPermissionManager:
    """增强权限管理器"""
    
    def __init__(self):
        self.dimensions = {
            'menu': MenuPermissionDimension(),
            'data': DataPermissionDimension(),
            'field': FieldPermissionDimension(),
        }
        self.cache_prefix = 'enhanced_perm'
        self.cache_timeout = 300  # 5分钟缓存
    
    def check_permission(
        self, 
        user: User, 
        permission_code: str, 
        resource: Any = None,
        dimension: str = 'menu'
    ) -> bool:
        """
        检查用户权限
        
        Args:
            user: 用户对象
            permission_code: 权限代码
            resource: 资源对象
            dimension: 权限维度
        """
        if not user or not permission_code:
            return False
        
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return True
        
        # 构建缓存键
        cache_key = f"{self.cache_prefix}:check:{user.id}:{permission_code}:{dimension}"
        
        # 尝试从缓存获取
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # 检查权限
        try:
            dimension_handler = self.dimensions.get(dimension)
            if not dimension_handler:
                zhi_logger.warning(f"未知的权限维度: {dimension}")
                return False
            
            has_permission = dimension_handler.check_permission(user, resource or permission_code, 'access')
            
            # 缓存结果
            cache.set(cache_key, has_permission, self.cache_timeout)
            
            return has_permission
            
        except Exception as e:
            zhi_logger.error(f"权限检查失败: {e}")
            return False
    
    def filter_queryset(
        self, 
        user: User, 
        queryset: QuerySet, 
        action: str = 'view',
        dimension: str = 'data'
    ) -> QuerySet:
        """
        根据权限过滤查询集
        
        Args:
            user: 用户对象
            queryset: 原始查询集
            action: 操作类型
            dimension: 权限维度
        """
        if not user:
            return queryset.none()
        
        if user.is_superuser:
            return queryset
        
        dimension_handler = self.dimensions.get(dimension)
        if not dimension_handler:
            zhi_logger.warning(f"未知的权限维度: {dimension}")
            return queryset
        
        try:
            return dimension_handler.filter_queryset(user, queryset, action)
        except Exception as e:
            zhi_logger.error(f"查询集过滤失败: {e}")
            return queryset.none()
    
    def get_user_permissions(self, user: User, permission_type: str = None) -> List[str]:
        """
        获取用户所有权限
        
        Args:
            user: 用户对象
            permission_type: 权限类型过滤
        """
        if not user:
            return []
        
        cache_key = f"{self.cache_prefix}:user_perms:{user.id}:{permission_type or 'all'}"
        
        # 尝试从缓存获取
        cached_permissions = cache.get(cache_key)
        if cached_permissions is not None:
            return cached_permissions
        
        try:
            # 获取用户所有权限
            permissions_query = user.roles.filter(
                is_active=True
            ).values_list(
                'permissions__code', flat=True
            ).filter(
                permissions__is_active=True
            )
            
            if permission_type:
                permissions_query = permissions_query.filter(
                    permissions__permission_type=permission_type
                )
            
            permissions = list(set(permissions_query))
            
            # 缓存结果
            cache.set(cache_key, permissions, self.cache_timeout)
            
            return permissions
            
        except Exception as e:
            zhi_logger.error(f"获取用户权限失败: {e}")
            return []
    
    def get_permitted_fields(self, user: User, model_class) -> Set[str]:
        """获取用户有权限的字段"""
        field_dimension = self.dimensions.get('field')
        if isinstance(field_dimension, FieldPermissionDimension):
            return field_dimension.get_permitted_fields(user, model_class)
        return set()
    
    def invalidate_user_cache(self, user_id: int):
        """清除用户权限缓存"""
        try:
            # 清除用户相关的所有权限缓存
            cache_pattern = f"{self.cache_prefix}:*:{user_id}:*"
            # 这里需要根据实际的缓存后端实现批量删除
            zhi_logger.info(f"清除用户权限缓存: {user_id}")
        except Exception as e:
            zhi_logger.error(f"清除权限缓存失败: {e}")
    
    def batch_check_permissions(
        self, 
        user: User, 
        permission_codes: List[str],
        dimension: str = 'menu'
    ) -> Dict[str, bool]:
        """
        批量检查权限
        
        Args:
            user: 用户对象
            permission_codes: 权限代码列表
            dimension: 权限维度
        """
        results = {}
        
        for perm_code in permission_codes:
            results[perm_code] = self.check_permission(user, perm_code, dimension=dimension)
        
        return results


# 全局权限管理器实例
enhanced_permission_manager = EnhancedPermissionManager()


def permission_required(
    permission_code: str,
    dimension: str = 'menu',
    raise_exception: bool = True
):
    """
    权限装饰器
    
    Args:
        permission_code: 权限代码
        dimension: 权限维度
        raise_exception: 是否抛出异常
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            user = getattr(request, 'user', None)
            
            if not user or not user.is_authenticated:
                if raise_exception:
                    from django.core.exceptions import PermissionDenied
                    raise PermissionDenied("用户未认证")
                return None
            
            if not enhanced_permission_manager.check_permission(
                user, permission_code, dimension=dimension
            ):
                if raise_exception:
                    from django.core.exceptions import PermissionDenied
                    raise PermissionDenied(f"权限不足: {permission_code}")
                return None
            
            return func(self, request, *args, **kwargs)
        return wrapper
    return decorator
