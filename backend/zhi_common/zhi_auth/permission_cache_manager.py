"""
智能权限缓存管理器
"""

import json
import hashlib
from typing import Dict, List, Optional, Set, Any, Union
from datetime import datetime, timedelta
from functools import wraps

from django.core.cache import cache
from django.contrib.auth import get_user_model
from django.db.models.signals import post_save, post_delete, m2m_changed
from django.dispatch import receiver

from zhi_common.zhi_logger import zhi_logger

User = get_user_model()


class PermissionCacheManager:
    """权限缓存管理器"""
    
    def __init__(self):
        self.cache_prefix = 'perm_cache'
        self.default_timeout = 300  # 5分钟
        self.long_timeout = 3600    # 1小时
        self.short_timeout = 60     # 1分钟
    
    def _generate_cache_key(self, key_type: str, *args) -> str:
        """生成缓存键"""
        key_data = ':'.join(str(arg) for arg in args)
        return f"{self.cache_prefix}:{key_type}:{key_data}"
    
    def _generate_hash_key(self, data: Any) -> str:
        """生成数据哈希键"""
        if isinstance(data, dict):
            data_str = json.dumps(data, sort_keys=True)
        else:
            data_str = str(data)
        return hashlib.md5(data_str.encode()).hexdigest()[:16]
    
    def cache_user_permissions(self, user_id: int, permissions: List[str], timeout: int = None) -> bool:
        """缓存用户权限列表"""
        try:
            cache_key = self._generate_cache_key('user_perms', user_id)
            cache.set(cache_key, permissions, timeout or self.default_timeout)
            
            # 同时缓存权限集合，用于快速查找
            perm_set_key = self._generate_cache_key('user_perm_set', user_id)
            cache.set(perm_set_key, set(permissions), timeout or self.default_timeout)
            
            zhi_logger.debug(f"缓存用户权限: user_id={user_id}, count={len(permissions)}")
            return True
        except Exception as e:
            zhi_logger.error(f"缓存用户权限失败: {e}")
            return False
    
    def get_user_permissions(self, user_id: int) -> Optional[List[str]]:
        """获取用户权限列表"""
        try:
            cache_key = self._generate_cache_key('user_perms', user_id)
            return cache.get(cache_key)
        except Exception as e:
            zhi_logger.error(f"获取用户权限缓存失败: {e}")
            return None
    
    def cache_permission_check(
        self, 
        user_id: int, 
        permission_code: str, 
        result: bool,
        resource_hash: str = None,
        timeout: int = None
    ) -> bool:
        """缓存权限检查结果"""
        try:
            cache_key = self._generate_cache_key(
                'perm_check', 
                user_id, 
                permission_code,
                resource_hash or 'global'
            )
            cache.set(cache_key, result, timeout or self.short_timeout)
            return True
        except Exception as e:
            zhi_logger.error(f"缓存权限检查结果失败: {e}")
            return False
    
    def get_permission_check(
        self, 
        user_id: int, 
        permission_code: str,
        resource_hash: str = None
    ) -> Optional[bool]:
        """获取权限检查结果"""
        try:
            cache_key = self._generate_cache_key(
                'perm_check', 
                user_id, 
                permission_code,
                resource_hash or 'global'
            )
            return cache.get(cache_key)
        except Exception as e:
            zhi_logger.error(f"获取权限检查缓存失败: {e}")
            return None
    
    def cache_user_roles(self, user_id: int, roles: List[Dict], timeout: int = None) -> bool:
        """缓存用户角色信息"""
        try:
            cache_key = self._generate_cache_key('user_roles', user_id)
            cache.set(cache_key, roles, timeout or self.long_timeout)
            
            # 缓存角色ID列表，用于快速查找
            role_ids = [role['id'] for role in roles]
            role_ids_key = self._generate_cache_key('user_role_ids', user_id)
            cache.set(role_ids_key, role_ids, timeout or self.long_timeout)
            
            return True
        except Exception as e:
            zhi_logger.error(f"缓存用户角色失败: {e}")
            return False
    
    def get_user_roles(self, user_id: int) -> Optional[List[Dict]]:
        """获取用户角色信息"""
        try:
            cache_key = self._generate_cache_key('user_roles', user_id)
            return cache.get(cache_key)
        except Exception as e:
            zhi_logger.error(f"获取用户角色缓存失败: {e}")
            return None
    
    def cache_role_permissions(self, role_id: int, permissions: List[str], timeout: int = None) -> bool:
        """缓存角色权限"""
        try:
            cache_key = self._generate_cache_key('role_perms', role_id)
            cache.set(cache_key, permissions, timeout or self.long_timeout)
            return True
        except Exception as e:
            zhi_logger.error(f"缓存角色权限失败: {e}")
            return False
    
    def get_role_permissions(self, role_id: int) -> Optional[List[str]]:
        """获取角色权限"""
        try:
            cache_key = self._generate_cache_key('role_perms', role_id)
            return cache.get(cache_key)
        except Exception as e:
            zhi_logger.error(f"获取角色权限缓存失败: {e}")
            return None
    
    def cache_data_scope_filter(
        self, 
        user_id: int, 
        model_name: str, 
        filter_conditions: Dict,
        timeout: int = None
    ) -> bool:
        """缓存数据权限过滤条件"""
        try:
            filter_hash = self._generate_hash_key(filter_conditions)
            cache_key = self._generate_cache_key('data_scope', user_id, model_name, filter_hash)
            cache.set(cache_key, filter_conditions, timeout or self.default_timeout)
            return True
        except Exception as e:
            zhi_logger.error(f"缓存数据权限过滤条件失败: {e}")
            return False
    
    def get_data_scope_filter(self, user_id: int, model_name: str, filter_hash: str) -> Optional[Dict]:
        """获取数据权限过滤条件"""
        try:
            cache_key = self._generate_cache_key('data_scope', user_id, model_name, filter_hash)
            return cache.get(cache_key)
        except Exception as e:
            zhi_logger.error(f"获取数据权限过滤条件缓存失败: {e}")
            return None
    
    def invalidate_user_cache(self, user_id: int) -> bool:
        """清除用户相关的所有缓存"""
        try:
            patterns = [
                f"{self.cache_prefix}:user_perms:{user_id}",
                f"{self.cache_prefix}:user_perm_set:{user_id}",
                f"{self.cache_prefix}:user_roles:{user_id}",
                f"{self.cache_prefix}:user_role_ids:{user_id}",
                f"{self.cache_prefix}:perm_check:{user_id}:*",
                f"{self.cache_prefix}:data_scope:{user_id}:*",
            ]
            
            for pattern in patterns:
                # 这里需要根据实际缓存后端实现模式删除
                # 简化实现，直接删除已知的键
                if '*' not in pattern:
                    cache.delete(pattern)
            
            zhi_logger.info(f"清除用户缓存: user_id={user_id}")
            return True
        except Exception as e:
            zhi_logger.error(f"清除用户缓存失败: {e}")
            return False
    
    def invalidate_role_cache(self, role_id: int) -> bool:
        """清除角色相关的缓存"""
        try:
            cache_key = self._generate_cache_key('role_perms', role_id)
            cache.delete(cache_key)
            
            # 需要清除所有使用该角色的用户缓存
            # 这里可以通过维护角色-用户映射来优化
            zhi_logger.info(f"清除角色缓存: role_id={role_id}")
            return True
        except Exception as e:
            zhi_logger.error(f"清除角色缓存失败: {e}")
            return False
    
    def warm_up_cache(self, user_ids: List[int] = None) -> Dict[str, int]:
        """预热权限缓存"""
        results = {'success': 0, 'failed': 0}
        
        try:
            if user_ids is None:
                # 获取活跃用户列表
                user_ids = User.objects.filter(
                    is_active=True,
                    last_login__gte=datetime.now() - timedelta(days=30)
                ).values_list('id', flat=True)[:1000]  # 限制数量
            
            for user_id in user_ids:
                try:
                    # 预加载用户权限
                    user = User.objects.select_related().prefetch_related('roles__permissions').get(id=user_id)
                    
                    # 缓存用户角色
                    roles_data = []
                    for role in user.roles.filter(is_active=True):
                        roles_data.append({
                            'id': role.id,
                            'code': role.code,
                            'name': role.name,
                            'data_scope': role.data_scope
                        })
                    self.cache_user_roles(user_id, roles_data)
                    
                    # 缓存用户权限
                    permissions = []
                    for role in user.roles.filter(is_active=True):
                        role_perms = list(role.permissions.filter(is_active=True).values_list('code', flat=True))
                        permissions.extend(role_perms)
                        # 同时缓存角色权限
                        self.cache_role_permissions(role.id, role_perms)
                    
                    self.cache_user_permissions(user_id, list(set(permissions)))
                    results['success'] += 1
                    
                except Exception as e:
                    zhi_logger.error(f"预热用户缓存失败: user_id={user_id}, error={e}")
                    results['failed'] += 1
            
            zhi_logger.info(f"权限缓存预热完成: {results}")
            return results
            
        except Exception as e:
            zhi_logger.error(f"权限缓存预热失败: {e}")
            return results
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            # 这里需要根据实际缓存后端实现
            # 简化实现，返回基本信息
            return {
                'cache_prefix': self.cache_prefix,
                'default_timeout': self.default_timeout,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            zhi_logger.error(f"获取缓存统计失败: {e}")
            return {}


# 全局权限缓存管理器实例
permission_cache_manager = PermissionCacheManager()


def cached_permission_check(timeout: int = None):
    """权限检查缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(user_id: int, permission_code: str, resource: Any = None, *args, **kwargs):
            # 生成资源哈希
            resource_hash = None
            if resource:
                resource_hash = permission_cache_manager._generate_hash_key(resource)
            
            # 尝试从缓存获取
            cached_result = permission_cache_manager.get_permission_check(
                user_id, permission_code, resource_hash
            )
            
            if cached_result is not None:
                return cached_result
            
            # 执行原函数
            result = func(user_id, permission_code, resource, *args, **kwargs)
            
            # 缓存结果
            permission_cache_manager.cache_permission_check(
                user_id, permission_code, result, resource_hash, timeout
            )
            
            return result
        return wrapper
    return decorator


# 信号处理器 - 自动清除相关缓存
@receiver(post_save, sender='zhi_oauth.User')
def clear_user_cache_on_save(sender, instance, **kwargs):
    """用户保存时清除缓存"""
    permission_cache_manager.invalidate_user_cache(instance.id)


@receiver(post_save, sender='zhi_oauth.Role')
def clear_role_cache_on_save(sender, instance, **kwargs):
    """角色保存时清除缓存"""
    permission_cache_manager.invalidate_role_cache(instance.id)


@receiver(post_save, sender='zhi_oauth.UserRole')
def clear_user_role_cache_on_save(sender, instance, **kwargs):
    """用户角色关系保存时清除缓存"""
    permission_cache_manager.invalidate_user_cache(instance.user_id)
    permission_cache_manager.invalidate_role_cache(instance.role_id)


@receiver(post_delete, sender='zhi_oauth.UserRole')
def clear_user_role_cache_on_delete(sender, instance, **kwargs):
    """用户角色关系删除时清除缓存"""
    permission_cache_manager.invalidate_user_cache(instance.user_id)
    permission_cache_manager.invalidate_role_cache(instance.role_id)
