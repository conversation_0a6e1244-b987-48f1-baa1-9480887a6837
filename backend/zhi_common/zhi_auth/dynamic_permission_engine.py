"""
动态权限策略引擎 - 支持复杂权限规则和策略
"""

import json
import re
from typing import Dict, List, Optional, Any, Callable, Union
from datetime import datetime, time
from abc import ABC, abstractmethod
from enum import Enum

from django.contrib.auth import get_user_model
from django.db.models import Q
from django.utils import timezone

from zhi_common.zhi_logger import zhi_logger

User = get_user_model()


class PolicyOperator(Enum):
    """策略操作符"""
    AND = "and"
    OR = "or"
    NOT = "not"
    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    LESS_THAN = "lt"
    IN = "in"
    NOT_IN = "not_in"
    CONTAINS = "contains"
    REGEX = "regex"


class PermissionCondition(ABC):
    """权限条件抽象基类"""
    
    @abstractmethod
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """评估条件"""
        pass


class TimeBasedCondition(PermissionCondition):
    """基于时间的权限条件"""
    
    def __init__(self, start_time: str = None, end_time: str = None, 
                 weekdays: List[int] = None, timezone_name: str = None):
        """
        Args:
            start_time: 开始时间 (HH:MM格式)
            end_time: 结束时间 (HH:MM格式)
            weekdays: 允许的星期几 (0=周一, 6=周日)
            timezone_name: 时区名称
        """
        self.start_time = time.fromisoformat(start_time) if start_time else None
        self.end_time = time.fromisoformat(end_time) if end_time else None
        self.weekdays = weekdays or []
        self.timezone_name = timezone_name
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """评估时间条件"""
        now = timezone.now()
        
        # 检查星期几
        if self.weekdays and now.weekday() not in self.weekdays:
            return False
        
        # 检查时间范围
        current_time = now.time()
        if self.start_time and current_time < self.start_time:
            return False
        if self.end_time and current_time > self.end_time:
            return False
        
        return True


class IPBasedCondition(PermissionCondition):
    """基于IP的权限条件"""
    
    def __init__(self, allowed_ips: List[str] = None, blocked_ips: List[str] = None):
        self.allowed_ips = allowed_ips or []
        self.blocked_ips = blocked_ips or []
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """评估IP条件"""
        client_ip = context.get('client_ip')
        if not client_ip:
            return True  # 如果没有IP信息，默认允许
        
        # 检查黑名单
        if client_ip in self.blocked_ips:
            return False
        
        # 检查白名单
        if self.allowed_ips and client_ip not in self.allowed_ips:
            return False
        
        return True


class AttributeBasedCondition(PermissionCondition):
    """基于属性的权限条件"""
    
    def __init__(self, attribute_path: str, operator: PolicyOperator, value: Any):
        """
        Args:
            attribute_path: 属性路径，如 'user.department.name'
            operator: 比较操作符
            value: 比较值
        """
        self.attribute_path = attribute_path
        self.operator = operator
        self.value = value
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """评估属性条件"""
        try:
            # 获取属性值
            attr_value = self._get_nested_attribute(context, self.attribute_path)
            
            # 执行比较
            return self._compare_values(attr_value, self.operator, self.value)
            
        except Exception as e:
            zhi_logger.warning(f"属性条件评估失败: {e}")
            return False
    
    def _get_nested_attribute(self, obj: Any, path: str) -> Any:
        """获取嵌套属性值"""
        parts = path.split('.')
        current = obj
        
        for part in parts:
            if isinstance(current, dict):
                current = current.get(part)
            else:
                current = getattr(current, part, None)
            
            if current is None:
                return None
        
        return current
    
    def _compare_values(self, attr_value: Any, operator: PolicyOperator, expected_value: Any) -> bool:
        """比较值"""
        if operator == PolicyOperator.EQUALS:
            return attr_value == expected_value
        elif operator == PolicyOperator.NOT_EQUALS:
            return attr_value != expected_value
        elif operator == PolicyOperator.GREATER_THAN:
            return attr_value > expected_value
        elif operator == PolicyOperator.LESS_THAN:
            return attr_value < expected_value
        elif operator == PolicyOperator.IN:
            return attr_value in expected_value
        elif operator == PolicyOperator.NOT_IN:
            return attr_value not in expected_value
        elif operator == PolicyOperator.CONTAINS:
            return expected_value in str(attr_value)
        elif operator == PolicyOperator.REGEX:
            return bool(re.match(expected_value, str(attr_value)))
        else:
            return False


class CompositeCondition(PermissionCondition):
    """复合权限条件"""
    
    def __init__(self, operator: PolicyOperator, conditions: List[PermissionCondition]):
        self.operator = operator
        self.conditions = conditions
    
    def evaluate(self, context: Dict[str, Any]) -> bool:
        """评估复合条件"""
        if not self.conditions:
            return True
        
        if self.operator == PolicyOperator.AND:
            return all(condition.evaluate(context) for condition in self.conditions)
        elif self.operator == PolicyOperator.OR:
            return any(condition.evaluate(context) for condition in self.conditions)
        elif self.operator == PolicyOperator.NOT:
            # NOT操作符只对第一个条件取反
            return not self.conditions[0].evaluate(context)
        else:
            return False


class PermissionPolicy:
    """权限策略"""
    
    def __init__(self, 
                 name: str,
                 description: str = "",
                 conditions: List[PermissionCondition] = None,
                 effect: str = "allow",  # allow 或 deny
                 priority: int = 0):
        self.name = name
        self.description = description
        self.conditions = conditions or []
        self.effect = effect
        self.priority = priority
    
    def evaluate(self, context: Dict[str, Any]) -> Optional[bool]:
        """
        评估策略
        
        Returns:
            True: 明确允许
            False: 明确拒绝
            None: 策略不适用
        """
        # 如果所有条件都满足
        if all(condition.evaluate(context) for condition in self.conditions):
            return self.effect == "allow"
        
        # 策略不适用
        return None


class DynamicPermissionEngine:
    """动态权限策略引擎"""
    
    def __init__(self):
        self.policies: List[PermissionPolicy] = []
        self.custom_evaluators: Dict[str, Callable] = {}
    
    def add_policy(self, policy: PermissionPolicy):
        """添加权限策略"""
        self.policies.append(policy)
        # 按优先级排序
        self.policies.sort(key=lambda p: p.priority, reverse=True)
    
    def remove_policy(self, policy_name: str):
        """移除权限策略"""
        self.policies = [p for p in self.policies if p.name != policy_name]
    
    def register_evaluator(self, name: str, evaluator: Callable):
        """注册自定义评估器"""
        self.custom_evaluators[name] = evaluator
    
    def evaluate_permission(self, 
                          user: User, 
                          permission_code: str,
                          resource: Any = None,
                          request_context: Dict[str, Any] = None) -> bool:
        """
        评估权限
        
        Args:
            user: 用户对象
            permission_code: 权限代码
            resource: 资源对象
            request_context: 请求上下文
        """
        # 构建评估上下文
        context = self._build_context(user, permission_code, resource, request_context)
        
        # 按优先级评估策略
        for policy in self.policies:
            result = policy.evaluate(context)
            
            if result is not None:
                zhi_logger.debug(f"策略 {policy.name} 返回结果: {result}")
                return result
        
        # 如果没有策略匹配，使用默认权限检查
        return self._default_permission_check(user, permission_code, resource)
    
    def _build_context(self, 
                      user: User, 
                      permission_code: str,
                      resource: Any = None,
                      request_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """构建评估上下文"""
        context = {
            'user': user,
            'permission_code': permission_code,
            'resource': resource,
            'timestamp': timezone.now(),
            'weekday': timezone.now().weekday(),
            'hour': timezone.now().hour,
        }
        
        # 添加用户属性
        if user:
            context.update({
                'user_id': user.id,
                'username': user.username,
                'is_superuser': user.is_superuser,
                'is_staff': user.is_staff,
                'user_roles': [role.code for role in user.roles.filter(is_active=True)],
            })
            
            # 添加用户组织信息
            if hasattr(user, 'organization'):
                context['user_organization'] = user.organization
            if hasattr(user, 'department'):
                context['user_department'] = user.department
        
        # 添加请求上下文
        if request_context:
            context.update(request_context)
        
        return context
    
    def _default_permission_check(self, user: User, permission_code: str, resource: Any = None) -> bool:
        """默认权限检查逻辑"""
        if not user or not user.is_authenticated:
            return False
        
        if user.is_superuser:
            return True
        
        # 检查用户角色权限
        return user.roles.filter(
            is_active=True,
            permissions__code=permission_code,
            permissions__is_active=True
        ).exists()
    
    def create_policy_from_config(self, config: Dict[str, Any]) -> PermissionPolicy:
        """从配置创建策略"""
        conditions = []
        
        # 解析条件配置
        for condition_config in config.get('conditions', []):
            condition = self._create_condition_from_config(condition_config)
            if condition:
                conditions.append(condition)
        
        return PermissionPolicy(
            name=config['name'],
            description=config.get('description', ''),
            conditions=conditions,
            effect=config.get('effect', 'allow'),
            priority=config.get('priority', 0)
        )
    
    def _create_condition_from_config(self, config: Dict[str, Any]) -> Optional[PermissionCondition]:
        """从配置创建条件"""
        condition_type = config.get('type')
        
        if condition_type == 'time':
            return TimeBasedCondition(
                start_time=config.get('start_time'),
                end_time=config.get('end_time'),
                weekdays=config.get('weekdays'),
                timezone_name=config.get('timezone')
            )
        elif condition_type == 'ip':
            return IPBasedCondition(
                allowed_ips=config.get('allowed_ips'),
                blocked_ips=config.get('blocked_ips')
            )
        elif condition_type == 'attribute':
            operator = PolicyOperator(config.get('operator', 'eq'))
            return AttributeBasedCondition(
                attribute_path=config['attribute_path'],
                operator=operator,
                value=config['value']
            )
        elif condition_type == 'composite':
            operator = PolicyOperator(config.get('operator', 'and'))
            sub_conditions = []
            for sub_config in config.get('conditions', []):
                sub_condition = self._create_condition_from_config(sub_config)
                if sub_condition:
                    sub_conditions.append(sub_condition)
            return CompositeCondition(operator, sub_conditions)
        
        return None
    
    def load_policies_from_file(self, file_path: str):
        """从文件加载策略配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                policies_config = json.load(f)
            
            for policy_config in policies_config.get('policies', []):
                policy = self.create_policy_from_config(policy_config)
                self.add_policy(policy)
            
            zhi_logger.info(f"从文件加载策略: {file_path}, 数量: {len(policies_config.get('policies', []))}")
            
        except Exception as e:
            zhi_logger.error(f"加载策略文件失败: {e}")
    
    def get_applicable_policies(self, context: Dict[str, Any]) -> List[PermissionPolicy]:
        """获取适用的策略列表"""
        applicable_policies = []
        
        for policy in self.policies:
            # 简单检查：如果策略有条件且第一个条件可能适用
            if not policy.conditions or any(
                self._condition_might_apply(condition, context) 
                for condition in policy.conditions
            ):
                applicable_policies.append(policy)
        
        return applicable_policies
    
    def _condition_might_apply(self, condition: PermissionCondition, context: Dict[str, Any]) -> bool:
        """检查条件是否可能适用（快速预检查）"""
        # 这里可以实现一些快速的预检查逻辑
        # 避免执行复杂的条件评估
        return True


# 全局动态权限引擎实例
dynamic_permission_engine = DynamicPermissionEngine()


# 预定义策略示例
def setup_default_policies():
    """设置默认策略"""
    
    # 工作时间策略
    work_time_policy = PermissionPolicy(
        name="work_time_access",
        description="工作时间访问策略",
        conditions=[
            TimeBasedCondition(
                start_time="09:00",
                end_time="18:00",
                weekdays=[0, 1, 2, 3, 4]  # 周一到周五
            )
        ],
        effect="allow",
        priority=10
    )
    
    # 管理员全天候访问策略
    admin_policy = PermissionPolicy(
        name="admin_full_access",
        description="管理员全天候访问",
        conditions=[
            AttributeBasedCondition(
                attribute_path="user.is_superuser",
                operator=PolicyOperator.EQUALS,
                value=True
            )
        ],
        effect="allow",
        priority=100
    )
    
    # IP限制策略
    ip_restriction_policy = PermissionPolicy(
        name="ip_restriction",
        description="IP访问限制",
        conditions=[
            IPBasedCondition(
                allowed_ips=["***********/24", "10.0.0.0/8"]
            )
        ],
        effect="allow",
        priority=50
    )
    
    dynamic_permission_engine.add_policy(work_time_policy)
    dynamic_permission_engine.add_policy(admin_policy)
    dynamic_permission_engine.add_policy(ip_restriction_policy)
