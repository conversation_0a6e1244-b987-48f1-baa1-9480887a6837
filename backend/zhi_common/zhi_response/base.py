import json
from datetime import datetime
from typing import Any, Optional, Union

from django.http import HttpResponse
from pydantic import BaseModel, ConfigDict

from zhi_common.core_context import get_trace_id
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_tools.ensure_json import ensure_json


class ZhiResponse(HttpResponse):
    """增强版HTTP响应类"""

    def __init__(
            self,
            data=None,
            message='ok',
            code=ResponseCode.SUCCESS,
            success=True,
            status_code=200,
            **kwargs
            ):
        response_data = {
            "code": code,
            "data": data,
            "message": message,
            "success": success,
            "trace_id": get_trace_id(),
            "timestamp": datetime.now().isoformat()
            }
        content = json.dumps(ensure_json(response_data))

        super().__init__(
            content=content,
            content_type='application/json',
            status=status_code,
            **kwargs
            )


class ZhiModelResponse(BaseModel):
    """文档兼容响应模型"""
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={datetime: lambda v: v.isoformat()}
        )

    code: int = ResponseCode.SUCCESS
    data: Optional[Any] = None
    message: str = "ok"
    success: bool = True
    trace_id: Optional[str] = None
    timestamp: str = datetime.now().isoformat()

    @classmethod
    def from_response(cls, response: ZhiResponse) -> BaseModel:
        """从ZhiResponse转换"""
        try:
            data = json.loads(response.content)
            return cls(**data)
        except json.JSONDecodeError:
            return cls(
                code=ResponseCode.INTERNAL_ERROR,
                success=False,
                message="Invalid response format"
                )


def create_response(
        data=None,
        message='ok',
        code=ResponseCode.SUCCESS,
        success=True,
        status_code=200,
        as_model=False
        ) -> Union[ZhiResponse, ZhiModelResponse]:
    """响应创建工厂"""
    if as_model:
        return ZhiModelResponse(
            data=data,
            message=message,
            code=code,
            success=success,
            trace_id=get_trace_id()
            )
    return ZhiResponse(
        data=data,
        message=message,
        code=code,
        success=success,
        status_code=status_code
        )
