from typing import Generic, TypeVar, Optional, List, Any
from pydantic import BaseModel
from datetime import datetime
from ninja import Schema, Field
from zhi_common.zhi_consts.core_res_code import ResponseCode

T = TypeVar("T")  # 定义泛型类型


class BaseResponseSchema(BaseModel, Generic[T]):
    """基础响应 Schema（支持泛型）"""
    code: int = ResponseCode.SUCCESS
    message: str = "success"
    success: bool =  True
    trace_id: str = "trace-uuid4.hex"
    timestamp: datetime
    data: Optional[T] = None  # 泛型数据字段

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()  # 确保 datetime 正确序列化
            }


class PaginatedResponseSchema(BaseResponseSchema[List[T]]):
    """分页响应 Schema"""
    total: int = 0
    page: int = 1
    pages: int = 1


#  列表id映射返回值
class ListIDValueMappingSchema(Schema):
    id: str
    value: str = Field(..., alias='value')


# 导入导出相关Schema
class ExportRequestSchema(Schema):
    """导出请求Schema"""
    format: str = Field(default="excel", description="导出格式: csv, excel, json")
    filename: Optional[str] = Field(default=None, description="文件名")
    fields: Optional[List[str]] = Field(default=None, description="要导出的字段列表")
    filters: Optional[dict] = Field(default=None, description="过滤条件")


class ImportResultSchema(Schema):
    """导入结果Schema"""
    success: bool = Field(description="是否成功")
    total_rows: int = Field(description="总行数")
    success_rows: int = Field(description="成功行数")
    error_rows: int = Field(description="错误行数")
    errors: List[str] = Field(default=[], description="错误信息列表")
    message: str = Field(description="结果消息")


class ImportPreviewSchema(Schema):
    """导入预览Schema"""
    headers: List[str] = Field(description="表头列表")
    sample_data: List[dict] = Field(description="示例数据")
    total_rows: int = Field(description="总行数")
    field_mapping: Optional[dict] = Field(default=None, description="字段映射建议")


class BatchOperationResultSchema(Schema):
    """批量操作结果Schema"""
    success: bool = Field(description="是否成功")
    total: int = Field(description="总数")
    success_count: int = Field(description="成功数量")
    failed_count: int = Field(description="失败数量")
    errors: List[str] = Field(default=[], description="错误信息")
    message: str = Field(description="操作结果消息")
