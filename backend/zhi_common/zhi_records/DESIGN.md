# ZhiAdmin 审计日志系统设计文档

## 🎯 设计目标

### 业务目标
- **合规性**：满足企业级应用的审计要求
- **可追溯性**：完整记录数据变更历史
- **用户友好**：提供直观的变更展示
- **性能优化**：不影响主业务流程

### 技术目标
- **高性能**：异步处理，毫秒级响应
- **低存储**：精细化记录，减少冗余
- **高可用**：支持分布式部署
- **易扩展**：模块化设计，便于定制

## 🏗️ 架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   业务模型      │    │   审计记录器    │    │   存储层        │
│  (ZhiCoreModel) │───▶│ (ZhiRecordChanges)│───▶│  (AuditLog)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │   异步处理器    │              │
         └─────────────▶│ (AsyncWorkers)  │──────────────┘
                        └─────────────────┘
```

### 核心组件

#### 1. 模型层 (ZhiCoreModel)
- **职责**：自动触发审计日志记录
- **特点**：无侵入式集成，继承即可使用
- **关键方法**：
  - `save()`: 保存时自动记录变更
  - `delete()`: 删除时记录操作
  - `is_audit_log_enabled()`: 控制是否启用审计

#### 2. 记录器 (ZhiRecordChanges)
- **职责**：处理变更检测和格式化
- **特点**：精细化字段比较，结构化输出
- **关键功能**：
  - 字段变更检测
  - 中文名称映射
  - 值格式化处理

#### 3. 异步处理器 (AsyncWorkers)
- **职责**：异步执行审计日志记录
- **特点**：支持Celery和asyncio两种模式
- **优势**：不阻塞主业务，支持重试机制

#### 4. 存储层 (AuditLog)
- **职责**：持久化审计日志数据
- **特点**：结构化存储，支持高效查询
- **索引策略**：按资源ID、用户ID、时间建立索引

## 💡 核心设计理念

### 1. 精细化记录
**问题**：传统方案记录整个对象，产生大量冗余数据
```json
// 传统方案 - 冗余数据多
{
  "old_values": {"id": 123, "name": "张三", "age": 25, "email": "...", ...},
  "new_values": {"id": 123, "name": "李四", "age": 25, "email": "...", ...}
}
```

**解决方案**：只记录变更字段
```json
// 精细化方案 - 只记录变更
{
  "old_values": [{"field_name": "name", "field_cn_name": "姓名", "value": "张三"}],
  "new_values": [{"field_name": "name", "field_cn_name": "姓名", "value": "李四"}]
}
```

**优势**：
- 存储空间节省90%以上
- 查询性能显著提升
- 变更信息更加清晰

### 2. 结构化信息
**设计思路**：每个变更字段包含三个维度的信息
- `field_name`: 技术维度（数据库字段名）
- `field_cn_name`: 业务维度（用户友好的中文名）
- `value`: 数据维度（实际的字段值）

**前端展示效果**：
```
姓名：张三 → 李四
状态：启用 → 禁用
上次登录：2025-07-25 11:43:14 → 2025-07-25 13:20:23
```

### 3. 异步处理
**设计原则**：审计日志记录不能影响主业务性能

**实现方案**：
```python
# 同步保存业务数据
super().save(*args, **kwargs)

# 异步记录审计日志
if self.is_audit_log_enabled():
    record_changes(...)  # 异步执行
```

**技术选型**：
- **Celery**：分布式任务队列，适合生产环境
- **asyncio**：Python原生异步，适合轻量级场景

### 4. 配置驱动
**设计理念**：通过配置控制审计行为，无需修改代码

**配置层次**：
1. **全局配置**：系统级别的审计开关
2. **应用配置**：每个应用独立的审计配置
3. **模型配置**：每个模型的字段级配置
4. **实例配置**：特定实例的动态配置

## 🔧 关键技术实现

### 1. 字段变更检测
```python
def record_changes(self):
    for field_name in self.model_info['field_names'].keys():
        if field_name in NONE_RECORD_FIELD_NAME:
            continue  # 跳过公共字段

        # 安全获取字段值，防止None异常
        current_value = self.current_instance.get(field_name) if self.current_instance else None
        previous_value = self.previous_instance.get(field_name)

        if current_value != previous_value:
            # 记录变更，生成结构化数据
            temp_new_value = {
                'field_name': field_name,
                'field_cn_name': self.get_field_verbose_name(field_name),
                'value': current_value
            }
            temp_old_value = {
                'field_name': field_name,
                'field_cn_name': self.get_field_verbose_name(field_name),
                'value': previous_value
            }
            new_values.append(temp_new_value)
            old_values.append(temp_old_value)
```

### 2. 资源ID安全获取
**问题**：`to_dict_no_pub_fields`过滤掉了`id`字段，导致无法标识资源

**解决方案1**：在传递给审计记录器时单独添加ID
```python
# 获取过滤后的数据
current_instance_data = self.to_dict_no_pub_fields.copy()
# 单独添加ID用于资源标识（支持字符串ID）
current_instance_data['id'] = str(self.id)

record_changes(current_instance=current_instance_data)
```

**解决方案2**：安全的资源ID获取方法
```python
def _get_safe_resource_id(self):
    """安全获取资源ID"""
    try:
        # 优先从current_instance获取
        if self.current_instance and isinstance(self.current_instance, dict):
            resource_id = self.current_instance.get('id')
            if resource_id is not None:
                return str(resource_id)

        # 如果current_instance没有id，尝试从previous_instance获取
        if self.previous_instance and isinstance(self.previous_instance, dict):
            resource_id = self.previous_instance.get('id')
            if resource_id is not None:
                return str(resource_id)

        # 如果都没有，返回默认值
        return 'unknown'
    except Exception as e:
        zhi_logger.warning(f'获取资源ID失败: {str(e)}')
        return 'unknown'
```

**优势**：
- 保持原有的字段过滤逻辑
- 确保资源能够被正确标识
- 不影响审计日志的精细化记录
- 支持字符串ID和数字ID
- 完善的异常处理

### 3. 异步错误处理
```python
@shared_task(bind=True, max_retries=3)
def _record_changes_task(self, model_info, user_info, previous_instance, current_instance):
    try:
        async_to_sync(async_record_changes)(...)
    except Exception as e:
        logger.error(f"审计日志记录失败: {str(e)}")
        # 重试机制
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        raise
```

### 4. 用户信息安全传递
**问题**：`user_info`包含不兼容的字段（如`modifier`）

**解决方案**：安全提取用户信息
```python
@staticmethod
def log_action(action, user_info, resource_id, changes=None):
    """将变更记录到日志或其他存储方式中"""
    from zhi_logger.models import AuditLog
    if not changes:
        changes = {}

    # 安全提取用户信息，只传递AuditLog模型支持的字段
    audit_user_info = {}
    if user_info:
        if 'creator_id' in user_info:
            audit_user_info['creator_id'] = user_info['creator_id']
        if 'creator_name' in user_info:
            audit_user_info['creator_name'] = user_info['creator_name']
        # 注意：不传递 'modifier' 字段，因为AuditLog模型没有这个字段

    AuditLog.objects.create(
        action=action,
        old_values=changes.get('old_values', []),
        new_values=changes.get('new_values', []),
        resource_id=resource_id,
        **audit_user_info
    )
```

### 5. 防止无限递归审计
**问题**：`AuditLog`模型继承自`ZhiCoreModel`，会记录自身的审计日志

**解决方案**：在`AuditLog`模型中禁用自身审计
```python
class AuditLog(ZhiCoreModel):
    # ... 字段定义 ...

    def is_audit_log_enabled(self):
        """禁用AuditLog模型自身的审计日志记录，避免无限递归"""
        return False
```

## 📊 性能优化策略

### 1. 存储优化
- **字段过滤**：只记录业务相关字段
- **数据压缩**：JSON格式存储，支持压缩
- **分区策略**：按时间分区，提高查询效率

### 2. 查询优化
- **索引策略**：resource_id、creator_id、created_at复合索引
- **缓存机制**：热点数据Redis缓存
- **分页查询**：避免大结果集查询

### 3. 异步优化
- **批量处理**：合并多个审计日志写入
- **队列优化**：使用专用队列处理审计任务
- **失败重试**：指数退避重试策略

## 🔒 安全设计

### 1. 敏感字段保护
```python
# 敏感字段配置
SENSITIVE_FIELDS = ['password', 'token', 'secret', 'key']

# 配置中禁用敏感字段记录
'password': {
    'field_name': '密码',
    'is_enabled': False,  # 不记录密码变更
    'is_important': True,
}
```

### 2. 权限控制
- **查看权限**：只有授权用户才能查看审计日志
- **操作权限**：审计日志只能查看，不能修改或删除
- **数据隔离**：多租户环境下的数据隔离

### 3. 数据完整性
- **防篡改**：审计日志一旦生成不可修改
- **备份策略**：重要审计数据定期备份
- **审计审计**：对审计系统本身进行监控

## 🚀 扩展性设计

### 1. 插件化架构
```python
class CustomRecordHandler:
    def handle_field_change(self, field_name, old_value, new_value):
        # 自定义字段处理逻辑
        pass

# 注册自定义处理器
audit_config_manager.register_handler('MyModel', CustomRecordHandler)
```

### 2. 多存储支持
- **数据库存储**：默认的关系型数据库存储
- **文件存储**：大量数据的文件系统存储
- **云存储**：AWS S3、阿里云OSS等云存储
- **时序数据库**：InfluxDB等时序数据库

### 3. 多格式输出
- **JSON格式**：默认的结构化格式
- **XML格式**：企业级系统集成
- **CSV格式**：数据分析和报表
- **自定义格式**：通过插件支持任意格式

## 📈 监控和运维

### 1. 性能监控
- **处理延迟**：审计日志处理时间监控
- **队列长度**：异步任务队列长度监控
- **错误率**：审计日志记录失败率监控

### 2. 容量规划
- **存储增长**：审计日志存储空间增长趋势
- **查询负载**：审计日志查询QPS监控
- **清理策略**：过期数据自动清理

### 3. 告警机制
- **处理失败**：审计日志记录失败告警
- **队列积压**：异步任务队列积压告警
- **存储告警**：存储空间不足告警

## 🔄 未来规划

### 1. 功能增强
- **实时推送**：WebSocket实时推送审计事件
- **智能分析**：基于AI的异常行为检测
- **可视化**：审计数据的图表化展示

### 2. 性能提升
- **流式处理**：Kafka等流式处理框架
- **分布式存储**：Elasticsearch等分布式搜索引擎
- **边缘计算**：边缘节点的审计数据预处理

### 3. 生态集成
- **第三方集成**：与企业现有审计系统集成
- **标准化**：支持行业标准的审计格式
- **云原生**：Kubernetes等云原生环境支持
