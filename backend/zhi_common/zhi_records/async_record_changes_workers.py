"""
异步记录变更工具

提供异步和同步的记录变更功能，支持：
- 异步记录模型变更到 AuditLog 模型
- 异步记录操作日志
- 使用新的日志SDK
- 支持Celery异步任务
"""

import asyncio
from typing import Dict, Any, Optional
from asgiref.sync import sync_to_async, async_to_sync
from django.conf import settings
from django.utils import timezone

from zhi_common.zhi_logger import get_logger
from zhi_common.zhi_records.core_records_change import ZhiRecordChanges

# 创建专用的日志记录器
logger = get_logger("audit_logger")


def _get_audit_log_model():
    """
    动态获取 AuditLog 模型，避免循环导入
    """
    try:
        from zhi_logger.models import AuditLog
        return AuditLog
    except ImportError:
        logger.warning("无法导入 AuditLog 模型，审计日志将不会保存到数据库")
        return None


async def async_record_changes(
    model_info: Dict[str, Any],
    user_info: Dict[str, Any],
    previous_instance: Optional[Dict[str, Any]] = None,
    current_instance: Optional[Dict[str, Any]] = None
):
    """
    异步记录模型变更到 AuditLog 模型

    Args:
        model_info: 模型信息
        user_info: 用户信息
        previous_instance: 变更前的实例数据
        current_instance: 变更后的实例数据
    """
    try:
        # 1. 使用原有的记录变更处理器
        record_handler = ZhiRecordChanges(
            model_info=model_info,
            user_info=user_info,
            previous_instance=previous_instance,
            current_instance=current_instance
        )

        # 异步执行记录变更（这已经包含了保存到AuditLog的逻辑）
        await sync_to_async(record_handler.record_changes)()

        # 3. 记录到日志SDK
        action = "创建" if not previous_instance else "更新"
        model_name = f"{model_info['model_info']['app_label']}.{model_info['model_info']['object_name']}"

        # 安全获取资源ID
        resource_id = 'unknown'
        if current_instance and isinstance(current_instance, dict):
            resource_id = current_instance.get('id', 'unknown')
        elif previous_instance and isinstance(previous_instance, dict):
            resource_id = previous_instance.get('id', 'unknown')

        logger.info(
            f"模型变更记录: {model_name} {action}",
            category="audit",
            user_id=user_info.get('creator_id'),
            extra_data={
                'model': model_name,
                'action': action,
                'resource_id': resource_id,
                'has_changes': bool(previous_instance),
                'user_info': user_info
            }
        )

    except Exception as e:
        logger.error(
            f"异步记录模型变更失败: {str(e)}",
            category="audit",
            extra_data={
                'model_info': model_info,
                'error': str(e)
            }
        )
        raise


async def _save_to_audit_log(
    model_info: Dict[str, Any],
    user_info: Dict[str, Any],
    previous_instance: Optional[Dict[str, Any]] = None,
    current_instance: Optional[Dict[str, Any]] = None
):
    """
    保存审计日志到 AuditLog 模型

    Args:
        model_info: 模型信息
        user_info: 用户信息
        previous_instance: 变更前的实例数据
        current_instance: 变更后的实例数据
    """
    try:
        AuditLog = _get_audit_log_model()
        if not AuditLog:
            return

        # 确定操作类型
        if not previous_instance and current_instance:
            action = "创建"
            old_values = None
            new_values = current_instance
        elif previous_instance and current_instance:
            action = "修改"
            old_values = previous_instance
            new_values = current_instance
        elif previous_instance and not current_instance:
            action = "删除"
            old_values = previous_instance
            new_values = None
        else:
            return  # 无效的操作

        # 获取资源ID
        resource_id = (current_instance or previous_instance or {}).get('id', 'unknown')

        # 使用 AuditLog 的异步创建方法
        audit_log = await AuditLog.async_create_audit_log(
            resource_id=str(resource_id),
            action=action,
            old_values=old_values,
            new_values=new_values,
            creator_id=user_info.get('creator_id'),
            creator_name=user_info.get('creator_name', '')
        )

        logger.debug(
            f"审计日志已保存到 AuditLog: {action} {resource_id}",
            category="audit",
            extra_data={
                'audit_log_id': audit_log.id,
                'resource_id': resource_id,
                'action': action
            }
        )

    except Exception as e:
        logger.error(
            f"保存审计日志到 AuditLog 失败: {str(e)}",
            category="audit",
            extra_data={
                'model_info': model_info,
                'error': str(e)
            }
        )
        # 不抛出异常，避免影响主业务流程


def record_changes(
    model_info: Dict[str, Any],
    user_info: Dict[str, Any],
    previous_instance: Optional[Dict[str, Any]] = None,
    current_instance: Optional[Dict[str, Any]] = None
):
    """
    同步记录模型变更（兼容现有代码）
    
    根据配置决定是否使用异步任务
    """
    try:
        # 检查是否启用Celery异步任务
        if _should_use_celery():
            # 使用Celery异步任务
            _record_changes_task.delay(
                model_info=model_info,
                user_info=user_info,
                previous_instance=previous_instance,
                current_instance=current_instance
            )
        else:
            # 直接异步执行
            try:
                # 尝试在现有事件循环中执行
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行，创建任务
                    asyncio.create_task(async_record_changes(
                        model_info, user_info, previous_instance, current_instance
                    ))
                else:
                    # 如果没有事件循环，同步执行
                    async_to_sync(async_record_changes)(
                        model_info, user_info, previous_instance, current_instance
                    )
            except RuntimeError:
                # 如果无法获取事件循环，同步执行
                async_to_sync(async_record_changes)(
                    model_info, user_info, previous_instance, current_instance
                )
                
    except Exception as e:
        logger.error(
            f"记录模型变更失败: {str(e)}",
            category="audit",
            extra_data={
                'model_info': model_info,
                'error': str(e)
            }
        )


async def async_record_log_action(
    action: str,
    user_info: Dict[str, Any],
    resource_id: str,
    extra_data: Optional[Dict[str, Any]] = None
):
    """
    异步记录操作日志到 AuditLog 模型

    Args:
        action: 操作类型
        user_info: 用户信息
        resource_id: 资源ID
        extra_data: 额外数据
    """
    try:
        # 1. 使用ZhiRecordChanges的静态方法记录操作
        await sync_to_async(ZhiRecordChanges.log_action)(
            action=action,
            user_info=user_info,
            resource_id=resource_id,
            changes=extra_data or {}
        )

        # 2. 保存到 AuditLog 模型
        await _save_action_to_audit_log(
            action=action,
            user_info=user_info,
            resource_id=resource_id,
            extra_data=extra_data
        )

        # 3. 记录到日志SDK
        logger.info(
            f"操作日志记录: {action}",
            category="audit",
            user_id=user_info.get('creator_id'),
            extra_data={
                'action': action,
                'resource_id': resource_id,
                'user_info': user_info,
                'extra_data': extra_data
            }
        )

    except Exception as e:
        logger.error(
            f"异步记录操作日志失败: {str(e)}",
            category="audit",
            extra_data={
                'action': action,
                'resource_id': resource_id,
                'error': str(e)
            }
        )
        raise


async def _save_action_to_audit_log(
    action: str,
    user_info: Dict[str, Any],
    resource_id: str,
    extra_data: Optional[Dict[str, Any]] = None
):
    """
    保存操作日志到 AuditLog 模型

    Args:
        action: 操作类型
        user_info: 用户信息
        resource_id: 资源ID
        extra_data: 额外数据
    """
    try:
        AuditLog = _get_audit_log_model()
        if not AuditLog:
            return

        # 使用 AuditLog 的异步创建方法
        audit_log = await AuditLog.async_create_audit_log(
            resource_id=str(resource_id),
            action=action,
            old_values=None,  # 操作日志通常不记录旧值
            new_values=extra_data,  # 将额外数据作为新值
            creator_id=user_info.get('creator_id'),
            creator_name=user_info.get('creator_name', '')
        )

        logger.debug(
            f"操作日志已保存到 AuditLog: {action} {resource_id}",
            category="audit",
            extra_data={
                'audit_log_id': audit_log.id,
                'resource_id': resource_id,
                'action': action
            }
        )

    except Exception as e:
        logger.error(
            f"保存操作日志到 AuditLog 失败: {str(e)}",
            category="audit",
            extra_data={
                'action': action,
                'resource_id': resource_id,
                'error': str(e)
            }
        )
        # 不抛出异常，避免影响主业务流程


def record_log_action(
    action: str,
    user_info: Dict[str, Any],
    resource_id: str,
    extra_data: Optional[Dict[str, Any]] = None
):
    """
    同步记录操作日志（兼容现有代码）
    """
    try:
        # 检查是否启用Celery异步任务
        if _should_use_celery():
            # 使用Celery异步任务
            _record_log_action_task.delay(
                action=action,
                user_info=user_info,
                resource_id=resource_id,
                extra_data=extra_data
            )
        else:
            # 直接异步执行
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(async_record_log_action(
                        action, user_info, resource_id, extra_data
                    ))
                else:
                    async_to_sync(async_record_log_action)(
                        action, user_info, resource_id, extra_data
                    )
            except RuntimeError:
                async_to_sync(async_record_log_action)(
                    action, user_info, resource_id, extra_data
                )
                
    except Exception as e:
        logger.error(
            f"记录操作日志失败: {str(e)}",
            category="audit",
            extra_data={
                'action': action,
                'resource_id': resource_id,
                'error': str(e)
            }
        )


def _should_use_celery() -> bool:
    """检查是否应该使用Celery异步任务"""
    try:
        # 检查Celery配置
        if not hasattr(settings, 'CELERY_BROKER_URL'):
            return False
            
        # 检查是否在测试环境
        if hasattr(settings, 'TESTING') and settings.TESTING:
            return False
            
        # 检查是否显式禁用
        if hasattr(settings, 'USE_CELERY_FOR_AUDIT') and not settings.USE_CELERY_FOR_AUDIT:
            return False
            
        return True
    except Exception:
        return False


# Celery任务定义（如果Celery可用）
try:
    from celery import shared_task
    
    @shared_task(bind=True, max_retries=3)
    def _record_changes_task(self, model_info, user_info, previous_instance=None, current_instance=None):
        """Celery任务：记录模型变更"""
        try:
            async_to_sync(async_record_changes)(
                model_info, user_info, previous_instance, current_instance
            )
        except Exception as e:
            logger.error(f"Celery任务记录变更失败: {str(e)}")
            # 重试任务
            if self.request.retries < self.max_retries:
                raise self.retry(countdown=60 * (self.request.retries + 1))
            raise
    
    @shared_task(bind=True, max_retries=3)
    def _record_log_action_task(self, action, user_info, resource_id, extra_data=None):
        """Celery任务：记录操作日志"""
        try:
            async_to_sync(async_record_log_action)(
                action, user_info, resource_id, extra_data
            )
        except Exception as e:
            logger.error(f"Celery任务记录操作失败: {str(e)}")
            # 重试任务
            if self.request.retries < self.max_retries:
                raise self.retry(countdown=60 * (self.request.retries + 1))
            raise
            
except ImportError:
    # Celery不可用，定义兼容的任务对象
    class MockCeleryTask:
        def delay(self, model_info, user_info, previous_instance=None, current_instance=None):
            """模拟Celery的delay方法，直接同步执行"""
            try:
                # 参数验证
                if not isinstance(model_info, dict):
                    logger.error(f"model_info 必须是字典类型，实际类型: {type(model_info)}, 值: {model_info}")
                    return

                if not isinstance(user_info, dict):
                    logger.error(f"user_info 必须是字典类型，实际类型: {type(user_info)}, 值: {user_info}")
                    return

                # 直接同步执行异步函数
                async_to_sync(async_record_changes)(
                    model_info, user_info, previous_instance, current_instance
                )

            except Exception as e:
                logger.error(f"模拟Celery任务执行失败: {str(e)}")

    class MockLogActionTask:
        def delay(self, action, user_info, resource_id, extra_data=None):
            """模拟Celery的delay方法，直接同步执行"""
            try:
                # 参数验证
                if not isinstance(user_info, dict):
                    logger.error(f"user_info 必须是字典类型，实际类型: {type(user_info)}, 值: {user_info}")
                    return

                # 直接同步执行异步函数
                async_to_sync(async_record_log_action)(
                    action, user_info, resource_id, extra_data
                )

            except Exception as e:
                logger.error(f"模拟Celery日志任务执行失败: {str(e)}")

    _record_changes_task = MockCeleryTask()
    _record_log_action_task = MockLogActionTask()
