# ZhiAdmin 审计日志记录系统

ZhiAdmin 的审计日志记录系统提供了完整的模型变更追踪功能，支持精细化字段记录、异步处理和多种存储方式。

## 🎯 设计理念

### 核心目标
- **精细化记录**：只记录实际变更的字段，避免冗余数据
- **结构化信息**：提供字段名、中文名称和值的结构化数据
- **前端友好**：便于前端统一展示变更历史
- **高性能**：异步处理，不影响主业务流程
- **安全可靠**：防止递归审计，确保系统稳定性

### 设计优势
1. **存储高效**：相比记录所有字段，存储空间节省90%以上
2. **查询性能**：结构化数据便于解析和展示
3. **用户体验**：支持"字段名：旧值 → 新值"的友好格式
4. **审计完整性**：既能追踪变更历史，又能准确标识资源
5. **错误恢复**：完善的异常处理和重试机制

## 📊 数据格式

### 审计日志格式
```json
{
  "resource_id": "user-123456",
  "action": "更新",
  "old_values": [
    {
      "field_name": "last_login",
      "field_cn_name": "上次登录", 
      "value": "2025-07-25 11:43:14"
    }
  ],
  "new_values": [
    {
      "field_name": "last_login",
      "field_cn_name": "上次登录",
      "value": "2025-07-25 13:20:23"
    }
  ],
  "creator_id": "admin",
  "creator_name": "管理员",
  "created_at": "2025-07-25 13:20:23"
}
```

### 字段说明
- `field_name`: 数据库字段名
- `field_cn_name`: 字段的中文显示名称（从model的verbose_name获取）
- `value`: 字段的实际值
- `resource_id`: 资源的唯一标识符
- `action`: 操作类型（创建/更新/删除）

## 🚀 使用方法

### 1. 模型继承
```python
from zhi_common.zhi_model.core_model import ZhiCoreModel

class MyModel(ZhiCoreModel):
    name = models.CharField(max_length=100, verbose_name="名称")
    status = models.CharField(max_length=20, verbose_name="状态")
    
    class Meta:
        db_table = 'my_model'
```

### 2. 审计配置
在应用目录下创建 `audit_config.py`：

```python
# backend/your_app/audit_config.py

AUDIT_LOG_CONFIG = {
    'MyModel': {
        'is_enabled': True,
        'using_fields': {
            'name': {
                'field_name': '名称',
                'is_enabled': True,
                'is_important': True,
            },
            'status': {
                'field_name': '状态',
                'is_enabled': True,
                'is_important': True,
            },
            'password': {
                'field_name': '密码',
                'is_enabled': False,  # 敏感字段不记录
                'is_important': True,
            },
        }
    },
}
```

### 3. 自动记录
继承 `ZhiCoreModel` 的模型会自动记录审计日志：

```python
# 创建记录
instance = MyModel.objects.create(name="测试", status="active")
# 自动记录：action="创建"

# 更新记录  
instance.status = "inactive"
instance.save()
# 自动记录：action="更新"，只记录status字段的变更

# 删除记录
instance.delete()
# 自动记录：action="删除"
```

### 4. 手动记录
```python
from zhi_common.zhi_records.async_record_changes_workers import record_changes

record_changes(
    model_info={
        'model_info': {'app_label': 'myapp', 'object_name': 'MyModel'},
        'field_names': {'name': '名称', 'status': '状态'}
    },
    user_info={'creator_id': 'user123', 'creator_name': '用户'},
    previous_instance={'id': '123', 'status': 'active'},
    current_instance={'id': '123', 'status': 'inactive'}
)
```

## ⚙️ 配置选项

### 全局配置
```python
# settings.py

# 是否启用Celery异步任务
USE_CELERY_FOR_AUDIT = True

# 审计日志保留天数
AUDIT_RETENTION_DAYS = 90

# 是否记录敏感字段
LOG_SENSITIVE_FIELDS = False
```

### 字段配置
```python
'field_name': {
    'field_name': '字段显示名称',
    'is_enabled': True,      # 是否记录此字段的变更
    'is_important': True,    # 是否为重要字段
    'type': 'enum',         # 字段类型（用于值转换）
    'enum_config': {        # 枚举值配置
        '1': '启用',
        '0': '禁用'
    }
}
```

## 🔧 高级功能

### 异步处理
系统支持两种异步处理方式：

1. **Celery任务**（推荐）
```python
# 自动使用Celery异步任务
USE_CELERY_FOR_AUDIT = True
```

2. **asyncio异步**
```python
# 使用Python原生异步
USE_CELERY_FOR_AUDIT = False
```

### 核心类和方法

#### ZhiRecordChanges 类
```python
from zhi_common.zhi_records.core_records_change import ZhiRecordChanges

# 创建记录器实例
record_handler = ZhiRecordChanges(
    model_info=model_info,
    user_info=user_info,
    previous_instance=previous_instance,
    current_instance=current_instance
)

# 记录变更
record_handler.record_changes()

# 安全获取资源ID
resource_id = record_handler._get_safe_resource_id()
```

#### 关键方法说明
- `record_changes()`: 主要记录方法，检测字段变更并生成审计日志
- `_get_safe_resource_id()`: 安全获取资源ID，防止KeyError
- `log_action()`: 静态方法，将审计日志保存到数据库
- `get_field_verbose_name()`: 获取字段的中文显示名称

### 自定义字段处理
```python
class MyModel(ZhiCoreModel):
    def get_model_info(self):
        info = super().get_model_info()
        # 自定义字段信息
        info['field_names']['custom_field'] = '自定义字段'
        return info
```

### 禁用审计日志
```python
class MyModel(ZhiCoreModel):
    def is_audit_log_enabled(self):
        return False  # 禁用此模型的审计日志
```

## 📈 性能优化

### 1. 字段过滤
系统自动过滤不需要记录的公共字段：
```python
NONE_RECORD_FIELD_NAME = [
    'id', 'seq', 'creator_id', 'modifier_id', 
    'creator_name', 'modifier_name', 'org_id',
    'updated_at', 'created_at'
]
```

### 2. 异步处理
- 审计日志记录不会阻塞主业务流程
- 支持Celery分布式任务队列
- 失败重试机制

### 3. 存储优化
- 只记录变更字段，大幅减少存储空间
- JSON格式存储，便于查询和解析
- 支持数据压缩和归档

## 🛠️ 故障排除

### 常见问题

#### 1. KeyError('id') 错误
**原因**：资源ID获取失败
**解决**：系统已自动修复，确保传递完整的实例数据

#### 2. 审计日志不记录
**检查项**：
- 模型是否继承 `ZhiCoreModel`
- `audit_config.py` 配置是否正确
- `is_enabled` 是否为 `True`

#### 3. 字段显示名称不正确
**解决**：检查模型字段的 `verbose_name` 或审计配置中的 `field_name`

#### 4. 异步任务失败
**检查项**：
- Celery服务是否正常运行
- 数据库连接是否正常
- 查看Celery日志获取详细错误信息

### 调试方法
```python
# 启用调试日志
import logging
logging.getLogger('audit_logger').setLevel(logging.DEBUG)

# 检查配置
from zhi_common.zhi_model.audit_config_manager import audit_config_manager
config = audit_config_manager.get_model_config('app.Model')
print(config)
```

## 📚 API 接口

### 审计日志查询
```bash
# 获取审计日志列表
GET /api/logger/audit-logs/

# 获取资源变更历史
GET /api/logger/audit-logs/resource/{resource_id}/

# 获取用户操作历史  
GET /api/logger/audit-logs/user/{creator_id}/

# 获取统计信息
GET /api/logger/audit-logs/stats?days=7
```

### 响应格式
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

## 🔒 安全考虑

### 敏感字段处理
```python
# 敏感字段不记录变更
'password': {
    'field_name': '密码',
    'is_enabled': False,
    'is_important': True,
}
```

### 权限控制
- 审计日志查看需要相应权限
- 支持按用户、角色进行访问控制
- 敏感操作单独标记和管理

## 📋 最佳实践

1. **合理配置字段**：只记录业务关键字段的变更
2. **定期清理**：设置合理的日志保留期限
3. **监控性能**：关注异步任务的执行情况
4. **安全审计**：定期检查敏感操作日志
5. **备份策略**：重要审计日志需要定期备份

## 🔄 版本历史

- **v1.0.0** - 基础审计日志功能
- **v1.1.0** - 添加异步处理支持
- **v1.2.0** - 优化存储格式，添加字段中文名
- **v1.3.0** - 修复资源ID获取问题，保持精细化记录
- **v1.4.0** - 重大修复版本：
  - 🔧 修复 KeyError('id') 错误
  - 🔧 修复 AuditLog 参数错误
  - 🔧 解决重复记录问题
  - 🔧 防止 AuditLog 模型无限递归审计
  - 🔧 优化字符串ID支持
  - 🔧 增强异常处理和错误恢复
