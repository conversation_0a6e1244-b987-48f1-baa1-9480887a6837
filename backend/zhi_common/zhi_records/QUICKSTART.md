# ZhiAdmin 审计日志系统快速开始指南

## 🚀 5分钟快速上手

### 步骤1：模型继承
将您的模型继承自 `ZhiCoreModel`：

```python
# models.py
from zhi_common.zhi_model.core_model import ZhiCoreModel

class Product(ZhiCoreModel):
    name = models.CharField(max_length=100, verbose_name="产品名称")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格")
    status = models.CharField(max_length=20, verbose_name="状态")
    description = models.TextField(verbose_name="描述", blank=True)

    class Meta:
        db_table = 'product'
        verbose_name = "产品"

    # 可选：自定义审计行为
    def is_audit_log_enabled(self):
        """控制是否启用审计日志"""
        return True  # 默认启用，可根据业务需求调整
```

### 步骤2：创建审计配置
在您的应用目录下创建 `audit_config.py`：

```python
# myapp/audit_config.py
AUDIT_LOG_CONFIG = {
    'Product': {
        'is_enabled': True,
        'using_fields': {
            'name': {
                'field_name': '产品名称',
                'is_enabled': True,
                'is_important': True,
            },
            'price': {
                'field_name': '价格',
                'is_enabled': True,
                'is_important': True,
            },
            'status': {
                'field_name': '状态',
                'is_enabled': True,
                'is_important': True,
                'type': 'enum',
                'enum_config': {
                    'active': '启用',
                    'inactive': '禁用'
                }
            },
            'description': {
                'field_name': '描述',
                'is_enabled': False,  # 不记录描述变更
                'is_important': False,
            },
        }
    },
}
```

### 步骤3：测试审计功能
```python
# 创建产品
product = Product.objects.create(
    name="iPhone 15",
    price=999.99,
    status="active"
)

# 修改产品
product.price = 899.99
product.status = "inactive"
product.save()

# 查看审计日志
from zhi_logger.models import AuditLog
logs = AuditLog.objects.filter(resource_id=product.id)
for log in logs:
    print(f"操作: {log.action}")
    print(f"变更: {log.new_values}")
```

### 步骤4：查看结果
审计日志将显示如下格式：
```json
{
  "action": "更新",
  "old_values": [
    {"field_name": "price", "field_cn_name": "价格", "value": "999.99"},
    {"field_name": "status", "field_cn_name": "状态", "value": "启用"}
  ],
  "new_values": [
    {"field_name": "price", "field_cn_name": "价格", "value": "899.99"},
    {"field_name": "status", "field_cn_name": "状态", "value": "禁用"}
  ]
}
```

## 📋 常用配置模板

### 用户模型配置
```python
'User': {
    'is_enabled': True,
    'using_fields': {
        'username': {
            'field_name': '用户名',
            'is_enabled': True,
            'is_important': True,
        },
        'email': {
            'field_name': '邮箱',
            'is_enabled': True,
            'is_important': True,
        },
        'is_active': {
            'field_name': '是否激活',
            'is_enabled': True,
            'is_important': True,
            'type': 'enum',
            'enum_config': {
                True: '激活',
                False: '禁用'
            }
        },
        'password': {
            'field_name': '密码',
            'is_enabled': False,  # 敏感字段不记录
            'is_important': True,
        },
        'last_login': {
            'field_name': '上次登录',
            'is_enabled': True,
            'is_important': False,
        },
    }
},
```

### 订单模型配置
```python
'Order': {
    'is_enabled': True,
    'using_fields': {
        'order_no': {
            'field_name': '订单号',
            'is_enabled': True,
            'is_important': True,
        },
        'total_amount': {
            'field_name': '总金额',
            'is_enabled': True,
            'is_important': True,
        },
        'status': {
            'field_name': '订单状态',
            'is_enabled': True,
            'is_important': True,
            'type': 'enum',
            'enum_config': {
                'pending': '待付款',
                'paid': '已付款',
                'shipped': '已发货',
                'delivered': '已送达',
                'cancelled': '已取消'
            }
        },
        'payment_method': {
            'field_name': '支付方式',
            'is_enabled': True,
            'is_important': True,
            'type': 'enum',
            'enum_config': {
                'alipay': '支付宝',
                'wechat': '微信支付',
                'bank': '银行卡'
            }
        },
    }
},
```

## 🔧 高级配置示例

### 字典值转换配置
```python
'Department': {
    'is_enabled': True,
    'using_fields': {
        'level': {
            'field_name': '部门级别',
            'is_enabled': True,
            'is_important': True,
            'type': 'dictionary',
            'dictionary_config': {
                'dictionary_name': 'department_level',
                'default': '未知级别'
            }
        },
        'parent_id': {
            'field_name': '上级部门',
            'is_enabled': True,
            'is_important': True,
            'type': 'foreign_key',
            'foreign_key_config': {
                'model': 'Department',
                'display_field': 'name',
                'default': '无上级部门'
            }
        },
    }
},
```

### 条件启用配置
```python
class SensitiveModel(ZhiCoreModel):
    def is_audit_log_enabled(self):
        # 只有管理员操作才记录审计日志
        from zhi_common.zhi_tools.g_local_thread_user_info import get_request_user_info
        user_info = get_request_user_info()
        return user_info.get('is_superuser', False)
```

## 🎯 使用场景示例

### 场景1：电商系统商品管理
```python
# 商品价格调整审计
product = Product.objects.get(id=123)
old_price = product.price

product.price = 799.99  # 降价促销
product.save()

# 自动记录：价格从 999.99 调整为 799.99
```

### 场景2：用户权限变更审计
```python
# 用户权限提升审计
user = User.objects.get(username='john')
user.is_staff = True
user.is_superuser = True
user.save()

# 自动记录：用户权限变更历史
```

### 场景3：订单状态流转审计
```python
# 订单状态变更审计
order = Order.objects.get(order_no='ORD20250725001')
order.status = 'shipped'
order.tracking_no = 'SF1234567890'
order.save()

# 自动记录：订单状态从"已付款"变更为"已发货"
```

## 📊 前端展示示例

### Vue.js 组件示例
```vue
<template>
  <div class="audit-log">
    <h3>变更历史</h3>
    <div v-for="log in auditLogs" :key="log.id" class="log-item">
      <div class="log-header">
        <span class="action">{{ log.action }}</span>
        <span class="time">{{ formatTime(log.created_at) }}</span>
        <span class="user">{{ log.creator_name }}</span>
      </div>
      <div class="log-changes">
        <div v-for="change in getChanges(log)" :key="change.field_name" class="change-item">
          <span class="field-name">{{ change.field_cn_name }}:</span>
          <span class="old-value">{{ change.old_value }}</span>
          <span class="arrow">→</span>
          <span class="new-value">{{ change.new_value }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      auditLogs: []
    }
  },
  methods: {
    getChanges(log) {
      const changes = [];
      if (log.old_values && log.new_values) {
        for (let i = 0; i < log.old_values.length; i++) {
          changes.push({
            field_name: log.old_values[i].field_name,
            field_cn_name: log.old_values[i].field_cn_name,
            old_value: log.old_values[i].value,
            new_value: log.new_values[i].value
          });
        }
      }
      return changes;
    },
    formatTime(time) {
      return new Date(time).toLocaleString();
    }
  }
}
</script>
```

### React 组件示例
```jsx
import React from 'react';

const AuditLog = ({ resourceId }) => {
  const [logs, setLogs] = useState([]);

  useEffect(() => {
    fetchAuditLogs(resourceId).then(setLogs);
  }, [resourceId]);

  const renderChanges = (log) => {
    if (!log.old_values || !log.new_values) return null;
    
    return log.old_values.map((oldVal, index) => {
      const newVal = log.new_values[index];
      return (
        <div key={oldVal.field_name} className="change-item">
          <span className="field-name">{oldVal.field_cn_name}:</span>
          <span className="old-value">{oldVal.value}</span>
          <span className="arrow">→</span>
          <span className="new-value">{newVal.value}</span>
        </div>
      );
    });
  };

  return (
    <div className="audit-log">
      <h3>变更历史</h3>
      {logs.map(log => (
        <div key={log.id} className="log-item">
          <div className="log-header">
            <span className="action">{log.action}</span>
            <span className="time">{new Date(log.created_at).toLocaleString()}</span>
            <span className="user">{log.creator_name}</span>
          </div>
          <div className="log-changes">
            {renderChanges(log)}
          </div>
        </div>
      ))}
    </div>
  );
};
```

## 🔍 调试技巧

### 快速检查审计是否工作
```python
# 在Django shell中测试
python manage.py shell

>>> from myapp.models import Product
>>> from zhi_logger.models import AuditLog

# 创建测试数据
>>> p = Product.objects.create(name="测试产品", price=100)
>>> print(f"产品ID: {p.id}")

# 修改数据
>>> p.price = 200
>>> p.save()

# 等待异步任务完成（如果使用Celery）
>>> import time
>>> time.sleep(2)

# 检查审计日志
>>> logs = AuditLog.objects.filter(resource_id=str(p.id))  # 注意转换为字符串
>>> for log in logs:
...     print(f"操作: {log.action}")
...     print(f"变更: {log.new_values}")
...     print(f"格式: {type(log.new_values)}")  # 应该是list类型
```

### 验证修复效果
```python
# 检查是否还有KeyError('id')问题
>>> from zhi_common.zhi_records.core_records_change import ZhiRecordChanges
>>>
>>> # 模拟测试数据
>>> model_info = {
...     'model_info': {'app_label': 'myapp', 'object_name': 'Product'},
...     'field_names': {'name': '产品名称', 'price': '价格'}
... }
>>> user_info = {'creator_id': 'test', 'creator_name': '测试用户'}
>>> current_instance = {'id': 'test-123', 'name': '产品', 'price': 100}
>>>
>>> handler = ZhiRecordChanges(model_info, user_info, None, current_instance)
>>> resource_id = handler._get_safe_resource_id()
>>> print(f"资源ID: {resource_id}")  # 应该显示 'test-123' 而不是 'unknown'
```

### 检查配置是否生效
```python
from zhi_common.zhi_model.audit_config_manager import audit_config_manager

# 检查模型是否启用审计
model_path = 'myapp.Product'
print(f"审计启用状态: {audit_config_manager.is_model_audit_enabled(model_path)}")

# 检查字段配置
using_fields = audit_config_manager.get_model_using_fields(model_path)
print(f"记录字段: {using_fields}")
```

## 📞 获取帮助

如果遇到问题，请按以下顺序排查：

1. **查看日志**：检查应用日志和Celery日志
2. **检查配置**：确认模型继承和配置文件正确
3. **测试功能**：使用上述调试代码测试基本功能
4. **查看文档**：参考 `TROUBLESHOOTING.md` 详细排查
5. **联系支持**：提供错误日志和配置信息

## 🎉 恭喜！

您已经成功配置了 ZhiAdmin 审计日志系统！现在您可以：

- ✅ 自动记录模型变更历史
- ✅ 查看结构化的变更信息
- ✅ 在前端展示友好的变更记录
- ✅ 满足企业级审计要求

继续探索更多高级功能，让您的应用更加完善！
