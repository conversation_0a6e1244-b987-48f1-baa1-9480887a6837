# ZhiAdmin 审计日志系统故障排除指南

## 🚨 常见问题及解决方案

### 1. KeyError('id') 错误 ✅ 已修复

**错误现象**：
```
KeyError: 'id'
File "zhi_common/zhi_records/core_records_change.py", line 224
zhi_logger.info(f'<action:{action}><resource_id:{self.current_instance["id"]}>')
```

**根本原因**：
- `to_dict_no_pub_fields` 方法过滤掉了 `id` 字段
- 审计记录器无法获取资源ID进行标识

**解决方案**：
系统已自动修复，采用两层防护：

1. **在模型层单独添加ID字段**：
```python
# 修复后的代码
current_instance_data = self.to_dict_no_pub_fields.copy()
current_instance_data['id'] = str(self.id)  # 支持字符串ID
```

2. **添加安全的资源ID获取方法**：
```python
def _get_safe_resource_id(self):
    """安全获取资源ID，支持多种边界情况"""
    try:
        # 优先从current_instance获取
        if self.current_instance and isinstance(self.current_instance, dict):
            resource_id = self.current_instance.get('id')
            if resource_id is not None:
                return str(resource_id)

        # 备用方案：从previous_instance获取
        if self.previous_instance and isinstance(self.previous_instance, dict):
            resource_id = self.previous_instance.get('id')
            if resource_id is not None:
                return str(resource_id)

        return 'unknown'
    except Exception as e:
        zhi_logger.warning(f'获取资源ID失败: {str(e)}')
        return 'unknown'
```

**验证修复**：
```python
# 检查修复是否生效
from zhi_common.zhi_records.core_records_change import ZhiRecordChanges

handler = ZhiRecordChanges(...)
resource_id = handler._get_safe_resource_id()
print(f"资源ID: {resource_id}")  # 应该显示正确的ID而不是'unknown'
```

### 2. AuditLog() got unexpected keyword arguments: 'modifier' ✅ 已修复

**错误现象**：
```
TypeError: AuditLog() got unexpected keyword arguments: 'modifier'
```

**根本原因**：
- `user_info` 包含了 `AuditLog` 模型不支持的字段（如 `modifier`）
- 直接使用 `**user_info` 传递参数导致错误

**解决方案**：
系统已修复，安全提取用户信息：
```python
@staticmethod
def log_action(action, user_info, resource_id, changes=None):
    """将变更记录到日志或其他存储方式中"""
    from zhi_logger.models import AuditLog
    if not changes:
        changes = {}

    # 安全提取用户信息，只传递AuditLog模型支持的字段
    audit_user_info = {}
    if user_info:
        if 'creator_id' in user_info:
            audit_user_info['creator_id'] = user_info['creator_id']
        if 'creator_name' in user_info:
            audit_user_info['creator_name'] = user_info['creator_name']
        # 注意：不传递 'modifier' 字段，因为AuditLog模型没有这个字段

    AuditLog.objects.create(
        action=action,
        old_values=changes.get('old_values', []),
        new_values=changes.get('new_values', []),
        resource_id=resource_id,
        **audit_user_info
    )
```

### 3. 重复记录审计日志 ✅ 已修复

**错误现象**：
单次操作产生多条审计日志记录，且格式不一致

**根本原因**：
- 多个地方同时记录审计日志
- `AuditLog` 模型自身也会触发审计日志记录，导致无限递归

**解决方案**：
1. **移除重复的记录调用**：
```python
# 修复前：多个地方记录
await sync_to_async(record_handler.record_changes)()  # 第一次记录
await _save_to_audit_log(...)  # 第二次记录（已移除）

# 修复后：只保留一个记录点
await sync_to_async(record_handler.record_changes)()  # 只记录一次
```

2. **禁用AuditLog模型自身的审计**：
```python
class AuditLog(ZhiCoreModel):
    # ... 字段定义 ...

    def is_audit_log_enabled(self):
        """禁用AuditLog模型自身的审计日志记录，避免无限递归"""
        return False
```

### 4. 审计日志格式异常 ✅ 已修复

**问题现象**：
审计日志显示为原始字典而不是结构化格式

**错误格式**：
```json
{
  "old_values": {"id": 123, "name": "张三", "status": "active", ...},
  "new_values": {"id": 123, "name": "李四", "status": "active", ...}
}
```

**正确格式**：
```json
{
  "old_values": [{"field_name": "name", "field_cn_name": "姓名", "value": "张三"}],
  "new_values": [{"field_name": "name", "field_cn_name": "姓名", "value": "李四"}]
}
```

**解决方案**：
确保使用正确的数据传递方式：
```python
# ✅ 正确：使用 to_dict_no_pub_fields + id
current_instance_data = self.to_dict_no_pub_fields.copy()
current_instance_data['id'] = str(self.id)

# ❌ 错误：直接使用 to_dict（会产生完整字典格式）
current_instance_data = self.to_dict
```

### 5. 审计日志不记录

**可能原因及解决方案**：

#### 3.1 模型未继承 ZhiCoreModel
```python
# 错误示例
class MyModel(models.Model):  # ❌ 未继承 ZhiCoreModel
    name = models.CharField(max_length=100)

# 正确示例
class MyModel(ZhiCoreModel):  # ✅ 继承 ZhiCoreModel
    name = models.CharField(max_length=100)
```

#### 3.2 审计配置未启用
```python
# 检查配置
from zhi_common.zhi_model.audit_config_manager import audit_config_manager

model_path = 'myapp.MyModel'
is_enabled = audit_config_manager.is_model_audit_enabled(model_path)
print(f"审计是否启用: {is_enabled}")

# 如果未启用，检查 audit_config.py
AUDIT_LOG_CONFIG = {
    'MyModel': {
        'is_enabled': True,  # ✅ 确保为 True
        'using_fields': {...}
    }
}
```

#### 3.3 模型重写了审计方法
```python
class MyModel(ZhiCoreModel):
    def is_audit_log_enabled(self):
        return False  # ❌ 禁用了审计日志
        
    # 修复：移除此方法或返回 True
    def is_audit_log_enabled(self):
        return True  # ✅ 启用审计日志
```

### 4. Celery任务失败

**错误现象**：
```
[ERROR] Task zhi_common.zhi_records.async_record_changes_workers._record_changes_task raised unexpected: ...
```

**排查步骤**：

#### 4.1 检查Celery服务状态
```bash
# 检查Celery worker是否运行
celery -A your_project worker --loglevel=info

# 检查任务队列状态
celery -A your_project inspect active
```

#### 4.2 检查数据库连接
```python
# 测试数据库连接
from django.db import connection
try:
    connection.ensure_connection()
    print("数据库连接正常")
except Exception as e:
    print(f"数据库连接失败: {e}")
```

#### 4.3 检查任务参数
```python
# 检查传递给Celery任务的参数
def debug_task_params(model_info, user_info, previous_instance, current_instance):
    print(f"model_info: {type(model_info)} - {model_info}")
    print(f"user_info: {type(user_info)} - {user_info}")
    print(f"previous_instance: {type(previous_instance)} - {previous_instance}")
    print(f"current_instance: {type(current_instance)} - {current_instance}")
```

### 5. 审计日志格式异常

**问题现象**：
审计日志显示为原始字典而不是结构化格式

**错误格式**：
```json
{
  "old_values": {"id": 123, "name": "张三", "status": "active", ...},
  "new_values": {"id": 123, "name": "李四", "status": "active", ...}
}
```

**正确格式**：
```json
{
  "old_values": [{"field_name": "name", "field_cn_name": "姓名", "value": "张三"}],
  "new_values": [{"field_name": "name", "field_cn_name": "姓名", "value": "李四"}]
}
```

**解决方案**：
确保使用正确的数据传递方式：
```python
# ✅ 正确：使用 to_dict_no_pub_fields + id
current_instance_data = self.to_dict_no_pub_fields.copy()
current_instance_data['id'] = str(self.pk)

# ❌ 错误：直接使用 to_dict
current_instance_data = self.to_dict
```

## 🔧 调试工具和方法

### 1. 启用调试日志
```python
# settings.py
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'audit_logger': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'core_model': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}
```

### 2. 审计配置检查工具
```python
# 创建检查脚本：check_audit_config.py
from zhi_common.zhi_model.audit_config_manager import audit_config_manager

def check_model_audit_config(app_label, model_name):
    model_path = f"{app_label}.{model_name}"
    
    print(f"检查模型: {model_path}")
    print(f"是否启用审计: {audit_config_manager.is_model_audit_enabled(model_path)}")
    
    config = audit_config_manager.get_model_config(model_path)
    print(f"配置详情: {config}")
    
    using_fields = audit_config_manager.get_model_using_fields(model_path)
    print(f"记录字段: {using_fields}")

# 使用示例
check_model_audit_config('myapp', 'MyModel')
```

### 3. 审计日志测试工具
```python
# 创建测试脚本：test_audit_log.py
def test_audit_log_recording():
    from myapp.models import MyModel
    
    # 创建测试实例
    instance = MyModel.objects.create(name="测试", status="active")
    print(f"创建实例: {instance.id}")
    
    # 修改实例
    instance.name = "修改后的名称"
    instance.save()
    print("修改实例完成")
    
    # 检查审计日志
    from zhi_logger.models import AuditLog
    logs = AuditLog.objects.filter(resource_id=instance.id).order_by('-created_at')
    
    for log in logs:
        print(f"操作: {log.action}")
        print(f"旧值: {log.old_values}")
        print(f"新值: {log.new_values}")
        print("---")

test_audit_log_recording()
```

### 4. Celery任务监控
```python
# 监控Celery任务状态
from celery import current_app

def monitor_audit_tasks():
    inspect = current_app.control.inspect()
    
    # 活跃任务
    active_tasks = inspect.active()
    print(f"活跃任务: {active_tasks}")
    
    # 预定任务
    scheduled_tasks = inspect.scheduled()
    print(f"预定任务: {scheduled_tasks}")
    
    # 失败任务
    failed_tasks = inspect.failed()
    print(f"失败任务: {failed_tasks}")

monitor_audit_tasks()
```

## 📊 性能监控

### 1. 审计日志性能指标
```python
# 性能监控脚本
import time
from django.db import connection
from zhi_logger.models import AuditLog

def monitor_audit_performance():
    start_time = time.time()
    
    # 查询最近1小时的审计日志数量
    from django.utils import timezone
    from datetime import timedelta
    
    one_hour_ago = timezone.now() - timedelta(hours=1)
    count = AuditLog.objects.filter(created_at__gte=one_hour_ago).count()
    
    end_time = time.time()
    query_time = end_time - start_time
    
    print(f"最近1小时审计日志数量: {count}")
    print(f"查询耗时: {query_time:.3f}秒")
    print(f"数据库查询次数: {len(connection.queries)}")

monitor_audit_performance()
```

### 2. 存储空间监控
```python
def monitor_audit_storage():
    from django.db import connection
    
    with connection.cursor() as cursor:
        # 查询审计日志表大小（MySQL示例）
        cursor.execute("""
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size(MB)'
            FROM information_schema.tables 
            WHERE table_schema = DATABASE() 
            AND table_name LIKE '%audit_log%'
        """)
        
        results = cursor.fetchall()
        for table_name, size_mb in results:
            print(f"表 {table_name}: {size_mb} MB")

monitor_audit_storage()
```

## 🛠️ 维护和优化

### 1. 定期清理过期数据
```python
# 清理脚本：cleanup_audit_logs.py
from django.utils import timezone
from datetime import timedelta
from zhi_logger.models import AuditLog

def cleanup_old_audit_logs(days=90):
    cutoff_date = timezone.now() - timedelta(days=days)
    
    # 删除过期的审计日志
    deleted_count = AuditLog.objects.filter(
        created_at__lt=cutoff_date
    ).delete()[0]
    
    print(f"清理了 {deleted_count} 条过期审计日志")
    return deleted_count

# 设置定时任务执行清理
cleanup_old_audit_logs(days=90)
```

### 2. 数据库索引优化
```sql
-- 为审计日志表添加必要的索引
CREATE INDEX idx_audit_log_resource_created ON zhi_system_audit_log(resource_id, created_at DESC);
CREATE INDEX idx_audit_log_creator_created ON zhi_system_audit_log(creator_id, created_at DESC);
CREATE INDEX idx_audit_log_action_created ON zhi_system_audit_log(action, created_at DESC);
```

### 3. 配置优化建议
```python
# settings.py 优化配置

# Celery配置优化
CELERY_TASK_ROUTES = {
    'zhi_common.zhi_records.async_record_changes_workers.*': {
        'queue': 'audit_logs',  # 使用专用队列
        'routing_key': 'audit_logs',
    },
}

# 审计日志配置优化
AUDIT_LOG_CONFIG = {
    # 只记录重要字段，减少存储空间
    'batch_size': 100,  # 批量处理大小
    'max_retries': 3,   # 最大重试次数
    'retry_delay': 60,  # 重试延迟（秒）
}
```

## 📋 最佳实践检查清单

### ✅ 配置检查
- [ ] 模型继承了 `ZhiCoreModel`
- [ ] 创建了 `audit_config.py` 配置文件
- [ ] 配置中 `is_enabled` 为 `True`
- [ ] 敏感字段设置为 `is_enabled: False`
- [ ] Celery服务正常运行

### ✅ 性能检查
- [ ] 只记录必要的业务字段
- [ ] 设置了合理的日志保留期限
- [ ] 建立了必要的数据库索引
- [ ] 监控了异步任务队列状态

### ✅ 安全检查
- [ ] 敏感字段不记录变更
- [ ] 审计日志访问权限控制
- [ ] 定期备份重要审计数据
- [ ] 监控异常操作模式

### ✅ 运维检查
- [ ] 设置了性能监控告警
- [ ] 配置了日志清理策略
- [ ] 建立了故障恢复流程
- [ ] 定期检查系统健康状态
