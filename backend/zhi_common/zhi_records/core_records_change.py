from datetime import datetime, date
from django import apps
from decimal import Decimal

from zhi_common.zhi_model.audit_config_manager import audit_config_manager
from zhi_common.zhi_logger import zhi_logger
from application.dispatch import get_dictionary_values

NONE_RECORD_FIELD_NAME = [
    'id', 'seq',
    'creator_id', 'modifier_id', 'creator_name', 'modifier_name', 'org_id',
    'updated_at', 'created_at',
    ]


class AuditLogConfiguration(object):

    audit_log_model = "zhi_logger.AuditLog"

    @classmethod
    def get_audit_log_model(cls):
        from django.apps import apps
        return apps.get_model(cls.audit_log_model)

    @staticmethod
    def get_model_all_using_fields(source_model):
        """
        获取模型的所有启用字段列表

        新逻辑：
        1. 表级别：默认不生效，需要显式配置 is_enabled: True
        2. 字段级别：默认全部记录（除了NONE_RECORD_FIELD_NAME），只有显式设置 is_enabled: False 的字段才不记录
        """
        # 尝试不同的模型路径格式
        model_config = {}
        possible_paths = [
            source_model,  # 直接使用模型名
            f"zhi_oauth.{source_model}",  # zhi_oauth应用
            f"zhi_logger.{source_model}",  # zhi_logger应用
        ]

        for path in possible_paths:
            config = audit_config_manager.get_model_config(path)
            if config:
                model_config = config
                break

        # 检查表级别是否启用
        if not model_config.get('is_enabled', False):
            return []  # 表级别未启用，返回空列表

        # 获取模型的所有字段
        try:
            from django.apps import apps
            model_class = None

            # 尝试通过不同方式获取模型类
            for app_config in apps.get_app_configs():
                try:
                    model_class = app_config.get_model(source_model)
                    if model_class:
                        break
                except LookupError:
                    continue

            if not model_class:
                # 如果找不到模型类，回退到配置中的字段
                using_fields_config = model_config.get('using_fields', {})
                return [field_name for field_name, field_config in using_fields_config.items()
                       if field_config.get('is_enabled', True)]

            # 获取模型的所有字段名
            all_field_names = [field.name for field in model_class._meta.fields]

            # 过滤掉默认排除的字段
            available_fields = [field_name for field_name in all_field_names
                              if field_name not in NONE_RECORD_FIELD_NAME]

            # 获取字段配置
            using_fields_config = model_config.get('using_fields', {})

            # 应用字段级别的配置：默认启用，只有显式设置 is_enabled: False 的才禁用
            enabled_fields = []
            for field_name in available_fields:
                field_config = using_fields_config.get(field_name, {})
                # 默认启用，只有显式设置为False才禁用
                if field_config.get('is_enabled', True):
                    enabled_fields.append(field_name)

            return enabled_fields

        except Exception as e:
            zhi_logger.warning(f'获取模型字段失败: {source_model}, 错误: {str(e)}')
            # 出错时回退到原有逻辑
            using_fields_config = model_config.get('using_fields', {})
            return [field_name for field_name, field_config in using_fields_config.items()
                   if field_config.get('is_enabled', True)]

    @staticmethod
    def get_field_config(source_model):
        # 尝试不同的模型路径格式
        possible_paths = [
            source_model,
            f"zhi_oauth.{source_model}",
            f"zhi_logger.{source_model}",
        ]

        for path in possible_paths:
            config = audit_config_manager.get_model_config(path)
            if config:
                return config
        return {}

    @staticmethod
    def is_model_audit_enabled(source_model):
        """检查模型是否启用审计日志"""
        # 尝试不同的模型路径格式
        possible_paths = [
            source_model,
            f"zhi_oauth.{source_model}",
            f"zhi_logger.{source_model}",
        ]

        for path in possible_paths:
            config = audit_config_manager.get_model_config(path)
            if config:
                return config.get('is_enabled', False)
        return False

    @staticmethod
    def get_field_using_fields(source_model):
        # 尝试不同的模型路径格式
        possible_paths = [
            source_model,
            f"zhi_oauth.{source_model}",
            f"zhi_logger.{source_model}",
        ]

        for path in possible_paths:
            config = audit_config_manager.get_model_config(path)
            if config:
                return config.get('using_fields', {})
        return {}

    @staticmethod
    def get_field_relation_model_config(source_model, relation_model):
        # 尝试不同的模型路径格式
        possible_paths = [
            source_model,
            f"zhi_oauth.{source_model}",
            f"zhi_logger.{source_model}",
        ]

        for path in possible_paths:
            config = audit_config_manager.get_model_config(path)
            if config:
                return config.get('using_fields', {}).get(relation_model, {})
        return {}


class HandlerRecordValue(object):
    def __init__(self, model_info, field_name, value):
        self.model_info = model_info
        self.field_name = field_name
        self.value = value

    def handle(self):
        field_config = self.get_field_config()
        if not field_config:
            return self.value
        handler_func = getattr(self, f'handle_{field_config.get("type")}', None)
        if not handler_func:
            return self.value
        value = handler_func(field_config=field_config)
        return value

    def get_model(self, relation_model_info=None):
        model = apps.get_model(
            app_label=self.model_info['model_info']['app_label'],
            model_name=self.model_info['model_info']['object_name']
            )
        if relation_model_info:
            if '.' in relation_model_info:
                model = apps.get_model(
                    app_label=relation_model_info.split('.')[0],
                    model_name=relation_model_info.split('.')[1]
                    )
        return model

    def get_field_config(self):
        """获取模型的审计日志配置"""
        model_path = '%s.%s' % (
            self.model_info['model_info']['app_label'],
            self.model_info['model_info']['object_name']
            )
        return AuditLogConfiguration.get_field_relation_model_config(model_path, self.field_name)

    def handle_foreign_key(self, field_config):
        model = self.get_model(field_config['relation_model'])
        _filter = {field_config['key']: self.value}
        obj = model.objects.filter(**_filter).order_by('-update_datetime').first()
        if not obj:
            return self.value
        return getattr(obj, field_config['value'])

    def handle_dictionary(self, field_config):
        default_value = field_config.get('default', 'self')
        if default_value == 'self':
            default_value = self.value
        dictionary_dict = {}
        try:
            dictionary_config = get_dictionary_values(field_config['dictionary_key'])
            [dictionary_dict.update({i['value']: i['label']}) for i in dictionary_config['children']]
        except Exception as e:
            zhi_logger.warning(
                f'No dictionary or Dictionary configuration Error:'
                f'<field_config: {str(field_config)}>\n<ErrorDetail: {str(e)}>'
                )
        if not dictionary_dict:
            zhi_logger.warning(f'Dictionary is Blank!:<field_config: {str(field_config)}>')
        value = dictionary_dict.get(self.value, default_value)
        return value

    def handle_enum(self, field_config):
        default_value = field_config.get('default', 'self')
        if default_value == 'self':
            default_value = self.value
        enum_config = field_config.get('enum_config', {})
        if not enum_config:
            zhi_logger.warning(f'No enum or Enum configuration Error: <field_config: {str(field_config)}>')
            return default_value
        value = enum_config.get(self.value, default_value)
        return value


class ZhiRecordChanges(object):
    def __init__(self, model_info, user_info, previous_instance=None, current_instance=None):
        self.model_info = model_info
        self.source_model = (f'{self.model_info["model_info"]["app_label"]}.'
                             f'{self.model_info["model_info"]["object_name"]}')
        self.user_info = user_info
        self.previous_instance = previous_instance
        self.current_instance = current_instance

    @staticmethod
    def __field_to_value__(value):
        if value is None:
            current_value = None
        elif isinstance(value, bool):
            current_value = value
        elif isinstance(value, datetime):
            current_value = value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(value, Decimal):
            current_value = str(value)
        elif isinstance(value, date):
            current_value = value.strftime('%Y-%m-%d')
        else:
            current_value = value
        return current_value

    def get_field_verbose_name(self, field_name):
        """获取字段的 verbose_name """
        try:
            return self.model_info['field_names'].get(field_name, '')
        except Exception as e:
            zhi_logger.warning(f'<FieldNameNotFound:{str(e)}>')
            return field_name

    def _get_safe_resource_id(self):
        """安全获取资源ID"""
        try:
            # 优先从current_instance获取
            if self.current_instance and isinstance(self.current_instance, dict):
                resource_id = self.current_instance.get('id')
                if resource_id is not None:
                    return str(resource_id)

            # 如果current_instance没有id，尝试从previous_instance获取
            if self.previous_instance and isinstance(self.previous_instance, dict):
                resource_id = self.previous_instance.get('id')
                if resource_id is not None:
                    return str(resource_id)

            # 如果都没有，返回默认值
            return 'unknown'
        except Exception as e:
            zhi_logger.warning(f'获取资源ID失败: {str(e)}')
            return 'unknown'

    def record_changes(self):
        """记录字段的变更"""
        new_values = []
        old_values = []
        action = '更新'
        all_using_fields = AuditLogConfiguration.get_model_all_using_fields(self.source_model)
        if self.previous_instance:
            for field_name in self.model_info['field_names'].keys():
                if field_name in NONE_RECORD_FIELD_NAME:
                    continue
                # 安全获取字段值
                current_value = self.current_instance.get(field_name) if self.current_instance else None
                previous_value = self.previous_instance.get(field_name)
                # 处理为 json 数据, 避免 json 时，出现对象无法转换的问题
                current_value = self.__field_to_value__(current_value)
                previous_value = self.__field_to_value__(previous_value)

                if current_value != previous_value:
                    # 检查字段是否在配置中启用了记录
                    if field_name in all_using_fields:
                        temp_new_value = {
                            'field_name': field_name,
                            'field_cn_name': self.get_field_verbose_name(field_name),
                            'value': current_value
                            }
                        temp_old_value = {
                            'field_name': field_name,
                            'field_cn_name': self.get_field_verbose_name(field_name),
                            'value': previous_value
                            }
                        # 处理为前端可读的信息
                        current_display_value = HandlerRecordValue(
                            model_info=self.model_info, field_name=field_name, value=current_value
                            ).handle()
                        temp_new_value.update(
                            {
                                'value': current_display_value,
                                'source_value': current_value,
                                }
                            )
                        previous_display_value = HandlerRecordValue(
                            model_info=self.model_info, field_name=field_name, value=previous_value
                            ).handle()
                        temp_old_value.update(
                            {
                                'value': previous_display_value,
                                'source_value': previous_value,
                                }
                            )
                        new_values.append(temp_new_value)
                        old_values.append(temp_old_value)
        else:
            action = '创建'
            for field_name in self.model_info['field_names']:
                if field_name in NONE_RECORD_FIELD_NAME:
                    continue
                # 检查字段是否在配置中启用了记录
                if field_name in all_using_fields:
                    # 安全获取字段值
                    current_value = self.current_instance.get(field_name) if self.current_instance else None
                    current_value = self.__field_to_value__(current_value)
                    # 处理为前端可读的信息
                    temp_new_value = {
                        'field_name': field_name,
                        'field_cn_name': self.get_field_verbose_name(field_name),
                        'value': current_value,
                        }
                    current_display_value = HandlerRecordValue(
                        model_info=self.model_info, field_name=field_name, value=current_value
                        ).handle()
                    temp_new_value.update(
                        {
                            'value': current_display_value,
                            'source_value': current_value,
                            }
                        )
                    new_values.append(temp_new_value)
        if new_values or old_values:
            # 安全获取资源ID
            resource_id = self._get_safe_resource_id()
            zhi_logger.info(f'<action:{action}><resource_id:{resource_id}>'
                           f'<changes_values:{str({"new_values": new_values, "old_values": old_values})}>'
                           )
            self.log_action(
                action=action,
                user_info=self.user_info,
                resource_id=resource_id,
                changes={
                    'new_values': new_values,
                    'old_values': old_values,
                    },
                )

    @staticmethod
    def log_action(action, user_info, resource_id, changes=None):
        """将变更记录到日志或其他存储方式中"""
        # TODO 最好使用zhi_common.zhi_logger 下新建sdk 解耦后使用异步任务同步日志
        from zhi_logger.models import AuditLog
        if not changes:
            changes = {}

        # 安全提取用户信息，只传递AuditLog模型支持的字段
        audit_user_info = {}
        if user_info:
            # 映射用户信息字段到AuditLog模型字段
            if 'creator_id' in user_info:
                audit_user_info['creator_id'] = user_info['creator_id']
            if 'creator_name' in user_info:
                audit_user_info['creator_name'] = user_info['creator_name']
            # 注意：不传递 'modifier' 字段，因为AuditLog模型没有这个字段

        AuditLog.objects.create(
            action=action,
            old_values=changes.get('old_values', []),
            new_values=changes.get('new_values', []),
            resource_id=resource_id,
            **audit_user_info
            )
        zhi_logger.info(
            f'<action:{action}><resource_id:{resource_id}>'
            f'<changes_values:{str(changes)}>'
            )
