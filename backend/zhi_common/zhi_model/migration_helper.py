"""
数据库迁移辅助工具 - 帮助处理用户字段的迁移
"""
from django.db import migrations, models
from django.contrib.auth import get_user_model

User = get_user_model()


def migrate_user_fields_forward(apps, schema_editor):
    """
    正向迁移：从ForeignKey迁移到CharField
    
    将现有的creator和modifier外键字段的数据迁移到新的字符串字段
    """
    # 这里需要根据具体的模型来处理
    # 示例代码，需要根据实际情况调整
    
    # 获取所有使用CoreModel的模型
    from django.apps import apps as django_apps
    
    for model in django_apps.get_models():
        if hasattr(model, 'creator_id') and hasattr(model, 'creator_name'):
            # 处理每个模型的数据迁移
            for instance in model.objects.all():
                # 如果有旧的creator外键，迁移到新字段
                if hasattr(instance, 'creator') and instance.creator:
                    instance.creator_id = str(instance.creator.id)
                    instance.creator_name = get_user_display_name(instance.creator)
                
                # 如果有旧的modifier字段，迁移到新字段
                if hasattr(instance, 'modifier') and instance.modifier:
                    # 假设modifier是用户ID字符串
                    try:
                        user = User.objects.get(id=instance.modifier)
                        instance.modifier_id = str(user.id)
                        instance.modifier_name = get_user_display_name(user)
                    except User.DoesNotExist:
                        pass
                
                instance.save()


def migrate_user_fields_reverse(apps, schema_editor):
    """
    反向迁移：从CharField迁移回ForeignKey
    
    将字符串字段的数据迁移回外键字段
    """
    # 反向迁移逻辑
    from django.apps import apps as django_apps
    
    for model in django_apps.get_models():
        if hasattr(model, 'creator_id') and hasattr(model, 'creator'):
            for instance in model.objects.all():
                # 从字符串字段恢复到外键字段
                if instance.creator_id:
                    try:
                        user = User.objects.get(id=instance.creator_id)
                        instance.creator = user
                        instance.save()
                    except User.DoesNotExist:
                        pass


def get_user_display_name(user):
    """
    获取用户显示名称
    
    Args:
        user: 用户对象
        
    Returns:
        str: 用户显示名称
    """
    # 尝试获取真实姓名
    first_name = getattr(user, 'first_name', '')
    last_name = getattr(user, 'last_name', '')
    
    if first_name or last_name:
        return f"{first_name}{last_name}".strip()
    
    # 使用用户名
    if hasattr(user, 'username') and user.username:
        return user.username
    
    # 使用邮箱
    if hasattr(user, 'email') and user.email:
        return user.email.split('@')[0]
    
    # 最后使用ID
    return str(user.id)


class Migration(migrations.Migration):
    """
    用户字段迁移示例
    
    这个迁移文件需要在每个使用CoreModel的应用中创建
    """
    
    dependencies = [
        # 添加依赖的迁移文件
    ]
    
    operations = [
        # 1. 添加新的字符串字段
        migrations.AddField(
            model_name='your_model',  # 替换为实际的模型名
            name='creator_id',
            field=models.CharField(
                max_length=63, null=True, blank=True, db_index=True,
                verbose_name='创建人ID', help_text="创建人用户ID", db_comment='创建人用户ID'
            ),
        ),
        migrations.AddField(
            model_name='your_model',  # 替换为实际的模型名
            name='creator_name',
            field=models.CharField(
                max_length=255, null=True, blank=True,
                verbose_name='创建人姓名', help_text="创建人姓名", db_comment='创建人姓名'
            ),
        ),
        migrations.AddField(
            model_name='your_model',  # 替换为实际的模型名
            name='modifier_id',
            field=models.CharField(
                max_length=63, null=True, blank=True, db_index=True,
                verbose_name='修改人ID', help_text="修改人用户ID", db_comment='修改人用户ID'
            ),
        ),
        migrations.AddField(
            model_name='your_model',  # 替换为实际的模型名
            name='modifier_name',
            field=models.CharField(
                max_length=255, null=True, blank=True,
                verbose_name='修改人姓名', help_text="修改人姓名", db_comment='修改人姓名'
            ),
        ),
        
        # 2. 迁移数据
        migrations.RunPython(
            migrate_user_fields_forward,
            migrate_user_fields_reverse,
        ),
        
        # 3. 删除旧的外键字段（可选，建议先保留一段时间）
        # migrations.RemoveField(
        #     model_name='your_model',
        #     name='creator',
        # ),
        # migrations.RemoveField(
        #     model_name='your_model',
        #     name='modifier',
        # ),
    ]
