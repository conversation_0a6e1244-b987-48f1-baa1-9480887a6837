"""
ZhiAdmin 通用模型
提供共用的基础模型和工具类
"""
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from .core_model import ZhiCoreModel
from uuid import uuid4


class BasePermissionModel(ZhiCoreModel):
    """
    权限基础模型
    提供通用的权限相关字段
    """
    name = models.CharField(
        max_length=100,
        verbose_name="名称",
        help_text="权限名称",
        db_comment="权限名称"
        )
    code = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="编码",
        help_text="权限编码",
        db_comment="权限编码"
        )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name="描述",
        help_text="权限描述",
        db_comment="权限描述"
        )
    status = models.BooleanField(
        default=True,
        verbose_name="状态",
        help_text="是否启用",
        db_comment="是否启用"
        )
    sort_order = models.IntegerField(
        default=0,
        verbose_name="排序",
        help_text="排序序号",
        db_comment="排序序号"
        )

    class Meta:
        abstract = True


class TenantModel(ZhiCoreModel):
    """
    租户基础模型
    所有需要租户隔离的模型都应继承此模型
    """
    tenant_id = models.CharField(
        max_length=63,
        null=True,
        blank=True,
        db_index=True,
        verbose_name="租户ID",
        help_text="数据所属租户",
        db_comment="数据所属租户"
        )

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        """自动设置租户ID"""
        if not self.tenant_id:
            from zhi_common.zhi_tools.g_local_thread_user_info import get_request_user_info
            user_info = get_request_user_info()
            self.tenant_id = user_info.get('tenant_id')
        super().save(*args, **kwargs)


class GenericPermissionTarget(models.Model):
    """
    通用权限目标模型
    用于存储任意模型的权限关联
    """
    id = models.CharField(
        max_length=63,
        primary_key=True,
        default=lambda: f'perm_target-{uuid4().hex}'[:63],
        editable=False
        )

    # 权限主体（角色、用户等）
    subject_content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        related_name='permission_subjects'
        )
    subject_object_id = models.CharField(max_length=63)
    subject_object = GenericForeignKey('subject_content_type', 'subject_object_id')

    # 权限目标（资源对象）
    target_content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE,
        related_name='permission_targets'
        )
    target_object_id = models.CharField(max_length=63)
    target_object = GenericForeignKey('target_content_type', 'target_object_id')

    # 权限类型
    permission_type = models.CharField(
        max_length=50,
        choices=[
            ('read', '读取'),
            ('write', '写入'),
            ('delete', '删除'),
            ('execute', '执行'),
            ('admin', '管理'),
            ],
        default='read'
        )

    # 权限范围
    permission_scope = models.JSONField(
        default=dict,
        verbose_name="权限范围",
        help_text="具体的权限范围配置"
        )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        app_label = 'zhi_common'  # 添加 app_label
        db_table = 'zhi_common_generic_permission_target'
        verbose_name = '通用权限目标'
        verbose_name_plural = verbose_name
        unique_together = [
            ('subject_content_type', 'subject_object_id',
             'target_content_type', 'target_object_id', 'permission_type')
            ]
        indexes = [
            models.Index(fields=['subject_content_type', 'subject_object_id']),
            models.Index(fields=['target_content_type', 'target_object_id']),
            models.Index(fields=['permission_type']),
            ]
