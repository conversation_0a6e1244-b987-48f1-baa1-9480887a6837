"""
用户信息管理器 - 为模型提供用户信息的统一管理
"""
from typing import Optional, Dict, Any
from django.contrib.auth import get_user_model
from django.core.cache import cache
from django.conf import settings


def get_user_model_safe():
    """安全获取用户模型，避免循环导入"""
    try:
        return get_user_model()
    except Exception:
        # 如果无法获取用户模型，返回None，稍后再试
        return None


class UserInfoManager:
    """
    用户信息管理器
    
    为模型的creator和modifier字段提供统一的用户信息管理
    支持缓存优化，避免频繁查询用户表
    """
    
    def __init__(self, cache_timeout: int = 300):
        """
        初始化用户信息管理器
        
        Args:
            cache_timeout: 缓存超时时间（秒），默认5分钟
        """
        self.cache_timeout = cache_timeout
        self.cache_prefix = 'user_info_mgr'
    
    def get_user_info(self, user_id: str, use_cache: bool = True) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Args:
            user_id: 用户ID
            use_cache: 是否使用缓存
            
        Returns:
            dict: 包含用户ID和姓名的字典，如果用户不存在返回None
        """
        if not user_id:
            return None
        
        # 先从缓存获取
        if use_cache:
            cache_key = f"{self.cache_prefix}:user:{user_id}"
            cached_info = cache.get(cache_key)
            if cached_info:
                return cached_info
        
        try:
            # 获取用户模型
            User = get_user_model_safe()
            if not User:
                return None

            # 从数据库获取用户信息
            user = User.objects.get(id=user_id)
            user_info = {
                'id': str(user.id),
                'name': self._get_user_display_name(user),
                'username': user.username,
                'email': getattr(user, 'email', ''),
            }

            # 缓存用户信息
            if use_cache:
                cache.set(cache_key, user_info, self.cache_timeout)

            return user_info

        except Exception:
            return None
    
    def get_user_display_name(self, user_id: str) -> str:
        """
        获取用户显示名称
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 用户显示名称，如果用户不存在返回空字符串
        """
        user_info = self.get_user_info(user_id)
        return user_info['name'] if user_info else ''
    
    def _get_user_display_name(self, user) -> str:
        """
        获取用户的显示名称
        
        优先级：first_name + last_name > username > email
        
        Args:
            user: 用户对象
            
        Returns:
            str: 用户显示名称
        """
        # 尝试获取真实姓名
        first_name = getattr(user, 'first_name', '')
        last_name = getattr(user, 'last_name', '')
        
        if first_name or last_name:
            return f"{first_name}{last_name}".strip()
        
        # 使用用户名
        if hasattr(user, 'username') and user.username:
            return user.username
        
        # 使用邮箱
        if hasattr(user, 'email') and user.email:
            return user.email.split('@')[0]
        
        # 最后使用ID
        return str(user.id)
    
    def set_creator_info(self, instance, user_id: str):
        """
        设置创建人信息
        
        Args:
            instance: 模型实例
            user_id: 用户ID
        """
        if not user_id:
            return
        
        user_info = self.get_user_info(user_id)
        if user_info:
            instance.creator_id = user_info['id']
            instance.creator_name = user_info['name']
    
    def set_modifier_info(self, instance, user_id: str):
        """
        设置修改人信息
        
        Args:
            instance: 模型实例
            user_id: 用户ID
        """
        if not user_id:
            return
        
        user_info = self.get_user_info(user_id)
        if user_info:
            instance.modifier_id = user_info['id']
            instance.modifier_name = user_info['name']
    
    def invalidate_user_cache(self, user_id: str):
        """
        清除用户缓存
        
        Args:
            user_id: 用户ID
        """
        cache_key = f"{self.cache_prefix}:user:{user_id}"
        cache.delete(cache_key)
    
    def batch_get_user_info(self, user_ids: list, use_cache: bool = True) -> Dict[str, Dict[str, Any]]:
        """
        批量获取用户信息
        
        Args:
            user_ids: 用户ID列表
            use_cache: 是否使用缓存
            
        Returns:
            dict: 用户ID到用户信息的映射
        """
        if not user_ids:
            return {}
        
        result = {}
        uncached_ids = []
        
        # 先从缓存获取
        if use_cache:
            cache_keys = [f"{self.cache_prefix}:user:{uid}" for uid in user_ids]
            cached_data = cache.get_many(cache_keys)
            
            for user_id in user_ids:
                cache_key = f"{self.cache_prefix}:user:{user_id}"
                if cache_key in cached_data:
                    result[user_id] = cached_data[cache_key]
                else:
                    uncached_ids.append(user_id)
        else:
            uncached_ids = user_ids
        
        # 从数据库获取未缓存的用户
        if uncached_ids:
            try:
                # 获取用户模型
                User = get_user_model_safe()
                if not User:
                    return result

                users = User.objects.filter(id__in=uncached_ids)
                cache_data = {}

                for user in users:
                    user_info = {
                        'id': str(user.id),
                        'name': self._get_user_display_name(user),
                        'username': user.username,
                        'email': getattr(user, 'email', ''),
                    }
                    result[str(user.id)] = user_info

                    if use_cache:
                        cache_key = f"{self.cache_prefix}:user:{user.id}"
                        cache_data[cache_key] = user_info

                # 批量缓存
                if use_cache and cache_data:
                    cache.set_many(cache_data, self.cache_timeout)

            except Exception:
                pass
        
        return result


# 全局实例
user_info_manager = UserInfoManager()


def get_user_display_name(user_id: str) -> str:
    """
    便捷函数：获取用户显示名称
    
    Args:
        user_id: 用户ID
        
    Returns:
        str: 用户显示名称
    """
    return user_info_manager.get_user_display_name(user_id)


def set_model_user_info(instance, creator_id: str = None, modifier_id: str = None):
    """
    便捷函数：设置模型的用户信息
    
    Args:
        instance: 模型实例
        creator_id: 创建人ID
        modifier_id: 修改人ID
    """
    if creator_id:
        user_info_manager.set_creator_info(instance, creator_id)
    
    if modifier_id:
        user_info_manager.set_modifier_info(instance, modifier_id)


def invalidate_user_cache(user_id: str):
    """
    便捷函数：清除用户缓存
    
    Args:
        user_id: 用户ID
    """
    user_info_manager.invalidate_user_cache(user_id)
