# ZhiModel - 增强模型系统

ZhiModel 是 ZhiAdmin 系统的核心模型层，包含 ZhiCoreModel 基础模型类和自动用户信息填充系统，提供了丰富的功能来增强 Django 模型的能力。

## 🏗️ 系统架构

```
zhi_common/zhi_model/
├── core_model.py                     # 核心模型基类 (ZhiCoreModel, CoreModel)
├── enhanced_user_manager.py          # 增强用户管理器（核心）
├── auto_user_mixin.py                # 自动用户信息混入类和装饰器
├── user_info_manager.py              # 用户信息管理器（基础服务）
├── AUTO_USER_INFO_USAGE.md           # 自动用户信息填充使用指南
├── AUTO_USER_INFO_SYSTEM_OVERVIEW.md # 系统总览
├── test_auto_user_info.py            # 系统测试文件
└── README.md                         # 本文件
```

## 🚀 主要功能

### 1. 自动用户信息填充系统 ⭐ (新增)

**核心目标**：在创建实例资源时，自动加入用户信息到数据库模型中

**支持的操作**：
- ✅ `create()` - 单个创建，自动填充创建人信息
- ✅ `bulk_create()` - 批量创建，自动填充创建人信息
- ✅ `update()` - 更新，自动填充修改人信息
- ✅ `bulk_update()` - 批量更新，自动填充修改人信息

**自动填充字段**：
- `creator_id` - 创建人ID
- `creator_name` - 创建人姓名
- `modifier_id` - 修改人ID
- `modifier_name` - 修改人姓名
- `org_id` - 组织ID

**使用方式**：
- 继承 `AutoUserInfoMixin` 混入类
- 使用 `@auto_user_info` 装饰器
- 调用便捷函数进行数据库操作

### 2. 异步操作支持
- `async_save()` - 异步保存
- `async_delete()` - 异步删除（支持软删除和硬删除）

### 3. 自动审计日志
- 自动记录模型的创建、更新、删除操作
- 支持子项目独立配置
- 异步记录，不影响业务性能

### 4. 便捷的数据转换
- `to_dict` - 获取模型的字典表示
- `to_dict_no_pub_fields` - 获取不包含公共字段的字典

### 5. 软删除支持
- 自动管理 `is_deleted` 和 `deleted_at` 字段
- 支持硬删除选项

### 6. 用户信息追踪
- 自动记录创建者和更新者信息
- 支持从请求上下文获取用户信息

## 📖 使用方法

### 自动用户信息填充系统使用

#### 方法1：使用混入类（推荐）

```python
from zhi_common.zhi_model.auto_user_mixin import AutoUserInfoMixin

class Product(AutoUserInfoMixin):
    name = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)

# 使用 - 自动填充用户信息
product = Product.objects.create(name='iPhone 15', price=999.99)
print(f"创建人: {product.creator_name}")

# 批量创建 - 自动填充用户信息
products = Product.objects.bulk_create([
    Product(name='iPhone 15 Pro', price=1199.99),
    Product(name='iPhone 15 Pro Max', price=1399.99)
])
```

#### 方法2：使用装饰器

```python
from zhi_common.zhi_model.auto_user_mixin import auto_user_info

@auto_user_info
class Order(models.Model):
    order_no = models.CharField(max_length=50)
    # 需要手动添加用户信息字段
    creator_id = models.CharField(max_length=64, null=True, blank=True)
    creator_name = models.CharField(max_length=100, null=True, blank=True)
    modifier_id = models.CharField(max_length=64, null=True, blank=True)
    modifier_name = models.CharField(max_length=100, null=True, blank=True)
```

#### 方法3：使用便捷函数

```python
from zhi_common.zhi_model.auto_user_mixin import (
    create_with_user_info,
    bulk_create_with_user_info,
    update_with_user_info,
    bulk_update_with_user_info
)

# 创建单个记录
product = create_with_user_info(Product, {'name': '测试产品', 'price': 99.99})

# 批量创建
products = bulk_create_with_user_info(Product, [
    {'name': '产品1', 'price': 100},
    {'name': '产品2', 'price': 200}
])

# 更新记录
update_with_user_info(
    Product.objects.filter(name__startswith='产品'),
    {'price': 150}
)

# 批量更新
products = Product.objects.filter(name__startswith='产品')
for product in products:
    product.price += 50
bulk_update_with_user_info(products, ['price'])
```

### ZhiCoreModel 基本使用

```python
from zhi_common.zhi_model.core_model import ZhiCoreModel

class MyModel(ZhiCoreModel):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    class Meta:
        db_table = 'my_model'
```

### 异步操作

```python
# 在异步视图中使用
async def create_model(request):
    instance = MyModel(name="测试", description="异步创建")
    await instance.async_save()
    return JsonResponse({"id": instance.id})

async def delete_model(request, model_id):
    instance = await MyModel.objects.aget(id=model_id)
    
    # 软删除（默认）
    await instance.async_delete()
    
    # 硬删除
    await instance.async_delete(force_delete=True)
```

### 数据转换

```python
# 获取模型的字典表示
instance = MyModel.objects.get(id=1)
data = instance.to_dict
print(data)  # {'id': 1, 'name': '测试', 'description': '...', 'created_at': '...'}

# 获取不包含公共字段的字典
clean_data = instance.to_dict_no_pub_fields
print(clean_data)  # {'name': '测试', 'description': '...'}
```

## ⚙️ 审计配置

### 审计日志存储

审计日志现在会自动保存到 `zhi_logger.models.AuditLog` 模型中，提供：
- 统一的审计日志存储
- 丰富的查询和统计功能
- RESTful API 接口
- 实时日志推送

### 子项目独立配置

每个子项目可以创建自己的 `audit_config.py` 文件：

```python
# backend/your_app/audit_config.py

AUDIT_LOG_CONFIG = {
    'YourModel': {
        'is_enabled': True,
        'using_fields': {
            'name': {
                'field_name': '名称',
                'is_enabled': True,
                'is_important': True,
            },
            'description': {
                'field_name': '描述',
                'is_enabled': True,
                'is_important': False,
            },
            'password': {
                'field_name': '密码',
                'is_enabled': False,  # 敏感字段不记录
                'is_important': True,
            },
        }
    },
}
```

### 审计日志 API

审计日志提供了完整的 API 接口：

```bash
# 获取审计日志列表
GET /api/logger/audit-logs/

# 获取审计日志详情
GET /api/logger/audit-logs/{id}/

# 获取资源变更历史
GET /api/logger/audit-logs/resource/{resource_id}/

# 获取用户操作历史
GET /api/logger/audit-logs/user/{creator_id}/

# 获取统计信息
GET /api/logger/audit-logs/stats?days=7

# 获取敏感操作
GET /api/logger/audit-logs/sensitive/
```

### 配置选项说明

- `is_enabled`: 是否启用该模型的审计日志
- `using_fields`: 字段配置字典
  - `field_name`: 字段的显示名称
  - `is_enabled`: 是否记录该字段的变更
  - `is_important`: 是否为重要字段

### 管理审计配置

```bash
# 列出所有审计配置
python backend/zhi_scripts/manage_projects.py audit list

# 测试指定项目的配置
python backend/zhi_scripts/manage_projects.py audit test --project zhi_logger

# 重新加载配置
python backend/zhi_scripts/manage_projects.py audit reload --project zhi_oauth

# 或者直接使用审计配置管理脚本
python backend/zhi_scripts/manage_audit_config.py list
python backend/zhi_scripts/manage_audit_config.py test --project zhi_logger
python backend/zhi_scripts/manage_audit_config.py validate
```

## 🔧 高级功能

### 自定义用户信息

```python
class MyModel(ZhiCoreModel):
    def get_self_user_info(self):
        # 自定义用户信息获取逻辑
        return {
            'creator_id': self.creator_id or 'system',
            'creator_name': getattr(self.creator, 'username', 'System'),
            'ip_address': '127.0.0.1',
            'user_agent': 'Custom Agent'
        }
```

### 自定义模型信息

```python
class MyModel(ZhiCoreModel):
    def get_model_info(self):
        info = super().get_model_info()
        info['custom_field'] = 'custom_value'
        return info
```

### 禁用审计日志

```python
# 临时禁用审计日志
class MyModel(ZhiCoreModel):
    def is_audit_log_enabled(self):
        return False  # 始终返回 False
```

## 🚨 注意事项

### 1. 异步操作
- 异步方法只能在异步上下文中使用
- 确保数据库支持异步操作
- 注意事务管理

### 2. 审计日志
- 审计日志是异步记录的，不会影响主业务流程
- 敏感字段（如密码）建议设置 `is_enabled: False`
- 审计日志会占用存储空间，建议定期清理

### 3. 软删除
- 软删除的记录仍然存在于数据库中
- 需要在查询时过滤已删除的记录
- 使用 `force_delete=True` 进行物理删除

### 4. 性能考虑
- 审计日志记录是异步的，但仍会消耗资源
- 对于高频操作的模型，考虑禁用审计或减少记录字段
- 使用 Celery 可以进一步提高性能

## 🔍 故障排除

### 审计日志不记录
1. 检查模型是否继承了 `ZhiCoreModel`
2. 检查 `audit_config.py` 中的配置
3. 检查 `is_enabled` 是否为 `True`
4. 查看日志中的错误信息

### 异步操作失败
1. 确保在异步上下文中调用
2. 检查数据库连接配置
3. 查看异步相关的错误日志

### 配置不生效
1. 重新加载配置：`python backend/zhi_scripts/manage_audit_config.py reload`
2. 检查配置文件语法
3. 重启应用服务

## 📚 相关文档

### 自动用户信息填充系统
- **详细使用指南**: [AUTO_USER_INFO_USAGE.md](./AUTO_USER_INFO_USAGE.md)
- **系统总览**: [AUTO_USER_INFO_SYSTEM_OVERVIEW.md](./AUTO_USER_INFO_SYSTEM_OVERVIEW.md)
- **系统测试**: [test_auto_user_info.py](./test_auto_user_info.py)

### ZhiCoreModel 相关
- [审计配置管理器](audit_config_manager.py)
- [异步记录工具](../../../utils/base/async_record_changes_workers.py)
- [日志SDK文档](../zhi_logger/README.md)
- [多项目架构](../../MULTI_PROJECT_ARCHITECTURE.md)

## 🤝 贡献

如果您发现问题或有改进建议，请：
1. 查看现有的 Issues
2. 创建新的 Issue 描述问题
3. 提交 Pull Request

## 🎉 系统特性总结

### ZhiModel 增强模型系统为您提供：

1. **🔄 自动用户信息填充** - 完全自动化的用户信息填充，支持所有数据库操作
2. **📊 多种使用方式** - 混入类、装饰器、便捷函数，适应不同场景
3. **⚡ 高性能优化** - 缓存机制、批量操作优化，支持大批量数据处理
4. **🛡️ 强容错机制** - 完善的错误处理，保证系统稳定性
5. **📝 完整的审计日志** - 自动记录所有操作，支持异步处理
6. **🔧 简单易用** - 清晰的接口设计，丰富的文档和示例

### 🧪 测试验证

```bash
# 测试自动用户信息填充系统
python backend/zhi_common/zhi_model/test_auto_user_info.py

# 验证Django启动（无MRO冲突）
python -c "import django; import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth'); django.setup(); print('Django启动成功！')"
```

---

💡 **提示**:
- 使用 `python backend/zhi_scripts/check_project.py` 检查项目配置是否正确
- 查看 [AUTO_USER_INFO_USAGE.md](./AUTO_USER_INFO_USAGE.md) 获取详细的使用指南
- 通过自动用户信息填充系统，让数据库操作更智能，让开发更高效 ✨
