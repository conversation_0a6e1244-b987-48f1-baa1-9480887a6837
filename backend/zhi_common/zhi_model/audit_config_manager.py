"""
审计配置管理器

支持子项目独立配置审计日志，互不影响
每个子项目可以有自己的审计配置，不依赖全局配置
"""

import os
import importlib
from typing import Dict, Any, Optional
from django.conf import settings
from django.apps import apps

from zhi_common.zhi_logger import get_logger

logger = get_logger("audit_config")


class AuditConfigManager:
    """审计配置管理器"""
    
    _configs = {}  # 缓存配置
    _initialized = False
    
    @classmethod
    def initialize(cls):
        """初始化配置管理器"""
        if cls._initialized:
            return
            
        cls._load_all_configs()
        cls._initialized = True
    
    @classmethod
    def _load_all_configs(cls):
        """加载所有子项目的审计配置"""
        try:
            # 获取所有已安装的应用
            installed_apps = settings.INSTALLED_APPS
            
            for app_name in installed_apps:
                # 跳过Django内置应用
                if app_name.startswith('django.'):
                    continue
                    
                cls._load_app_config(app_name)
                
            logger.info(
                f"审计配置加载完成，共加载 {len(cls._configs)} 个配置",
                category="audit",
                extra_data={'loaded_configs': list(cls._configs.keys())}
            )
            
        except Exception as e:
            logger.error(
                f"加载审计配置失败: {str(e)}",
                category="audit",
                extra_data={'error': str(e)}
            )
    
    @classmethod
    def _load_app_config(cls, app_name: str):
        """加载单个应用的审计配置"""
        try:
            # 尝试导入应用的审计配置
            config_module_name = f"{app_name}.audit_config"
            
            try:
                config_module = importlib.import_module(config_module_name)
                
                # 获取配置字典
                if hasattr(config_module, 'AUDIT_LOG_CONFIG'):
                    app_config = config_module.AUDIT_LOG_CONFIG
                    
                    # 将应用配置合并到全局配置中
                    for model_key, model_config in app_config.items():
                        # 确保模型键包含应用名称
                        if '.' not in model_key:
                            model_key = f"{app_name}.{model_key}"
                        
                        cls._configs[model_key] = model_config
                    
                    logger.debug(
                        f"加载应用审计配置: {app_name}",
                        category="audit",
                        extra_data={
                            'app_name': app_name,
                            'models_count': len(app_config)
                        }
                    )
                    
            except ImportError:
                # 应用没有审计配置文件，跳过
                pass
                
        except Exception as e:
            logger.warning(
                f"加载应用 {app_name} 的审计配置失败: {str(e)}",
                category="audit",
                extra_data={
                    'app_name': app_name,
                    'error': str(e)
                }
            )
    
    @classmethod
    def get_model_config(cls, model_path: str) -> Dict[str, Any]:
        """
        获取模型的审计配置
        
        Args:
            model_path: 模型路径，格式为 "app_label.ModelName"
            
        Returns:
            模型的审计配置字典
        """
        cls.initialize()
        
        # 尝试不同的键格式
        possible_keys = [
            model_path,  # app_label.ModelName
            model_path.lower(),  # app_label.modelname
        ]
        
        # 如果模型路径不包含应用名，尝试添加
        if '.' not in model_path:
            # 尝试从Django应用中查找
            for app_config in apps.get_app_configs():
                possible_keys.extend([
                    f"{app_config.label}.{model_path}",
                    f"{app_config.label}.{model_path.lower()}"
                ])
        
        # 查找配置
        for key in possible_keys:
            if key in cls._configs:
                return cls._configs[key]
        
        # 返回空配置
        return {}
    
    @classmethod
    def is_model_audit_enabled(cls, model_path: str) -> bool:
        """
        检查模型是否启用审计日志
        
        Args:
            model_path: 模型路径
            
        Returns:
            是否启用审计日志
        """
        config = cls.get_model_config(model_path)
        return config.get('is_enabled', False)
    
    @classmethod
    def get_model_using_fields(cls, model_path: str) -> Dict[str, Any]:
        """
        获取模型的字段配置
        
        Args:
            model_path: 模型路径
            
        Returns:
            字段配置字典
        """
        config = cls.get_model_config(model_path)
        return config.get('using_fields', {})
    
    @classmethod
    def get_field_config(cls, model_path: str, field_name: str) -> Dict[str, Any]:
        """
        获取字段的配置
        
        Args:
            model_path: 模型路径
            field_name: 字段名称
            
        Returns:
            字段配置字典
        """
        using_fields = cls.get_model_using_fields(model_path)
        return using_fields.get(field_name, {})
    
    @classmethod
    def reload_config(cls, app_name: Optional[str] = None):
        """
        重新加载配置
        
        Args:
            app_name: 应用名称，如果为None则重新加载所有配置
        """
        if app_name:
            # 清除特定应用的配置
            keys_to_remove = [key for key in cls._configs.keys() if key.startswith(f"{app_name}.")]
            for key in keys_to_remove:
                del cls._configs[key]
            
            # 重新加载特定应用的配置
            cls._load_app_config(app_name)
        else:
            # 重新加载所有配置
            cls._configs.clear()
            cls._initialized = False
            cls.initialize()
        
        logger.info(
            f"审计配置重新加载完成",
            category="audit",
            extra_data={
                'app_name': app_name,
                'total_configs': len(cls._configs)
            }
        )
    
    @classmethod
    def get_all_configs(cls) -> Dict[str, Any]:
        """获取所有配置"""
        cls.initialize()
        return cls._configs.copy()
    
    @classmethod
    def add_model_config(cls, model_path: str, config: Dict[str, Any]):
        """
        动态添加模型配置
        
        Args:
            model_path: 模型路径
            config: 配置字典
        """
        cls.initialize()
        cls._configs[model_path] = config
        
        logger.info(
            f"动态添加审计配置: {model_path}",
            category="audit",
            extra_data={
                'model_path': model_path,
                'config': config
            }
        )


# 全局配置管理器实例
audit_config_manager = AuditConfigManager()


# 兼容性函数，保持与原有代码的兼容性
def get_model_audit_config(model_path: str) -> Dict[str, Any]:
    """获取模型的审计配置（兼容性函数）"""
    return audit_config_manager.get_model_config(model_path)


def is_model_audit_enabled(model_path: str) -> bool:
    """检查模型是否启用审计日志（兼容性函数）"""
    return audit_config_manager.is_model_audit_enabled(model_path)


def get_model_using_fields(model_path: str) -> Dict[str, Any]:
    """获取模型的字段配置（兼容性函数）"""
    return audit_config_manager.get_model_using_fields(model_path)


def get_field_config(model_path: str, field_name: str) -> Dict[str, Any]:
    """获取字段的配置（兼容性函数）"""
    return audit_config_manager.get_field_config(model_path, field_name)
