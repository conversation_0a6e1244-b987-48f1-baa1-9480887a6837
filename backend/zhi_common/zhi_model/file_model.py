"""
文件存储模型
用于记录上传文件的元数据信息
"""

from django.db import models
from django.contrib.auth import get_user_model
from zhi_common.zhi_model.core_model import CoreModel

User = get_user_model()


class FileStorage(CoreModel):
    """文件存储模型"""
    
    # 文件类型选择
    FILE_TYPE_CHOICES = [
        ('image', '图片'),
        ('document', '文档'),
        ('archive', '压缩包'),
        ('video', '视频'),
        ('audio', '音频'),
        ('other', '其他'),
    ]
    
    # 文件状态选择
    STATUS_CHOICES = [
        ('active', '正常'),
        ('deleted', '已删除'),
        ('expired', '已过期'),
    ]
    
    file_id = models.CharField(
        max_length=64, 
        unique=True, 
        db_index=True,
        verbose_name="文件唯一标识",
        help_text="文件的唯一标识符"
    )
    
    original_name = models.CharField(
        max_length=255,
        verbose_name="原始文件名",
        help_text="用户上传时的原始文件名"
    )
    
    file_name = models.CharField(
        max_length=255,
        verbose_name="存储文件名",
        help_text="在服务器上存储的文件名"
    )
    
    file_path = models.CharField(
        max_length=500,
        verbose_name="文件路径",
        help_text="文件在服务器上的相对路径"
    )
    
    file_url = models.URLField(
        max_length=500,
        verbose_name="文件访问URL",
        help_text="文件的访问URL"
    )
    
    file_size = models.BigIntegerField(
        verbose_name="文件大小",
        help_text="文件大小，单位：字节"
    )
    
    file_type = models.CharField(
        max_length=20,
        choices=FILE_TYPE_CHOICES,
        default='other',
        verbose_name="文件类型",
        help_text="文件的分类类型"
    )
    
    content_type = models.CharField(
        max_length=100,
        verbose_name="MIME类型",
        help_text="文件的MIME类型"
    )
    
    file_extension = models.CharField(
        max_length=20,
        verbose_name="文件扩展名",
        help_text="文件的扩展名"
    )
    
    file_hash = models.CharField(
        max_length=64,
        db_index=True,
        verbose_name="文件哈希值",
        help_text="文件的MD5哈希值，用于去重"
    )
    
    upload_ip = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="上传IP",
        help_text="文件上传时的客户端IP地址"
    )
    
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name="用户代理",
        help_text="文件上传时的用户代理信息"
    )
    
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='active',
        db_index=True,
        verbose_name="文件状态",
        help_text="文件的当前状态"
    )
    
    download_count = models.PositiveIntegerField(
        default=0,
        verbose_name="下载次数",
        help_text="文件被下载的次数"
    )
    
    last_download_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="最后下载时间",
        help_text="文件最后一次被下载的时间"
    )
    
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="过期时间",
        help_text="文件的过期时间，为空表示永不过期"
    )
    
    tags = models.JSONField(
        default=list,
        blank=True,
        verbose_name="文件标签",
        help_text="文件的标签列表，用于分类和搜索"
    )
    
    metadata = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="文件元数据",
        help_text="文件的额外元数据信息"
    )
    
    description = models.TextField(
        blank=True,
        verbose_name="文件描述",
        help_text="文件的描述信息"
    )
    
    class Meta:
        db_table = 'zhi_file_storage'
        verbose_name = '文件存储'
        verbose_name_plural = '文件存储'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['file_id']),
            models.Index(fields=['file_hash']),
            models.Index(fields=['file_type']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
            models.Index(fields=['creator_id']),
        ]
    
    def __str__(self):
        return f"{self.original_name} ({self.file_id})"
    
    @property
    def file_size_human(self):
        """人类可读的文件大小"""
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} PB"
    
    @property
    def is_image(self):
        """是否为图片文件"""
        return self.file_type == 'image'
    
    @property
    def is_document(self):
        """是否为文档文件"""
        return self.file_type == 'document'
    
    @property
    def is_active(self):
        """文件是否处于活跃状态"""
        return self.status == 'active'
    
    def increment_download_count(self):
        """增加下载次数"""
        from django.utils import timezone
        self.download_count += 1
        self.last_download_at = timezone.now()
        self.save(update_fields=['download_count', 'last_download_at'])
    
    def mark_as_deleted(self):
        """标记文件为已删除"""
        self.status = 'deleted'
        self.save(update_fields=['status'])
    
    def add_tag(self, tag):
        """添加标签"""
        if tag not in self.tags:
            self.tags.append(tag)
            self.save(update_fields=['tags'])
    
    def remove_tag(self, tag):
        """移除标签"""
        if tag in self.tags:
            self.tags.remove(tag)
            self.save(update_fields=['tags'])
    
    def set_metadata(self, key, value):
        """设置元数据"""
        self.metadata[key] = value
        self.save(update_fields=['metadata'])
    
    def get_metadata(self, key, default=None):
        """获取元数据"""
        return self.metadata.get(key, default)


class FileAccessLog(CoreModel):
    """文件访问日志模型"""
    
    ACTION_CHOICES = [
        ('upload', '上传'),
        ('download', '下载'),
        ('view', '查看'),
        ('delete', '删除'),
    ]
    
    file = models.ForeignKey(
        FileStorage,
        on_delete=models.CASCADE,
        related_name='access_logs',
        verbose_name="关联文件"
    )
    
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name="操作类型"
    )
    
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="IP地址"
    )
    
    user_agent = models.TextField(
        null=True,
        blank=True,
        verbose_name="用户代理"
    )
    
    referer = models.URLField(
        null=True,
        blank=True,
        verbose_name="来源页面"
    )
    
    extra_data = models.JSONField(
        default=dict,
        blank=True,
        verbose_name="额外数据"
    )
    
    class Meta:
        db_table = 'zhi_file_access_log'
        verbose_name = '文件访问日志'
        verbose_name_plural = '文件访问日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['file', 'action']),
            models.Index(fields=['created_at']),
            models.Index(fields=['creator_id']),
        ]
    
    def __str__(self):
        return f"{self.get_action_display()} - {self.file.original_name}"
