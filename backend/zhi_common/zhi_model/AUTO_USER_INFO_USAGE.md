# 自动用户信息填充系统使用指南

## 概述

自动用户信息填充系统为您的模型提供了在创建实例资源时自动加入用户信息到数据库模型中的功能，支持 `bulk_create`、`create`、`update` 和 `bulk_update` 操作。

## 🚀 快速开始

### 方法1：使用混入类（推荐）

```python
from zhi_common.zhi_model.auto_user_mixin import AutoUserInfoMixin

class MyModel(AutoUserInfoMixin):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    
    # 这些字段会被自动填充（如果存在）：
    # creator_id, creator_name, modifier_id, modifier_name, org_id
```

### 方法2：使用装饰器

```python
from zhi_common.zhi_model.auto_user_mixin import auto_user_info

@auto_user_info
class MyModel(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
```

### 方法3：使用便捷函数

```python
from zhi_common.zhi_model.auto_user_mixin import (
    create_with_user_info,
    bulk_create_with_user_info,
    update_with_user_info,
    bulk_update_with_user_info
)

# 创建单个记录
instance = create_with_user_info(MyModel, {'name': '测试'})

# 批量创建
instances = bulk_create_with_user_info(MyModel, [
    {'name': '测试1'},
    {'name': '测试2'},
    {'name': '测试3'}
])

# 更新记录
update_with_user_info(
    MyModel.objects.filter(name__startswith='测试'),
    {'description': '批量更新'}
)

# 批量更新
instances = MyModel.objects.filter(name__startswith='测试')
for instance in instances:
    instance.description = f'更新：{instance.name}'

bulk_update_with_user_info(instances, ['description'])
```

## 📋 支持的操作

### 1. 自动创建（create）

```python
# 使用混入类的模型
instance = MyModel.objects.create(name='自动填充用户信息')
print(f"创建人: {instance.creator_name}")
print(f"修改人: {instance.modifier_name}")

# 手动指定用户
instance = MyModel.objects.create(name='指定用户', creator_id='user123')
```

### 2. 自动批量创建（bulk_create）

```python
# 创建多个实例，自动填充用户信息
instances = [
    MyModel(name='批量1'),
    MyModel(name='批量2'),
    MyModel(name='批量3')
]

created_instances = MyModel.objects.bulk_create(instances)
# 所有实例都会自动填充创建人和修改人信息
```

### 3. 自动更新（update）

```python
# 批量更新，自动填充修改人信息
updated_count = MyModel.objects.filter(
    name__startswith='批量'
).update(description='批量更新测试')

# 修改人信息会自动更新
```

### 4. 自动批量更新（bulk_update）

```python
# 获取要更新的实例
instances = MyModel.objects.filter(name__startswith='批量')

# 修改实例
for instance in instances:
    instance.description = f'更新：{instance.name}'

# 批量更新，自动填充修改人信息
MyModel.objects.bulk_update(instances, ['description'])
```

## 🔧 高级用法

### 1. 指定特定用户

```python
# 创建时指定特定用户
from zhi_common.zhi_model.enhanced_user_manager import enhanced_create

instance = enhanced_create(MyModel, {
    'name': '指定用户创建'
}, user_id='specific_user_id')
```

### 2. 批量操作指定用户

```python
from zhi_common.zhi_model.enhanced_user_manager import enhanced_bulk_create

instances = enhanced_bulk_create(MyModel, [
    {'name': '批量1'},
    {'name': '批量2'}
], user_id='specific_user_id')
```

### 3. 检查模型用户字段

```python
from zhi_common.zhi_model.auto_user_mixin import auto_user_info_service

field_info = auto_user_info_service.get_model_user_fields_info(MyModel)
print(field_info)
# 输出：{'has_creator_id': True, 'has_creator_name': True, ...}
```

## 📊 自动填充的字段

系统会自动检测并填充以下字段（如果存在）：

### 创建时填充：
- `creator_id` - 创建人ID
- `creator_name` - 创建人姓名
- `modifier_id` - 修改人ID（创建时与创建人相同）
- `modifier_name` - 修改人姓名（创建时与创建人相同）
- `org_id` - 组织ID

### 更新时填充：
- `modifier_id` - 修改人ID
- `modifier_name` - 修改人姓名

## 🎯 实际应用示例

### 示例1：产品管理模型

```python
from zhi_common.zhi_model.auto_user_mixin import AutoUserInfoMixin

class Product(AutoUserInfoMixin):
    name = models.CharField(max_length=100, verbose_name="产品名称")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格")
    description = models.TextField(blank=True, verbose_name="描述")
    
    class Meta:
        db_table = 'products'
        verbose_name = '产品'
        verbose_name_plural = '产品'

# 使用
product = Product.objects.create(name='iPhone 15', price=999.99)
print(f"产品创建人: {product.creator_name}")

# 批量创建
products_data = [
    {'name': 'iPhone 15 Pro', 'price': 1199.99},
    {'name': 'iPhone 15 Pro Max', 'price': 1399.99}
]
products = Product.objects.bulk_create([Product(**data) for data in products_data])
```

### 示例2：订单管理模型

```python
@auto_user_info
class Order(models.Model):
    order_no = models.CharField(max_length=50, unique=True, verbose_name="订单号")
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="总金额")
    status = models.CharField(max_length=20, default='pending', verbose_name="状态")
    
    class Meta:
        db_table = 'orders'

# 使用
order = Order.objects.create(
    order_no='ORD20240127001',
    total_amount=1999.98
)

# 更新订单状态
Order.objects.filter(order_no='ORD20240127001').update(status='paid')
# 修改人信息会自动更新
```

## ⚠️ 注意事项

### 1. 字段要求

确保您的模型包含相应的用户信息字段：

```python
class MyModel(models.Model):
    # 业务字段
    name = models.CharField(max_length=100)
    
    # 用户信息字段（可选，系统会自动检测）
    creator_id = models.CharField(max_length=64, null=True, blank=True, verbose_name="创建人ID")
    creator_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="创建人姓名")
    modifier_id = models.CharField(max_length=64, null=True, blank=True, verbose_name="修改人ID")
    modifier_name = models.CharField(max_length=100, null=True, blank=True, verbose_name="修改人姓名")
    org_id = models.CharField(max_length=64, null=True, blank=True, verbose_name="组织ID")
```

### 2. 用户上下文

系统依赖于请求上下文来获取当前用户信息。确保：

- 用户已登录
- 请求中包含有效的用户信息
- 中间件正确设置了用户上下文

### 3. 性能考虑

- 批量操作时，系统会优化用户信息查询，使用缓存减少数据库访问
- 大批量操作建议设置合适的 `batch_size`
- 用户信息缓存默认5分钟，可根据需要调整

### 4. 错误处理

如果用户信息获取失败，系统会：
- 记录警告日志
- 继续执行数据库操作（不会因为用户信息填充失败而中断业务）
- 相关字段保持原值或为空

## 🔍 调试和监控

### 启用调试日志

```python
import logging
logging.getLogger('enhanced_user_manager').setLevel(logging.DEBUG)
logging.getLogger('auto_user_mixin').setLevel(logging.DEBUG)
```

### 检查用户信息填充状态

```python
from zhi_common.zhi_model.enhanced_user_manager import enhanced_user_manager

# 检查当前用户信息
current_user = enhanced_user_manager.get_current_user_info()
print(f"当前用户: {current_user}")

# 检查模型字段
field_info = enhanced_user_manager.get_model_user_fields(MyModel)
print(f"模型字段信息: {field_info}")
```

## 🚀 最佳实践

1. **统一使用混入类**：推荐使用 `AutoUserInfoMixin` 混入类，保持代码一致性
2. **合理设置批量大小**：大批量操作时设置合适的 `batch_size`（建议1000）
3. **启用缓存**：确保Redis缓存正常工作，提高用户信息查询性能
4. **监控日志**：关注用户信息填充相关的日志，及时发现问题
5. **测试覆盖**：为使用自动用户信息填充的模型编写完整的测试用例

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                   自动用户信息填充系统                        │
├─────────────────────────────────────────────────────────────┤
│  AutoUserInfoMixin     │  @auto_user_info  │  便捷函数        │
│  (混入类)              │  (装饰器)         │  (函数接口)      │
├─────────────────────────────────────────────────────────────┤
│                 EnhancedManager & EnhancedQuerySet           │
│                 (增强的管理器和查询集)                        │
├─────────────────────────────────────────────────────────────┤
│                 EnhancedUserManager                         │
│                 (增强用户管理器)                             │
├─────────────────────────────────────────────────────────────┤
│  UserInfoManager  │  用户上下文获取  │  缓存系统             │
│  (用户信息管理)    │  (请求上下文)    │  (性能优化)           │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件说明

#### 1. 用户接口层
- **AutoUserInfoMixin**: 混入类，为模型提供自动用户信息填充功能
- **@auto_user_info**: 装饰器，为现有模型添加自动用户信息填充
- **便捷函数**: 提供简单易用的函数接口

#### 2. 数据库操作层
- **EnhancedManager**: 增强的模型管理器，重写了 create、bulk_create 等方法
- **EnhancedQuerySet**: 增强的查询集，自动处理用户信息填充

#### 3. 业务逻辑层
- **EnhancedUserManager**: 核心业务逻辑，处理用户信息的准备和填充

#### 4. 基础服务层
- **UserInfoManager**: 用户信息管理，提供用户信息查询和缓存
- **用户上下文获取**: 从请求中获取当前用户信息
- **缓存系统**: 优化用户信息查询性能

### 数据流程图

```
用户操作 → 接口层 → 数据库操作层 → 业务逻辑层 → 基础服务层
   ↓         ↓           ↓            ↓           ↓
创建/更新 → 混入类/装饰器 → 增强管理器 → 用户信息准备 → 用户信息查询
   ↓         ↓           ↓            ↓           ↓
数据保存 ← 自动填充 ← 增强查询集 ← 用户信息填充 ← 缓存优化
```

### 工作原理

1. **用户发起操作**: 调用 create、bulk_create、update、bulk_update 等方法
2. **接口层拦截**: 混入类或装饰器拦截数据库操作
3. **获取用户上下文**: 从当前请求中获取用户信息
4. **准备用户数据**: 根据操作类型准备相应的用户信息字段
5. **执行数据库操作**: 使用填充了用户信息的数据执行原始操作
6. **缓存优化**: 缓存用户信息，减少重复查询

### 扩展性设计

#### 支持的操作类型
- **CREATE**: 填充 creator_id, creator_name, modifier_id, modifier_name, org_id
- **UPDATE**: 填充 modifier_id, modifier_name
- **BULK_CREATE**: 批量填充创建人信息
- **BULK_UPDATE**: 批量填充修改人信息

#### 字段检测机制
系统会自动检测模型是否包含用户信息字段：
- `creator_id` - 创建人ID
- `creator_name` - 创建人姓名
- `modifier_id` - 修改人ID
- `modifier_name` - 修改人姓名
- `org_id` - 组织ID

只有存在的字段才会被填充，不存在的字段会被忽略。

#### 缓存策略
- **用户信息缓存**: 5分钟缓存时间，减少用户表查询
- **批量查询优化**: 批量获取用户信息，减少数据库访问
- **缓存失效机制**: 用户信息变更时自动清除相关缓存

### 性能优化

#### 1. 缓存机制
```python
# 用户信息缓存配置
USER_INFO_CACHE_TIMEOUT = 300  # 5分钟
CACHE_PREFIX = 'user_info_mgr'

# 批量查询优化
user_info_manager.batch_get_user_info(user_ids, use_cache=True)
```

#### 2. 批量操作优化
```python
# 批量创建优化
BULK_CREATE_BATCH_SIZE = 1000  # 每批1000条记录

# 批量更新优化
BULK_UPDATE_BATCH_SIZE = 1000  # 每批1000条记录
```

#### 3. 数据库查询优化
- 使用 `select_related` 优化外键查询
- 批量查询用户信息，避免 N+1 查询问题
- 智能字段检测，只填充存在的字段

### 容错机制

#### 1. 用户信息获取失败
- 记录警告日志
- 继续执行数据库操作
- 不影响业务数据保存

#### 2. 字段不存在
- 自动跳过不存在的字段
- 不会抛出异常
- 保证系统稳定性

#### 3. 缓存失效
- 自动降级到数据库查询
- 不影响功能正常使用
- 记录相关日志

通过这个系统，您可以轻松实现在创建实例资源时自动加入用户信息到数据库模型中，支持所有常见的数据库操作！🎉
