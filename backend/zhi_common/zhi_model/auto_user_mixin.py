"""
自动用户信息混入类

为模型提供自动用户信息填充功能，包括：
- 自动填充创建人和修改人信息
- 支持 bulk_create、create、update、bulk_update 等操作
- 提供装饰器方式快速集成
"""

from typing import Dict, Any, List
from django.db import models
from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver
from functools import wraps
from zhi_common.zhi_model.enhanced_user_manager import EnhancedUserManager, enhanced_user_manager
from zhi_common.zhi_logger import get_logger

logger = get_logger(module_name="auto_user_mixin")


class AutoUserInfoMixin(models.Model):
    """
    自动用户信息混入类
    
    为模型添加自动用户信息填充功能
    使用此混入的模型将自动在创建和更新时填充用户信息
    """
    
    # 使用增强的管理器
    objects = EnhancedUserManager()
    
    class Meta:
        abstract = True
    
    def save(self, *args, **kwargs):
        """重写save方法，自动填充用户信息"""
        try:
            # 判断是否为新建
            is_create = self.pk is None
            
            if is_create:
                # 创建时填充创建人信息
                current_user = enhanced_user_manager.get_current_user_info()
                user_id = current_user['user_id']
                
                if user_id:
                    user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
                    if user_info:
                        # 填充创建人信息（如果未设置）
                        if hasattr(self, 'creator_id') and not getattr(self, 'creator_id', None):
                            self.creator_id = user_info['id']
                        if hasattr(self, 'creator_name') and not getattr(self, 'creator_name', None):
                            self.creator_name = user_info['name']
                        
                        # 填充组织信息
                        if hasattr(self, 'org_id') and not getattr(self, 'org_id', None):
                            self.org_id = current_user['org_id']
                        
                        # 创建时同时设置修改人信息
                        if hasattr(self, 'modifier_id') and not getattr(self, 'modifier_id', None):
                            self.modifier_id = user_info['id']
                        if hasattr(self, 'modifier_name') and not getattr(self, 'modifier_name', None):
                            self.modifier_name = user_info['name']
            else:
                # 更新时填充修改人信息
                current_user = enhanced_user_manager.get_current_user_info()
                user_id = current_user['user_id']
                
                if user_id:
                    user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
                    if user_info:
                        if hasattr(self, 'modifier_id'):
                            self.modifier_id = user_info['id']
                        if hasattr(self, 'modifier_name'):
                            self.modifier_name = user_info['name']
            
            # 调用父类的save方法
            super().save(*args, **kwargs)
            
            logger.debug(f"自动用户信息填充完成: {self.__class__.__name__} - {self.pk}")
            
        except Exception as e:
            logger.error(f"自动用户信息填充失败: {self.__class__.__name__} - {e}")
            # 即使填充失败，也要保存数据
            super().save(*args, **kwargs)


def auto_user_info(model_class):
    """
    装饰器：为模型类添加自动用户信息填充功能
    
    使用方式：
    @auto_user_info
    class MyModel(models.Model):
        name = models.CharField(max_length=100)
        # 会自动处理 creator_id, creator_name, modifier_id, modifier_name 等字段
    """
    
    # 检查模型是否已经有用户信息字段
    field_names = [f.name for f in model_class._meta.fields]
    has_user_fields = any(field in field_names for field in ['creator_id', 'creator_name', 'modifier_id', 'modifier_name'])
    
    if not has_user_fields:
        logger.warning(f"模型 {model_class.__name__} 没有用户信息字段，自动用户信息填充可能无效")
    
    # 替换默认管理器
    if not hasattr(model_class, 'objects') or not isinstance(model_class.objects, EnhancedUserManager):
        model_class.add_to_class('objects', EnhancedUserManager())
    
    # 保存原始的save方法
    original_save = model_class.save
    
    def enhanced_save(self, *args, **kwargs):
        """增强的save方法"""
        try:
            # 判断是否为新建
            is_create = self.pk is None
            
            if is_create:
                # 创建时填充创建人信息
                current_user = enhanced_user_manager.get_current_user_info()
                user_id = current_user['user_id']
                
                if user_id:
                    user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
                    if user_info:
                        # 填充创建人信息（如果未设置）
                        if hasattr(self, 'creator_id') and not getattr(self, 'creator_id', None):
                            self.creator_id = user_info['id']
                        if hasattr(self, 'creator_name') and not getattr(self, 'creator_name', None):
                            self.creator_name = user_info['name']
                        
                        # 填充组织信息
                        if hasattr(self, 'org_id') and not getattr(self, 'org_id', None):
                            self.org_id = current_user['org_id']
                        
                        # 创建时同时设置修改人信息
                        if hasattr(self, 'modifier_id') and not getattr(self, 'modifier_id', None):
                            self.modifier_id = user_info['id']
                        if hasattr(self, 'modifier_name') and not getattr(self, 'modifier_name', None):
                            self.modifier_name = user_info['name']
            else:
                # 更新时填充修改人信息
                current_user = enhanced_user_manager.get_current_user_info()
                user_id = current_user['user_id']
                
                if user_id:
                    user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
                    if user_info:
                        if hasattr(self, 'modifier_id'):
                            self.modifier_id = user_info['id']
                        if hasattr(self, 'modifier_name'):
                            self.modifier_name = user_info['name']
            
            # 调用原始的save方法
            return original_save(self, *args, **kwargs)
            
        except Exception as e:
            logger.error(f"自动用户信息填充失败: {model_class.__name__} - {e}")
            # 即使填充失败，也要保存数据
            return original_save(self, *args, **kwargs)
    
    # 替换save方法
    model_class.save = enhanced_save
    
    logger.info(f"为模型 {model_class.__name__} 添加了自动用户信息填充功能")
    
    return model_class


class AutoUserInfoService:
    """
    自动用户信息服务
    
    提供统一的接口来处理各种数据库操作的用户信息自动填充
    """
    
    def __init__(self):
        self.enhanced_manager = enhanced_user_manager
    
    def create_with_user_info(self, model_class: models.Model, data: Dict[str, Any], user_id: str = None):
        """创建记录并自动填充用户信息"""
        return self.enhanced_manager.enhanced_create(model_class, data, user_id)
    
    def bulk_create_with_user_info(
        self, 
        model_class: models.Model, 
        data_list: List[Dict[str, Any]], 
        user_id: str = None,
        batch_size: int = 1000
    ):
        """批量创建记录并自动填充用户信息"""
        return self.enhanced_manager.enhanced_bulk_create(model_class, data_list, user_id, batch_size)
    
    def update_with_user_info(
        self, 
        queryset: models.QuerySet, 
        data: Dict[str, Any], 
        user_id: str = None
    ):
        """更新记录并自动填充修改人信息"""
        return self.enhanced_manager.enhanced_update(queryset, data, user_id)
    
    def bulk_update_with_user_info(
        self, 
        instances: List[models.Model], 
        fields: List[str], 
        user_id: str = None,
        batch_size: int = 1000
    ):
        """批量更新记录并自动填充修改人信息"""
        return self.enhanced_manager.enhanced_bulk_update(instances, fields, user_id, batch_size)
    
    def get_model_user_fields_info(self, model_class: models.Model) -> Dict[str, bool]:
        """获取模型的用户字段信息"""
        return self.enhanced_manager.get_model_user_fields(model_class)


# 全局服务实例
auto_user_info_service = AutoUserInfoService()


# 便捷函数
def create_with_user_info(model_class: models.Model, data: Dict[str, Any], user_id: str = None):
    """便捷函数：创建记录并自动填充用户信息"""
    return auto_user_info_service.create_with_user_info(model_class, data, user_id)


def bulk_create_with_user_info(
    model_class: models.Model, 
    data_list: List[Dict[str, Any]], 
    user_id: str = None,
    batch_size: int = 1000
):
    """便捷函数：批量创建记录并自动填充用户信息"""
    return auto_user_info_service.bulk_create_with_user_info(model_class, data_list, user_id, batch_size)


def update_with_user_info(
    queryset: models.QuerySet, 
    data: Dict[str, Any], 
    user_id: str = None
):
    """便捷函数：更新记录并自动填充修改人信息"""
    return auto_user_info_service.update_with_user_info(queryset, data, user_id)


def bulk_update_with_user_info(
    instances: List[models.Model], 
    fields: List[str], 
    user_id: str = None,
    batch_size: int = 1000
):
    """便捷函数：批量更新记录并自动填充修改人信息"""
    return auto_user_info_service.bulk_update_with_user_info(instances, fields, user_id, batch_size)
