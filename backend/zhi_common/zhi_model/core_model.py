from typing import Any, Dict
from uuid import uuid4

from asgiref.sync import sync_to_async
from django.apps import apps
from django.db import models
from django.utils import timezone

from application.settings import base
from zhi_common.zhi_logger import get_logger
from zhi_common.zhi_model.audit_config_manager import audit_config_manager
from zhi_common.zhi_model.user_info_manager import user_info_manager
from zhi_common.zhi_records.async_record_changes_workers import (
    async_record_changes, async_record_log_action, record_changes, record_log_action,
    )
# 导入增强管理器，避免MRO冲突
from zhi_common.zhi_model.enhanced_user_manager import EnhancedManager
from zhi_common.zhi_records.core_records_change import NONE_RECORD_FIELD_NAME, ZhiRecordChanges

# 创建专用的日志记录器
logger = get_logger("core_model")


class CoreModel(models.Model):
    """
    核心标准抽象模型模型,可直接继承使用
    增加审计字段, 覆盖字段时, 字段名称请勿修改, 必须统一审计字段名称
    """
    objects = EnhancedManager()  # 使用增强管理器，支持自动用户信息填充
    seq = models.BigAutoField(
        primary_key=True, help_text="seq", verbose_name="seq", db_comment='主键Seq'
        )
    id = models.CharField(
        max_length=63, default='', unique=True, db_index=True, help_text="Id", verbose_name="Id", db_comment='Id'
        )
    # 优化用户字段设计：使用CharField存储用户ID，避免外键约束
    creator_id = models.CharField(
        max_length=63, null=True, blank=True, db_index=True,
        verbose_name='创建人ID', help_text="创建人用户ID", db_comment='创建人用户ID'
    )
    creator_name = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name='创建人姓名', help_text="创建人姓名", db_comment='创建人姓名'
    )
    modifier_id = models.CharField(
        max_length=63, null=True, blank=True, db_index=True,
        verbose_name='修改人ID', help_text="修改人用户ID", db_comment='修改人用户ID'
    )
    modifier_name = models.CharField(
        max_length=255, null=True, blank=True,
        verbose_name='修改人姓名', help_text="修改人姓名", db_comment='修改人姓名'
    )
    updated_at = models.DateTimeField(
        auto_now=True, null=True, blank=True, help_text="修改时间", verbose_name="修改时间", db_comment='修改时间'
        )
    created_at = models.DateTimeField(
        auto_now_add=True, null=True, blank=True, help_text="创建时间", verbose_name="创建时间", db_comment='创建时间'
        )
    is_deleted = models.BooleanField(
        verbose_name="是否软删除", help_text='是否软删除', default=False, db_index=True, db_comment='是否软删除'
        )
    deleted_at = models.DateTimeField(
        default=None, null=True, blank=True,
        help_text="删除时间", verbose_name="删除时间", db_comment='删除时间'
        )

    @classmethod
    def get_db_table(cls):
        return cls._meta.db_table

    def get_previous_instance(self):
        """获取上一次保存的实例，用于比较字段变化"""
        return type(self).objects.filter(id=self.id).order_by('-updated_at').first()

    def get_self_user_info(self):
        return {
            'creator_id': self.creator_id,
            'creator_name': self.creator_name,
            'modifier_id': self.modifier_id,
            'modifier_name': self.modifier_name,
            # 'dept_belong_id': self.dept_belong_id,
            }

    @property
    def to_dict_no_pub_fields(self) -> Dict[str, Any]:
        """
        去除掉公共字段信息
        返回不包含公共字段的字典表示
        """
        data = {}
        try:
            for field in self._meta.fields:
                field_name = field.attname

                if field_name in NONE_RECORD_FIELD_NAME:
                    continue

                try:
                    value = getattr(self, field_name, None)
                    value = ZhiRecordChanges.__field_to_value__(value)
                    data[field_name] = value
                except Exception as e:
                    logger.warning(
                        f"获取字段值失败: {field_name}",
                        category="model",
                        extra_data={
                            'model': self.__class__.__name__,
                            'field': field_name,
                            'error': str(e)
                            }
                        )
                    data[field_name] = None

        except Exception as e:
            logger.error(
                f"to_dict_no_pub_fields 失败: {str(e)}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'error': str(e)
                    }
                )

        return data

    @property
    def to_dict(self) -> Dict[str, Any]:
        """
        获取模型的字典表示
        包含所有字段
        """
        data = {}
        try:
            for field in self._meta.fields:
                field_name = field.attname

                try:
                    value = getattr(self, field_name, None)
                    value = ZhiRecordChanges.__field_to_value__(value)
                    data[field_name] = value
                except Exception as e:
                    logger.warning(
                        f"获取字段值失败: {field_name}",
                        category="model",
                        extra_data={
                            'model': self.__class__.__name__,
                            'field': field_name,
                            'error': str(e)
                            }
                        )
                    data[field_name] = None

        except Exception as e:
            logger.error(
                f"to_dict 失败: {str(e)}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'error': str(e)
                    }
                )

        return data

    def get_model_info(self):
        tmp_fields = {}
        [tmp_fields.update(
            {field.attname: getattr(field, 'verbose_name', '未配置')}
            ) for field in self._meta.fields]
        model_info = {
            'field_names': tmp_fields,
            'model_info': {
                'app_label': self._meta.app_label,
                'object_name': self._meta.object_name,
                },
            }
        return model_info

    async def async_save(self, *args, **kwargs):
        """
        异步保存方法，专为异步上下文设计
        在异步视图中使用: await model_instance.async_save()

        改进：
        - 更好的错误处理
        - 使用新的日志SDK
        - 优化审计日志记录
        """
        previous_instance = None
        is_create = not bool(self.id)

        try:
            # 为新记录生成ID
            if is_create and not self.id:
                t_db_table = self.get_db_table().split('_', 1)[1]
                if t_db_table.endswith('s'):
                    t_db_table = t_db_table[:-1]
                self.id = f'{t_db_table}-{uuid4().hex}'[:63]

            # 获取变更前的实例
            if not is_create:
                try:
                    previous_instance = await sync_to_async(self.get_previous_instance)()
                    if previous_instance:
                        # 获取变更前实例数据，确保包含id字段用于资源标识
                        previous_instance_data = previous_instance.to_dict_no_pub_fields.copy()
                        previous_instance_data['id'] = str(previous_instance.id)
                        previous_instance = previous_instance_data
                except Exception as e:
                    logger.warning(
                        f"获取变更前实例失败: {str(e)}",
                        category="model",
                        extra_data={
                            'model': self.__class__.__name__,
                            'id': str(self.id),
                            'error': str(e)
                            }
                        )

            # 异步保存
            await sync_to_async(super().save)(*args, **kwargs)

            # 记录审计日志
            if self.is_audit_log_enabled():
                try:
                    # 获取当前实例数据，但需要确保包含id字段用于资源标识
                    current_instance_data = self.to_dict_no_pub_fields.copy()
                    current_instance_data['id'] = str(self.id)  # 使用字符串id作为资源标识

                    await async_record_changes(
                        model_info=self.get_model_info(),
                        user_info=self.get_self_user_info(),
                        previous_instance=previous_instance,
                        current_instance=current_instance_data
                        )
                except Exception as e:
                    logger.error(
                        f"异步记录审计日志失败: {str(e)}",
                        category="audit",
                        extra_data={
                            'model': self.__class__.__name__,
                            'id': str(self.id),
                            'is_create': is_create,
                            'error': str(e)
                            }
                        )

            # 记录操作日志
            action = "创建" if is_create else "更新"
            logger.info(
                f"模型异步保存成功: {self.__class__.__name__} {action}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'id': str(self.id),
                    'action': action,
                    'has_audit': self.is_audit_log_enabled()
                    }
                )

        except Exception as e:
            logger.error(
                f"异步保存失败: {str(e)}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'id': str(getattr(self, 'id', 'unknown')),
                    'is_create': is_create,
                    'error': str(e)
                    }
                )
            raise

    def save(self, *args, **kwargs):
        """
        同步保存方法，兼容现有代码
        自动适配异步记录，改进错误处理
        """
        previous_instance = None
        is_create = not bool(self.id)

        try:
            # 为新记录生成ID
            if is_create and not self.id:
                t_db_table = self.get_db_table().split('_', 1)[1]
                if t_db_table.endswith('s'):
                    t_db_table = t_db_table[:-1]
                self.id = f'{t_db_table}-{uuid4().hex}'[:63]

            # 自动填充用户信息
            self._auto_fill_user_info(is_create)

            # 获取变更前的实例
            if not is_create:
                try:
                    previous_instance = self.get_previous_instance()
                    if previous_instance:
                        # 获取变更前实例数据，确保包含id字段用于资源标识
                        previous_instance_data = previous_instance.to_dict_no_pub_fields.copy()
                        previous_instance_data['id'] = str(previous_instance.id)
                        previous_instance = previous_instance_data
                except Exception as e:
                    logger.warning(
                        f"获取变更前实例失败: {str(e)}",
                        category="model",
                        extra_data={
                            'model': self.__class__.__name__,
                            'id': str(self.id),
                            'error': str(e)
                            }
                        )

            # 同步保存
            super().save(*args, **kwargs)

            # 记录审计日志
            if self.is_audit_log_enabled():
                try:
                    # 获取当前实例数据，但需要确保包含id字段用于资源标识
                    current_instance_data = self.to_dict_no_pub_fields.copy()
                    current_instance_data['id'] = str(self.id)  # 使用字符串id作为资源标识

                    record_changes(
                        model_info=self.get_model_info(),
                        user_info=self.get_self_user_info(),
                        previous_instance=previous_instance,
                        current_instance=current_instance_data
                        )
                except Exception as e:
                    logger.error(
                        f"记录审计日志失败: {str(e)}",
                        category="audit",
                        extra_data={
                            'model': self.__class__.__name__,
                            'id': str(self.id),
                            'is_create': is_create,
                            'error': str(e)
                            }
                        )

            # 记录操作日志
            action = "创建" if is_create else "更新"
            logger.info(
                f"模型保存成功: {self.__class__.__name__} {action}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'id': str(self.id),
                    'action': action,
                    'has_audit': self.is_audit_log_enabled()
                    }
                )

        except Exception as e:
            logger.error(
                f"保存失败: {str(e)}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'id': str(getattr(self, 'id', 'unknown')),
                    'is_create': is_create,
                    'error': str(e)
                    }
                )
            raise

    def _auto_fill_user_info(self, is_create: bool = False):
        """自动填充用户信息"""
        try:
            from zhi_common.zhi_model.enhanced_user_manager import enhanced_user_manager

            # 获取当前用户信息
            current_user = enhanced_user_manager.get_current_user_info()
            user_id = current_user.get('user_id')

            if not user_id:
                return

            # 获取用户详细信息
            user_info = enhanced_user_manager.user_info_manager.get_user_info(user_id)
            if not user_info:
                return

            if is_create:
                # 创建时填充创建人信息
                if hasattr(self, 'creator_id') and not getattr(self, 'creator_id', None):
                    self.creator_id = user_info['id']
                if hasattr(self, 'creator_name') and not getattr(self, 'creator_name', None):
                    self.creator_name = user_info['name']

                # 填充组织信息
                if hasattr(self, 'org_id') and not getattr(self, 'org_id', None):
                    self.org_id = current_user.get('org_id')

                # 创建时同时设置修改人信息
                if hasattr(self, 'modifier_id') and not getattr(self, 'modifier_id', None):
                    self.modifier_id = user_info['id']
                if hasattr(self, 'modifier_name') and not getattr(self, 'modifier_name', None):
                    self.modifier_name = user_info['name']
            else:
                # 更新时填充修改人信息
                if hasattr(self, 'modifier_id'):
                    self.modifier_id = user_info['id']
                if hasattr(self, 'modifier_name'):
                    self.modifier_name = user_info['name']

        except Exception as e:
            logger.warning(f"自动填充用户信息失败: {e}")
            # 不抛出异常，避免影响正常的保存流程

    async def async_delete(self, using=None, force_delete: bool = False):
        """
        异步删除方法

        Args:
            using: 数据库别名
            force_delete: 是否强制物理删除（默认软删除）

        改进：
        - 支持软删除和硬删除
        - 更好的错误处理
        - 使用新的日志SDK
        """
        try:
            # 记录删除前的状态，确保包含id字段用于资源标识
            previous_instance = self.to_dict_no_pub_fields.copy()
            previous_instance['id'] = str(self.id)

            if force_delete:
                # 硬删除
                if self.is_audit_log_enabled():
                    try:
                        await async_record_log_action(
                            action='硬删除',
                            user_info=self.get_self_user_info(),
                            resource_id=str(self.id),
                            extra_data=previous_instance
                            )
                    except Exception as e:
                        logger.error(
                            f"记录硬删除审计日志失败: {str(e)}",
                            category="audit",
                            extra_data={
                                'model': self.__class__.__name__,
                                'id': str(self.id),
                                'error': str(e)
                                }
                            )

                # 执行物理删除
                await sync_to_async(super().delete)(using=using)

                logger.info(
                    f"模型硬删除成功: {self.__class__.__name__}",
                    category="model",
                    extra_data={
                        'model': self.__class__.__name__,
                        'id': str(self.id),
                        'action': '硬删除'
                        }
                    )
            else:
                # 软删除
                self.is_deleted = True
                self.deleted_at = timezone.now()

                if self.is_audit_log_enabled():
                    try:
                        await async_record_log_action(
                            action='软删除',
                            user_info=self.get_self_user_info(),
                            resource_id=str(self.id),
                            extra_data=previous_instance
                            )
                    except Exception as e:
                        logger.error(
                            f"记录软删除审计日志失败: {str(e)}",
                            category="audit",
                            extra_data={
                                'model': self.__class__.__name__,
                                'id': str(self.id),
                                'error': str(e)
                                }
                            )

                # 保存软删除状态
                await self.async_save(using=using)

                logger.info(
                    f"模型软删除成功: {self.__class__.__name__}",
                    category="model",
                    extra_data={
                        'model': self.__class__.__name__,
                        'id': str(self.id),
                        'action': '软删除'
                        }
                    )

        except Exception as e:
            logger.error(
                f"异步删除失败: {str(e)}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'id': str(self.id),
                    'force_delete': force_delete,
                    'error': str(e)
                    }
                )
            raise

    def delete(self, using=None, keep_parents=False, force_delete: bool = False):
        """
        同步删除方法

        Args:
            using: 数据库别名
            keep_parents: 是否保留父对象
            force_delete: 是否强制物理删除（默认软删除）

        改进：
        - 支持软删除和硬删除
        - 更好的错误处理
        - 使用新的日志SDK
        """
        try:
            # 记录删除前的状态，确保包含id字段用于资源标识
            previous_instance = self.to_dict_no_pub_fields.copy()
            previous_instance['id'] = str(self.id)

            if force_delete:
                # 硬删除
                if self.is_audit_log_enabled():
                    try:
                        record_log_action(
                            action='硬删除',
                            user_info=self.get_self_user_info(),
                            resource_id=str(self.id),
                            extra_data=previous_instance
                            )
                    except Exception as e:
                        logger.error(
                            f"记录硬删除审计日志失败: {str(e)}",
                            category="audit",
                            extra_data={
                                'model': self.__class__.__name__,
                                'id': str(self.id),
                                'error': str(e)
                                }
                            )

                # 执行物理删除
                super().delete(using=using, keep_parents=keep_parents)

                logger.info(
                    f"模型硬删除成功: {self.__class__.__name__}",
                    category="model",
                    extra_data={
                        'model': self.__class__.__name__,
                        'id': str(self.id),
                        'action': '硬删除'
                        }
                    )
            else:
                # 软删除
                self.is_deleted = True
                self.deleted_at = timezone.now()

                if self.is_audit_log_enabled():
                    try:
                        record_log_action(
                            action='软删除',
                            user_info=self.get_self_user_info(),
                            resource_id=str(self.id),
                            extra_data=previous_instance
                            )
                    except Exception as e:
                        logger.error(
                            f"记录软删除审计日志失败: {str(e)}",
                            category="audit",
                            extra_data={
                                'model': self.__class__.__name__,
                                'id': str(self.id),
                                'error': str(e)
                                }
                            )

                # 保存软删除状态
                self.save(using=using)

                logger.info(
                    f"模型软删除成功: {self.__class__.__name__}",
                    category="model",
                    extra_data={
                        'model': self.__class__.__name__,
                        'id': str(self.id),
                        'action': '软删除'
                        }
                    )

        except Exception as e:
            logger.error(
                f"删除失败: {str(e)}",
                category="model",
                extra_data={
                    'model': self.__class__.__name__,
                    'id': str(self.id),
                    'force_delete': force_delete,
                    'error': str(e)
                    }
                )
            raise

    @property
    @sync_to_async
    def async_to_dict(self):
        """异步获取字典表示"""
        return self.to_dict

    def is_audit_log_enabled(self) -> bool:
        """
        检查当前模型是否开启了审计日志
        使用新的配置管理器，支持子项目独立配置
        """
        try:
            model_path = f'{self._meta.app_label}.{self._meta.object_name}'
            return audit_config_manager.is_model_audit_enabled(model_path)
        except Exception as e:
            logger.warning(
                f"检查审计日志配置失败: {str(e)}",
                category="audit",
                extra_data={
                    'model': self.__class__.__name__,
                    'error': str(e)
                    }
                )
            return False

    class Meta:
        abstract = True
        verbose_name = '核心模型'
        verbose_name_plural = verbose_name


class ZhiCoreModel(CoreModel):
    org_id = models.CharField(
        max_length=63, help_text="数据归属部门", null=True, blank=True, verbose_name="数据归属部门",
        db_comment='数据归属部门'
        )

    # description = models.CharField(
    #     max_length=255, verbose_name="描述", null=True, blank=True, help_text="描述", db_comment='描述'
    #     )

    def get_self_user_info(self):
        """获取当前用户信息"""
        from zhi_common.zhi_tools.g_local_thread_user_info import get_request_user_info
        user_info = get_request_user_info()
        return {
            'creator_id': user_info.get('id'),
            'modifier': user_info.get('id'),
            'org_id': user_info.get('org_id'),
            }

    def save(self, *args, **kwargs):
        """同步保存方法，自动填充审计字段"""
        user_info = self.get_self_user_info()

        # 如果是新建记录，设置创建人和部门
        if not self.id:
            creator_id = self.creator_id or user_info['creator_id']
            if creator_id:
                user_info_manager.set_creator_info(self, creator_id)
            self.org_id = self.org_id or user_info['org_id']

        # 总是更新修改人
        modifier_id = self.modifier_id or user_info['modifier'] or self.creator_id
        if modifier_id:
            user_info_manager.set_modifier_info(self, modifier_id)

        super().save(*args, **kwargs)

    async def async_save(self, *args, **kwargs):
        """异步保存方法，自动填充审计字段"""
        user_info = self.get_self_user_info()

        # 如果是新建记录，设置创建人和部门
        if not self.id:
            creator_id = self.creator_id or user_info['creator_id']
            if creator_id:
                user_info_manager.set_creator_info(self, creator_id)
            self.org_id = self.org_id or user_info['org_id']

        # 总是更新修改人
        modifier_id = self.modifier_id or user_info['modifier']
        if modifier_id:
            user_info_manager.set_modifier_info(self, modifier_id)

        await super().async_save(*args, **kwargs)

    def get_creator_display_name(self) -> str:
        """获取创建人显示名称"""
        return self.creator_name or user_info_manager.get_user_display_name(self.creator_id)

    def get_modifier_display_name(self) -> str:
        """获取修改人显示名称"""
        return self.modifier_name or user_info_manager.get_user_display_name(self.modifier_id)

    def refresh_user_info(self):
        """刷新用户信息（从数据库重新获取）"""
        if self.creator_id:
            user_info_manager.set_creator_info(self, self.creator_id)
        if self.modifier_id:
            user_info_manager.set_modifier_info(self, self.modifier_id)

    @property
    def creator_info(self) -> dict:
        """获取创建人完整信息"""
        if not self.creator_id:
            return {}
        return user_info_manager.get_user_info(self.creator_id) or {}

    @property
    def modifier_info(self) -> dict:
        """获取修改人完整信息"""
        if not self.modifier_id:
            return {}
        return user_info_manager.get_user_info(self.modifier_id) or {}

    class Meta:
        abstract = True
        verbose_name = 'Zhiadmin核心模型'
        verbose_name_plural = verbose_name


def get_all_models_objects(model_name=None):
    """
    获取所有 models 对象
    :return: {}
    """
    base.ALL_MODELS_OBJECTS = {}
    if not base.ALL_MODELS_OBJECTS:
        all_models = apps.get_models()
        for item in list(all_models):
            table = {
                "tableName": item._meta.verbose_name,
                "table": item.__name__,
                "tableFields": []
                }
            for field in item._meta.fields:
                fields = {
                    "title": field.verbose_name,
                    "field": field.name
                    }
                table['tableFields'].append(fields)
            base.ALL_MODELS_OBJECTS.setdefault(item.__name__, {"table": table, "object": item})
    if model_name:
        return base.ALL_MODELS_OBJECTS[model_name] or {}
    return base.ALL_MODELS_OBJECTS or {}
