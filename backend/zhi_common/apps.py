"""
ZhiCommon 应用配置

ZhiAdmin 通用组件应用，包含：
- 通用模型基类
- 日志SDK
- 审计配置管理
- 记录变更工具
- 通用工具类
"""

from django.apps import AppConfig


class ZhiCommonConfig(AppConfig):
    """ZhiCommon 应用配置"""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'zhi_common'
    verbose_name = 'ZhiAdmin 通用组件'
    
    def ready(self):
        """应用就绪时的初始化操作"""
        try:
            # 初始化审计配置管理器
            from zhi_common.zhi_model.audit_config_manager import audit_config_manager
            audit_config_manager.initialize()
            
            # 初始化日志SDK
            from zhi_common.zhi_logger import get_logger
            logger = get_logger(__name__)
            logger.info("ZhiCommon 应用初始化完成", category="system")
            
        except Exception as e:
            # 避免初始化错误影响应用启动
            import warnings
            warnings.warn(f"ZhiCommon 应用初始化警告: {str(e)}", UserWarning)
