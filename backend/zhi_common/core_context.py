import uuid

from contextvars import ContextVar

from loguru import logger

trace_id_var = ContextVar('trace_id', default='')


def get_trace_id(default: str = None, prefix: str = 'trace') -> str:
    """获取当前请求的Trace ID"""
    return trace_id_var.get() or default or f"{prefix}-{uuid.uuid4()}"


def bind_logger() -> logger:
    """获取已绑定Trace ID的日志记录器"""
    return logger.bind(trace_id=get_trace_id())


def set_trace_id(trace_id: str = '', prefix: str = 'trace') -> None:
    """设置Trace ID到上下文"""
    if not trace_id:
        trace_id = f"{prefix}-{uuid.uuid4()}"
    trace_id_var.set(trace_id)
