@echo off
REM OAuth API Quick Test Script

echo ========================================
echo    OAuth API Response Format Test
echo ========================================

REM 切换到backend目录
cd /d "%~dp0\.."

REM Activate virtual environment
call .venv\Scripts\activate.bat

REM Set environment variables
set DJANGO_SETTINGS_MODULE=application.settings.base
set PYTHONPATH=%CD%

echo 1. Running basic fixes...
python fix_oauth_issues.py

echo.
echo 2. Starting test server...
start /B python manage.py runserver 0.0.0.0:8001

echo Waiting for server to start...
timeout /t 8 /nobreak >nul

echo.
echo 3. Running OAuth API tests...
python test_oauth_complete.py

echo.
echo 4. Test completed!
echo.
echo Test Results:
echo - If you see "All tests passed" - OAuth response format unification successful
echo - If some tests failed - Check specific error messages
echo.
echo Related Links:
echo - API Docs: http://localhost:8001/api/oauth/docs
echo - Migration Doc: zhi_oauth\RESPONSE_FORMAT_MIGRATION.md
echo.
pause
