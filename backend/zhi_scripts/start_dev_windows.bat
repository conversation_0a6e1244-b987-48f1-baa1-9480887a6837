@echo off
REM Windows开发环境启动脚本 (批处理版本)
REM 基于您提供的Linux脚本适配Windows环境

echo ========================================
echo    ZhiAdmin Windows 开发环境启动脚本
echo ========================================

REM 切换到backend目录
cd /d "%~dp0\.."

REM 激活虚拟环境
echo 激活虚拟环境...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo 错误: 无法激活虚拟环境
    echo 请确保虚拟环境存在: .venv
    pause
    exit /b 1
)

REM 设置环境变量
set DJANGO_SETTINGS_MODULE=application.settings.base
set PYTHONPATH=%CD%

REM 检查依赖
echo 检查依赖包...
python -c "import gevent" 2>nul
if errorlevel 1 (
    echo 安装 gevent...
    pip install gevent
)

python -c "import redis" 2>nul
if errorlevel 1 (
    echo 安装 redis...
    pip install redis
)

python -c "import celery" 2>nul
if errorlevel 1 (
    echo 安装 celery...
    pip install celery
)

echo.
echo 选择启动模式:
echo 1. Django开发服务器 (推荐)
echo 2. Celery Worker (异步任务)
echo 3. Celery Beat (定时任务)
echo 4. Celery Worker + Beat
echo 5. 完整开发环境 (Web + Celery)
echo 6. 运行OAuth API测试
echo 7. 测试ZhiCelery配置
echo 8. 测试多项目文档管理页面
echo.
set /p choice=请选择 (1-8):

if "%choice%"=="1" goto start_webapp
if "%choice%"=="2" goto start_celery_worker
if "%choice%"=="3" goto start_celery_beat
if "%choice%"=="4" goto start_celery_all
if "%choice%"=="5" goto start_all
if "%choice%"=="6" goto run_oauth_tests
if "%choice%"=="7" goto test_celery
if "%choice%"=="8" goto test_multi_docs
goto invalid_choice

:start_webapp
echo 启动Django开发服务器...
echo 地址: http://localhost:8001
echo API文档: http://localhost:8001/api/oauth/docs
echo.
echo 按 Ctrl+C 停止服务器
echo.
python manage.py runserver 0.0.0.0:8001
goto end

:start_celery_worker
echo 启动Celery Worker...
echo 模式: 异步任务处理
echo.
REM 使用Django管理命令启动Celery Worker（适配zhi_celery架构）
python manage.py celery_worker --pool=solo --loglevel=info --concurrency=1 --queues=default,logger,oauth
goto end

:start_celery_beat
echo 启动Celery Beat...
echo 模式: 定时任务调度
echo.
REM 使用Django管理命令启动Celery Beat
python manage.py celery_beat --loglevel=info
goto end

:start_celery_all
echo 启动Celery Worker + Beat...
echo 模式: 异步任务 + 定时任务
echo 注意: 生产环境建议分别启动Worker和Beat
echo.
REM 使用zhi_celery应用启动（Windows兼容）
celery -A zhi_celery.celery_app worker --loglevel=info --beat -c 1 -P solo
goto end

:start_all
echo 启动完整开发环境...
echo 将启动: Web服务器 + Celery服务
echo.
echo 注意: 这将在后台启动Celery，然后启动Web服务器
echo.

REM 启动Celery作为后台任务
echo 启动Celery后台服务...
start /B celery -A zhi_celery.celery_app worker --loglevel=info --beat -c 1 -P solo

REM 等待Celery启动
timeout /t 3 /nobreak >nul

echo 启动Web服务器...
echo 地址: http://localhost:8001
echo API文档: http://localhost:8001/api/oauth/docs
echo.
echo 按 Ctrl+C 停止服务器
echo.
python manage.py runserver 0.0.0.0:8001
goto end

:run_oauth_tests
echo 运行OAuth API测试...
echo.
echo 首先检查服务器是否运行...
python -c "import requests; requests.get('http://localhost:8001/api/oauth/docs', timeout=2)" 2>nul
if errorlevel 1 (
    echo 服务器未运行，启动测试服务器...
    start /B python manage.py runserver 0.0.0.0:8001
    echo 等待服务器启动...
    timeout /t 8 /nobreak >nul
)

echo 运行完整OAuth API测试...
python test_oauth_complete.py
goto end

:test_celery
echo 测试ZhiCelery配置...
echo.
python test_zhi_celery.py
goto end

:test_multi_docs
echo 测试多项目文档管理页面...
echo.
echo 首先检查服务器是否运行...
python -c "import requests; requests.get('http://localhost:8001/docs/', timeout=2)" 2>nul
if errorlevel 1 (
    echo 服务器未运行，启动测试服务器...
    start /B python manage.py runserver 0.0.0.0:8001
    echo 等待服务器启动...
    timeout /t 8 /nobreak >nul
)

echo 运行多项目文档管理页面测试...
python zhi_scripts\test_multi_project_docs.py

echo.
echo 访问地址:
echo - 多项目文档管理: http://localhost:8001/docs/
echo - API文档门户: http://localhost:8001/api/docs/
echo - OAuth项目: http://localhost:8001/docs/?project=oauth
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
pause
goto end

:end
echo.
echo 服务已停止
pause
