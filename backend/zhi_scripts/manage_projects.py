#!/usr/bin/env python
"""
ZhiAdmin 多项目管理脚本

支持启动不同的子项目:
- zhi_oauth: OAuth2 认证服务
- zhi_logger: 日志管理服务
- zhi_system: 系统管理服务
- interval_admin: 完整管理后台

使用方式:
python backend/zhi_scripts/manage_projects.py start zhi_logger --port 8002
python backend/zhi_scripts/manage_projects.py list
python backend/zhi_scripts/manage_projects.py migrate zhi_logger
python backend/zhi_scripts/manage_projects.py status
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

# 项目配置
PROJECTS = {
    'zhi_oauth': {
        'name': 'ZhiAdmin OAuth2认证',
        'settings': 'application.settings.zhi_oauth',
        'default_port': 8001,
        'description': 'OAuth2权限认证系统',
        'urls': '/api/oauth/',
        'docs': '/oauth/docs',
        'script': 'start_oauth.py'
    },
    'zhi_logger': {
        'name': 'ZhiAdmin 日志管理',
        'settings': 'application.settings.zhi_logger',
        'default_port': 8002,
        'description': '日志记录、分析和监控系统',
        'urls': '/api/logger/',
        'docs': '/logger/docs',
        'script': 'start_logger.py'
    },
    'zhi_system': {
        'name': 'ZhiAdmin 系统管理',
        'settings': 'application.settings.zhi_system',
        'default_port': 8003,
        'description': '系统配置管理模块',
        'urls': '/api/system/',
        'docs': '/system/docs',
        'script': 'start_system.py'
    },
    'interval_admin': {
        'name': 'ZhiAdmin 完整后台',
        'settings': 'application.settings.interval_admin',
        'default_port': 8000,
        'description': '完整的管理后台系统',
        'urls': '/api/',
        'docs': '/docs',
        'script': 'start_admin.py'
    }
}

def list_projects():
    """列出所有可用的项目"""
    print("🚀 ZhiAdmin 可用项目:")
    print("=" * 70)
    
    for project_id, config in PROJECTS.items():
        print(f"📦 {project_id}")
        print(f"   名称: {config['name']}")
        print(f"   描述: {config['description']}")
        print(f"   默认端口: {config['default_port']}")
        print(f"   API路径: {config['urls']}")
        print(f"   文档路径: {config['docs']}")
        print(f"   启动脚本: backend/zhi_scripts/{config['script']}")
        print()

def check_project_status():
    """检查所有项目的运行状态"""
    print("📊 项目运行状态检查:")
    print("=" * 50)
    
    try:
        import requests
        
        for project_id, config in PROJECTS.items():
            port = config['default_port']
            try:
                response = requests.get(f"http://localhost:{port}/health/", timeout=3)
                if response.status_code == 200:
                    status = "🟢 运行中"
                else:
                    status = f"🟡 异常 (状态码: {response.status_code})"
            except requests.exceptions.ConnectionError:
                status = "🔴 未运行"
            except requests.exceptions.Timeout:
                status = "🟡 响应超时"
            except Exception as e:
                status = f"❓ 未知 ({str(e)})"
            
            print(f"   {project_id:15} | 端口:{port:4} | {status}")
    
    except ImportError:
        print("   ⚠️  需要安装 requests 包来检查服务状态")
        print("   pip install requests")

def start_project(project_id: str, port: int = None, host: str = '0.0.0.0', debug: bool = False):
    """启动指定项目"""
    if project_id not in PROJECTS:
        print(f"❌ 未知项目: {project_id}")
        print("可用项目:", list(PROJECTS.keys()))
        return False
    
    config = PROJECTS[project_id]
    port = port or config['default_port']
    
    # 切换到backend目录
    os.chdir(backend_path)
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = config['settings']
    env['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'
    
    if debug:
        env['DEBUG'] = 'True'
    
    print(f"🚀 启动项目: {config['name']}")
    print(f"📍 地址: http://{host}:{port}")
    print(f"🔧 配置: {config['settings']}")
    print(f"📊 API文档: http://{host}:{port}{config['docs']}")
    print(f"🌐 API路径: http://{host}:{port}{config['urls']}")
    
    if project_id == 'zhi_logger':
        print(f"🔌 WebSocket: ws://{host}:{port}/ws/logs/")
    
    print("-" * 50)
    
    try:
        # 启动Django服务
        cmd = [
            sys.executable, 'manage.py', 'runserver',
            f'{host}:{port}',
            '--settings', config['settings']
        ]
        
        subprocess.run(cmd, env=env)
        
    except KeyboardInterrupt:
        print(f"\n✅ {config['name']} 已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def migrate_project(project_id: str):
    """为指定项目执行数据库迁移"""
    if project_id not in PROJECTS:
        print(f"❌ 未知项目: {project_id}")
        return False
    
    config = PROJECTS[project_id]
    
    # 切换到backend目录
    os.chdir(backend_path)
    
    # 设置环境变量
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = config['settings']
    env['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'
    
    print(f"🔄 执行数据库迁移: {config['name']}")
    
    try:
        # 生成迁移文件
        print("   📝 生成迁移文件...")
        subprocess.run([
            sys.executable, 'manage.py', 'makemigrations',
            '--settings', config['settings']
        ], env=env)
        
        # 执行迁移
        print("   🔄 执行迁移...")
        subprocess.run([
            sys.executable, 'manage.py', 'migrate',
            '--settings', config['settings']
        ], env=env)
        
        print(f"✅ {config['name']} 数据库迁移完成")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False
    
    return True

def collect_static(project_id: str):
    """收集静态文件"""
    if project_id not in PROJECTS:
        print(f"❌ 未知项目: {project_id}")
        return False
    
    config = PROJECTS[project_id]
    
    # 切换到backend目录
    os.chdir(backend_path)
    
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = config['settings']
    env['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'
    
    print(f"📦 收集静态文件: {config['name']}")
    
    try:
        subprocess.run([
            sys.executable, 'manage.py', 'collectstatic', '--noinput',
            '--settings', config['settings']
        ], env=env)
        
        print(f"✅ {config['name']} 静态文件收集完成")
        
    except Exception as e:
        print(f"❌ 收集失败: {e}")
        return False
    
    return True

def create_superuser(project_id: str):
    """创建超级用户"""
    if project_id not in PROJECTS:
        print(f"❌ 未知项目: {project_id}")
        return False
    
    config = PROJECTS[project_id]
    
    # 切换到backend目录
    os.chdir(backend_path)
    
    env = os.environ.copy()
    env['DJANGO_SETTINGS_MODULE'] = config['settings']
    env['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'
    
    print(f"👤 创建超级用户: {config['name']}")
    
    try:
        subprocess.run([
            sys.executable, 'manage.py', 'createsuperuser',
            '--settings', config['settings']
        ], env=env)
        
        print(f"✅ {config['name']} 超级用户创建完成")
        
    except Exception as e:
        print(f"❌ 创建失败: {e}")
        return False
    
    return True

def handle_audit_command(args):
    """处理审计配置命令"""
    if not args.audit_command:
        print("请指定审计命令: list, test, reload")
        return

    try:
        # 导入审计配置管理脚本
        import subprocess

        audit_script = backend_path / 'zhi_scripts' / 'manage_audit_config.py'

        if args.audit_command == 'list':
            subprocess.run([sys.executable, str(audit_script), 'list'])
        elif args.audit_command == 'test':
            cmd = [sys.executable, str(audit_script), 'test']
            if hasattr(args, 'project') and args.project:
                cmd.extend(['--project', args.project])
            subprocess.run(cmd)
        elif args.audit_command == 'reload':
            cmd = [sys.executable, str(audit_script), 'reload']
            if hasattr(args, 'project') and args.project:
                cmd.extend(['--project', args.project])
            subprocess.run(cmd)

    except Exception as e:
        print(f"❌ 审计命令执行失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='ZhiAdmin 多项目管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list 命令
    subparsers.add_parser('list', help='列出所有可用项目')
    
    # status 命令
    subparsers.add_parser('status', help='检查项目运行状态')
    
    # start 命令
    start_parser = subparsers.add_parser('start', help='启动项目')
    start_parser.add_argument('project', choices=list(PROJECTS.keys()), help='项目名称')
    start_parser.add_argument('--port', type=int, help='服务端口')
    start_parser.add_argument('--host', default='0.0.0.0', help='服务主机')
    start_parser.add_argument('--debug', action='store_true', help='启用调试模式')
    
    # migrate 命令
    migrate_parser = subparsers.add_parser('migrate', help='执行数据库迁移')
    migrate_parser.add_argument('project', choices=list(PROJECTS.keys()), help='项目名称')
    
    # collectstatic 命令
    static_parser = subparsers.add_parser('collectstatic', help='收集静态文件')
    static_parser.add_argument('project', choices=list(PROJECTS.keys()), help='项目名称')
    
    # createsuperuser 命令
    user_parser = subparsers.add_parser('createsuperuser', help='创建超级用户')
    user_parser.add_argument('project', choices=list(PROJECTS.keys()), help='项目名称')

    # audit 命令
    audit_parser = subparsers.add_parser('audit', help='审计配置管理')
    audit_subparsers = audit_parser.add_subparsers(dest='audit_command', help='审计命令')

    audit_subparsers.add_parser('list', help='列出审计配置')

    audit_test_parser = audit_subparsers.add_parser('test', help='测试审计配置')
    audit_test_parser.add_argument('--project', help='指定项目名称')

    audit_reload_parser = audit_subparsers.add_parser('reload', help='重新加载审计配置')
    audit_reload_parser.add_argument('--project', help='指定项目名称')

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    if args.command == 'list':
        list_projects()
    elif args.command == 'status':
        check_project_status()
    elif args.command == 'start':
        start_project(args.project, args.port, args.host, args.debug)
    elif args.command == 'migrate':
        migrate_project(args.project)
    elif args.command == 'collectstatic':
        collect_static(args.project)
    elif args.command == 'createsuperuser':
        create_superuser(args.project)
    elif args.command == 'audit':
        handle_audit_command(args)

if __name__ == '__main__':
    main()
