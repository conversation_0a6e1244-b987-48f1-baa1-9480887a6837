#!/usr/bin/env python
"""
完整的OAuth API测试 - 包括用户创建和完整流程
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
script_dir = Path(__file__).parent
backend_dir = script_dir.parent
sys.path.insert(0, str(backend_dir))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

import requests
import json
from datetime import datetime
from zhi_oauth.models import User


class CompleteOAuthTester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        self.test_user = None
        self.session_token = None
    
    def setup_test_user(self):
        """创建或获取测试用户"""
        print("🔄 设置测试用户...")
        
        try:
            # 尝试获取现有用户
            self.test_user = User.objects.filter(username='testuser').first()
            
            if not self.test_user:
                # 创建新的测试用户
                self.test_user = User.objects.create_user(
                    username='testuser',
                    email='<EMAIL>',
                    password='123456',
                    name='测试用户',
                    is_active=True
                )
                print("✅ 创建测试用户成功")
            else:
                # 更新密码确保一致
                self.test_user.set_password('123456')
                self.test_user.save()
                print("✅ 使用现有测试用户")
            
            return True
        except Exception as e:
            print(f"❌ 设置测试用户失败: {e}")
            return False
    
    def test_login_api(self):
        """测试登录API"""
        print("\n🔄 测试登录API...")
        
        url = f"{self.base_url}/api/oauth/login"
        data = {
            "username": "testuser",
            "password": "123456",
            "remember_me": False
        }
        
        try:
            response = self.session.post(url, json=data)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("响应格式:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 检查统一响应格式
                if self.check_unified_response_format(result):
                    print("✅ 登录API响应格式正确")
                    
                    # 提取session_token
                    if result.get('success') and result.get('data', {}).get('session_token'):
                        self.session_token = result['data']['session_token']
                        print(f"✅ 获取到session_token: {self.session_token[:20]}...")
                        return True
                    else:
                        print("❌ 登录失败或未获取到session_token")
                        return False
                else:
                    print("❌ 登录API响应格式不正确")
                    return False
            else:
                print(f"❌ 登录失败: {response.status_code}")
                print(response.text)
                return False
                
        except Exception as e:
            print(f"❌ 登录API测试异常: {e}")
            return False
    
    def test_user_info_api(self):
        """测试用户信息API"""
        print("\n🔄 测试用户信息API...")
        
        if not self.session_token:
            print("❌ 没有session_token，跳过测试")
            return False
        
        url = f"{self.base_url}/api/oauth/user/info"
        params = {"session_token": self.session_token}
        
        try:
            response = self.session.get(url, params=params)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("响应格式:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                if self.check_unified_response_format(result):
                    print("✅ 用户信息API响应格式正确")
                    return True
                else:
                    print("❌ 用户信息API响应格式不正确")
                    return False
            else:
                print(f"❌ 获取用户信息失败: {response.status_code}")
                print(response.text)
                return False
                
        except Exception as e:
            print(f"❌ 用户信息API测试异常: {e}")
            return False
    
    def test_oauth_applications_api(self):
        """测试OAuth应用列表API"""
        print("\n🔄 测试OAuth应用列表API...")
        
        if not self.session_token:
            print("❌ 没有session_token，跳过测试")
            return False
        
        url = f"{self.base_url}/api/oauth/applications"
        params = {"session_token": self.session_token}
        
        try:
            response = self.session.get(url, params=params)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("响应格式:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                if self.check_unified_response_format(result):
                    print("✅ 应用列表API响应格式正确")
                    return True
                else:
                    print("❌ 应用列表API响应格式不正确")
                    return False
            else:
                print(f"❌ 获取应用列表失败: {response.status_code}")
                print(response.text)
                return False
                
        except Exception as e:
            print(f"❌ 应用列表API测试异常: {e}")
            return False
    
    def check_unified_response_format(self, response_data):
        """检查统一响应格式"""
        required_fields = ['code', 'message', 'success', 'trace_id', 'timestamp']
        
        for field in required_fields:
            if field not in response_data:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        # 检查字段类型
        if not isinstance(response_data['code'], int):
            print("❌ code字段应为整数")
            return False
            
        if not isinstance(response_data['success'], bool):
            print("❌ success字段应为布尔值")
            return False
            
        if not isinstance(response_data['message'], str):
            print("❌ message字段应为字符串")
            return False
        
        return True
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🚀 开始完整OAuth API测试...")
        print(f"测试服务器: {self.base_url}")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        results = []
        
        # 1. 设置测试用户
        results.append(self.setup_test_user())
        
        # 2. 测试登录API
        results.append(self.test_login_api())
        
        # 3. 测试用户信息API
        results.append(self.test_user_info_api())
        
        # 4. 测试应用列表API
        results.append(self.test_oauth_applications_api())
        
        # 总结
        print("\n" + "=" * 60)
        success_count = sum(results)
        total_count = len(results)
        
        print(f"📊 测试结果: {success_count}/{total_count} 项测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！OAuth API响应格式统一化完全成功！")
            print("\n✅ 验证结果:")
            print("  - 统一响应格式已正确实现")
            print("  - 所有API都返回标准的BaseResponse格式")
            print("  - 包含code、message、success、trace_id、timestamp字段")
            print("  - 业务数据统一放在data字段中")
        else:
            print("⚠️  部分测试失败，请检查具体问题")
        
        return success_count == total_count


def main():
    """主函数"""
    # 检查服务器是否运行
    base_url = "http://localhost:8001"
    
    print("检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/api/oauth/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器未正常运行，请先启动服务器:")
            print("   python manage.py runserver 0.0.0.0:8001")
            return False
    except requests.exceptions.RequestException:
        print(f"❌ 无法连接到服务器 {base_url}")
        print("   请确保服务器正在运行:")
        print("   python manage.py runserver 0.0.0.0:8001")
        return False
    
    # 运行完整测试
    tester = CompleteOAuthTester(base_url)
    return tester.run_complete_test()


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
