#!/usr/bin/env python
"""
测试多项目文档管理页面
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
script_dir = Path(__file__).parent
backend_dir = script_dir.parent
sys.path.insert(0, str(backend_dir))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

import requests
from datetime import datetime


class MultiProjectDocsTester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_docs_pages(self):
        """测试文档页面访问"""
        print("🔄 测试多项目文档页面...")
        
        pages_to_test = [
            ('/docs/', '多项目文档管理页'),
            ('/api/docs/', 'API文档门户'),
            ('/docs/?project=oauth', 'OAuth项目文档'),
            ('/docs/?project=logger', 'Logger项目文档'),
            ('/docs/?project=celery', 'Celery项目文档'),
            ('/api/login-docs/', '向后兼容页面'),
        ]
        
        results = []
        
        for url, name in pages_to_test:
            try:
                full_url = f"{self.base_url}{url}"
                response = self.session.get(full_url)
                
                if response.status_code == 200:
                    print(f"✅ {name}: {response.status_code}")
                    results.append(True)
                    
                    # 检查页面内容
                    content = response.text
                    if 'ZhiAdmin' in content and '多项目' in content:
                        print(f"   ✅ 页面内容正确")
                    else:
                        print(f"   ⚠️  页面内容可能不完整")
                else:
                    print(f"❌ {name}: {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {name}: 连接失败 - {e}")
                results.append(False)
        
        return results
    
    def test_project_apis(self):
        """测试各项目API端点 - 统一OAuth认证"""
        print("\n🔄 测试项目API端点（统一OAuth认证）...")

        apis_to_test = [
            ('/api/oauth/docs', 'OAuth API文档'),
            ('/api/oauth/login', 'OAuth统一登录接口'),
        ]
        
        results = []
        
        for url, name in apis_to_test:
            try:
                full_url = f"{self.base_url}{url}"
                
                if 'login' in url:
                    # 测试登录接口
                    response = self.session.post(full_url, json={
                        'username': 'admin',
                        'password': '123456',
                        'remember_me': False
                    })
                else:
                    # 测试文档接口
                    response = self.session.get(full_url)
                
                if response.status_code == 200:
                    print(f"✅ {name}: {response.status_code}")
                    results.append(True)
                    
                    if 'login' in url:
                        # 检查登录响应格式
                        try:
                            data = response.json()
                            if 'code' in data and 'success' in data:
                                print(f"   ✅ BaseResponse格式正确")
                            else:
                                print(f"   ⚠️  响应格式需要检查")
                        except:
                            print(f"   ⚠️  响应不是JSON格式")
                else:
                    print(f"❌ {name}: {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {name}: 连接失败 - {e}")
                results.append(False)
        
        return results
    
    def test_project_filtering(self):
        """测试项目过滤功能"""
        print("\n🔄 测试项目过滤功能...")
        
        filters_to_test = [
            ('all', '显示所有项目'),
            ('oauth', '仅显示OAuth项目'),
            ('logger', '仅显示Logger项目'),
            ('celery', '仅显示Celery项目'),
            ('nonexistent', '不存在的项目（应回退到OAuth）'),
        ]
        
        results = []
        
        for project_filter, description in filters_to_test:
            try:
                url = f"{self.base_url}/docs/?project={project_filter}"
                response = self.session.get(url)
                
                if response.status_code == 200:
                    print(f"✅ {description}: 页面加载成功")
                    results.append(True)
                    
                    # 检查页面内容是否包含预期的项目信息
                    content = response.text
                    if project_filter == 'oauth' and 'OAuth认证系统' in content:
                        print(f"   ✅ OAuth项目内容正确显示")
                    elif project_filter == 'all' and 'OAuth认证系统' in content and 'Logger日志系统' in content:
                        print(f"   ✅ 所有项目内容正确显示")
                    
                else:
                    print(f"❌ {description}: {response.status_code}")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ {description}: 连接失败 - {e}")
                results.append(False)
        
        return results
    
    def run_complete_test(self):
        """运行完整测试"""
        print("🚀 开始多项目文档管理页面测试...")
        print(f"测试服务器: {self.base_url}")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        all_results = []
        
        # 1. 测试文档页面
        docs_results = self.test_docs_pages()
        all_results.extend(docs_results)
        
        # 2. 测试API端点
        api_results = self.test_project_apis()
        all_results.extend(api_results)
        
        # 3. 测试项目过滤
        filter_results = self.test_project_filtering()
        all_results.extend(filter_results)
        
        # 总结
        print("\n" + "=" * 60)
        success_count = sum(all_results)
        total_count = len(all_results)
        
        print(f"📊 测试结果: {success_count}/{total_count} 项测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！多项目文档管理页面工作正常！")
            print("\n✅ 功能验证:")
            print("  - 多项目文档管理页面正常加载")
            print("  - 项目过滤功能正常工作")
            print("  - API端点响应正常")
            print("  - 向后兼容性保持良好")
            print("\n🌐 访问地址:")
            print(f"  - 多项目文档管理: {self.base_url}/docs/")
            print(f"  - API文档门户: {self.base_url}/api/docs/")
            print(f"  - OAuth项目: {self.base_url}/docs/?project=oauth")
            print(f"  - Logger项目: {self.base_url}/docs/?project=logger")
        else:
            print("⚠️  部分测试失败，请检查具体问题")
        
        return success_count == total_count


def main():
    """主函数"""
    # 检查服务器是否运行
    base_url = "http://localhost:8001"
    
    print("检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/docs/", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器未正常运行，请先启动服务器:")
            print("   python manage.py runserver 0.0.0.0:8001")
            return False
    except requests.exceptions.RequestException:
        print(f"❌ 无法连接到服务器 {base_url}")
        print("   请确保服务器正在运行:")
        print("   python manage.py runserver 0.0.0.0:8001")
        return False
    
    # 运行完整测试
    tester = MultiProjectDocsTester(base_url)
    return tester.run_complete_test()


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
