#!/usr/bin/env python
"""
创建 AuditLog 模型迁移脚本

用于更新 AuditLog 模型的字段和索引

使用方式:
python backend/zhi_scripts/create_audit_log_migration.py
"""

import os
import sys
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_logger')
    import django
    django.setup()

def create_migration():
    """创建迁移文件"""
    setup_django()
    
    import subprocess
    
    print("🔄 创建 AuditLog 模型迁移...")
    
    try:
        # 切换到backend目录
        os.chdir(backend_path)
        
        # 创建迁移文件
        result = subprocess.run([
            sys.executable, 'manage.py', 'makemigrations', 'zhi_logger',
            '--name', 'update_audit_log_model',
            '--settings', 'application.settings.zhi_logger'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 迁移文件创建成功")
            print(result.stdout)
            
            # 询问是否执行迁移
            try:
                response = input("是否立即执行迁移? (y/N): ").lower().strip()
                if response in ['y', 'yes']:
                    apply_migration()
            except KeyboardInterrupt:
                print("\n⏹️  用户取消操作")
        else:
            print("❌ 迁移文件创建失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 创建迁移失败: {str(e)}")
        return False
    
    return True

def apply_migration():
    """执行迁移"""
    print("\n🔄 执行数据库迁移...")
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'migrate', 'zhi_logger',
            '--settings', 'application.settings.zhi_logger'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 数据库迁移执行成功")
            print(result.stdout)
        else:
            print("❌ 数据库迁移执行失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行迁移失败: {str(e)}")
        return False
    
    return True

def check_migration_status():
    """检查迁移状态"""
    print("🔍 检查迁移状态...")
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'showmigrations', 'zhi_logger',
            '--settings', 'application.settings.zhi_logger'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📋 迁移状态:")
            print(result.stdout)
        else:
            print("❌ 检查迁移状态失败")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 检查迁移状态失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 AuditLog 模型迁移工具")
    print("=" * 40)
    
    # 检查当前迁移状态
    check_migration_status()
    
    print("\n" + "=" * 40)
    
    # 创建迁移
    if create_migration():
        print("\n🎉 迁移操作完成！")
        
        print("\n📚 后续步骤:")
        print("1. 检查生成的迁移文件")
        print("2. 如果需要，可以手动执行迁移:")
        print("   python backend/zhi_scripts/manage_projects.py migrate zhi_logger")
        print("3. 测试 AuditLog 功能:")
        print("   python backend/zhi_scripts/test_audit_log.py")
    else:
        print("\n❌ 迁移操作失败")

if __name__ == '__main__':
    main()
