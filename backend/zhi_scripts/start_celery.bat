@echo off
REM ZhiCelery 异步任务服务启动脚本 (Windows)
REM 使用方式: 
REM   backend\zhi_scripts\start_celery.bat --worker
REM   backend\zhi_scripts\start_celery.bat --beat
REM   backend\zhi_scripts\start_celery.bat --all

setlocal enabledelayedexpansion

set OPTIONS=%1 %2 %3 %4 %5 %6 %7 %8 %9

echo.
echo ========================================
echo 🚀 ZhiCelery 异步任务服务启动器
echo ========================================
echo 🎯 选项: %OPTIONS%
echo.

REM 如果没有参数，显示帮助
if "%1"=="" (
    echo 使用方式:
    echo   %0 --worker          启动Worker
    echo   %0 --beat            启动Beat调度器
    echo   %0 --flower          启动Flower监控
    echo   %0 --all             启动所有服务
    echo.
    echo 示例:
    echo   %0 --worker --settings=application.settings.zhi_logger
    echo   %0 --all --loglevel=debug
    echo.
    pause
    goto :end
)

python backend\zhi_scripts\start_celery.py %OPTIONS%

:end
echo.
echo 🛑 服务已停止
pause
