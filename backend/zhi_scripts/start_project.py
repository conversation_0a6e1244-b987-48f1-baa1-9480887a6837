#!/usr/bin/env python
"""
ZhiAdmin 项目统一启动脚本

支持启动不同的子项目和服务组合
"""

import os
import sys
import argparse
import subprocess
import threading
import time
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

# 项目配置
PROJECTS = {
    'logger': {
        'name': '<PERSON>hi<PERSON>ogger',
        'settings': 'application.settings.zhi_logger',
        'port': 8002,
        'description': '日志管理系统'
    },
    'oauth': {
        'name': 'ZhiOA<PERSON>',
        'settings': 'application.settings.zhi_oauth',
        'port': 8001,
        'description': 'OAuth认证系统'
    },
    'admin': {
        'name': 'ZhiAdmin',
        'settings': 'application.settings.interval_admin',
        'port': 8000,
        'description': '管理后台系统'
    }
}


def start_django_project(project_key, port=None, **kwargs):
    """启动Django项目"""
    if project_key not in PROJECTS:
        print(f"❌ 未知项目: {project_key}")
        return

    project = PROJECTS[project_key]
    port = port or project['port']

    try:
        # 设置环境变量而不是使用--settings参数
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = project['settings']

        cmd = [
            sys.executable, 'manage.py', 'runserver',
            f'0.0.0.0:{port}'
        ]

        if not kwargs.get('reload', True):
            cmd.append('--noreload')

        print(f"🚀 启动 {project['name']} ({project['description']})...")
        print(f"📍 地址: http://0.0.0.0:{port}")
        print(f"🔧 配置: {project['settings']}")

        subprocess.run(cmd, cwd=backend_path, env=env)
    except KeyboardInterrupt:
        print(f"🛑 {project['name']} 已停止")
    except Exception as e:
        print(f"❌ {project['name']} 启动失败: {e}")


def start_celery_service(service_type, settings_module, **kwargs):
    """启动Celery服务"""
    try:
        # 设置环境变量
        env = os.environ.copy()
        env['DJANGO_SETTINGS_MODULE'] = settings_module

        if service_type == 'worker':
            cmd = [
                sys.executable, 'manage.py', 'celery_worker',
                '--pool=solo',
                f'--loglevel={kwargs.get("loglevel", "info")}',
                f'--queues={kwargs.get("queues", "default,logger")}'
            ]
        elif service_type == 'beat':
            cmd = [
                sys.executable, 'manage.py', 'celery_beat',
                f'--loglevel={kwargs.get("loglevel", "info")}'
            ]
        else:
            print(f"❌ 未知Celery服务类型: {service_type}")
            return

        print(f"🔄 启动Celery {service_type.title()}...")
        subprocess.run(cmd, cwd=backend_path, env=env)
    except KeyboardInterrupt:
        print(f"🛑 Celery {service_type.title()} 已停止")
    except Exception as e:
        print(f"❌ Celery {service_type.title()} 启动失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='ZhiAdmin 项目统一启动器')
    
    # 项目选择
    parser.add_argument('--project', choices=list(PROJECTS.keys()), 
                       help='要启动的项目')
    parser.add_argument('--port', type=int, help='自定义端口')
    
    # Celery服务
    parser.add_argument('--celery-worker', action='store_true', help='启动Celery Worker')
    parser.add_argument('--celery-beat', action='store_true', help='启动Celery Beat')
    parser.add_argument('--celery-all', action='store_true', help='启动所有Celery服务')
    
    # 组合启动
    parser.add_argument('--full-logger', action='store_true', 
                       help='启动完整的Logger系统 (Django + Celery)')
    parser.add_argument('--dev-mode', action='store_true', 
                       help='开发模式：启动所有服务')
    
    # 配置选项
    parser.add_argument('--settings', help='自定义Django设置模块')
    parser.add_argument('--loglevel', default='info', help='Celery日志级别')
    parser.add_argument('--no-reload', action='store_true', help='禁用自动重载')
    
    args = parser.parse_args()
    
    # 切换到backend目录
    os.chdir(backend_path)

    # 清理可能存在的重复环境变量
    if 'DJANGO_SETTINGS_MODULE' in os.environ:
        del os.environ['DJANGO_SETTINGS_MODULE']

    print("=" * 60)
    print("🚀 ZhiAdmin 项目统一启动器")
    print("=" * 60)
    
    # 开发模式：启动所有服务
    if args.dev_mode:
        print("🔧 开发模式：启动Logger项目...")
        print("⚠️  注意：Celery服务需要单独启动")
        print("   使用: python zhi_scripts/start_celery.py --all")

        # 直接启动Logger项目（不启动Celery，避免模块导入问题）
        start_django_project('logger', reload=not args.no_reload)
        return
    
    # 完整Logger系统
    if args.full_logger:
        print("📊 启动完整Logger系统...")
        
        # 启动Celery服务
        worker_thread = threading.Thread(
            target=start_celery_service,
            args=('worker', 'application.settings.zhi_logger'),
            kwargs={'loglevel': args.loglevel},
            daemon=True
        )
        beat_thread = threading.Thread(
            target=start_celery_service,
            args=('beat', 'application.settings.zhi_logger'),
            kwargs={'loglevel': args.loglevel},
            daemon=True
        )
        
        worker_thread.start()
        time.sleep(2)
        beat_thread.start()
        time.sleep(1)
        
        # 启动Logger Django项目
        start_django_project('logger', args.port, reload=not args.no_reload)
        return
    
    # 单独启动Celery服务
    if args.celery_all or args.celery_worker or args.celery_beat:
        settings_module = args.settings or 'application.settings.base'
        
        if args.celery_all:
            print("🔄 启动所有Celery服务...")
            worker_thread = threading.Thread(
                target=start_celery_service,
                args=('worker', settings_module),
                kwargs={'loglevel': args.loglevel}
            )
            beat_thread = threading.Thread(
                target=start_celery_service,
                args=('beat', settings_module),
                kwargs={'loglevel': args.loglevel}
            )
            
            worker_thread.start()
            time.sleep(2)
            beat_thread.start()
            
            try:
                worker_thread.join()
                beat_thread.join()
            except KeyboardInterrupt:
                print("🛑 正在停止Celery服务...")
        
        elif args.celery_worker:
            start_celery_service('worker', settings_module, loglevel=args.loglevel)
        elif args.celery_beat:
            start_celery_service('beat', settings_module, loglevel=args.loglevel)
        
        return
    
    # 启动指定项目
    if args.project:
        settings_module = args.settings or PROJECTS[args.project]['settings']
        os.environ['DJANGO_SETTINGS_MODULE'] = settings_module
        start_django_project(args.project, args.port, reload=not args.no_reload)
        return
    
    # 显示帮助信息
    print("请指定要启动的服务:")
    print()
    print("项目选项:")
    for key, project in PROJECTS.items():
        print(f"  --project {key:<8} 启动{project['name']} ({project['description']})")
    print()
    print("Celery选项:")
    print("  --celery-worker      启动Celery Worker")
    print("  --celery-beat        启动Celery Beat")
    print("  --celery-all         启动所有Celery服务")
    print()
    print("组合选项:")
    print("  --full-logger        启动完整Logger系统")
    print("  --dev-mode           开发模式（所有服务）")
    print()
    print("示例:")
    print("  python start_project.py --project logger")
    print("  python start_project.py --full-logger")
    print("  python start_project.py --dev-mode")


if __name__ == '__main__':
    main()
