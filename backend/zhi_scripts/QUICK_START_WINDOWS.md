# Windows快速启动指南

## 🚀 一键启动

### 最简单的方式
```bash
# 进入backend目录
cd backend

# 启动开发环境（交互式选择）
.\zhi_scripts\start_dev_windows.bat
```

### PowerShell方式（推荐）
```bash
# 启动Web服务器
.\zhi_scripts\start_dev_windows.ps1 -Mode webapp

# 启动完整环境（Web + Celery）
.\zhi_scripts\start_dev_windows.ps1 -Mode all

# 启动并初始化系统
.\zhi_scripts\start_dev_windows.ps1 -Mode webapp -Init
```

## 🔧 ZhiCelery异步任务

### 启动Celery服务
```bash
# 启动Worker（异步任务处理）
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode worker

# 启动Beat（定时任务调度）
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode beat

# 启动所有Celery服务
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode all
```

### 测试Celery配置
```bash
# 测试ZhiCelery配置
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode test

# 或直接运行测试脚本
python zhi_scripts\test_zhi_celery.py
```

## 🧪 OAuth API测试

### 快速测试
```bash
# 一键测试OAuth API响应格式
.\zhi_scripts\quick_test_oauth.bat
```

### 完整测试
```bash
# 运行完整的OAuth API测试套件
python zhi_scripts\test_oauth_complete.py
```

## 📋 常用命令组合

### 开发环境启动流程
```bash
# 1. 启动Web服务器
.\zhi_scripts\start_dev_windows.ps1 -Mode webapp

# 2. 另开终端启动Celery Worker
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode worker

# 3. 再开终端启动Celery Beat
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode beat
```

### 一键启动所有服务
```bash
# 启动Web + Celery所有服务
.\zhi_scripts\start_dev_windows.ps1 -Mode all
```

### 测试验证流程
```bash
# 1. 测试ZhiCelery配置
python zhi_scripts\test_zhi_celery.py

# 2. 测试OAuth API
python zhi_scripts\test_oauth_complete.py

# 3. 快速OAuth测试
.\zhi_scripts\quick_test_oauth.bat
```

## 🌐 访问地址

启动成功后，您可以访问：

- **Web应用**: http://localhost:8001
- **OAuth API文档**: http://localhost:8001/api/oauth/docs
- **管理后台**: http://localhost:8001/admin
- **Celery Flower监控**: http://localhost:5555 (如果启动了Flower)

## ⚡ 快速故障排除

### 1. 虚拟环境问题
```bash
# 确保虚拟环境存在
ls .venv

# 重新创建虚拟环境
python -m venv .venv
.venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Redis连接问题
```bash
# 使用Docker启动Redis
docker run -d -p 6379:6379 redis:alpine

# 检查Redis是否运行
redis-cli ping
```

### 3. 端口占用问题
```bash
# 使用不同端口启动
.\zhi_scripts\start_dev_windows.ps1 -Mode webapp -Port 8002
```

### 4. 权限问题
```bash
# 以管理员身份运行PowerShell
# 或设置执行策略
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 📊 项目状态检查

### OAuth响应格式统一化状态
- ✅ **75%完成** (3/4 API已统一)
- ✅ 登录API - 完美适配BaseResponse格式
- ✅ 用户信息API - 完美适配BaseResponse格式  
- ✅ 测试用户创建 - 成功
- ⚠️ 应用列表API - 需要调试权限问题

### ZhiCelery异步任务状态
- ✅ **100%完成**
- ✅ Celery应用配置 - 完美运行
- ✅ Logger任务模块 - 8个任务可用
- ✅ OAuth任务模块 - 5个任务可用
- ✅ 调度配置 - 15个定时任务
- ✅ Django管理命令 - celery_worker & celery_beat

## 🎯 下一步

1. **完善应用列表API** - 修复权限问题
2. **添加更多测试** - 扩展测试覆盖率
3. **性能优化** - 监控和优化异步任务
4. **文档完善** - 添加更多使用示例

## 💡 提示

- 所有脚本都会自动激活虚拟环境
- 脚本会自动检查和安装必要的依赖
- 详细的日志和错误信息会显示在控制台
- 使用 Ctrl+C 可以优雅地停止服务
