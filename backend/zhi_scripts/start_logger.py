#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON><PERSON><PERSON> 子项目启动脚本

使用方式:
python backend/zhi_scripts/start_logger.py [--port 8002] [--debug] [--with-celery]
"""

import os
import sys
import argparse
import subprocess
import threading
import time
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def start_celery_worker():
    """启动Celery Worker"""
    try:
        cmd = [
            sys.executable, 'manage.py', 'celery_worker',
            '--settings=application.settings.zhi_logger',
            '--pool=solo',  # Windows兼容
            '--loglevel=info',
            '--queues=default,logger'
        ]

        print("🔄 启动Celery Worker...")
        subprocess.run(cmd, cwd=backend_path)
    except KeyboardInterrupt:
        print("🛑 Celery Worker已停止")
    except Exception as e:
        print(f"❌ Celery Worker启动失败: {e}")


def start_celery_beat():
    """启动Celery Beat"""
    try:
        cmd = [
            sys.executable, 'manage.py', 'celery_beat',
            '--settings=application.settings.zhi_logger',
            '--loglevel=info'
        ]

        print("⏰ 启动Celery Beat...")
        subprocess.run(cmd, cwd=backend_path)
    except KeyboardInterrupt:
        print("🛑 Celery Beat已停止")
    except Exception as e:
        print(f"❌ Celery Beat启动失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='启动 ZhiLogger 子项目')
    parser.add_argument('--port', type=int, default=8002, help='服务端口 (默认: 8002)')
    parser.add_argument('--host', default='0.0.0.0', help='服务主机 (默认: 0.0.0.0)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--reload', action='store_true', help='启用自动重载')
    parser.add_argument('--with-celery', action='store_true', help='同时启动Celery Worker和Beat')
    parser.add_argument('--celery-only', action='store_true', help='仅启动Celery服务')
    parser.add_argument('--worker-only', action='store_true', help='仅启动Celery Worker')
    parser.add_argument('--beat-only', action='store_true', help='仅启动Celery Beat')

    args = parser.parse_args()

    # 切换到backend目录作为工作目录
    os.chdir(backend_path)

    # 清理并设置环境变量
    if 'DJANGO_SETTINGS_MODULE' in os.environ:
        del os.environ['DJANGO_SETTINGS_MODULE']
    os.environ['DJANGO_SETTINGS_MODULE'] = 'application.settings.zhi_logger'
    os.environ.setdefault('DJANGO_ALLOW_ASYNC_UNSAFE', 'true')

    if args.debug:
        os.environ['DEBUG'] = 'True'

    # 处理仅启动Celery的情况
    if args.celery_only or args.worker_only or args.beat_only:
        if args.worker_only:
            start_celery_worker()
        elif args.beat_only:
            start_celery_beat()
        else:  # celery_only
            # 启动Worker和Beat
            worker_thread = threading.Thread(target=start_celery_worker)
            beat_thread = threading.Thread(target=start_celery_beat)

            worker_thread.start()
            time.sleep(2)  # 等待Worker启动
            beat_thread.start()

            try:
                worker_thread.join()
                beat_thread.join()
            except KeyboardInterrupt:
                print("🛑 正在停止Celery服务...")
        return

    try:
        from django.core.management import execute_from_command_line

        # 如果启用Celery，在后台启动
        if args.with_celery:
            print("🔄 启动Celery服务...")
            worker_thread = threading.Thread(target=start_celery_worker, daemon=True)
            beat_thread = threading.Thread(target=start_celery_beat, daemon=True)

            worker_thread.start()
            time.sleep(2)  # 等待Worker启动
            beat_thread.start()
            time.sleep(1)  # 等待Beat启动

        # 构建Django启动命令
        cmd_args = [
            'manage.py',
            'runserver',
            f'{args.host}:{args.port}'
        ]

        if not args.reload:
            cmd_args.append('--noreload')

        print(f"🚀 启动 ZhiLogger 子项目...")
        print(f"📍 地址: http://{args.host}:{args.port}")
        print(f"🔧 配置: application.settings.zhi_logger")
        print(f"📊 API文档: http://{args.host}:{args.port}/api/logger/logger/docs")
        print(f"🔌 WebSocket: ws://{args.host}:{args.port}/ws/logs/")
        if args.with_celery:
            print(f"🔄 Celery: Worker + Beat 已启动")
        print("-" * 50)

        execute_from_command_line(cmd_args)

    except ImportError as exc:
        raise ImportError(
            "无法导入 Django。请确保已安装 Django 并且在 PYTHONPATH 环境变量中可用。"
            "是否忘记激活虚拟环境？"
        ) from exc

if __name__ == '__main__':
    main()
