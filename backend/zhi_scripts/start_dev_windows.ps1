# Windows开发环境启动脚本
# 基于您提供的Linux脚本适配Windows环境

param(
    [string]$Mode = "webapp",  # webapp, celery-worker, celery-beat, all
    [switch]$Init,             # 是否运行初始化
    [switch]$Migrate,          # 是否运行迁移
    [int]$Workers = 4,         # Web服务器工作进程数
    [int]$CeleryWorkers = 1,   # Celery工作进程数
    [string]$Host = "0.0.0.0", # 服务器主机
    [int]$Port = 8001          # 服务器端口
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "    ZhiAdmin Windows 开发环境启动脚本" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 获取脚本所在目录的父目录（backend目录）
$BackendPath = Split-Path -Parent $PSScriptRoot
Set-Location $BackendPath

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
try {
    & ".\.venv\Scripts\Activate.ps1"
    Write-Host "✅ 虚拟环境激活成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 虚拟环境激活失败: $_" -ForegroundColor Red
    Write-Host "请确保虚拟环境存在: .venv" -ForegroundColor Yellow
    exit 1
}

# 设置环境变量
$env:DJANGO_SETTINGS_MODULE = "application.settings.base"
$env:PYTHONPATH = Get-Location
Write-Host "✅ 环境变量设置完成" -ForegroundColor Green

# 检查并安装依赖
Write-Host "检查依赖包..." -ForegroundColor Yellow
$requiredPackages = @("gevent", "redis", "celery")
foreach ($package in $requiredPackages) {
    try {
        python -c "import $package" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $package 已安装" -ForegroundColor Green
        } else {
            Write-Host "⚠️  安装 $package..." -ForegroundColor Yellow
            pip install $package
        }
    } catch {
        Write-Host "⚠️  跳过 $package 检查" -ForegroundColor Gray
    }
}

# 运行数据库迁移
if ($Migrate -or $Init) {
    Write-Host "运行数据库迁移..." -ForegroundColor Yellow
    python manage.py makemigrations
    python manage.py migrate
    Write-Host "✅ 数据库迁移完成" -ForegroundColor Green
}

# 运行初始化
if ($Init) {
    Write-Host "运行系统初始化..." -ForegroundColor Yellow
    python manage.py init -y
    Write-Host "✅ 系统初始化完成" -ForegroundColor Green
}

# 根据模式启动服务
switch ($Mode.ToLower()) {
    "webapp" {
        Write-Host "启动Web应用服务器..." -ForegroundColor Green
        Write-Host "模式: Django开发服务器" -ForegroundColor Cyan
        Write-Host "地址: http://${Host}:${Port}" -ForegroundColor Cyan
        Write-Host "API文档: http://${Host}:${Port}/api/oauth/docs" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "按 Ctrl+C 停止服务器" -ForegroundColor Yellow
        Write-Host ""
        
        # 使用Django开发服务器（适合开发环境）
        python manage.py runserver "${Host}:${Port}"
    }
    
    "webapp-uvicorn" {
        Write-Host "启动Web应用服务器 (Uvicorn)..." -ForegroundColor Green
        Write-Host "模式: Uvicorn ASGI服务器" -ForegroundColor Cyan
        Write-Host "地址: http://${Host}:${Port}" -ForegroundColor Cyan
        Write-Host "工作进程: $Workers" -ForegroundColor Cyan
        Write-Host ""
        
        # 检查uvicorn是否安装
        python -c "import uvicorn" 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "安装 uvicorn..." -ForegroundColor Yellow
            pip install uvicorn
        }
        
        uvicorn application.asgi:application --port $Port --host $Host --workers $Workers
    }
    
    "celery-worker" {
        Write-Host "启动Celery Worker..." -ForegroundColor Green
        Write-Host "模式: 异步任务处理" -ForegroundColor Cyan
        Write-Host "工作进程: $CeleryWorkers" -ForegroundColor Cyan
        Write-Host ""
        
        # 使用Django管理命令启动Celery Worker（适配zhi_celery架构）
        python manage.py celery_worker --pool=solo --loglevel=info --concurrency=$CeleryWorkers --queues=default,logger,oauth
    }
    
    "celery-beat" {
        Write-Host "启动Celery Beat..." -ForegroundColor Green
        Write-Host "模式: 定时任务调度" -ForegroundColor Cyan
        Write-Host ""
        
        # 使用Django管理命令启动Celery Beat
        python manage.py celery_beat --loglevel=info
    }
    
    "celery-all" {
        Write-Host "启动Celery Worker + Beat..." -ForegroundColor Green
        Write-Host "模式: 异步任务 + 定时任务" -ForegroundColor Cyan
        Write-Host "工作进程: $CeleryWorkers" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "注意: 生产环境建议分别启动Worker和Beat" -ForegroundColor Yellow
        Write-Host ""
        
        # 使用zhi_celery应用启动（Windows兼容）
        celery -A zhi_celery.celery_app worker --loglevel=info --beat -c $CeleryWorkers -P solo
    }
    
    "all" {
        Write-Host "启动完整开发环境..." -ForegroundColor Green
        Write-Host "将启动: Web服务器 + Celery Worker + Celery Beat" -ForegroundColor Cyan
        Write-Host ""
        
        # 启动Celery Worker作为后台任务
        Write-Host "启动Celery Worker..." -ForegroundColor Yellow
        $celeryWorkerJob = Start-Job -ScriptBlock {
            Set-Location $using:BackendPath
            & ".\.venv\Scripts\Activate.ps1"
            $env:DJANGO_SETTINGS_MODULE = "application.settings.base"
            python manage.py celery_worker --pool=solo --loglevel=info --concurrency=$using:CeleryWorkers --queues=default,logger,oauth
        }
        
        # 启动Celery Beat作为后台任务
        Write-Host "启动Celery Beat..." -ForegroundColor Yellow
        $celeryBeatJob = Start-Job -ScriptBlock {
            Set-Location $using:BackendPath
            & ".\.venv\Scripts\Activate.ps1"
            $env:DJANGO_SETTINGS_MODULE = "application.settings.base"
            python manage.py celery_beat --loglevel=info
        }
        
        # 等待后台服务启动
        Start-Sleep -Seconds 3
        
        Write-Host "启动Web服务器..." -ForegroundColor Yellow
        Write-Host "地址: http://${Host}:${Port}" -ForegroundColor Cyan
        Write-Host "API文档: http://${Host}:${Port}/api/oauth/docs" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "按 Ctrl+C 停止所有服务" -ForegroundColor Yellow
        Write-Host ""
        
        try {
            # 启动Web服务器（前台运行）
            python manage.py runserver "${Host}:${Port}"
        } finally {
            # 清理后台任务
            Write-Host "停止后台服务..." -ForegroundColor Yellow
            Stop-Job $celeryWorkerJob -ErrorAction SilentlyContinue
            Stop-Job $celeryBeatJob -ErrorAction SilentlyContinue
            Remove-Job $celeryWorkerJob -ErrorAction SilentlyContinue
            Remove-Job $celeryBeatJob -ErrorAction SilentlyContinue
        }
    }
    
    default {
        Write-Host "❌ 未知模式: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "可用模式:" -ForegroundColor Cyan
        Write-Host "  webapp        - Django开发服务器" -ForegroundColor White
        Write-Host "  webapp-uvicorn- Uvicorn ASGI服务器" -ForegroundColor White
        Write-Host "  celery-worker - Celery异步任务处理" -ForegroundColor White
        Write-Host "  celery-beat   - Celery定时任务调度" -ForegroundColor White
        Write-Host "  celery-all    - Celery Worker + Beat" -ForegroundColor White
        Write-Host "  all           - 完整开发环境" -ForegroundColor White
        Write-Host ""
        Write-Host "示例:" -ForegroundColor Cyan
        Write-Host "  .\zhi_scripts\start_dev_windows.ps1 -Mode webapp" -ForegroundColor Gray
        Write-Host "  .\zhi_scripts\start_dev_windows.ps1 -Mode celery-worker" -ForegroundColor Gray
        Write-Host "  .\zhi_scripts\start_dev_windows.ps1 -Mode all -Init" -ForegroundColor Gray
        exit 1
    }
}

Write-Host ""
Write-Host "服务已停止" -ForegroundColor Yellow
