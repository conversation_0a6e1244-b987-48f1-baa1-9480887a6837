#!/usr/bin/env python
"""
审计配置管理脚本

用于管理和测试各子项目的审计配置

使用方式:
python backend/zhi_scripts/manage_audit_config.py list
python backend/zhi_scripts/manage_audit_config.py test --project zhi_logger
python backend/zhi_scripts/manage_audit_config.py reload --project zhi_oauth
"""

import os
import sys
import argparse
from pathlib import Path
from typing import Dict, Any

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
    import django
    django.setup()

def list_audit_configs():
    """列出所有审计配置"""
    setup_django()
    
    from zhi_common.zhi_model.audit_config_manager import audit_config_manager
    
    print("📋 审计配置列表:")
    print("=" * 60)
    
    all_configs = audit_config_manager.get_all_configs()
    
    if not all_configs:
        print("   ⚠️  未找到任何审计配置")
        return
    
    # 按项目分组显示
    projects = {}
    for model_path, config in all_configs.items():
        if '.' in model_path:
            app_name, model_name = model_path.split('.', 1)
        else:
            app_name = 'unknown'
            model_name = model_path
        
        if app_name not in projects:
            projects[app_name] = []
        
        projects[app_name].append({
            'model': model_name,
            'enabled': config.get('is_enabled', False),
            'fields_count': len(config.get('using_fields', {}))
        })
    
    for project, models in projects.items():
        print(f"\n📦 {project}")
        for model_info in models:
            status = "✅" if model_info['enabled'] else "❌"
            print(f"   {status} {model_info['model']} ({model_info['fields_count']} 字段)")
    
    print(f"\n📊 统计:")
    print(f"   项目数量: {len(projects)}")
    print(f"   模型数量: {len(all_configs)}")
    print(f"   启用审计: {sum(1 for config in all_configs.values() if config.get('is_enabled', False))}")

def test_audit_config(project_name: str = None):
    """测试审计配置"""
    setup_django()
    
    from zhi_common.zhi_model.audit_config_manager import audit_config_manager
    from zhi_common.zhi_logger import get_logger
    
    logger = get_logger(__name__)
    
    print(f"🧪 测试审计配置 {f'({project_name})' if project_name else '(全部)'}")
    print("=" * 50)
    
    try:
        # 重新加载配置
        if project_name:
            audit_config_manager.reload_config(project_name)
        else:
            audit_config_manager.reload_config()
        
        # 获取配置
        all_configs = audit_config_manager.get_all_configs()
        
        if project_name:
            # 过滤指定项目的配置
            project_configs = {
                k: v for k, v in all_configs.items() 
                if k.startswith(f"{project_name}.")
            }
        else:
            project_configs = all_configs
        
        if not project_configs:
            print(f"   ❌ 未找到项目 {project_name} 的配置")
            return False
        
        # 测试每个模型配置
        success_count = 0
        total_count = len(project_configs)
        
        for model_path, config in project_configs.items():
            try:
                # 验证配置结构
                if not isinstance(config, dict):
                    print(f"   ❌ {model_path}: 配置不是字典类型")
                    continue
                
                if 'is_enabled' not in config:
                    print(f"   ❌ {model_path}: 缺少 is_enabled 字段")
                    continue
                
                if 'using_fields' not in config:
                    print(f"   ❌ {model_path}: 缺少 using_fields 字段")
                    continue
                
                using_fields = config['using_fields']
                if not isinstance(using_fields, dict):
                    print(f"   ❌ {model_path}: using_fields 不是字典类型")
                    continue
                
                # 验证字段配置
                field_errors = []
                for field_name, field_config in using_fields.items():
                    if not isinstance(field_config, dict):
                        field_errors.append(f"{field_name}: 不是字典类型")
                        continue
                    
                    required_attrs = ['field_name', 'is_enabled', 'is_important']
                    for attr in required_attrs:
                        if attr not in field_config:
                            field_errors.append(f"{field_name}: 缺少 {attr} 属性")
                
                if field_errors:
                    print(f"   ❌ {model_path}: 字段配置错误")
                    for error in field_errors[:3]:  # 只显示前3个错误
                        print(f"      - {error}")
                    if len(field_errors) > 3:
                        print(f"      - ... 还有 {len(field_errors) - 3} 个错误")
                    continue
                
                # 测试配置访问
                is_enabled = audit_config_manager.is_model_audit_enabled(model_path)
                using_fields_result = audit_config_manager.get_model_using_fields(model_path)
                
                print(f"   ✅ {model_path}: 配置有效 (启用: {is_enabled}, 字段: {len(using_fields_result)})")
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ {model_path}: 测试失败 - {str(e)}")
        
        print(f"\n📊 测试结果:")
        print(f"   成功: {success_count}/{total_count}")
        print(f"   成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("   🎉 所有配置测试通过！")
            return True
        else:
            print("   ⚠️  部分配置存在问题")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {str(e)}")
        return False

def reload_audit_config(project_name: str = None):
    """重新加载审计配置"""
    setup_django()
    
    from zhi_common.zhi_model.audit_config_manager import audit_config_manager
    
    print(f"🔄 重新加载审计配置 {f'({project_name})' if project_name else '(全部)'}")
    
    try:
        audit_config_manager.reload_config(project_name)
        
        # 显示加载结果
        all_configs = audit_config_manager.get_all_configs()
        
        if project_name:
            project_configs = {
                k: v for k, v in all_configs.items() 
                if k.startswith(f"{project_name}.")
            }
            print(f"   ✅ 重新加载完成，{project_name} 项目共 {len(project_configs)} 个模型配置")
        else:
            print(f"   ✅ 重新加载完成，共 {len(all_configs)} 个模型配置")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 重新加载失败: {str(e)}")
        return False

def validate_all_configs():
    """验证所有配置"""
    setup_django()
    
    print("🔍 验证所有审计配置")
    print("=" * 40)
    
    # 获取所有已安装的应用
    from django.conf import settings
    
    installed_apps = settings.INSTALLED_APPS
    zhi_apps = [app for app in installed_apps if app.startswith('zhi_')]
    
    print(f"发现 {len(zhi_apps)} 个 ZhiAdmin 应用:")
    for app in zhi_apps:
        print(f"   - {app}")
    
    print("\n检查审计配置文件:")
    
    for app in zhi_apps:
        config_file = backend_path / app / 'audit_config.py'
        if config_file.exists():
            print(f"   ✅ {app}/audit_config.py")
            
            # 测试配置
            if test_audit_config(app):
                print(f"      ✅ 配置有效")
            else:
                print(f"      ❌ 配置无效")
        else:
            print(f"   ⚠️  {app}/audit_config.py - 文件不存在")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='审计配置管理工具')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # list 命令
    subparsers.add_parser('list', help='列出所有审计配置')
    
    # test 命令
    test_parser = subparsers.add_parser('test', help='测试审计配置')
    test_parser.add_argument('--project', help='指定项目名称')
    
    # reload 命令
    reload_parser = subparsers.add_parser('reload', help='重新加载审计配置')
    reload_parser.add_argument('--project', help='指定项目名称')
    
    # validate 命令
    subparsers.add_parser('validate', help='验证所有配置')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == 'list':
            list_audit_configs()
        elif args.command == 'test':
            test_audit_config(args.project)
        elif args.command == 'reload':
            reload_audit_config(args.project)
        elif args.command == 'validate':
            validate_all_configs()
    except KeyboardInterrupt:
        print("\n⏹️  用户取消操作")
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")

if __name__ == '__main__':
    main()
