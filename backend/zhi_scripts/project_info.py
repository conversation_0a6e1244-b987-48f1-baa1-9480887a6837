#!/usr/bin/env python
"""
ZhiAdmin 项目信息概览

显示项目的基本信息、子项目列表和快速启动命令

使用方式:
python backend/zhi_scripts/project_info.py
"""

import os
import sys
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
project_root = backend_path.parent
sys.path.insert(0, str(backend_path))

def show_project_info():
    """显示项目基本信息"""
    print("🎯 ZhiAdmin - 智能管理系统")
    print("=" * 50)
    print("📍 项目根目录:", project_root)
    print("🔧 后端根目录:", backend_path)
    print("🐍 Python版本:", f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    print()

def show_project_structure():
    """显示项目结构"""
    print("📁 项目结构:")
    print("=" * 30)
    
    structure = [
        ("backend/", "后端根目录"),
        ("├── manage.py", "Django管理入口"),
        ("├── application/", "核心配置管理"),
        ("│   ├── settings/", "各子项目配置"),
        ("│   └── conf_urls/", "各子项目URL配置"),
        ("├── zhi_oauth/", "OAuth2认证子项目"),
        ("├── zhi_logger/", "日志管理子项目"),
        ("├── zhi_system/", "系统管理子项目"),
        ("├── zhi_common/", "通用组件和SDK"),
        ("└── zhi_scripts/", "项目管理脚本"),
    ]
    
    for path, desc in structure:
        print(f"   {path:<20} # {desc}")
    print()

def show_subprojects():
    """显示子项目列表"""
    print("🚀 子项目列表:")
    print("=" * 60)
    
    projects = [
        {
            'name': 'zhi_oauth',
            'port': 8001,
            'title': 'OAuth2权限认证系统',
            'features': ['OAuth2.0实现', 'JWT Token管理', '权限控制'],
            'script': 'start_oauth.py'
        },
        {
            'name': 'zhi_logger',
            'port': 8002,
            'title': '日志管理系统',
            'features': ['结构化日志', 'WebSocket推送', 'API日志', '日志分析'],
            'script': 'start_logger.py'
        },
        {
            'name': 'zhi_system',
            'port': 8003,
            'title': '系统管理模块',
            'features': ['用户管理', '角色权限', '系统监控', '配置管理'],
            'script': 'start_system.py'
        },
        {
            'name': 'interval_admin',
            'port': 8000,
            'title': '完整管理后台',
            'features': ['统一后台', '完整功能', '管理界面'],
            'script': 'start_admin.py'
        }
    ]
    
    for project in projects:
        print(f"📦 {project['name']}")
        print(f"   名称: {project['title']}")
        print(f"   端口: {project['port']}")
        print(f"   特性: {', '.join(project['features'])}")
        print(f"   启动: python backend/zhi_scripts/{project['script']}")
        print()

def show_quick_commands():
    """显示快速启动命令"""
    print("⚡ 快速启动命令:")
    print("=" * 40)
    
    commands = [
        ("安装依赖", "pip install -r backend/requirements.txt"),
        ("检查项目状态", "python backend/zhi_scripts/check_project.py"),
        ("列出所有项目", "python backend/zhi_scripts/manage_projects.py list"),
        ("检查运行状态", "python backend/zhi_scripts/manage_projects.py status"),
        ("启动OAuth服务", "python backend/zhi_scripts/start_oauth.py --port 8001"),
        ("启动日志服务", "python backend/zhi_scripts/start_logger.py --port 8002"),
        ("启动系统管理", "python backend/zhi_scripts/start_system.py --port 8003"),
        ("启动完整后台", "python backend/zhi_scripts/start_admin.py --port 8000"),
        ("数据库迁移", "python backend/zhi_scripts/manage_projects.py migrate zhi_logger"),
        ("创建超级用户", "python backend/zhi_scripts/manage_projects.py createsuperuser zhi_logger"),
    ]
    
    for desc, cmd in commands:
        print(f"   {desc:<12}: {cmd}")
    print()

def show_windows_commands():
    """显示Windows快速启动命令"""
    print("💻 Windows快速启动:")
    print("=" * 30)
    
    commands = [
        ("OAuth服务", "backend\\zhi_scripts\\start_oauth.bat 8001"),
        ("日志服务", "backend\\zhi_scripts\\start_logger.bat 8002"),
        ("系统管理", "backend\\zhi_scripts\\start_system.bat 8003"),
        ("完整后台", "backend\\zhi_scripts\\start_admin.bat 8000"),
    ]
    
    for desc, cmd in commands:
        print(f"   {desc:<8}: {cmd}")
    print()

def show_api_endpoints():
    """显示API端点信息"""
    print("🌐 API端点:")
    print("=" * 25)
    
    endpoints = [
        ("OAuth API", "http://localhost:8001/api/oauth/", "http://localhost:8001/oauth/docs"),
        ("日志 API", "http://localhost:8002/api/logger/", "http://localhost:8002/logger/docs"),
        ("系统 API", "http://localhost:8003/api/system/", "http://localhost:8003/system/docs"),
        ("完整 API", "http://localhost:8000/api/", "http://localhost:8000/docs"),
    ]
    
    for name, api_url, docs_url in endpoints:
        print(f"   {name:<8}: {api_url}")
        print(f"   {'文档':<8}: {docs_url}")
        print()

def show_special_features():
    """显示特殊功能"""
    print("✨ 特殊功能:")
    print("=" * 20)
    
    features = [
        ("WebSocket日志", "ws://localhost:8002/ws/logs/", "实时日志推送"),
        ("日志SDK", "from zhi_common.zhi_logger import get_logger", "统一日志记录"),
        ("多项目管理", "python backend/zhi_scripts/manage_projects.py", "统一管理工具"),
        ("项目检查", "python backend/zhi_scripts/check_project.py", "配置和依赖检查"),
    ]
    
    for name, usage, desc in features:
        print(f"   {name:<12}: {desc}")
        print(f"   {'使用':<12}: {usage}")
        print()

def show_documentation():
    """显示文档链接"""
    print("📚 相关文档:")
    print("=" * 20)
    
    docs = [
        ("项目README", "README.md"),
        ("架构设计", "backend/MULTI_PROJECT_ARCHITECTURE.md"),
        ("脚本说明", "backend/zhi_scripts/README.md"),
        ("日志SDK", "backend/zhi_common/zhi_logger/README.md"),
    ]
    
    for name, path in docs:
        print(f"   {name:<10}: {path}")
    print()

def main():
    """主函数"""
    show_project_info()
    show_project_structure()
    show_subprojects()
    show_quick_commands()
    show_windows_commands()
    show_api_endpoints()
    show_special_features()
    show_documentation()
    
    print("🎉 欢迎使用 ZhiAdmin！")
    print("💡 提示: 运行 python backend/zhi_scripts/check_project.py 检查项目配置")

if __name__ == '__main__':
    main()
