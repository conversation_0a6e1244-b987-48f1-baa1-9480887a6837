# ZhiAdmin 项目管理脚本

这个目录包含了用于管理 ZhiAdmin 各个子项目的启动和管理脚本。

## 📁 脚本列表

### 🚀 启动脚本
- `start_logger.py` - 启动日志管理子项目 (端口: 8002)
- `start_oauth.py` - 启动OAuth2认证子项目 (端口: 8001)  
- `start_system.py` - 启动系统管理子项目 (端口: 8003)
- `start_admin.py` - 启动完整管理后台 (端口: 8000)

### 🛠️ 管理脚本
- `manage_projects.py` - 统一的多项目管理工具
- `check_project.py` - 项目状态检查工具

### 💻 Windows批处理文件
- `start_logger.bat` - Windows快速启动日志服务
- `start_oauth.bat` - Windows快速启动OAuth服务
- `start_system.bat` - Windows快速启动系统管理服务
- `start_admin.bat` - Windows快速启动完整后台

## 🚀 使用方法

### 方式1: 使用单独的启动脚本

```bash
# 启动日志管理服务
python backend/zhi_scripts/start_logger.py --port 8002 --debug

# 启动OAuth2认证服务
python backend/zhi_scripts/start_oauth.py --port 8001

# 启动系统管理服务
python backend/zhi_scripts/start_system.py --port 8003

# 启动完整管理后台
python backend/zhi_scripts/start_admin.py --port 8000
```

### 方式2: 使用统一管理工具

```bash
# 列出所有可用项目
python backend/zhi_scripts/manage_projects.py list

# 检查项目运行状态
python backend/zhi_scripts/manage_projects.py status

# 启动指定项目
python backend/zhi_scripts/manage_projects.py start zhi_logger --port 8002
python backend/zhi_scripts/manage_projects.py start zhi_oauth --port 8001 --debug

# 执行数据库迁移
python backend/zhi_scripts/manage_projects.py migrate zhi_logger
python backend/zhi_scripts/manage_projects.py migrate zhi_oauth

# 收集静态文件
python backend/zhi_scripts/manage_projects.py collectstatic zhi_logger

# 创建超级用户
python backend/zhi_scripts/manage_projects.py createsuperuser zhi_logger
```

### 方式3: Windows快速启动

```cmd
REM 启动日志服务
backend\zhi_scripts\start_logger.bat 8002

REM 启动OAuth服务
backend\zhi_scripts\start_oauth.bat 8001

REM 启动系统管理服务
backend\zhi_scripts\start_system.bat 8003

REM 启动完整后台
backend\zhi_scripts\start_admin.bat 8000
```

### 方式4: 项目状态检查

```bash
# 检查默认项目(zhi_logger)
python backend/zhi_scripts/check_project.py

# 检查指定项目
python backend/zhi_scripts/check_project.py --project zhi_oauth

# 详细检查
python backend/zhi_scripts/check_project.py --project zhi_logger --verbose
```

## 📊 项目配置

| 项目 | 端口 | 配置文件 | API路径 | 文档路径 |
|------|------|----------|---------|----------|
| zhi_oauth | 8001 | application.settings.zhi_oauth | /api/oauth/ | /oauth/docs |
| zhi_logger | 8002 | application.settings.zhi_logger | /api/logger/ | /logger/docs |
| zhi_system | 8003 | application.settings.zhi_system | /api/system/ | /system/docs |
| interval_admin | 8000 | application.settings.interval_admin | /api/ | /docs |

## 🔧 参数说明

### 启动脚本参数
- `--port` - 指定服务端口
- `--host` - 指定服务主机 (默认: 0.0.0.0)
- `--debug` - 启用调试模式
- `--reload` - 启用自动重载

### 管理脚本命令
- `list` - 列出所有可用项目
- `status` - 检查项目运行状态
- `start` - 启动项目
- `migrate` - 执行数据库迁移
- `collectstatic` - 收集静态文件
- `createsuperuser` - 创建超级用户

### 检查脚本参数
- `--project` - 指定要检查的项目
- `--verbose` - 显示详细信息

## 🌟 使用示例

### 开发环境启动
```bash
# 1. 安装依赖
pip install -r backend/requirements.txt

# 2. 检查项目状态
python backend/zhi_scripts/check_project.py --project zhi_logger

# 3. 执行数据库迁移
python backend/zhi_scripts/manage_projects.py migrate zhi_logger

# 4. 启动开发服务器
python backend/zhi_scripts/start_logger.py --debug --reload
```

### 生产环境部署
```bash
# 1. 收集静态文件
python backend/zhi_scripts/manage_projects.py collectstatic zhi_logger

# 2. 执行数据库迁移
python backend/zhi_scripts/manage_projects.py migrate zhi_logger

# 3. 创建超级用户
python backend/zhi_scripts/manage_projects.py createsuperuser zhi_logger

# 4. 启动服务
python backend/zhi_scripts/start_logger.py --port 8002
```

### 多服务同时运行
```bash
# 在不同终端窗口中启动不同服务
# 终端1: OAuth服务
python backend/zhi_scripts/start_oauth.py --port 8001

# 终端2: 日志服务  
python backend/zhi_scripts/start_logger.py --port 8002

# 终端3: 系统管理服务
python backend/zhi_scripts/start_system.py --port 8003
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 使用不同端口启动
   python backend/zhi_scripts/start_logger.py --port 8012
   ```

2. **依赖包缺失**
   ```bash
   # 安装依赖
   pip install -r backend/requirements.txt
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库配置
   python backend/zhi_scripts/check_project.py --project zhi_logger
   ```

4. **Redis连接失败**
   ```bash
   # 确保Redis服务正在运行
   redis-server
   ```

### 日志查看
启动脚本会显示详细的启动信息，包括：
- 服务地址
- API文档地址
- WebSocket地址（日志服务）
- 配置文件路径

## 📝 注意事项

1. **工作目录**: 所有脚本都会自动切换到正确的工作目录
2. **环境变量**: 脚本会自动设置必要的Django环境变量
3. **虚拟环境**: 确保在正确的Python虚拟环境中运行脚本
4. **权限**: Linux/Mac系统可能需要给脚本添加执行权限: `chmod +x backend/zhi_scripts/*.py`

## 🆘 获取帮助

```bash
# 查看脚本帮助信息
python backend/zhi_scripts/start_logger.py --help
python backend/zhi_scripts/manage_projects.py --help
python backend/zhi_scripts/check_project.py --help
```

---

## 🆕 新增Windows开发环境脚本

### 通用开发环境启动
- `start_dev_windows.ps1` - PowerShell版本，支持多种启动模式
- `start_dev_windows.bat` - 批处理版本，交互式选择启动模式

### ZhiCelery专用启动
- `start_zhi_celery_windows.ps1` - 专门用于启动ZhiCelery服务
- `test_zhi_celery.py` - 测试ZhiCelery配置

### OAuth API测试
- `test_oauth_complete.py` - 完整的OAuth API测试套件
- `quick_test_oauth.bat` - 快速OAuth API测试

### 🚀 新脚本使用示例

#### 启动开发环境
```bash
# PowerShell方式
.\zhi_scripts\start_dev_windows.ps1 -Mode webapp
.\zhi_scripts\start_dev_windows.ps1 -Mode celery-worker
.\zhi_scripts\start_dev_windows.ps1 -Mode all -Init

# 批处理方式
.\zhi_scripts\start_dev_windows.bat
```

#### 启动ZhiCelery
```bash
# 启动Worker
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode worker

# 启动Beat
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode beat

# 启动所有服务
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode all

# 测试配置
.\zhi_scripts\start_zhi_celery_windows.ps1 -Mode test
```

#### 测试OAuth API
```bash
# 快速测试
.\zhi_scripts\quick_test_oauth.bat

# 完整测试
python zhi_scripts\test_oauth_complete.py
```

### ✨ 新脚本特性
- 🔄 自动激活虚拟环境
- 📦 自动检查和安装依赖
- 🔍 详细的状态检查和错误提示
- 🎯 支持多种启动模式
- 🧪 内置测试功能
- 📊 统一的响应格式验证
