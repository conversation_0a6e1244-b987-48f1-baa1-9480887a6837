#!/usr/bin/env python
"""
创建 zhi_common 应用迁移脚本

为 zhi_common 应用创建初始迁移文件

使用方式:
python backend/zhi_scripts/create_zhi_common_migration.py
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
    import django
    django.setup()

def create_migrations_directory():
    """创建迁移目录"""
    migrations_dir = backend_path / 'zhi_common' / 'migrations'
    migrations_dir.mkdir(exist_ok=True)
    
    # 创建 __init__.py 文件
    init_file = migrations_dir / '__init__.py'
    if not init_file.exists():
        init_file.write_text('')
        print(f"✅ 创建迁移目录: {migrations_dir}")
    
    return migrations_dir

def create_migration():
    """创建迁移文件"""
    print("🔄 为 zhi_common 应用创建迁移...")
    
    try:
        # 切换到backend目录
        os.chdir(backend_path)
        
        # 创建迁移文件
        result = subprocess.run([
            sys.executable, 'manage.py', 'makemigrations', 'zhi_common',
            '--name', 'initial_zhi_common_models',
            '--settings', 'application.settings.base'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ zhi_common 迁移文件创建成功")
            print(result.stdout)
            return True
        else:
            print("❌ zhi_common 迁移文件创建失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 创建迁移失败: {str(e)}")
        return False

def apply_migration():
    """执行迁移"""
    print("\n🔄 执行 zhi_common 数据库迁移...")
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'migrate', 'zhi_common',
            '--settings', 'application.settings.base'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ zhi_common 数据库迁移执行成功")
            print(result.stdout)
            return True
        else:
            print("❌ zhi_common 数据库迁移执行失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行迁移失败: {str(e)}")
        return False

def check_migration_status():
    """检查迁移状态"""
    print("🔍 检查 zhi_common 迁移状态...")
    
    try:
        result = subprocess.run([
            sys.executable, 'manage.py', 'showmigrations', 'zhi_common',
            '--settings', 'application.settings.base'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📋 zhi_common 迁移状态:")
            print(result.stdout)
            return True
        else:
            print("❌ 检查迁移状态失败")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 检查迁移状态失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 ZhiCommon 应用迁移工具")
    print("=" * 40)
    
    setup_django()
    
    # 创建迁移目录
    create_migrations_directory()
    
    # 检查当前迁移状态
    print("\n📋 当前迁移状态:")
    check_migration_status()
    
    print("\n" + "=" * 40)
    
    # 创建迁移
    if create_migration():
        print("\n" + "=" * 40)
        
        # 询问是否执行迁移
        try:
            response = input("是否立即执行迁移? (y/N): ").lower().strip()
            if response in ['y', 'yes']:
                if apply_migration():
                    print("\n🎉 zhi_common 应用迁移完成！")
                    
                    # 再次检查迁移状态
                    print("\n📋 迁移后状态:")
                    check_migration_status()
                else:
                    print("\n❌ 迁移执行失败")
            else:
                print("\n⏹️  跳过迁移执行")
                print("   稍后可以手动执行:")
                print("   python manage.py migrate zhi_common --settings=application.settings.base")
                
        except KeyboardInterrupt:
            print("\n⏹️  用户取消操作")
    else:
        print("\n❌ 迁移创建失败")
        
        print("\n🔧 故障排除建议:")
        print("   1. 检查 zhi_common 是否在 INSTALLED_APPS 中")
        print("   2. 检查模型定义是否正确")
        print("   3. 检查数据库连接是否正常")
        print("   4. 检查 Django 配置是否正确")

if __name__ == '__main__':
    main()
