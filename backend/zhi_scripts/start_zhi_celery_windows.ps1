# ZhiCelery Windows启动脚本
# 基于您的zhi_celery应用架构

param(
    [string]$Mode = "worker",      # worker, beat, flower, all, test
    [string]$Settings = "application.settings.base",
    [string]$LogLevel = "info",
    [int]$Concurrency = 2,
    [string]$Queues = "default,logger,oauth",
    [string]$Pool = "solo"         # Windows推荐使用solo
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "    ZhiCelery Windows 启动脚本" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 获取脚本所在目录的父目录（backend目录）
$BackendPath = Split-Path -Parent $PSScriptRoot
Set-Location $BackendPath

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
try {
    & ".\.venv\Scripts\Activate.ps1"
    Write-Host "✅ 虚拟环境激活成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 虚拟环境激活失败: $_" -ForegroundColor Red
    exit 1
}

# 设置环境变量
$env:DJANGO_SETTINGS_MODULE = $Settings
$env:PYTHONPATH = Get-Location
Write-Host "✅ 环境变量设置完成" -ForegroundColor Green
Write-Host "   DJANGO_SETTINGS_MODULE: $Settings" -ForegroundColor Gray

# 检查Redis连接
Write-Host "检查Redis连接..." -ForegroundColor Yellow
try {
    python -c "import redis; r=redis.Redis(); r.ping()" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Redis连接正常" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Redis连接失败，请确保Redis服务运行" -ForegroundColor Yellow
        Write-Host "   可以使用: docker run -d -p 6379:6379 redis:alpine" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️  无法检查Redis连接" -ForegroundColor Yellow
}

# 检查zhi_celery应用
Write-Host "检查zhi_celery应用..." -ForegroundColor Yellow
try {
    python -c "from zhi_celery.celery_app import app; print('ZhiCelery应用可用')" 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ zhi_celery应用检查通过" -ForegroundColor Green
    } else {
        Write-Host "❌ zhi_celery应用不可用" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 无法检查zhi_celery应用" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "启动配置:" -ForegroundColor Cyan
Write-Host "  模式: $Mode" -ForegroundColor White
Write-Host "  设置: $Settings" -ForegroundColor White
Write-Host "  日志级别: $LogLevel" -ForegroundColor White
Write-Host "  并发数: $Concurrency" -ForegroundColor White
Write-Host "  队列: $Queues" -ForegroundColor White
Write-Host "  进程池: $Pool" -ForegroundColor White
Write-Host ""

# 根据模式启动服务
switch ($Mode.ToLower()) {
    "worker" {
        Write-Host "启动Celery Worker..." -ForegroundColor Green
        Write-Host "使用Django管理命令启动（推荐）" -ForegroundColor Cyan
        Write-Host ""
        
        python manage.py celery_worker --pool=$Pool --loglevel=$LogLevel --concurrency=$Concurrency --queues=$Queues
    }
    
    "beat" {
        Write-Host "启动Celery Beat..." -ForegroundColor Green
        Write-Host "使用Django管理命令启动" -ForegroundColor Cyan
        Write-Host ""
        
        python manage.py celery_beat --loglevel=$LogLevel
    }
    
    "flower" {
        Write-Host "启动Celery Flower..." -ForegroundColor Green
        Write-Host "Web监控界面" -ForegroundColor Cyan
        Write-Host ""
        
        # 检查flower是否安装
        python -c "import flower" 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "安装flower..." -ForegroundColor Yellow
            pip install flower
        }
        
        celery -A zhi_celery.celery_app flower --loglevel=$LogLevel
    }
    
    "all" {
        Write-Host "启动完整Celery环境..." -ForegroundColor Green
        Write-Host "将启动: Worker + Beat + Flower" -ForegroundColor Cyan
        Write-Host ""
        
        # 启动Worker作为后台任务
        Write-Host "启动Celery Worker..." -ForegroundColor Yellow
        $workerJob = Start-Job -ScriptBlock {
            Set-Location $using:BackendPath
            & ".\.venv\Scripts\Activate.ps1"
            $env:DJANGO_SETTINGS_MODULE = $using:Settings
            python manage.py celery_worker --pool=$using:Pool --loglevel=$using:LogLevel --concurrency=$using:Concurrency --queues=$using:Queues
        }
        
        # 启动Beat作为后台任务
        Write-Host "启动Celery Beat..." -ForegroundColor Yellow
        $beatJob = Start-Job -ScriptBlock {
            Set-Location $using:BackendPath
            & ".\.venv\Scripts\Activate.ps1"
            $env:DJANGO_SETTINGS_MODULE = $using:Settings
            python manage.py celery_beat --loglevel=$using:LogLevel
        }
        
        # 启动Flower作为后台任务
        Write-Host "启动Celery Flower..." -ForegroundColor Yellow
        $flowerJob = Start-Job -ScriptBlock {
            Set-Location $using:BackendPath
            & ".\.venv\Scripts\Activate.ps1"
            $env:DJANGO_SETTINGS_MODULE = $using:Settings
            
            # 检查flower是否安装
            python -c "import flower" 2>$null
            if ($LASTEXITCODE -ne 0) {
                pip install flower
            }
            
            celery -A zhi_celery.celery_app flower --loglevel=$using:LogLevel
        }
        
        # 等待服务启动
        Start-Sleep -Seconds 5
        
        Write-Host ""
        Write-Host "✅ 所有服务已启动" -ForegroundColor Green
        Write-Host "   Worker: 后台运行" -ForegroundColor Gray
        Write-Host "   Beat: 后台运行" -ForegroundColor Gray
        Write-Host "   Flower: http://localhost:5555" -ForegroundColor Gray
        Write-Host ""
        Write-Host "按 Ctrl+C 停止所有服务" -ForegroundColor Yellow
        Write-Host ""
        
        try {
            # 保持前台运行，监控后台任务
            while ($true) {
                Start-Sleep -Seconds 5
                
                # 检查任务状态
                $workerState = Get-Job $workerJob | Select-Object -ExpandProperty State
                $beatState = Get-Job $beatJob | Select-Object -ExpandProperty State
                $flowerState = Get-Job $flowerJob | Select-Object -ExpandProperty State
                
                if ($workerState -eq "Failed" -or $beatState -eq "Failed" -or $flowerState -eq "Failed") {
                    Write-Host "⚠️  检测到服务异常，查看详细信息..." -ForegroundColor Yellow
                    
                    if ($workerState -eq "Failed") {
                        Write-Host "Worker错误:" -ForegroundColor Red
                        Receive-Job $workerJob
                    }
                    if ($beatState -eq "Failed") {
                        Write-Host "Beat错误:" -ForegroundColor Red
                        Receive-Job $beatJob
                    }
                    if ($flowerState -eq "Failed") {
                        Write-Host "Flower错误:" -ForegroundColor Red
                        Receive-Job $flowerJob
                    }
                    break
                }
            }
        } finally {
            # 清理后台任务
            Write-Host "停止所有服务..." -ForegroundColor Yellow
            Stop-Job $workerJob -ErrorAction SilentlyContinue
            Stop-Job $beatJob -ErrorAction SilentlyContinue
            Stop-Job $flowerJob -ErrorAction SilentlyContinue
            Remove-Job $workerJob -ErrorAction SilentlyContinue
            Remove-Job $beatJob -ErrorAction SilentlyContinue
            Remove-Job $flowerJob -ErrorAction SilentlyContinue
        }
    }
    
    "test" {
        Write-Host "测试Celery配置..." -ForegroundColor Green
        Write-Host ""
        
        python test_zhi_celery.py
    }
    
    default {
        Write-Host "❌ 未知模式: $Mode" -ForegroundColor Red
        Write-Host ""
        Write-Host "可用模式:" -ForegroundColor Cyan
        Write-Host "  worker  - 启动Celery Worker" -ForegroundColor White
        Write-Host "  beat    - 启动Celery Beat调度器" -ForegroundColor White
        Write-Host "  flower  - 启动Celery Flower监控" -ForegroundColor White
        Write-Host "  all     - 启动所有服务" -ForegroundColor White
        Write-Host "  test    - 测试Celery配置" -ForegroundColor White
        Write-Host ""
        Write-Host "示例:" -ForegroundColor Cyan
        Write-Host "  .\zhi_scripts\start_zhi_celery_windows.ps1 -Mode worker" -ForegroundColor Gray
        Write-Host "  .\zhi_scripts\start_zhi_celery_windows.ps1 -Mode beat" -ForegroundColor Gray
        Write-Host "  .\zhi_scripts\start_zhi_celery_windows.ps1 -Mode all" -ForegroundColor Gray
        Write-Host "  .\zhi_scripts\start_zhi_celery_windows.ps1 -Mode test" -ForegroundColor Gray
        exit 1
    }
}

Write-Host ""
Write-Host "服务已停止" -ForegroundColor Yellow
