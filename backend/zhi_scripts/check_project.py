#!/usr/bin/env python
"""
ZhiAdmin 项目状态检查脚本

检查项目配置、依赖和各子项目的状态

使用方式:
python backend/zhi_scripts/check_project.py [--project zhi_logger] [--verbose]
"""

import os
import sys
import importlib
import argparse
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def check_python_version():
    """检查Python版本"""
    print("🐍 Python版本检查:")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("   ❌ Python版本过低，建议使用Python 3.8+")
        return False
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查依赖包"""
    print("\n📦 依赖包检查:")
    
    required_packages = [
        'django',
        'ninja',
        'ninja_extra',
        'loguru',
        'channels',
        'celery',
        'redis',
        'pymysql',
    ]

    # 可选包（用于 WebSocket 功能）
    optional_packages = [
        'channels_redis',
    ]
    
    missing_packages = []
    missing_optional = []

    # 检查必需包
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - 未安装")
            missing_packages.append(package)

    # 检查可选包
    for package in optional_packages:
        try:
            importlib.import_module(package)
            print(f"   ✅ {package} (可选)")
        except ImportError:
            print(f"   ⚠️  {package} - 未安装 (可选，用于WebSocket功能)")
            missing_optional.append(package)

    if missing_packages:
        print(f"\n   缺少必需依赖包: {', '.join(missing_packages)}")
        print("   请运行: pip install -r backend/requirements.txt")
        return False

    if missing_optional:
        print(f"\n   缺少可选依赖包: {', '.join(missing_optional)}")
        print("   WebSocket日志推送功能将使用内存层（仅开发环境）")
        print("   生产环境建议安装: pip install channels_redis")

    return True

def check_project_structure():
    """检查项目结构"""
    print("\n📁 项目结构检查:")
    
    required_paths = [
        "manage.py",
        "application/settings/base.py",
        "application/settings/zhi_logger.py",
        "application/settings/zhi_oauth.py",
        "application/conf_urls/base.py",
        "application/conf_urls/zhi_logger.py",
        "application/conf_urls/zhi_oauth.py",
        "zhi_logger/models.py",
        "zhi_logger/api.py",
        "zhi_oauth/models.py",
        "zhi_common/zhi_logger/__init__.py",
        "zhi_common/zhi_logger/base.py",
        "zhi_scripts/manage_projects.py",
        "zhi_scripts/start_logger.py",
        "zhi_scripts/start_oauth.py",
    ]
    
    missing_files = []
    
    for path in required_paths:
        full_path = backend_path / path
        if full_path.exists():
            print(f"   ✅ {path}")
        else:
            print(f"   ❌ {path} - 文件不存在")
            missing_files.append(path)
    
    if missing_files:
        print(f"\n   缺少文件: {len(missing_files)} 个")
        return False
    
    return True

def check_settings(project_name: str = 'zhi_logger'):
    """检查Django设置"""
    print(f"\n⚙️  Django设置检查 ({project_name}):")
    
    try:
        os.chdir(backend_path)
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', f'application.settings.{project_name}')
        
        import django
        django.setup()
        
        from django.conf import settings
        
        # 检查关键设置
        checks = [
            ('DATABASES', 'default' in settings.DATABASES),
            ('REDIS_URL', hasattr(settings, 'REDIS_URL')),
            ('CHANNEL_LAYERS', hasattr(settings, 'CHANNEL_LAYERS') and 'default' in settings.CHANNEL_LAYERS),
            ('INSTALLED_APPS', len(settings.INSTALLED_APPS) > 0),
            ('CELERY_BROKER_URL', hasattr(settings, 'CELERY_BROKER_URL')),
            ('SECRET_KEY', hasattr(settings, 'SECRET_KEY') and settings.SECRET_KEY),
        ]
        
        all_good = True
        for setting_name, check_result in checks:
            if check_result:
                print(f"   ✅ {setting_name}")
            else:
                print(f"   ❌ {setting_name} - 配置有问题")
                all_good = False
        
        return all_good
        
    except Exception as e:
        print(f"   ❌ Django设置检查失败: {e}")
        return False

def check_database_connection(project_name: str = 'zhi_logger'):
    """检查数据库连接"""
    print(f"\n🗄️  数据库连接检查 ({project_name}):")
    
    try:
        from django.db import connection
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        if result:
            print("   ✅ 数据库连接正常")
            return True
        else:
            print("   ❌ 数据库连接异常")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False

def check_redis_connection(project_name: str = 'zhi_logger'):
    """检查Redis连接"""
    print(f"\n🔴 Redis连接检查 ({project_name}):")
    
    try:
        import redis
        from django.conf import settings
        
        # 解析Redis URL
        redis_url = settings.REDIS_URL
        r = redis.from_url(redis_url)
        
        # 测试连接
        r.ping()
        print("   ✅ Redis连接正常")
        return True
        
    except Exception as e:
        print(f"   ❌ Redis连接失败: {e}")
        return False

def check_migrations(project_name: str = 'zhi_logger'):
    """检查数据库迁移状态"""
    print(f"\n🔄 数据库迁移检查 ({project_name}):")
    
    try:
        import subprocess
        result = subprocess.run([
            sys.executable, 'manage.py', 'showmigrations', '--plan',
            '--settings', f'application.settings.{project_name}'
        ], capture_output=True, text=True, cwd=backend_path)
        
        if result.returncode == 0:
            output = result.stdout
            if '[ ]' in output:
                print("   ⚠️  有未应用的迁移")
                print(f"   建议运行: python backend/zhi_scripts/manage_projects.py migrate {project_name}")
                return False
            else:
                print("   ✅ 所有迁移已应用")
                return True
        else:
            print(f"   ❌ 检查迁移失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ 迁移检查失败: {e}")
        return False

def check_scripts():
    """检查启动脚本"""
    print("\n🚀 启动脚本检查:")
    
    scripts = [
        'start_logger.py',
        'start_oauth.py', 
        'start_system.py',
        'start_admin.py',
        'manage_projects.py',
        'check_project.py'
    ]
    
    all_good = True
    for script in scripts:
        script_path = backend_path / 'zhi_scripts' / script
        if script_path.exists():
            print(f"   ✅ {script}")
        else:
            print(f"   ❌ {script} - 文件不存在")
            all_good = False
    
    return all_good

def main():
    parser = argparse.ArgumentParser(description='ZhiAdmin 项目状态检查工具')
    parser.add_argument('--project', default='zhi_logger', 
                       choices=['zhi_logger', 'zhi_oauth', 'zhi_system', 'interval_admin'],
                       help='指定要检查的项目 (默认: zhi_logger)')
    parser.add_argument('--verbose', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    print(f"🔍 ZhiAdmin 项目状态检查 - {args.project}")
    print("=" * 60)
    
    checks = [
        check_python_version,
        check_dependencies,
        check_project_structure,
        check_scripts,
        lambda: check_settings(args.project),
        lambda: check_database_connection(args.project),
        lambda: check_redis_connection(args.project),
        lambda: check_migrations(args.project),
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
        except Exception as e:
            print(f"   ❌ 检查过程中出错: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 检查结果汇总:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"   通过: {passed}/{total}")
    
    if passed == total:
        print("   🎉 所有检查都通过了！项目状态良好。")
        print(f"\n🚀 可以启动 {args.project} 项目:")
        script_name = f"start_{args.project.split('_')[-1]}.py"
        print(f"   python backend/zhi_scripts/{script_name}")
        print("   或")
        print(f"   python backend/zhi_scripts/manage_projects.py start {args.project}")
    else:
        print("   ⚠️  有一些问题需要解决。")
        print("\n🔧 建议:")
        print("   1. 检查并安装缺少的依赖: pip install -r backend/requirements.txt")
        print("   2. 确保数据库和Redis服务正在运行")
        print(f"   3. 运行数据库迁移: python backend/zhi_scripts/manage_projects.py migrate {args.project}")
        print("   4. 检查配置文件中的数据库和Redis连接信息")

if __name__ == '__main__':
    main()
