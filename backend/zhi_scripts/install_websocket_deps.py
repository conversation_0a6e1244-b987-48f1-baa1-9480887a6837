#!/usr/bin/env python
"""
WebSocket 依赖安装脚本

自动检测并安装 WebSocket 日志推送所需的依赖包

使用方式:
python backend/zhi_scripts/install_websocket_deps.py
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_package(package_name):
    """安装包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def check_redis_connection():
    """检查 Redis 连接"""
    try:
        import redis
        from django.conf import settings
        
        # 设置 Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
        import django
        django.setup()
        
        # 测试 Redis 连接
        redis_url = settings.REDIS_URL
        r = redis.from_url(redis_url)
        r.ping()
        print("✅ Redis 连接正常")
        return True
        
    except Exception as e:
        print(f"❌ Redis 连接失败: {e}")
        return False

def test_channel_layer():
    """测试 Channel Layer"""
    try:
        from channels.layers import get_channel_layer
        
        channel_layer = get_channel_layer()
        if channel_layer is None:
            print("❌ Channel Layer 未配置")
            return False
        
        print(f"✅ Channel Layer 配置正确: {type(channel_layer).__name__}")
        return True
        
    except Exception as e:
        print(f"❌ Channel Layer 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查 WebSocket 日志推送依赖...")
    print("=" * 50)
    
    # 必需的包
    required_packages = [
        ('channels', 'Django Channels'),
        ('redis', 'Redis Python客户端'),
    ]
    
    # 可选的包
    optional_packages = [
        ('channels_redis', 'Channels Redis后端 (推荐)'),
    ]
    
    # 检查必需包
    print("📋 检查必需依赖:")
    missing_required = []
    
    for package, description in required_packages:
        if check_package_installed(package):
            print(f"   ✅ {package} - {description}")
        else:
            print(f"   ❌ {package} - {description} (未安装)")
            missing_required.append(package)
    
    # 检查可选包
    print("\n📋 检查可选依赖:")
    missing_optional = []
    
    for package, description in optional_packages:
        if check_package_installed(package):
            print(f"   ✅ {package} - {description}")
        else:
            print(f"   ⚠️  {package} - {description} (未安装)")
            missing_optional.append(package)
    
    # 安装缺失的包
    if missing_required or missing_optional:
        print("\n🔧 安装缺失的依赖...")
        
        # 安装必需包
        for package in missing_required:
            if not install_package(package):
                print(f"❌ 无法安装必需依赖 {package}，请手动安装")
                return False
        
        # 询问是否安装可选包
        if missing_optional:
            print(f"\n📦 发现可选依赖: {', '.join(missing_optional)}")
            
            # 特别推荐 channels_redis
            if 'channels_redis' in missing_optional:
                print("💡 强烈推荐安装 channels_redis 以获得更好的性能和稳定性")
                
                try:
                    response = input("是否安装 channels_redis? (y/N): ").lower().strip()
                    if response in ['y', 'yes']:
                        install_package('channels_redis')
                except KeyboardInterrupt:
                    print("\n⏹️  用户取消安装")
    
    # 测试配置
    print("\n🧪 测试配置...")
    
    # 测试 Redis 连接
    redis_ok = check_redis_connection()
    
    # 测试 Channel Layer
    channel_ok = test_channel_layer()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 安装结果:")
    
    if not missing_required and redis_ok and channel_ok:
        print("🎉 所有依赖都已正确安装和配置！")
        print("\n✨ WebSocket 日志推送功能已启用")
        print("📝 您现在可以使用以下功能:")
        print("   - 实时日志推送到前端")
        print("   - WebSocket 连接: ws://localhost:8002/ws/logs/")
        print("   - 按模块过滤日志")
        print("   - 按级别过滤日志")
        
    else:
        print("⚠️  存在一些问题需要解决:")
        
        if missing_required:
            print(f"   - 缺少必需依赖: {', '.join(missing_required)}")
        
        if not redis_ok:
            print("   - Redis 连接失败")
            print("     请确保 Redis 服务正在运行")
            print("     检查 settings/base.py 中的 REDIS_URL 配置")
        
        if not channel_ok:
            print("   - Channel Layer 配置有问题")
            print("     请检查 CHANNEL_LAYERS 设置")
        
        print("\n🔧 建议:")
        print("   1. 安装缺失的依赖")
        print("   2. 启动 Redis 服务: redis-server")
        print("   3. 检查配置文件")
        print("   4. 重新运行此脚本进行验证")
    
    print("\n📚 更多信息:")
    print("   - 配置示例: backend/zhi_common/zhi_logger/channel_config_example.py")
    print("   - 文档: backend/zhi_common/zhi_logger/README.md")

if __name__ == '__main__':
    main()
