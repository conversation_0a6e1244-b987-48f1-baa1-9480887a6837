#!/usr/bin/env python
"""
ZhiCelery 异步任务服务启动脚本

使用方式:
python backend/zhi_scripts/start_celery.py [--worker] [--beat] [--monitor]
"""

import os
import sys
import argparse
import subprocess
import threading
import time
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))


def start_worker(settings_module='application.settings.base', **kwargs):
    """启动Celery Worker"""
    try:
        cmd = [
            'D:/MyPythonProjects/pycharm-projects/zhi_ai_admin/backend/.venv/Scripts/python.exe', 'manage.py', 'celery_worker',
            f'--settings={settings_module}',
            '--pool=solo',  # Windows兼容
            f'--loglevel={kwargs.get("loglevel", "info")}',
            f'--queues={kwargs.get("queues", "default,logger")}',
            f'--concurrency={kwargs.get("concurrency", 2)}'
        ]
        
        if kwargs.get('autoscale'):
            cmd.append(f'--autoscale={kwargs["autoscale"]}')
        
        print("🔄 启动Celery Worker...")
        print(f"📋 命令: {' '.join(cmd)}")
        subprocess.run(cmd, cwd=backend_path)
    except KeyboardInterrupt:
        print("🛑 Celery Worker已停止")
    except Exception as e:
        print(f"❌ Celery Worker启动失败: {e}")


def start_beat(settings_module='application.settings.base', **kwargs):
    """启动Celery Beat"""
    try:
        cmd = [
            'D:/MyPythonProjects/pycharm-projects/zhi_ai_admin/backend/.venv/Scripts/python.exe', 'manage.py', 'celery_beat',
            f'--settings={settings_module}',
            f'--loglevel={kwargs.get("loglevel", "info")}'
        ]
        
        print("⏰ 启动Celery Beat...")
        print(f"📋 命令: {' '.join(cmd)}")
        subprocess.run(cmd, cwd=backend_path)
    except KeyboardInterrupt:
        print("🛑 Celery Beat已停止")
    except Exception as e:
        print(f"❌ Celery Beat启动失败: {e}")


def start_flower(settings_module='application.settings.base', **kwargs):
    """启动Flower监控"""
    try:
        cmd = [
            'celery', '-A', 'zhi_celery.celery_app', 'flower',
            f'--port={kwargs.get("flower_port", 5555)}',
            f'--loglevel={kwargs.get("loglevel", "info")}'
        ]
        
        print("🌸 启动Flower监控...")
        print(f"📋 命令: {' '.join(cmd)}")
        subprocess.run(cmd, cwd=backend_path, env={
            **os.environ,
            'DJANGO_SETTINGS_MODULE': settings_module
        })
    except KeyboardInterrupt:
        print("🛑 Flower监控已停止")
    except Exception as e:
        print(f"❌ Flower启动失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='启动 ZhiCelery 异步任务服务')
    
    # 服务选择
    parser.add_argument('--worker', action='store_true', help='启动Worker')
    parser.add_argument('--beat', action='store_true', help='启动Beat调度器')
    parser.add_argument('--flower', action='store_true', help='启动Flower监控')
    parser.add_argument('--all', action='store_true', help='启动所有服务')
    
    # 配置选项
    parser.add_argument('--settings', default='application.settings.celery_enabled',
                       help='Django设置模块 (默认: application.settings.celery_enabled)')
    parser.add_argument('--loglevel', default='info', help='日志级别 (默认: info)')
    parser.add_argument('--concurrency', type=int, default=2, help='Worker并发数 (默认: 2)')
    parser.add_argument('--queues', default='default,logger', 
                       help='队列名称，用逗号分隔 (默认: default,logger)')
    parser.add_argument('--autoscale', help='自动扩缩容 (格式: max,min 例如: 10,3)')
    parser.add_argument('--flower-port', type=int, default=5555, 
                       help='Flower端口 (默认: 5555)')
    
    args = parser.parse_args()
    
    # 切换到backend目录作为工作目录
    os.chdir(backend_path)
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', args.settings)
    
    # 如果没有指定任何服务，默认启动Worker
    if not any([args.worker, args.beat, args.flower, args.all]):
        args.worker = True
    
    kwargs = {
        'loglevel': args.loglevel,
        'concurrency': args.concurrency,
        'queues': args.queues,
        'autoscale': args.autoscale,
        'flower_port': args.flower_port
    }
    
    print("=" * 50)
    print("🚀 ZhiCelery 异步任务服务启动器")
    print("=" * 50)
    print(f"🔧 设置模块: {args.settings}")
    print(f"📊 日志级别: {args.loglevel}")
    
    if args.all:
        print("🎯 启动所有服务...")
        
        # 启动Worker
        worker_thread = threading.Thread(
            target=start_worker, 
            args=(args.settings,), 
            kwargs=kwargs,
            daemon=True
        )
        
        # 启动Beat
        beat_thread = threading.Thread(
            target=start_beat, 
            args=(args.settings,), 
            kwargs=kwargs,
            daemon=True
        )
        
        # 启动Flower
        flower_thread = threading.Thread(
            target=start_flower, 
            args=(args.settings,), 
            kwargs=kwargs,
            daemon=True
        )
        
        worker_thread.start()
        time.sleep(2)  # 等待Worker启动
        beat_thread.start()
        time.sleep(1)  # 等待Beat启动
        flower_thread.start()
        
        try:
            print("✅ 所有服务已启动，按Ctrl+C停止")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("🛑 正在停止所有服务...")
    
    else:
        # 启动指定的服务
        if args.worker:
            start_worker(args.settings, **kwargs)
        elif args.beat:
            start_beat(args.settings, **kwargs)
        elif args.flower:
            start_flower(args.settings, **kwargs)


if __name__ == '__main__':
    main()
