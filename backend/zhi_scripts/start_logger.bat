@echo off
REM ZhiLogger 子项目快速启动脚本 (Windows)
REM 使用方式:
REM   backend\zhi_scripts\start_logger.bat [port] [options]
REM   backend\zhi_scripts\start_logger.bat 8002 --with-celery
REM   backend\zhi_scripts\start_logger.bat --celery-only

setlocal enabledelayedexpansion

set PORT=%1
set OPTIONS=%2 %3 %4 %5 %6

REM 如果第一个参数是选项，则使用默认端口
if "%PORT:~0,2%"=="--" (
    set OPTIONS=%PORT% %OPTIONS%
    set PORT=8002
)

if "%PORT%"=="" set PORT=8002

echo.
echo ========================================
echo 🚀 ZhiLogger 子项目启动器
echo ========================================
echo 📍 端口: %PORT%
echo 🔧 配置: application.settings.zhi_logger
echo 🎯 选项: %OPTIONS%
echo.

REM 检查是否仅启动Celery
echo %OPTIONS% | findstr /C:"--celery-only" >nul
if !errorlevel! equ 0 (
    echo 🔄 仅启动Celery服务...
    python backend\zhi_scripts\start_logger.py --celery-only
    goto :end
)

echo %OPTIONS% | findstr /C:"--worker-only" >nul
if !errorlevel! equ 0 (
    echo 🔄 仅启动Celery Worker...
    python backend\zhi_scripts\start_logger.py --worker-only
    goto :end
)

echo %OPTIONS% | findstr /C:"--beat-only" >nul
if !errorlevel! equ 0 (
    echo ⏰ 仅启动Celery Beat...
    python backend\zhi_scripts\start_logger.py --beat-only
    goto :end
)

REM 启动完整服务
python backend\zhi_scripts\start_logger.py --port %PORT% %OPTIONS%

:end
echo.
echo 🛑 服务已停止
pause
