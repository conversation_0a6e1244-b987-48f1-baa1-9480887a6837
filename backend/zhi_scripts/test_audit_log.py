#!/usr/bin/env python
"""
审计日志功能测试脚本

测试 AuditLog 模型和相关功能

使用方式:
python backend/zhi_scripts/test_audit_log.py
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_logger')
    import django
    django.setup()

async def test_audit_log_creation():
    """测试审计日志创建"""
    print("🧪 测试审计日志创建...")
    
    try:
        from zhi_logger.models import AuditLog
        
        # 测试同步创建
        audit_log = AuditLog.objects.create_audit_log(
            resource_id="test-resource-001",
            action="创建",
            old_values=None,
            new_values={"name": "测试资源", "status": "active"},
            creator_id="test-user-001",
            creator_name="测试用户"
        )
        
        print(f"   ✅ 同步创建成功: {audit_log.id}")
        
        # 测试异步创建
        async_audit_log = await AuditLog.async_create_audit_log(
            resource_id="test-resource-002",
            action="修改",
            old_values={"name": "旧名称", "status": "inactive"},
            new_values={"name": "新名称", "status": "active"},
            creator_id="test-user-002",
            creator_name="测试用户2"
        )
        
        print(f"   ✅ 异步创建成功: {async_audit_log.id}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 创建失败: {str(e)}")
        return False

def test_audit_log_queries():
    """测试审计日志查询"""
    print("🧪 测试审计日志查询...")
    
    try:
        from zhi_logger.models import AuditLog
        
        # 测试基本查询
        total_count = AuditLog.objects.count()
        print(f"   📊 总记录数: {total_count}")
        
        # 测试资源历史查询
        resource_history = AuditLog.objects.get_resource_history("test-resource-001")
        print(f"   📊 test-resource-001 历史记录: {resource_history.count()}")
        
        # 测试用户操作查询
        user_actions = AuditLog.objects.get_user_actions("test-user-001")
        print(f"   📊 test-user-001 操作记录: {user_actions.count()}")
        
        # 测试按操作类型查询
        create_actions = AuditLog.objects.get_actions_by_type("创建")
        print(f"   📊 创建操作记录: {create_actions.count()}")
        
        # 测试模型方法
        if total_count > 0:
            latest_log = AuditLog.objects.first()
            print(f"   📝 最新记录摘要: {latest_log.get_changes_summary()}")
            print(f"   🔒 是否敏感操作: {latest_log.is_sensitive_change()}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 查询失败: {str(e)}")
        return False

async def test_async_record_workers():
    """测试异步记录工具"""
    print("🧪 测试异步记录工具...")
    
    try:
        from zhi_common.zhi_records.async_record_changes_workers import (
            async_record_changes, async_record_log_action
        )
        
        # 测试模型变更记录
        model_info = {
            'model_info': {
                'app_label': 'test_app',
                'object_name': 'TestModel'
            }
        }
        
        user_info = {
            'creator_id': 'test-user-003',
            'creator_name': '测试用户3',
            'ip_address': '127.0.0.1',
            'user_agent': 'Test Agent'
        }
        
        previous_instance = {"id": "test-003", "name": "旧名称", "status": "inactive"}
        current_instance = {"id": "test-003", "name": "新名称", "status": "active"}
        
        await async_record_changes(
            model_info=model_info,
            user_info=user_info,
            previous_instance=previous_instance,
            current_instance=current_instance
        )
        
        print("   ✅ 模型变更记录成功")
        
        # 测试操作日志记录
        await async_record_log_action(
            action="登录",
            user_info=user_info,
            resource_id="test-user-003",
            extra_data={"login_time": "2025-07-22 18:00:00", "ip": "127.0.0.1"}
        )
        
        print("   ✅ 操作日志记录成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 异步记录失败: {str(e)}")
        return False

def test_audit_config():
    """测试审计配置"""
    print("🧪 测试审计配置...")
    
    try:
        from zhi_common.zhi_model.audit_config_manager import audit_config_manager
        
        # 测试配置加载
        all_configs = audit_config_manager.get_all_configs()
        print(f"   📊 已加载配置数量: {len(all_configs)}")
        
        # 测试模型配置查询
        logger_config = audit_config_manager.get_model_config('zhi_logger.SystemLog')
        print(f"   📊 SystemLog 配置: {'已启用' if logger_config.get('is_enabled') else '未启用'}")
        
        # 测试字段配置查询
        using_fields = audit_config_manager.get_model_using_fields('zhi_logger.SystemLog')
        print(f"   📊 SystemLog 字段配置数量: {len(using_fields)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置测试失败: {str(e)}")
        return False

def test_core_model_integration():
    """测试核心模型集成"""
    print("🧪 测试核心模型集成...")
    
    try:
        from zhi_logger.models import SystemLog
        
        # 创建一个测试日志
        test_log = SystemLog(
            level="INFO",
            message="测试审计日志集成",
            category="test",
            module_name="test_module",
            user_id="test-user-004"
        )
        
        # 保存（应该触发审计日志）
        test_log.save()
        print(f"   ✅ 测试日志创建成功: {test_log.id}")
        
        # 修改日志
        test_log.message = "修改后的测试消息"
        test_log.save()
        print("   ✅ 测试日志修改成功")
        
        # 软删除
        test_log.delete()
        print("   ✅ 测试日志软删除成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 核心模型集成测试失败: {str(e)}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print("🧹 清理测试数据...")
    
    try:
        from zhi_logger.models import AuditLog, SystemLog
        
        # 删除测试审计日志
        test_audit_logs = AuditLog.objects.filter(
            resource_id__startswith="test-"
        )
        deleted_audit_count = test_audit_logs.count()
        test_audit_logs.delete()
        
        # 删除测试系统日志
        test_system_logs = SystemLog.objects.filter(
            category="test"
        )
        deleted_system_count = test_system_logs.count()
        test_system_logs.delete()
        
        print(f"   ✅ 清理完成: 审计日志 {deleted_audit_count} 条, 系统日志 {deleted_system_count} 条")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 清理失败: {str(e)}")
        return False

async def main():
    """主函数"""
    print("🔍 AuditLog 功能测试")
    print("=" * 50)
    
    setup_django()
    
    # 运行测试
    tests = [
        ("审计日志创建", test_audit_log_creation),
        ("审计日志查询", test_audit_log_queries),
        ("异步记录工具", test_async_record_workers),
        ("审计配置", test_audit_config),
        ("核心模型集成", test_core_model_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 清理测试数据
    print(f"\n🧹 清理测试数据")
    print("-" * 30)
    cleanup_test_data()
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name:<20}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！AuditLog 功能正常。")
    else:
        print("⚠️  部分测试失败，请检查相关配置和代码。")

if __name__ == '__main__':
    asyncio.run(main())
