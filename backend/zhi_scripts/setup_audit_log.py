#!/usr/bin/env python
"""
审计日志快速设置脚本

一键设置审计日志功能，包括：
- 创建数据库迁移
- 执行迁移
- 测试功能
- 加载配置

使用方式:
python backend/zhi_scripts/setup_audit_log.py
"""

import os
import sys
import subprocess
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def run_command(cmd, description, cwd=None):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd=cwd or backend_path
        )
        
        if result.returncode == 0:
            print(f"   ✅ {description}成功")
            if result.stdout.strip():
                print(f"   📝 输出: {result.stdout.strip()}")
            return True
        else:
            print(f"   ❌ {description}失败")
            if result.stderr.strip():
                print(f"   ❌ 错误: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"   ❌ {description}异常: {str(e)}")
        return False

def setup_django_environment():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_logger')

def create_migrations():
    """创建迁移文件"""
    return run_command([
        sys.executable, 'manage.py', 'makemigrations', 'zhi_logger',
        '--settings', 'application.settings.zhi_logger'
    ], "创建迁移文件")

def apply_migrations():
    """执行迁移"""
    return run_command([
        sys.executable, 'manage.py', 'migrate', 'zhi_logger',
        '--settings', 'application.settings.zhi_logger'
    ], "执行数据库迁移")

def test_audit_config():
    """测试审计配置"""
    audit_config_script = backend_path / 'zhi_scripts' / 'manage_audit_config.py'
    return run_command([
        sys.executable, str(audit_config_script), 'validate'
    ], "验证审计配置")

def test_audit_functionality():
    """测试审计功能"""
    test_script = backend_path / 'zhi_scripts' / 'test_audit_log.py'
    return run_command([
        sys.executable, str(test_script)
    ], "测试审计日志功能")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_modules = [
        'django',
        'ninja',
        'ninja_extra',
        'asgiref',
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} - 未安装")
            missing.append(module)
    
    if missing:
        print(f"   ⚠️  缺少依赖: {', '.join(missing)}")
        print("   请运行: pip install -r backend/requirements.txt")
        return False
    
    return True

def check_file_structure():
    """检查文件结构"""
    print("📁 检查文件结构...")
    
    required_files = [
        'zhi_logger/models.py',
        'zhi_logger/api.py',
        'zhi_logger/api/audit_log_api.py',
        'zhi_logger/schemas/audit_log_schemas.py',
        'zhi_common/zhi_records/async_record_changes_workers.py',
        'zhi_common/zhi_model/audit_config_manager.py',
        'zhi_common/zhi_model/core_model.py',
    ]
    
    missing = []
    for file_path in required_files:
        full_path = backend_path / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - 文件不存在")
            missing.append(file_path)
    
    if missing:
        print(f"   ⚠️  缺少文件: {len(missing)} 个")
        return False
    
    return True

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例:")
    print("=" * 40)
    
    examples = [
        ("查看审计配置", "python backend/zhi_scripts/manage_audit_config.py list"),
        ("测试审计功能", "python backend/zhi_scripts/test_audit_log.py"),
        ("启动日志服务", "python backend/zhi_scripts/start_logger.py --port 8002"),
        ("查看审计日志API", "http://localhost:8002/logger/docs"),
        ("获取审计日志", "GET http://localhost:8002/api/logger/audit-logs/"),
        ("获取统计信息", "GET http://localhost:8002/api/logger/audit-logs/stats"),
    ]
    
    for desc, cmd in examples:
        print(f"   {desc:<15}: {cmd}")

def main():
    """主函数"""
    print("🚀 ZhiAdmin 审计日志快速设置")
    print("=" * 50)
    
    setup_django_environment()
    
    # 检查步骤
    steps = [
        ("检查依赖", check_dependencies),
        ("检查文件结构", check_file_structure),
        ("创建迁移文件", create_migrations),
        ("执行数据库迁移", apply_migrations),
        ("验证审计配置", test_audit_config),
    ]
    
    results = []
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}")
        print("-" * 30)
        
        try:
            result = step_func()
            results.append((step_name, result))
            
            if not result:
                print(f"   ⚠️  {step_name}失败，可能影响后续步骤")
                
                # 询问是否继续
                try:
                    response = input("   是否继续下一步? (y/N): ").lower().strip()
                    if response not in ['y', 'yes']:
                        print("   ⏹️  用户选择停止")
                        break
                except KeyboardInterrupt:
                    print("\n   ⏹️  用户取消操作")
                    break
                    
        except Exception as e:
            print(f"   ❌ {step_name}异常: {str(e)}")
            results.append((step_name, False))
    
    # 显示结果
    print("\n" + "=" * 50)
    print("📊 设置结果:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for step_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {step_name:<20}: {status}")
    
    print(f"\n📈 成功率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 审计日志设置完成！")
        
        # 询问是否运行功能测试
        try:
            response = input("\n是否运行功能测试? (y/N): ").lower().strip()
            if response in ['y', 'yes']:
                print("\n🧪 运行功能测试")
                print("-" * 30)
                test_audit_functionality()
        except KeyboardInterrupt:
            print("\n⏹️  跳过功能测试")
        
        show_usage_examples()
        
        print("\n✨ 审计日志功能已就绪！")
        print("   现在您可以:")
        print("   1. 启动日志服务查看API文档")
        print("   2. 在模型中使用审计功能")
        print("   3. 通过API查询审计日志")
        
    else:
        print("\n⚠️  设置过程中遇到问题，请检查:")
        print("   1. 数据库连接是否正常")
        print("   2. 依赖包是否完整安装")
        print("   3. 文件权限是否正确")
        print("   4. Django配置是否正确")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  用户取消设置")
    except Exception as e:
        print(f"\n❌ 设置失败: {str(e)}")
