#!/usr/bin/env python
"""
ZhiAdmin 完整后台启动脚本

使用方式:
python backend/zhi_scripts/start_admin.py [--port 8000] [--debug]
"""

import os
import sys
import argparse
from pathlib import Path

# 添加backend目录到 Python 路径
backend_path = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_path))

def main():
    parser = argparse.ArgumentParser(description='启动 ZhiAdmin 完整后台')
    parser.add_argument('--port', type=int, default=8000, help='服务端口 (默认: 8000)')
    parser.add_argument('--host', default='0.0.0.0', help='服务主机 (默认: 0.0.0.0)')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--reload', action='store_true', help='启用自动重载')
    
    args = parser.parse_args()
    
    # 切换到backend目录作为工作目录
    os.chdir(backend_path)
    
    # 设置环境变量
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
    os.environ.setdefault('DJANGO_ALLOW_ASYNC_UNSAFE', 'true')
    
    if args.debug:
        os.environ['DEBUG'] = 'True'
    
    try:
        from django.core.management import execute_from_command_line
        
        # 构建启动命令
        cmd_args = [
            'manage.py',
            'runserver',
            f'{args.host}:{args.port}'
        ]
        
        if not args.reload:
            cmd_args.append('--noreload')
        
        print(f"🚀 启动 ZhiAdmin 完整后台...")
        print(f"📍 地址: http://{args.host}:{args.port}")
        print(f"🔧 配置: application.settings.interval_admin")
        print(f"📊 API文档: http://{args.host}:{args.port}/docs")
        print(f"🌐 管理后台: http://{args.host}:{args.port}/admin/")
        print(f"🔑 API接口: http://{args.host}:{args.port}/api/")
        print("-" * 50)
        
        execute_from_command_line(cmd_args)
        
    except ImportError as exc:
        raise ImportError(
            "无法导入 Django。请确保已安装 Django 并且在 PYTHONPATH 环境变量中可用。"
            "是否忘记激活虚拟环境？"
        ) from exc

if __name__ == '__main__':
    main()
