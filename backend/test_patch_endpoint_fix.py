"""
测试修复后的PATCH端点功能

验证PATCH端点是否正确使用可选Schema和文档
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from django.db import models
from zhi_common.zhi_model.core_model import ZhiCoreModel
from zhi_common.zhi_api.base_config import auto_crud_api
from zhi_common.zhi_api.zhi_crud import BaseModelService
import inspect


# 创建一个测试模型
class TestPatchModel(ZhiCoreModel):
    """测试PATCH端点的模型"""
    name = models.CharField(max_length=100, verbose_name="名称")
    description = models.TextField(blank=True, verbose_name="描述")
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, verbose_name="价格")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    class Meta:
        app_label = 'test'
        db_table = 'test_patch_model'


def test_patch_endpoint_schema():
    """测试PATCH端点是否使用正确的Schema"""
    print("🧪 测试PATCH端点Schema...")
    
    try:
        @auto_crud_api(
            TestPatchModel,
            prefix="test_patch",
            tags=["测试PATCH"],
            exclude_endpoints=['list_pagination', 'list_id_mappings']
        )
        class TestPatchService(BaseModelService):
            model = TestPatchModel
        
        print("   ✅ 自动CRUD装饰器应用成功")
        
        # 检查是否有patch方法
        if hasattr(TestPatchService, 'patch'):
            print("   ✅ 控制器有patch方法")
            
            # 检查patch方法的签名
            patch_method = getattr(TestPatchService, 'patch')
            signature = inspect.signature(patch_method)
            params = list(signature.parameters.keys())
            
            print(f"   📋 patch方法参数: {params}")
            
            # 检查data参数的类型注解
            if 'data' in signature.parameters:
                data_param = signature.parameters['data']
                data_annotation = data_param.annotation
                
                print(f"   📋 data参数类型: {data_annotation}")
                
                # 检查是否是我们构建的可选Schema
                if hasattr(data_annotation, '__name__'):
                    schema_name = data_annotation.__name__
                    if 'UpdateOptionalSchema' in schema_name:
                        print("   ✅ 使用了可选更新Schema")
                        
                        # 检查Schema的字段
                        if hasattr(data_annotation, '__annotations__'):
                            schema_fields = data_annotation.__annotations__
                            print(f"   📋 Schema字段: {list(schema_fields.keys())}")
                            
                            # 验证字段都是可选的
                            all_optional = True
                            for field_name, field_type in schema_fields.items():
                                field_type_str = str(field_type)
                                if 'Optional[' not in field_type_str and 'Union[' not in field_type_str:
                                    print(f"   ❌ 字段 {field_name} 不是可选的: {field_type}")
                                    all_optional = False
                                else:
                                    print(f"   ✅ 字段 {field_name} 是可选的: {field_type}")
                            
                            if all_optional:
                                print("   ✅ 所有字段都是可选的")
                                return True
                            else:
                                print("   ❌ 部分字段不是可选的")
                                return False
                        else:
                            print("   ❌ Schema没有字段注解")
                            return False
                    else:
                        print(f"   ❌ 没有使用可选更新Schema，使用的是: {schema_name}")
                        return False
                else:
                    print(f"   ❌ data参数类型注解异常: {data_annotation}")
                    return False
            else:
                print("   ❌ patch方法没有data参数")
                return False
        else:
            print("   ❌ 控制器没有patch方法")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_patch_vs_update_schema():
    """测试PATCH和UPDATE端点使用不同的Schema"""
    print("\n🧪 测试PATCH和UPDATE端点Schema差异...")
    
    try:
        @auto_crud_api(
            TestPatchModel,
            prefix="test_patch_update",
            tags=["测试PATCH UPDATE"],
            exclude_endpoints=['list_pagination', 'list_id_mappings']
        )
        class TestPatchUpdateService(BaseModelService):
            model = TestPatchModel
        
        # 检查update方法的Schema
        update_schema = None
        patch_schema = None
        
        if hasattr(TestPatchUpdateService, 'update'):
            update_method = getattr(TestPatchUpdateService, 'update')
            update_signature = inspect.signature(update_method)
            if 'data' in update_signature.parameters:
                update_schema = update_signature.parameters['data'].annotation
                print(f"   📋 UPDATE Schema: {update_schema}")
        
        if hasattr(TestPatchUpdateService, 'patch'):
            patch_method = getattr(TestPatchUpdateService, 'patch')
            patch_signature = inspect.signature(patch_method)
            if 'data' in patch_signature.parameters:
                patch_schema = patch_signature.parameters['data'].annotation
                print(f"   📋 PATCH Schema: {patch_schema}")
        
        # 比较两个Schema
        if update_schema and patch_schema:
            if update_schema != patch_schema:
                print("   ✅ UPDATE和PATCH使用不同的Schema")
                
                # 检查PATCH Schema是否是可选的
                if hasattr(patch_schema, '__name__') and 'UpdateOptionalSchema' in patch_schema.__name__:
                    print("   ✅ PATCH使用可选更新Schema")
                    return True
                else:
                    print("   ❌ PATCH没有使用可选更新Schema")
                    return False
            else:
                print("   ❌ UPDATE和PATCH使用相同的Schema")
                return False
        else:
            print("   ❌ 无法获取Schema信息")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_service_methods():
    """测试服务类是否有必要的方法"""
    print("\n🧪 测试服务类方法...")
    
    try:
        service = BaseModelService(model_class=TestPatchModel)
        
        required_methods = [
            'patch',
            'patch_instance', 
            'update_instance',
            '_should_update_field'
        ]
        
        methods_found = 0
        for method_name in required_methods:
            if hasattr(service, method_name):
                print(f"   ✅ 服务有 {method_name} 方法")
                methods_found += 1
            else:
                print(f"   ❌ 服务没有 {method_name} 方法")
        
        return methods_found == len(required_methods)
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试修复后的PATCH端点功能...")
    print("🎯 目标：验证PATCH端点使用正确的可选Schema和文档")
    
    tests = [
        test_patch_endpoint_schema,
        test_patch_vs_update_schema,
        test_service_methods,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！PATCH端点修复成功。")
        print("\n✅ 修复的问题:")
        print("   - ✅ PATCH端点现在使用可选更新Schema")
        print("   - ✅ 所有字段都是Optional类型")
        print("   - ✅ 自动过滤None值实现'有值则更新'")
        print("   - ✅ UPDATE和PATCH使用不同的Schema")
        print("   - ✅ 服务类有完整的方法支持")
        print("\n💡 API端点对比:")
        print("   PUT /api/resource/{id}  - 全量更新（必填字段Schema）")
        print("   PATCH /api/resource/{id} - 部分更新（可选字段Schema）")
        print("\n🔧 实现细节:")
        print("   - PATCH端点自动构建可选Schema")
        print("   - 自动过滤None值，只更新有值的字段")
        print("   - 智能字段验证，根据字段属性判断是否更新")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
