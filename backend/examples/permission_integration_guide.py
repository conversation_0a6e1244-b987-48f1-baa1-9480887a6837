"""
权限系统集成指南和使用示例
"""

from typing import Dict, List, Optional, Any
from datetime import datetime

from django.db import models
from django.contrib.auth import get_user_model
from ninja import Schema
from ninja_extra import http_get, http_post, http_put, http_delete

from zhi_common.zhi_model.core_model import ZhiCoreModel
from zhi_common.zhi_services.enhanced_expose import enhanced_auto_crud_api, APIVersion
from zhi_common.zhi_services.base_model_service import BaseModelService
from zhi_common.zhi_auth.unified_permission_manager import (
    unified_permission_manager, 
    require_permission, 
    filter_by_permission
)

User = get_user_model()


# ==================== 1. 模型定义示例 ====================

class Document(ZhiCoreModel):
    """文档模型 - 演示权限控制"""
    title = models.CharField(max_length=200, verbose_name="标题")
    content = models.TextField(verbose_name="内容")
    category = models.CharField(max_length=50, verbose_name="分类")
    is_confidential = models.BooleanField(default=False, verbose_name="是否机密")
    department = models.CharField(max_length=100, verbose_name="所属部门")
    
    class Meta:
        db_table = 'document'
        verbose_name = "文档"
        verbose_name_plural = "文档"


# ==================== 2. Schema定义 ====================

class DocumentCreateSchema(Schema):
    """文档创建Schema"""
    title: str
    content: str
    category: str
    is_confidential: bool = False
    department: str


class DocumentResponseSchema(Schema):
    """文档响应Schema - 支持字段级权限"""
    id: str
    title: str
    content: Optional[str] = None  # 根据权限动态显示
    category: str
    is_confidential: Optional[bool] = None  # 敏感字段
    department: str
    created_at: datetime
    creator_name: Optional[str] = None


# ==================== 3. 服务层集成 ====================

class DocumentService(BaseModelService):
    """文档服务 - 集成权限管理"""
    
    def __init__(self, model_class, **kwargs):
        super().__init__(model_class, **kwargs)
        self.permission_manager = unified_permission_manager
    
    def list(self, user: User, filters: Dict = None, **kwargs):
        """列表查询 - 自动应用数据权限过滤"""
        queryset = self.model.objects.filter(is_deleted=False)
        
        # 应用基础过滤条件
        if filters:
            queryset = queryset.filter(**filters)
        
        # 应用数据权限过滤
        queryset = self.permission_manager.filter_queryset(
            user, queryset, action='view', dimension='data'
        )
        
        # 序列化时应用字段权限
        results = []
        for obj in queryset:
            serialized = self.serialize_with_field_permissions(obj, user)
            results.append(serialized)
        
        return self._handle_response(data=results, message="ok")
    
    def retrieve(self, user: User, obj_id: str, **kwargs):
        """详情查询 - 检查对象级权限"""
        try:
            # 获取对象
            obj = self.model.objects.get(id=obj_id, is_deleted=False)
            
            # 检查查看权限
            if not self.permission_manager.check_permission(
                user, 'document.view', resource=obj,
                context={'action': 'retrieve', 'object_id': obj_id}
            ):
                return self._handle_response(
                    data=None, code=403, message="权限不足", success=False
                )
            
            # 序列化时应用字段权限
            data = self.serialize_with_field_permissions(obj, user)
            
            return self._handle_response(data=data, message="ok")
            
        except self.model.DoesNotExist:
            return self._handle_response(
                data=None, code=404, message="文档不存在", success=False
            )
    
    def create(self, user: User, data: Dict, **kwargs):
        """创建 - 检查创建权限"""
        # 检查创建权限
        if not self.permission_manager.check_permission(
            user, 'document.create',
            context={'action': 'create', 'data': data}
        ):
            return self._handle_response(
                data=None, code=403, message="无创建权限", success=False
            )
        
        # 检查机密文档创建权限
        if data.get('is_confidential') and not self.permission_manager.check_permission(
            user, 'document.create_confidential'
        ):
            return self._handle_response(
                data=None, code=403, message="无机密文档创建权限", success=False
            )
        
        return super().create(data, **kwargs)
    
    def update(self, user: User, obj_id: str, data: Dict, **kwargs):
        """更新 - 检查更新权限"""
        try:
            obj = self.model.objects.get(id=obj_id, is_deleted=False)
            
            # 检查基础更新权限
            if not self.permission_manager.check_permission(
                user, 'document.update', resource=obj,
                context={'action': 'update', 'object_id': obj_id}
            ):
                return self._handle_response(
                    data=None, code=403, message="无更新权限", success=False
                )
            
            # 检查字段级权限
            field_permissions = self.permission_manager.get_permitted_fields(user, self.model)
            
            # 过滤不允许修改的字段
            filtered_data = {}
            for field, value in data.items():
                if field in field_permissions or field in ['title', 'content']:  # 基础字段
                    filtered_data[field] = value
                else:
                    # 记录权限拒绝
                    self.permission_manager.audit_logger.log_permission_check(
                        user, f'document.field.{field}', False,
                        context={'action': 'field_update', 'field': field}
                    )
            
            return super().update(obj_id, filtered_data, **kwargs)
            
        except self.model.DoesNotExist:
            return self._handle_response(
                data=None, code=404, message="文档不存在", success=False
            )
    
    def delete(self, user: User, obj_id: str, **kwargs):
        """删除 - 检查删除权限"""
        try:
            obj = self.model.objects.get(id=obj_id, is_deleted=False)
            
            # 检查删除权限
            if not self.permission_manager.check_permission(
                user, 'document.delete', resource=obj,
                context={'action': 'delete', 'object_id': obj_id}
            ):
                return self._handle_response(
                    data=None, code=403, message="无删除权限", success=False
                )
            
            # 机密文档需要特殊权限
            if obj.is_confidential and not self.permission_manager.check_permission(
                user, 'document.delete_confidential', resource=obj
            ):
                return self._handle_response(
                    data=None, code=403, message="无机密文档删除权限", success=False
                )
            
            return super().delete(obj_id, **kwargs)
            
        except self.model.DoesNotExist:
            return self._handle_response(
                data=None, code=404, message="文档不存在", success=False
            )
    
    def serialize_with_field_permissions(self, obj, user: User) -> Dict:
        """根据字段权限序列化对象"""
        # 获取用户有权限的字段
        permitted_fields = self.permission_manager.get_permitted_fields(user, self.model)
        
        # 基础字段（所有用户都能看到）
        base_fields = {'id', 'title', 'category', 'created_at'}
        
        data = {
            'id': str(obj.id),
            'title': obj.title,
            'category': obj.category,
            'created_at': obj.created_at,
        }
        
        # 根据权限添加字段
        if 'content' in permitted_fields or user.is_superuser:
            data['content'] = obj.content
        
        if 'is_confidential' in permitted_fields or user.is_superuser:
            data['is_confidential'] = obj.is_confidential
        
        if 'department' in permitted_fields or user.is_superuser:
            data['department'] = obj.department
        
        if 'creator_name' in permitted_fields or user.is_superuser:
            data['creator_name'] = getattr(obj.creator, 'username', None)
        
        return data
    
    def get_user_accessible_categories(self, user: User) -> List[str]:
        """获取用户可访问的文档分类"""
        # 应用数据权限过滤
        queryset = self.permission_manager.filter_queryset(
            user, self.model.objects.filter(is_deleted=False), 
            action='view', dimension='data'
        )
        
        # 获取不重复的分类列表
        categories = queryset.values_list('category', flat=True).distinct()
        return list(categories)


# ==================== 4. API控制器集成 ====================

@enhanced_auto_crud_api(
    model_class=Document,
    prefix="documents",
    tags=["文档管理"],
    schema_in=DocumentCreateSchema,
    schema_out=DocumentResponseSchema,
    version=APIVersion.V2,
    pagination=True,
    exclude=[],  # 使用自定义权限控制，不排除默认端点
    audit_config={
        'enable': True,
        'sensitive_fields': ['is_confidential', 'content'],
        'async_logging': True
    }
)
class DocumentController(DocumentService):
    """文档控制器 - 集成权限管理"""
    
    @require_permission(
        'document.view',
        context_getter=lambda request, *args, **kwargs: {
            'action': 'list_categories',
            'filters': request.GET.dict()
        }
    )
    @http_get('/categories')
    def get_categories(self, request):
        """获取文档分类列表 - 需要查看权限"""
        categories = self.get_user_accessible_categories(request.user)
        return {
            'categories': categories,
            'count': len(categories)
        }
    
    @require_permission(
        'document.view_statistics',
        context_getter=lambda request, *args, **kwargs: {
            'action': 'statistics',
            'department': request.GET.get('department')
        }
    )
    @http_get('/statistics')
    def get_statistics(self, request):
        """获取文档统计信息 - 需要统计权限"""
        user = request.user
        
        # 应用数据权限过滤
        queryset = unified_permission_manager.filter_queryset(
            user, self.model.objects.filter(is_deleted=False),
            action='view', dimension='data'
        )
        
        # 统计信息
        total_count = queryset.count()
        confidential_count = queryset.filter(is_confidential=True).count()
        category_stats = list(
            queryset.values('category')
            .annotate(count=models.Count('id'))
            .order_by('-count')
        )
        
        return {
            'total_documents': total_count,
            'confidential_documents': confidential_count,
            'category_distribution': category_stats,
            'user_permissions': unified_permission_manager.get_user_permissions(
                user, permission_type='document'
            )
        }
    
    @require_permission(
        'document.batch_update',
        context_getter=lambda request, *args, **kwargs: {
            'action': 'batch_update',
            'document_ids': request.data.get('document_ids', [])
        }
    )
    @http_post('/batch-update')
    def batch_update(self, request):
        """批量更新文档 - 需要批量更新权限"""
        document_ids = request.data.get('document_ids', [])
        update_data = request.data.get('update_data', {})
        
        results = {'success': [], 'failed': []}
        
        for doc_id in document_ids:
            try:
                result = self.update(request.user, doc_id, update_data)
                if result.get('success'):
                    results['success'].append(doc_id)
                else:
                    results['failed'].append({
                        'id': doc_id,
                        'error': result.get('message', '更新失败')
                    })
            except Exception as e:
                results['failed'].append({
                    'id': doc_id,
                    'error': str(e)
                })
        
        return {
            'message': f'批量更新完成，成功: {len(results["success"])}, 失败: {len(results["failed"])}',
            'results': results
        }
    
    @require_permission('document.export')
    @http_get('/export')
    def export_documents(self, request):
        """导出文档 - 需要导出权限"""
        user = request.user
        format_type = request.GET.get('format', 'csv')
        
        # 应用数据权限过滤
        queryset = unified_permission_manager.filter_queryset(
            user, self.model.objects.filter(is_deleted=False),
            action='export', dimension='data'
        )
        
        # 根据字段权限过滤导出字段
        permitted_fields = unified_permission_manager.get_permitted_fields(user, self.model)
        
        export_data = []
        for obj in queryset:
            row_data = {'ID': obj.id, '标题': obj.title, '分类': obj.category}
            
            if 'content' in permitted_fields:
                row_data['内容'] = obj.content
            if 'is_confidential' in permitted_fields:
                row_data['机密级别'] = '机密' if obj.is_confidential else '普通'
            if 'department' in permitted_fields:
                row_data['部门'] = obj.department
            
            export_data.append(row_data)
        
        return {
            'format': format_type,
            'data': export_data,
            'count': len(export_data),
            'exported_at': datetime.now().isoformat(),
            'exported_by': user.username
        }


# ==================== 5. 权限策略配置示例 ====================

def setup_document_permissions():
    """设置文档权限策略"""
    from zhi_common.zhi_auth.dynamic_permission_engine import (
        dynamic_permission_engine, PermissionPolicy, TimeBasedCondition,
        AttributeBasedCondition, PolicyOperator
    )
    
    # 工作时间访问机密文档策略
    confidential_access_policy = PermissionPolicy(
        name="confidential_document_work_hours",
        description="机密文档只能在工作时间访问",
        conditions=[
            TimeBasedCondition(
                start_time="09:00",
                end_time="18:00",
                weekdays=[0, 1, 2, 3, 4]  # 周一到周五
            ),
            AttributeBasedCondition(
                attribute_path="resource.is_confidential",
                operator=PolicyOperator.EQUALS,
                value=True
            )
        ],
        effect="allow",
        priority=20
    )
    
    # 部门数据访问策略
    department_access_policy = PermissionPolicy(
        name="department_data_access",
        description="用户只能访问本部门的文档",
        conditions=[
            AttributeBasedCondition(
                attribute_path="user.department",
                operator=PolicyOperator.EQUALS,
                value="resource.department"  # 这里需要特殊处理
            )
        ],
        effect="allow",
        priority=10
    )
    
    dynamic_permission_engine.add_policy(confidential_access_policy)
    dynamic_permission_engine.add_policy(department_access_policy)


# ==================== 6. 中间件集成示例 ====================

class DocumentPermissionMiddleware:
    """文档权限中间件"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.permission_manager = unified_permission_manager
    
    def __call__(self, request):
        # 在请求处理前添加权限上下文
        if hasattr(request, 'user') and request.user.is_authenticated:
            # 预加载用户权限到请求上下文
            request.user_permissions = self.permission_manager.get_user_permissions(
                request.user
            )
            
            # 添加请求上下文信息
            request.permission_context = {
                'client_ip': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT'),
                'session_id': request.session.session_key,
                'timestamp': datetime.now(),
                'path': request.path,
                'method': request.method
            }
        
        response = self.get_response(request)
        return response


# ==================== 7. 管理命令示例 ====================

from django.core.management.base import BaseCommand

class Command(BaseCommand):
    """权限管理命令"""
    help = '权限系统管理工具'
    
    def add_arguments(self, parser):
        parser.add_argument('action', choices=[
            'warm_cache', 'clear_cache', 'audit_report', 'detect_anomalies'
        ])
        parser.add_argument('--user-id', type=int, help='用户ID')
        parser.add_argument('--days', type=int, default=7, help='天数')
    
    def handle(self, *args, **options):
        action = options['action']
        
        if action == 'warm_cache':
            result = unified_permission_manager.warm_up_cache()
            self.stdout.write(f"缓存预热完成: {result}")
        
        elif action == 'clear_cache':
            user_id = options.get('user_id')
            if user_id:
                result = unified_permission_manager.clear_user_cache(user_id)
                self.stdout.write(f"用户缓存清除: {result}")
            else:
                self.stdout.write("请指定用户ID")
        
        elif action == 'audit_report':
            days = options['days']
            stats = unified_permission_manager.get_permission_stats(days)
            self.stdout.write(f"权限统计报告 (最近{days}天):")
            self.stdout.write(f"总检查次数: {stats.get('total_checks', 0)}")
            self.stdout.write(f"允许次数: {stats.get('granted_count', 0)}")
            self.stdout.write(f"拒绝次数: {stats.get('denied_count', 0)}")
        
        elif action == 'detect_anomalies':
            anomalies = unified_permission_manager.detect_security_anomalies()
            self.stdout.write(f"检测到 {len(anomalies)} 个安全异常:")
            for anomaly in anomalies:
                self.stdout.write(f"- {anomaly['type']}: {anomaly['description']}")


# ==================== 8. 使用说明 ====================

"""
集成步骤:

1. 安装依赖:
   - 确保已安装所有权限管理模块
   - 配置缓存后端 (Redis推荐)

2. 配置settings.py:
   ```python
   # 启用权限管理功能
   PERMISSION_MANAGEMENT = {
       'ENABLE_CACHING': True,
       'ENABLE_DYNAMIC_POLICIES': True,
       'ENABLE_AUDITING': True,
       'ENABLE_MONITORING': True,
       'CACHE_TIMEOUT': 300,
   }
   
   # 添加中间件
   MIDDLEWARE += [
       'your_app.middleware.DocumentPermissionMiddleware',
   ]
   ```

3. 数据库迁移:
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

4. 初始化权限数据:
   ```python
   # 创建基础权限
   from zhi_oauth.models import Permission
   
   permissions = [
       {'code': 'document.view', 'name': '查看文档', 'permission_type': 'api'},
       {'code': 'document.create', 'name': '创建文档', 'permission_type': 'api'},
       {'code': 'document.update', 'name': '更新文档', 'permission_type': 'api'},
       {'code': 'document.delete', 'name': '删除文档', 'permission_type': 'api'},
       {'code': 'document.export', 'name': '导出文档', 'permission_type': 'api'},
   ]
   
   for perm_data in permissions:
       Permission.objects.get_or_create(**perm_data)
   ```

5. 预热缓存:
   ```bash
   python manage.py permission_management warm_cache
   ```

6. 监控和维护:
   - 定期检查权限统计
   - 监控安全异常
   - 清理过期缓存
"""
