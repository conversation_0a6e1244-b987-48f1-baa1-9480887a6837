"""
增强版API使用示例
"""

from typing import List, Optional
from datetime import datetime

from django.db import models
from ninja import Schema
from ninja_extra import http_get, http_post

from zhi_common.zhi_model.core_model import ZhiCoreModel
from zhi_common.zhi_services.enhanced_expose import (
    enhanced_auto_crud_api, 
    versioned_api_route, 
    APIVersion
)
from zhi_common.zhi_services.base_model_service import BaseModelService
from zhi_common.zhi_services.cache_manager import api_cache
from zhi_common.zhi_services.validation_manager import create_validation_manager


# ==================== 模型定义 ====================

class Product(ZhiCoreModel):
    """产品模型"""
    name = models.CharField(max_length=100, verbose_name="产品名称")
    description = models.TextField(blank=True, verbose_name="产品描述")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格")
    category = models.CharField(max_length=50, verbose_name="分类")
    stock = models.IntegerField(default=0, verbose_name="库存")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    
    class Meta:
        db_table = 'product'
        verbose_name = "产品"
        verbose_name_plural = "产品"


# ==================== Schema定义 ====================

class ProductCreateSchema(Schema):
    """产品创建Schema"""
    name: str
    description: Optional[str] = None
    price: float
    category: str
    stock: int = 0
    is_active: bool = True


class ProductUpdateSchema(Schema):
    """产品更新Schema"""
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[float] = None
    category: Optional[str] = None
    stock: Optional[int] = None
    is_active: Optional[bool] = None


class ProductResponseSchema(Schema):
    """产品响应Schema"""
    id: str
    name: str
    description: Optional[str]
    price: float
    category: str
    stock: int
    is_active: bool
    created_at: datetime
    updated_at: datetime


class ProductStatsSchema(Schema):
    """产品统计Schema"""
    total_products: int
    active_products: int
    total_value: float
    categories: List[dict]
    low_stock_products: int


# ==================== 服务层定义 ====================

class ProductService(BaseModelService):
    """产品服务"""
    
    def __init__(self, model_class, **kwargs):
        super().__init__(model_class, **kwargs)
        
        # 配置验证管理器
        validation_config = {
            'field_rules': {
                'name': [
                    {'type': 'required'},
                    {'type': 'length', 'params': {'min_length': 2, 'max_length': 100}}
                ],
                'price': [
                    {'type': 'required'},
                    {'type': 'range', 'params': {'min_value': 0.01}}
                ],
                'stock': [
                    {'type': 'non_negative_int'}
                ]
            },
            'converters': {
                'price': 'decimal',
                'stock': 'int',
                'is_active': 'bool'
            }
        }
        self.validation_manager = create_validation_manager(validation_config)
    
    @api_cache(
        cache_key="product_stats",
        backend="redis",
        ttl=600,  # 10分钟缓存
        vary_on=['category']
    )
    def get_product_stats(self, category: str = None) -> dict:
        """获取产品统计信息（带缓存）"""
        queryset = self.model.objects.filter(is_deleted=False)
        
        if category:
            queryset = queryset.filter(category=category)
        
        total_products = queryset.count()
        active_products = queryset.filter(is_active=True).count()
        
        # 计算总价值
        total_value = sum(
            product.price * product.stock 
            for product in queryset.select_related()
        )
        
        # 按分类统计
        categories = list(
            queryset.values('category')
            .annotate(count=models.Count('id'))
            .order_by('-count')
        )
        
        # 低库存产品
        low_stock_products = queryset.filter(stock__lt=10).count()
        
        return {
            'total_products': total_products,
            'active_products': active_products,
            'total_value': float(total_value),
            'categories': categories,
            'low_stock_products': low_stock_products
        }
    
    def bulk_update_stock(self, updates: List[dict]) -> dict:
        """批量更新库存"""
        updated_count = 0
        errors = []
        
        for update in updates:
            try:
                product_id = update.get('id')
                new_stock = update.get('stock')
                
                if not product_id or new_stock is None:
                    errors.append(f"无效的更新数据: {update}")
                    continue
                
                product = self.model.objects.get(id=product_id, is_deleted=False)
                product.stock = new_stock
                product.save()
                updated_count += 1
                
            except self.model.DoesNotExist:
                errors.append(f"产品不存在: {product_id}")
            except Exception as e:
                errors.append(f"更新失败 {product_id}: {str(e)}")
        
        return {
            'updated_count': updated_count,
            'errors': errors,
            'success': len(errors) == 0
        }
    
    def export_data(self, format_type: str = 'csv', **kwargs) -> dict:
        """导出产品数据"""
        queryset = self.model.objects.filter(is_deleted=False)
        
        # 应用过滤条件
        if 'category' in kwargs:
            queryset = queryset.filter(category=kwargs['category'])
        
        if 'is_active' in kwargs:
            queryset = queryset.filter(is_active=kwargs['is_active'])
        
        data = []
        for product in queryset:
            data.append({
                'ID': product.id,
                '名称': product.name,
                '描述': product.description or '',
                '价格': str(product.price),
                '分类': product.category,
                '库存': product.stock,
                '状态': '激活' if product.is_active else '禁用',
                '创建时间': product.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })
        
        return {
            'format': format_type,
            'data': data,
            'count': len(data),
            'exported_at': datetime.now().isoformat()
        }


# ==================== API控制器定义 ====================

@enhanced_auto_crud_api(
    model_class=Product,
    prefix="products",
    tags=["产品管理"],
    schema_in=ProductCreateSchema,
    schema_out=ProductResponseSchema,
    version=APIVersion.V2,
    pagination=True,
    exclude=[],  # 不排除任何默认端点
    extra_query_fields={
        'category': ('分类过滤', str),
        'is_active': ('状态过滤', bool),
        'min_price': ('最低价格', float),
        'max_price': ('最高价格', float)
    },
    enum_fields_config={
        'category': {
            'source': 'config',
            'data': ['电子产品', '服装', '食品', '图书', '其他']
        }
    },
    cache_config={
        'list_pagination': {
            'strategy': 'redis',
            'ttl': 300
        },
        'retrieve': {
            'strategy': 'memory',
            'ttl': 600
        }
    },
    rate_limit_config={
        'create': {'requests': 10, 'window': 60},
        'update': {'requests': 20, 'window': 60},
        'delete': {'requests': 5, 'window': 60}
    },
    audit_config={
        'enable': True,
        'sensitive_fields': ['price'],
        'async_logging': True
    },
    export_config={
        'formats': ['csv', 'excel', 'json'],
        'max_records': 10000
    }
)
class ProductController(ProductService):
    """产品控制器 - 使用增强版自动CRUD API"""
    
    @versioned_api_route(
        http_get,
        '/stats',
        version=APIVersion.V2,
        response=ProductStatsSchema,
        summary="获取产品统计信息",
        description="获取产品的统计信息，包括总数、分类分布等",
        cache_strategy='redis',
        cache_ttl=600
    )
    def get_stats(self, request, category: str = None):
        """获取产品统计信息"""
        return self.get_product_stats(category=category)
    
    @versioned_api_route(
        http_post,
        '/bulk-update-stock',
        version=APIVersion.V2,
        summary="批量更新库存",
        description="批量更新多个产品的库存数量",
        rate_limit={'requests': 5, 'window': 60}
    )
    def bulk_update_stock_api(self, request, updates: List[dict]):
        """批量更新库存API"""
        return self.bulk_update_stock(updates)
    
    @versioned_api_route(
        http_get,
        '/low-stock',
        version=APIVersion.V2,
        response=List[ProductResponseSchema],
        summary="获取低库存产品",
        description="获取库存低于指定数量的产品列表",
        cache_strategy='memory',
        cache_ttl=300
    )
    def get_low_stock_products(self, request, threshold: int = 10):
        """获取低库存产品"""
        products = self.model.objects.filter(
            is_deleted=False,
            stock__lt=threshold
        ).order_by('stock')
        
        return [
            {
                'id': p.id,
                'name': p.name,
                'description': p.description,
                'price': float(p.price),
                'category': p.category,
                'stock': p.stock,
                'is_active': p.is_active,
                'created_at': p.created_at,
                'updated_at': p.updated_at
            }
            for p in products
        ]
    
    @versioned_api_route(
        http_post,
        '/batch-activate',
        version=APIVersion.V2,
        summary="批量激活产品",
        description="批量激活或禁用产品"
    )
    def batch_activate(self, request, product_ids: List[str], is_active: bool = True):
        """批量激活/禁用产品"""
        updated_count = self.model.objects.filter(
            id__in=product_ids,
            is_deleted=False
        ).update(is_active=is_active)
        
        return {
            'updated_count': updated_count,
            'is_active': is_active,
            'message': f'成功{"激活" if is_active else "禁用"}{updated_count}个产品'
        }


# ==================== 使用说明 ====================

"""
使用示例:

1. 自动生成的CRUD端点:
   - GET /v2/products/list_pagination - 分页列表
   - GET /v2/products/list_all - 全部列表  
   - GET /v2/products/{id} - 获取详情
   - POST /v2/products/ - 创建产品
   - PUT /v2/products/{id} - 更新产品
   - DELETE /v2/products/{id} - 删除产品
   - POST /v2/products/batch_create - 批量创建
   - GET /v2/products/list_id_mappings - ID映射

2. 自定义端点:
   - GET /v2/products/stats - 产品统计
   - POST /v2/products/bulk-update-stock - 批量更新库存
   - GET /v2/products/low-stock - 低库存产品
   - POST /v2/products/batch-activate - 批量激活

3. 导出端点:
   - GET /v2/products/export/csv - CSV导出
   - GET /v2/products/export/excel - Excel导出
   - GET /v2/products/export/json - JSON导出

4. 特性:
   - API版本管理 (v2)
   - Redis/内存缓存
   - 速率限制
   - 数据验证
   - 审计日志
   - 权限控制
   - 统一响应格式
"""
