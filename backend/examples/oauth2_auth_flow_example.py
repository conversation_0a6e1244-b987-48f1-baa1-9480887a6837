"""
OAuth2认证流程完整示例
密码登录 + Authorization Code OAuth2统一认证
"""

import requests
import json
from urllib.parse import urlparse, parse_qs


class OAuth2AuthFlowExample:
    """OAuth2认证流程示例"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/auth"
        self.session_token = None
        self.access_token = None
        
    def step1_password_login(self, username, password, remember_me=False):
        """
        步骤1: 密码登录
        
        Args:
            username: 用户名（支持用户名、邮箱、手机号）
            password: 密码
            remember_me: 是否记住登录
            
        Returns:
            dict: 登录结果
        """
        print("=== 步骤1: 密码登录 ===")
        
        url = f"{self.api_base}/login"
        data = {
            "username": username,
            "password": password,
            "remember_me": remember_me
        }
        
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"响应状态: {response.status_code}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            self.session_token = result['session_token']
            print(f"✅ 登录成功，获得session_token: {self.session_token[:20]}...")
            return result
        else:
            print(f"❌ 登录失败: {result.get('message')}")
            return None
    
    def step2_get_oauth_applications(self):
        """
        步骤2: 获取可用的OAuth2应用列表
        
        Returns:
            list: 应用列表
        """
        print("\n=== 步骤2: 获取OAuth2应用列表 ===")
        
        if not self.session_token:
            print("❌ 需要先登录获取session_token")
            return None
        
        url = f"{self.api_base}/oauth2/applications"
        params = {"session_token": self.session_token}
        
        response = requests.get(url, params=params)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"响应状态: {response.status_code}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            applications = result['applications']
            print(f"✅ 获取到 {len(applications)} 个可用应用")
            for app in applications:
                print(f"  - {app['name']} (client_id: {app['client_id']})")
            return applications
        else:
            print(f"❌ 获取应用列表失败: {result.get('message')}")
            return None
    
    def step3_oauth2_authorize(self, client_id, redirect_uri, scope="read write", state=None):
        """
        步骤3: OAuth2授权
        
        Args:
            client_id: OAuth2客户端ID
            redirect_uri: 重定向URI
            scope: 权限范围
            state: 状态参数
            
        Returns:
            dict: 授权结果
        """
        print("\n=== 步骤3: OAuth2授权 ===")
        
        if not self.session_token:
            print("❌ 需要先登录获取session_token")
            return None
        
        url = f"{self.api_base}/oauth2/authorize"
        data = {
            "session_token": self.session_token,
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": scope,
            "state": state
        }
        
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"响应状态: {response.status_code}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            auth_code = result['authorization_code']
            redirect_url = result['redirect_url']
            print(f"✅ 授权成功，获得authorization_code: {auth_code[:20]}...")
            print(f"📍 重定向URL: {redirect_url}")
            
            # 从重定向URL中提取授权码
            parsed_url = urlparse(redirect_url)
            query_params = parse_qs(parsed_url.query)
            extracted_code = query_params.get('code', [None])[0]
            
            return {
                'authorization_code': extracted_code or auth_code,
                'redirect_url': redirect_url,
                'state': query_params.get('state', [None])[0]
            }
        else:
            print(f"❌ 授权失败: {result.get('message')}")
            return None
    
    def step4_exchange_code_for_token(self, authorization_code, client_id, client_secret, redirect_uri):
        """
        步骤4: 授权码换取访问令牌
        
        Args:
            authorization_code: 授权码
            client_id: 客户端ID
            client_secret: 客户端密钥
            redirect_uri: 重定向URI
            
        Returns:
            dict: 令牌信息
        """
        print("\n=== 步骤4: 授权码换取访问令牌 ===")
        
        url = f"{self.api_base}/oauth2/token"
        data = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "client_id": client_id,
            "client_secret": client_secret,
            "redirect_uri": redirect_uri
        }
        
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"响应状态: {response.status_code}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            self.access_token = result['access_token']
            print(f"✅ 令牌交换成功，获得access_token: {self.access_token[:20]}...")
            return result
        else:
            print(f"❌ 令牌交换失败: {result.get('message')}")
            return None
    
    def step5_use_access_token(self, api_endpoint="/api/user/profile"):
        """
        步骤5: 使用访问令牌调用API
        
        Args:
            api_endpoint: API端点
            
        Returns:
            dict: API响应
        """
        print("\n=== 步骤5: 使用访问令牌调用API ===")
        
        if not self.access_token:
            print("❌ 需要先获取access_token")
            return None
        
        url = f"{self.base_url}{api_endpoint}"
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers)
        
        print(f"请求URL: {url}")
        print(f"请求头: {json.dumps(headers, indent=2, ensure_ascii=False)}")
        print(f"响应状态: {response.status_code}")
        
        try:
            result = response.json()
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if response.status_code == 200:
                print("✅ API调用成功")
                return result
            else:
                print(f"❌ API调用失败: {result.get('message', '未知错误')}")
                return None
        except json.JSONDecodeError:
            print(f"❌ 响应不是有效的JSON: {response.text}")
            return None
    
    def step6_refresh_token(self, refresh_token, client_id, client_secret):
        """
        步骤6: 刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            client_id: 客户端ID
            client_secret: 客户端密钥
            
        Returns:
            dict: 新的令牌信息
        """
        print("\n=== 步骤6: 刷新访问令牌 ===")
        
        url = f"{self.api_base}/oauth2/token"
        data = {
            "grant_type": "refresh_token",
            "refresh_token": refresh_token,
            "client_id": client_id,
            "client_secret": client_secret
        }
        
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"响应状态: {response.status_code}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            self.access_token = result['access_token']
            print(f"✅ 令牌刷新成功，获得新的access_token: {self.access_token[:20]}...")
            return result
        else:
            print(f"❌ 令牌刷新失败: {result.get('message')}")
            return None
    
    def step7_logout(self):
        """
        步骤7: 登出
        
        Returns:
            dict: 登出结果
        """
        print("\n=== 步骤7: 登出 ===")
        
        url = f"{self.api_base}/logout"
        data = {
            "session_token": self.session_token,
            "access_token": self.access_token
        }
        
        response = requests.post(url, json=data)
        result = response.json()
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        print(f"响应状态: {response.status_code}")
        print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            print("✅ 登出成功")
            self.session_token = None
            self.access_token = None
            return result
        else:
            print(f"❌ 登出失败: {result.get('message')}")
            return None
    
    def run_complete_flow(self, username, password, client_id, client_secret, redirect_uri):
        """
        运行完整的OAuth2认证流程
        
        Args:
            username: 用户名
            password: 密码
            client_id: OAuth2客户端ID
            client_secret: OAuth2客户端密钥
            redirect_uri: 重定向URI
        """
        print("🚀 开始完整的OAuth2认证流程")
        print("=" * 50)
        
        # 步骤1: 密码登录
        login_result = self.step1_password_login(username, password)
        if not login_result:
            return
        
        # 步骤2: 获取OAuth2应用列表
        applications = self.step2_get_oauth_applications()
        if not applications:
            return
        
        # 步骤3: OAuth2授权
        auth_result = self.step3_oauth2_authorize(client_id, redirect_uri)
        if not auth_result:
            return
        
        # 步骤4: 授权码换取访问令牌
        token_result = self.step4_exchange_code_for_token(
            auth_result['authorization_code'],
            client_id,
            client_secret,
            redirect_uri
        )
        if not token_result:
            return
        
        # 步骤5: 使用访问令牌调用API
        api_result = self.step5_use_access_token()
        
        # 步骤6: 刷新令牌（如果有refresh_token）
        if token_result.get('refresh_token'):
            refresh_result = self.step6_refresh_token(
                token_result['refresh_token'],
                client_id,
                client_secret
            )
        
        # 步骤7: 登出
        logout_result = self.step7_logout()
        
        print("\n" + "=" * 50)
        print("🎉 OAuth2认证流程完成！")


# 使用示例
if __name__ == "__main__":
    # 创建OAuth2认证流程实例
    oauth_flow = OAuth2AuthFlowExample("http://localhost:8000")
    
    # 配置参数（需要根据实际情况修改）
    USERNAME = "admin"  # 用户名
    PASSWORD = "admin123"  # 密码
    CLIENT_ID = "your-client-id"  # OAuth2客户端ID
    CLIENT_SECRET = "your-client-secret"  # OAuth2客户端密钥
    REDIRECT_URI = "http://localhost:3000/callback"  # 重定向URI
    
    # 运行完整流程
    oauth_flow.run_complete_flow(
        username=USERNAME,
        password=PASSWORD,
        client_id=CLIENT_ID,
        client_secret=CLIENT_SECRET,
        redirect_uri=REDIRECT_URI
    )
    
    print("\n📝 流程说明:")
    print("1. 用户使用用户名/密码登录，获得session_token")
    print("2. 基于session_token获取可用的OAuth2应用列表")
    print("3. 选择应用进行OAuth2授权，获得authorization_code")
    print("4. 使用authorization_code换取access_token和refresh_token")
    print("5. 使用access_token调用受保护的API")
    print("6. 使用refresh_token刷新access_token（可选）")
    print("7. 登出清除所有令牌")
    
    print("\n🔧 集成说明:")
    print("- 前端应用可以使用这个流程实现统一认证")
    print("- 支持单点登录(SSO)，一次登录多个应用")
    print("- 支持PKCE增强安全性")
    print("- 支持令牌刷新机制")
    print("- 支持多种登录方式（用户名/邮箱/手机号）")
