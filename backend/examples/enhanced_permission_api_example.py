"""
集成权限管理的增强API使用示例
"""

from typing import List, Optional
from datetime import datetime

from django.db import models
from ninja import Schema
from ninja_extra import http_get, http_post, http_put, http_delete

from zhi_common.zhi_model.core_model import ZhiCoreModel
from zhi_common.zhi_services.enhanced_expose import (
    enhanced_auto_crud_api, 
    versioned_api_route, 
    APIVersion,
    PermissionDimension,
    PermissionAction,
    permission_required_route
)
from zhi_common.zhi_services.enhanced_model_service import EnhancedModelService


# ==================== 模型定义 ====================

class Article(ZhiCoreModel):
    """文章模型 - 演示完整权限控制"""
    title = models.CharField(max_length=200, verbose_name="标题")
    content = models.TextField(verbose_name="内容")
    category = models.CharField(max_length=50, verbose_name="分类")
    is_published = models.BooleanField(default=False, verbose_name="是否发布")
    is_featured = models.BooleanField(default=False, verbose_name="是否推荐")
    view_count = models.IntegerField(default=0, verbose_name="浏览次数")
    author_id = models.CharField(max_length=50, verbose_name="作者ID")
    department = models.CharField(max_length=100, verbose_name="所属部门")
    
    class Meta:
        db_table = 'article'
        verbose_name = "文章"
        verbose_name_plural = "文章"


# ==================== Schema定义 ====================

class ArticleCreateSchema(Schema):
    """文章创建Schema"""
    title: str
    content: str
    category: str
    is_published: bool = False
    is_featured: bool = False
    department: str


class ArticleUpdateSchema(Schema):
    """文章更新Schema"""
    title: Optional[str] = None
    content: Optional[str] = None
    category: Optional[str] = None
    is_published: Optional[bool] = None
    is_featured: Optional[bool] = None
    department: Optional[str] = None


class ArticleResponseSchema(Schema):
    """文章响应Schema - 支持字段级权限"""
    id: str
    title: str
    content: Optional[str] = None  # 根据权限动态显示
    category: str
    is_published: bool
    is_featured: Optional[bool] = None  # 敏感字段
    view_count: Optional[int] = None  # 统计字段
    author_id: Optional[str] = None  # 敏感字段
    department: Optional[str] = None  # 部门字段
    created_at: datetime
    updated_at: datetime


class ArticleStatsSchema(Schema):
    """文章统计Schema"""
    total_articles: int
    published_articles: int
    featured_articles: int
    categories: List[dict]
    top_authors: List[dict]


# ==================== 服务层定义 ====================

class ArticleService(EnhancedModelService):
    """文章服务 - 集成权限管理"""
    
    def __init__(self, model_class, **kwargs):
        # 定义权限配置
        permission_config = {
            'view': 'article.view',
            'create': 'article.create',
            'update': 'article.update',
            'delete': 'article.delete',
            'publish': 'article.publish',
            'feature': 'article.feature',
            'export': 'article.export',
            'statistics': 'article.statistics'
        }
        
        super().__init__(
            model_class, 
            permission_config=permission_config,
            enable_data_permissions=True,
            enable_field_permissions=True,
            enable_audit_logging=True,
            **kwargs
        )
    
    def get_article_statistics(self, user, category: str = None) -> dict:
        """获取文章统计信息（带权限控制）"""
        # 检查统计权限
        if not self.check_permission('statistics', user=user):
            self.log_audit_event('statistics', False, user=user)
            return self._handle_response(
                data=None, code=403, message="无统计权限", success=False
            )
        
        # 应用数据权限过滤
        queryset = self.filter_queryset_by_permissions(
            self.model.objects.filter(is_deleted=False), user, 'view'
        )
        
        if category:
            queryset = queryset.filter(category=category)
        
        # 统计信息
        total_articles = queryset.count()
        published_articles = queryset.filter(is_published=True).count()
        featured_articles = queryset.filter(is_featured=True).count()
        
        # 分类统计
        categories = list(
            queryset.values('category')
            .annotate(count=models.Count('id'))
            .order_by('-count')
        )
        
        # 作者统计（如果有权限查看作者信息）
        top_authors = []
        if self.check_permission('view', user=user):
            top_authors = list(
                queryset.values('author_id')
                .annotate(count=models.Count('id'))
                .order_by('-count')[:10]
            )
        
        result = {
            'total_articles': total_articles,
            'published_articles': published_articles,
            'featured_articles': featured_articles,
            'categories': categories,
            'top_authors': top_authors
        }
        
        self.log_audit_event('statistics', True, user=user, context={'category': category})
        return self._handle_response(data=result, message="ok")
    
    def publish_article(self, article_id: str, user, is_published: bool = True) -> dict:
        """发布/取消发布文章"""
        try:
            article = self.model.objects.get(id=article_id, is_deleted=False)
            
            # 检查发布权限
            if not self.check_permission('publish', resource=article, user=user):
                self.log_audit_event('publish', False, resource=article, user=user)
                return self._handle_response(
                    data=None, code=403, message="无发布权限", success=False
                )
            
            # 更新发布状态
            article.is_published = is_published
            article.save()
            
            action = "发布" if is_published else "取消发布"
            self.log_audit_event('publish', True, resource=article, user=user, 
                               context={'action': action, 'is_published': is_published})
            
            return self._handle_response(
                data={'id': article_id, 'is_published': is_published},
                message=f"文章{action}成功"
            )
            
        except self.model.DoesNotExist:
            return self._handle_response(
                data=None, code=404, message="文章不存在", success=False
            )
    
    def batch_update_category(self, article_ids: List[str], new_category: str, user) -> dict:
        """批量更新文章分类"""
        results = {'success': [], 'failed': []}
        
        for article_id in article_ids:
            try:
                article = self.model.objects.get(id=article_id, is_deleted=False)
                
                # 检查更新权限
                if not self.check_permission('update', resource=article, user=user):
                    results['failed'].append({
                        'id': article_id,
                        'error': '权限不足'
                    })
                    continue
                
                # 更新分类
                old_category = article.category
                article.category = new_category
                article.save()
                
                results['success'].append(article_id)
                self.log_audit_event('update', True, resource=article, user=user,
                                   context={'field': 'category', 'old_value': old_category, 'new_value': new_category})
                
            except self.model.DoesNotExist:
                results['failed'].append({
                    'id': article_id,
                    'error': '文章不存在'
                })
            except Exception as e:
                results['failed'].append({
                    'id': article_id,
                    'error': str(e)
                })
        
        return self._handle_response(
            data=results,
            message=f"批量更新完成，成功: {len(results['success'])}, 失败: {len(results['failed'])}"
        )


# ==================== API控制器定义 ====================

@enhanced_auto_crud_api(
    model_class=Article,
    prefix="articles",
    tags=["文章管理"],
    schema_in=ArticleCreateSchema,
    schema_out=ArticleResponseSchema,
    version=APIVersion.V2,
    pagination=True,
    exclude=[],  # 不排除任何默认端点
    extra_query_fields={
        'category': ('分类过滤', str),
        'is_published': ('发布状态过滤', bool),
        'author_id': ('作者过滤', str),
        'department': ('部门过滤', str)
    },
    enum_fields_config={
        'category': {
            'source': 'config',
            'data': ['技术', '产品', '运营', '设计', '其他']
        }
    },
    cache_config={
        'list_pagination': {
            'strategy': 'redis',
            'ttl': 300
        },
        'retrieve': {
            'strategy': 'memory',
            'ttl': 600
        }
    },
    rate_limit_config={
        'create': {'requests': 10, 'window': 60},
        'update': {'requests': 20, 'window': 60},
        'delete': {'requests': 5, 'window': 60}
    },
    audit_config={
        'enable': True,
        'sensitive_fields': ['is_featured', 'author_id'],
        'async_logging': True
    },
    export_config={
        'formats': ['csv', 'excel', 'json'],
        'max_records': 10000
    },
    # 权限管理配置
    permission_config={
        'view': 'article.view',
        'create': 'article.create',
        'update': 'article.update',
        'delete': 'article.delete',
        'export': 'article.export'
    },
    enable_data_permissions=True,
    enable_field_permissions=True,
    enable_audit_logging=True,
    permission_prefix='article'
)
class ArticleController(ArticleService):
    """文章控制器 - 集成完整权限管理"""
    
    @versioned_api_route(
        http_get,
        '/statistics',
        version=APIVersion.V2,
        response=ArticleStatsSchema,
        summary="获取文章统计信息",
        description="获取文章的统计信息，包括总数、分类分布等",
        cache_strategy='redis',
        cache_ttl=600,
        permission_code='article.statistics',
        permission_dimension=PermissionDimension.API,
        field_permissions=True
    )
    def get_statistics(self, request, category: str = None):
        """获取文章统计信息"""
        return self.get_article_statistics(request.user, category=category)
    
    @versioned_api_route(
        http_post,
        '/{article_id}/publish',
        version=APIVersion.V2,
        summary="发布文章",
        description="发布或取消发布文章",
        permission_code='article.publish',
        permission_dimension=PermissionDimension.API
    )
    def publish_article_api(self, request, article_id: str, is_published: bool = True):
        """发布文章API"""
        return self.publish_article(article_id, request.user, is_published)
    
    @versioned_api_route(
        http_post,
        '/batch-update-category',
        version=APIVersion.V2,
        summary="批量更新分类",
        description="批量更新多篇文章的分类",
        rate_limit={'requests': 5, 'window': 60},
        permission_code='article.batch_update',
        permission_dimension=PermissionDimension.API
    )
    def batch_update_category_api(self, request, article_ids: List[str], new_category: str):
        """批量更新分类API"""
        return self.batch_update_category(article_ids, new_category, request.user)
    
    @permission_required_route(
        permission_code='article.view_sensitive',
        dimension=PermissionDimension.API,
        field_permissions=True
    )
    @versioned_api_route(
        http_get,
        '/sensitive-data',
        version=APIVersion.V2,
        summary="获取敏感数据",
        description="获取文章的敏感统计数据"
    )
    def get_sensitive_data(self, request):
        """获取敏感数据 - 需要特殊权限"""
        # 应用数据权限过滤
        queryset = self.filter_queryset_by_permissions(
            self.model.objects.filter(is_deleted=False), request.user, 'view'
        )
        
        # 敏感统计信息
        sensitive_stats = {
            'total_views': queryset.aggregate(total=models.Sum('view_count'))['total'] or 0,
            'author_distribution': list(
                queryset.values('author_id')
                .annotate(count=models.Count('id'))
                .order_by('-count')
            ),
            'department_stats': list(
                queryset.values('department')
                .annotate(count=models.Count('id'))
                .order_by('-count')
            )
        }
        
        return self._handle_response(data=sensitive_stats, message="ok")


# ==================== 使用说明 ====================

"""
集成权限管理的API使用示例:

1. 自动生成的CRUD端点（带权限控制）:
   - GET /v2/articles/list_pagination - 分页列表（需要article.view权限）
   - GET /v2/articles/{id} - 获取详情（需要article.view权限，支持对象级权限）
   - POST /v2/articles/ - 创建文章（需要article.create权限）
   - PUT /v2/articles/{id} - 更新文章（需要article.update权限，支持字段级权限）
   - DELETE /v2/articles/{id} - 删除文章（需要article.delete权限）
   - GET /v2/articles/export/csv - 导出数据（需要article.export权限）

2. 自定义端点（带权限控制）:
   - GET /v2/articles/statistics - 文章统计（需要article.statistics权限）
   - POST /v2/articles/{id}/publish - 发布文章（需要article.publish权限）
   - POST /v2/articles/batch-update-category - 批量更新（需要article.batch_update权限）
   - GET /v2/articles/sensitive-data - 敏感数据（需要article.view_sensitive权限）

3. 权限管理特性:
   - 多维度权限控制：菜单、数据、字段、API四个维度
   - 数据权限过滤：根据用户角色自动过滤可访问的数据
   - 字段权限控制：根据用户权限动态显示/隐藏字段
   - 实时审计日志：记录所有权限检查和操作行为
   - 智能缓存管理：权限相关的查询结果自动缓存
   - 动态权限策略：支持基于时间、IP、属性的复杂权限规则

4. 配置示例:
   ```python
   # 在settings.py中配置权限管理
   PERMISSION_MANAGEMENT = {
       'ENABLE_CACHING': True,
       'ENABLE_DYNAMIC_POLICIES': True,
       'ENABLE_AUDITING': True,
       'ENABLE_MONITORING': True,
   }
   
   # 初始化权限数据
   from zhi_oauth.models import Permission, Role
   
   # 创建文章相关权限
   permissions = [
       {'code': 'article.view', 'name': '查看文章', 'permission_type': 'api'},
       {'code': 'article.create', 'name': '创建文章', 'permission_type': 'api'},
       {'code': 'article.update', 'name': '更新文章', 'permission_type': 'api'},
       {'code': 'article.delete', 'name': '删除文章', 'permission_type': 'api'},
       {'code': 'article.publish', 'name': '发布文章', 'permission_type': 'api'},
       {'code': 'article.export', 'name': '导出文章', 'permission_type': 'api'},
       {'code': 'article.statistics', 'name': '文章统计', 'permission_type': 'api'},
   ]
   
   for perm_data in permissions:
       Permission.objects.get_or_create(**perm_data)
   ```

5. 监控和维护:
   ```bash
   # 查看权限使用统计
   python manage.py permission_management audit_report --days 7
   
   # 检测权限异常
   python manage.py permission_management detect_anomalies
   
   # 预热权限缓存
   python manage.py permission_management warm_cache
   ```
"""
