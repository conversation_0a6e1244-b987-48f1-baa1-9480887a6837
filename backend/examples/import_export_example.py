"""
导入导出功能使用示例

本示例展示如何在CRUD基类中配置和使用导入导出功能
"""

from typing import List, Dict, Any
from django.db import models
from ninja import Schema
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_response.schemas.base import (
    BaseResponseSchema, 
    ExportRequestSchema, 
    ImportResultSchema,
    ImportPreviewSchema
)
from ninja_extra import http_get, http_post
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile


# 示例模型
class Product(models.Model):
    """产品模型"""
    name = models.CharField(max_length=100, verbose_name="产品名称")
    code = models.CharField(max_length=50, unique=True, verbose_name="产品编码")
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="价格")
    category = models.CharField(max_length=50, verbose_name="分类")
    stock = models.IntegerField(default=0, verbose_name="库存")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    description = models.TextField(blank=True, verbose_name="描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    class Meta:
        db_table = 'example_product'
        verbose_name = "产品"
        verbose_name_plural = "产品"


# Schema定义
class ProductSchemaIn(Schema):
    name: str
    code: str
    price: float
    category: str
    stock: int = 0
    is_active: bool = True
    description: str = ""


class ProductSchemaOut(Schema):
    id: int
    name: str
    code: str
    price: float
    category: str
    stock: int
    is_active: bool
    description: str
    created_at: str
    updated_at: str


# 自定义字段验证器
def validate_price(value):
    """价格验证器"""
    if value <= 0:
        raise ValueError("价格必须大于0")
    return value


def validate_stock(value):
    """库存验证器"""
    if value < 0:
        raise ValueError("库存不能为负数")
    return value


@auto_crud_api(
    Product,
    prefix="Product",
    tags=["产品管理"],
    schema_in=ProductSchemaIn,
    schema_out=ProductSchemaOut,
    exclude=[]
)
class ProductControllerAPI(BaseModelService):
    """产品管理API - 支持导入导出功能"""
    
    model = Product
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS
    
    # 导出配置
    export_settings = {
        'enabled': True,  # 启用导出功能
        'max_rows': 10000,  # 最大导出行数
        'fields': ['name', 'code', 'price', 'category', 'stock', 'is_active', 'description'],  # 允许导出的字段
        'field_mapping': {  # 字段中文映射
            'name': '产品名称',
            'code': '产品编码',
            'price': '价格',
            'category': '分类',
            'stock': '库存',
            'is_active': '是否启用',
            'description': '描述'
        }
    }
    
    # 导入配置
    import_settings = {
        'enabled': True,  # 启用导入功能
        'max_rows': 1000,  # 最大导入行数
        'max_file_size': 5 * 1024 * 1024,  # 最大文件大小 5MB
        'sheet_name': None,  # Excel工作表名称，None表示使用活动工作表
        'field_mapping': {  # 字段映射：文件字段名 -> 模型字段名
            '产品名称': 'name',
            '产品编码': 'code',
            '价格': 'price',
            '分类': 'category',
            '库存': 'stock',
            '是否启用': 'is_active',
            '描述': 'description'
        },
        'field_validators': {  # 字段验证器
            'price': validate_price,
            'stock': validate_stock
        }
    }
    
    @api_route(http_get, "/export", response=None)
    def export_products(self, request, export_request: ExportRequestSchema = None):
        """
        导出产品数据
        
        支持的格式：csv, excel, json
        """
        # 从查询参数获取导出配置
        format_type = request.GET.get('format', 'excel')
        filename = request.GET.get('filename', None)
        fields = request.GET.getlist('fields') if request.GET.getlist('fields') else None
        
        # 构建过滤条件
        filters = {}
        if request.GET.get('category'):
            filters['category'] = request.GET.get('category')
        if request.GET.get('is_active') is not None:
            filters['is_active'] = request.GET.get('is_active').lower() == 'true'
        
        return self.export_data(
            format_type=format_type,
            filename=filename,
            filters=filters,
            fields=fields
        )
    
    @api_route(http_post, "/import", response={200: BaseResponseSchema[ImportResultSchema]})
    def import_products(self, request, file: UploadedFile):
        """
        导入产品数据
        
        支持的格式：csv, excel
        """
        preview_only = request.POST.get('preview_only', 'false').lower() == 'true'
        
        return self.import_data(
            file=file,
            preview_only=preview_only
        )
    
    @api_route(http_get, "/import/template", response=None)
    def get_import_template(self, request):
        """
        下载导入模板
        
        支持的格式：csv, excel
        """
        format_type = request.GET.get('format', 'excel')
        return self.get_import_template(format_type=format_type)
    
    @api_route(http_get, "/export/fields", response={200: BaseResponseSchema[List[Dict[str, Any]]]})
    def get_export_fields(self, request):
        """
        获取可导出的字段列表
        """
        return self.get_export_fields()
    
    @api_route(http_post, "/import/preview", response={200: BaseResponseSchema[ImportPreviewSchema]})
    def preview_import(self, request, file: UploadedFile):
        """
        预览导入数据
        """
        return self.import_data(file=file, preview_only=True)


# 使用示例
"""
1. 导出数据：
   GET /api/products/export?format=excel&category=电子产品
   GET /api/products/export?format=csv&fields=name,code,price

2. 导入数据：
   POST /api/products/import
   Content-Type: multipart/form-data
   Body: file=products.xlsx

3. 预览导入：
   POST /api/products/import/preview
   Content-Type: multipart/form-data
   Body: file=products.xlsx

4. 下载模板：
   GET /api/products/import/template?format=excel

5. 获取字段列表：
   GET /api/products/export/fields
"""


# 高级配置示例
class AdvancedProductService(BaseModelService):
    """高级产品服务 - 展示更多配置选项"""
    
    model = Product
    
    # 复杂的导出配置
    export_settings = {
        'enabled': True,
        'max_rows': 50000,
        'fields': None,  # None表示导出所有非排除字段
        'field_mapping': {
            'name': '产品名称',
            'code': '产品编码',
            'price': '单价(元)',
            'category': '产品分类',
            'stock': '库存数量',
            'is_active': '状态',
            'description': '产品描述',
            'created_at': '创建时间',
            'updated_at': '更新时间'
        },
        # 可以添加数据转换函数
        'data_transformers': {
            'is_active': lambda x: '启用' if x else '禁用',
            'price': lambda x: f"¥{x:.2f}"
        }
    }
    
    # 复杂的导入配置
    import_settings = {
        'enabled': True,
        'max_rows': 5000,
        'max_file_size': 10 * 1024 * 1024,  # 10MB
        'field_mapping': {
            '产品名称': 'name',
            '产品编码': 'code',
            '单价(元)': 'price',
            '产品分类': 'category',
            '库存数量': 'stock',
            '状态': 'is_active',
            '产品描述': 'description'
        },
        'field_validators': {
            'name': lambda x: x.strip() if isinstance(x, str) else str(x),
            'code': lambda x: x.upper().strip() if isinstance(x, str) else str(x),
            'price': validate_price,
            'stock': validate_stock,
            'is_active': lambda x: x in ['启用', 'True', '1', 'true'] if isinstance(x, str) else bool(x)
        },
        # 导入前的数据预处理
        'pre_processors': [
            lambda data: {k: v.strip() if isinstance(v, str) else v for k, v in data.items()},
            # 可以添加更多预处理函数
        ]
    }
