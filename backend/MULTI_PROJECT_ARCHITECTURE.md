# ZhiAdmin 多项目架构设计

## 🏗️ 架构概述

ZhiAdmin 采用**单体仓库多项目**的架构模式，通过统一的配置管理系统支持多个独立的子项目。每个子项目都可以独立启动、部署和扩展。

**核心设计理念**：
- `backend/` 作为后端根目录，包含所有后端代码
- 通过 `application.settings` 统一管理各子项目配置
- 每个子项目可独立启动：`python manage.py runserver --settings=application.settings.xxx`
- 统一的管理脚本位于 `backend/zhi_scripts/`

## 📁 项目结构

```
zhi_ai_admin/                    # 项目根目录
├── backend/                     # 后端根目录 ⭐
│   ├── manage.py               # Django管理入口
│   ├── requirements.txt        # Python依赖 ⭐
│   ├── application/            # 核心启动配置管理
│   │   ├── settings/          # 各子项目的配置文件
│   │   │   ├── base.py       # 基础配置
│   │   │   ├── zhi_oauth.py  # OAuth子项目配置
│   │   │   ├── zhi_logger.py # 日志子项目配置
│   │   │   ├── zhi_system.py # 系统管理子项目配置
│   │   │   └── interval_admin.py # 完整后台配置
│   │   └── conf_urls/        # 各子项目的URL配置
│   │       ├── base.py       # 基础URL配置
│   │       ├── zhi_oauth.py  # OAuth URL配置
│   │       ├── zhi_logger.py # 日志URL配置
│   │       └── zhi_system.py # 系统URL配置
│   ├── zhi_oauth/            # OAuth2认证子项目
│   ├── zhi_logger/           # 日志管理子项目
│   ├── zhi_system/           # 系统管理子项目
│   ├── zhi_common/           # 通用组件和SDK
│   │   └── zhi_logger/       # 日志SDK
│   └── zhi_scripts/          # 项目管理脚本 ⭐
│       ├── start_logger.py   # 日志服务启动脚本
│       ├── start_oauth.py    # OAuth服务启动脚本
│       ├── manage_projects.py # 统一管理工具
│       └── README.md         # 脚本使用说明
├── frontend/                 # 前端目录（如果有）
└── docs/                     # 项目文档
```

## 🚀 子项目列表

### 1. zhi_oauth - OAuth2认证服务
- **端口**: 8001
- **配置**: `application.settings.zhi_oauth`
- **功能**: OAuth2权限认证系统
- **API**: `/api/oauth/`
- **文档**: `/oauth/docs`

### 2. zhi_logger - 日志管理服务
- **端口**: 8002
- **配置**: `application.settings.zhi_logger`
- **功能**: 日志记录、分析和监控系统
- **API**: `/api/logger/`
- **文档**: `/logger/docs`
- **WebSocket**: `/ws/logs/`

### 3. zhi_system - 系统管理服务
- **端口**: 8003
- **配置**: `application.settings.zhi_system`
- **功能**: 系统配置管理模块
- **API**: `/api/system/`
- **文档**: `/system/docs`

### 4. interval_admin - 完整管理后台
- **端口**: 8000
- **配置**: `application.settings.interval_admin`
- **功能**: 完整的管理后台系统
- **API**: `/api/`
- **文档**: `/docs`

## 🔧 启动方式

### 方式1: 使用Django管理命令（在backend目录下）
```bash
# 进入backend目录
cd backend

# 启动OAuth子项目
python manage.py runserver --settings=application.settings.zhi_oauth 0.0.0.0:8001

# 启动日志管理子项目
python manage.py runserver --settings=application.settings.zhi_logger 0.0.0.0:8002

# 启动系统管理子项目
python manage.py runserver --settings=application.settings.zhi_system 0.0.0.0:8003

# 启动完整后台
python manage.py runserver --settings=application.settings.interval_admin 0.0.0.0:8000
```

### 方式2: 使用项目管理脚本（从项目根目录）
```bash
# 列出所有可用项目
python backend/zhi_scripts/manage_projects.py list

# 启动指定项目
python backend/zhi_scripts/manage_projects.py start zhi_logger --port 8002
python backend/zhi_scripts/manage_projects.py start zhi_oauth --port 8001 --debug

# 检查项目运行状态
python backend/zhi_scripts/manage_projects.py status

# 执行数据库迁移
python backend/zhi_scripts/manage_projects.py migrate zhi_logger

# 收集静态文件
python backend/zhi_scripts/manage_projects.py collectstatic zhi_logger

# 创建超级用户
python backend/zhi_scripts/manage_projects.py createsuperuser zhi_logger
```

### 方式3: 使用专用启动脚本（从项目根目录）
```bash
# 启动日志子项目
python backend/zhi_scripts/start_logger.py --port 8002 --debug

# 启动OAuth子项目
python backend/zhi_scripts/start_oauth.py --port 8001

# 启动系统管理子项目
python backend/zhi_scripts/start_system.py --port 8003

# 启动完整后台
python backend/zhi_scripts/start_admin.py --port 8000
```

### 方式4: Windows快速启动
```cmd
REM 从项目根目录运行
backend\zhi_scripts\start_logger.bat 8002
backend\zhi_scripts\start_oauth.bat 8001
backend\zhi_scripts\start_system.bat 8003
backend\zhi_scripts\start_admin.bat 8000
```

### 方式5: 项目状态检查
```bash
# 检查项目配置和依赖
python backend/zhi_scripts/check_project.py --project zhi_logger

# 检查所有项目
python backend/zhi_scripts/check_project.py --project zhi_oauth
python backend/zhi_scripts/check_project.py --project zhi_system
```

## 🐳 Docker部署

### 单个子项目部署
```bash
# 构建日志子项目镜像
cd docker/zhi_logger
docker-compose up -d
```

### 完整系统部署
```bash
# 启动所有服务
docker-compose -f docker/docker-compose.yml up -d
```

## ⚙️ 配置管理

### 基础配置 (backend/application/settings/base.py)
包含所有子项目共享的基础配置：
- 数据库配置 (MySQL)
- Redis配置 (缓存和Celery)
- Celery配置 (异步任务)
- 中间件配置
- 基础应用列表
- 日志配置
- 安全配置

### 子项目配置
每个子项目都有自己的配置文件，继承基础配置并添加特定配置：

```python
# backend/application/settings/zhi_logger.py
from application.settings.base import *
from loguru import logger

# 子项目特定的URL配置
ROOT_URLCONF = 'application.conf_urls.zhi_logger'
LOGIN_URL = '/admin/login/'

# 子项目特定的应用
LOGGER_APPS = [
    "zhi_logger",
    "zhi_common",
]

# 合并应用列表
INSTALLED_APPS += LOGGER_APPS

# 子项目特定的配置
LOGGER_SETTINGS = {
    'WEBSOCKET_ENABLED': True,
    'LOG_RETENTION_DAYS': 30,
    'API_LOG_ENABLED': True,
    'AUTO_CLEANUP': True,
    # ...
}

# 中间件配置（日志子项目特定）
MIDDLEWARE += [
    'zhi_common.zhi_logger.middleware.SystemLogMiddleware',
    'zhi_common.zhi_logger.middleware.EnhancedApiLoggingMiddleware',
]

# Celery任务配置
CELERY_BEAT_SCHEDULE.update({
    'cleanup-old-logs': {
        'task': 'zhi_logger.tasks.cleanup_old_logs_task',
        'schedule': 60 * 60 * 24,  # 24小时
        'args': (30,),
    },
})

logger.debug(f"ZhiLogger 子项目配置加载完成")
```

### URL配置
每个子项目都有独立的URL配置：

```python
# backend/application/conf_urls/zhi_logger.py
from application.conf_urls.base import *
from zhi_logger.api import api as logger_api

app_urls = [
    path('api/logger/', logger_api.urls),
]

urlpatterns += app_urls
```

## 🔗 服务间通信

### 1. 共享数据库
所有子项目共享同一个数据库，通过模型关系进行数据交互。

### 2. Redis缓存
使用Redis进行缓存和会话管理，支持跨服务的数据共享。

### 3. WebSocket通信
日志子项目提供WebSocket服务，其他子项目可以推送日志消息。

### 4. API调用
子项目之间可以通过HTTP API进行通信。

## 📊 监控和日志

### 日志记录
所有子项目都使用统一的日志SDK：
```python
from zhi_common.zhi_logger import get_logger

logger = get_logger(__name__)
logger.info("服务启动", category="system")
```

### 实时监控
通过日志子项目的WebSocket接口可以实时监控所有服务的运行状态。

## 🔒 安全考虑

### 1. 服务隔离
每个子项目可以独立部署，降低安全风险。

### 2. 权限控制
通过OAuth2子项目统一管理用户权限。

### 3. 网络安全
在生产环境中，建议使用反向代理和防火墙保护各个服务。

## 🚀 扩展指南

### 添加新的子项目

1. **创建应用目录**
   ```bash
   # 在backend目录下创建新应用
   cd backend
   python manage.py startapp zhi_newapp
   ```

2. **创建配置文件**
   ```python
   # backend/application/settings/zhi_newapp.py
   from application.settings.base import *
   from loguru import logger

   # 子项目URL配置
   ROOT_URLCONF = 'application.conf_urls.zhi_newapp'
   LOGIN_URL = '/admin/login/'

   # 子项目特定的应用
   NEWAPP_APPS = [
       "zhi_newapp",
       "zhi_common",
   ]

   # 合并应用列表
   INSTALLED_APPS += NEWAPP_APPS

   # 子项目特定配置
   NEWAPP_SETTINGS = {
       'FEATURE_ENABLED': True,
       'DEFAULT_PAGE_SIZE': 20,
   }

   logger.debug(f"ZhiNewApp 子项目配置加载完成")
   ```

3. **创建URL配置**
   ```python
   # backend/application/conf_urls/zhi_newapp.py
   from application.conf_urls.base import *
   from zhi_newapp.api import api as newapp_api

   app_urls = [
       path('api/newapp/', newapp_api.urls),
   ]

   urlpatterns += app_urls
   ```

4. **创建启动脚本**
   ```python
   # backend/zhi_scripts/start_newapp.py
   # 参考其他启动脚本的模板
   ```

5. **更新管理脚本**
   在 `backend/zhi_scripts/manage_projects.py` 的 `PROJECTS` 字典中添加新项目配置：
   ```python
   'zhi_newapp': {
       'name': 'ZhiAdmin 新应用',
       'settings': 'application.settings.zhi_newapp',
       'default_port': 8004,
       'description': '新应用描述',
       'urls': '/api/newapp/',
       'docs': '/newapp/docs',
       'script': 'start_newapp.py'
   }
   ```

## 🎯 最佳实践

### 1. 配置管理
- 使用环境变量管理敏感配置（数据库密码、API密钥等）
- 为不同环境创建不同的配置文件（开发、测试、生产）
- 保持配置的一致性和可维护性
- 使用 `from application.settings.base import *` 继承基础配置

### 2. 依赖管理
- 共享的依赖和SDK放在 `backend/zhi_common/` 中
- 子项目特定的依赖在各自的应用中管理
- 定期更新和维护依赖
- 使用 `backend/requirements.txt` 管理Python包依赖

### 3. 数据库管理
- 使用Django的迁移系统管理数据库变更
- 为每个子项目单独执行迁移：`python backend/zhi_scripts/manage_projects.py migrate zhi_logger`
- 注意跨项目的模型依赖关系
- 共享模型放在 `zhi_common` 中

### 4. 脚本管理
- 所有管理脚本统一放在 `backend/zhi_scripts/` 目录
- 脚本支持从项目根目录直接运行
- 提供Windows批处理文件支持
- 包含详细的帮助信息和错误处理

### 5. 日志管理
- 使用统一的日志SDK：`from zhi_common.zhi_logger import get_logger`
- 每个模块创建自己的日志记录器：`logger = get_logger(__name__)`
- 支持WebSocket实时日志推送
- 自动记录API请求和系统事件

### 6. 测试策略
- 为每个子项目编写独立的测试
- 使用集成测试验证服务间的交互
- 自动化测试流程
- 使用项目检查脚本验证配置

### 7. 开发工作流
```bash
# 1. 检查项目状态
python backend/zhi_scripts/check_project.py --project zhi_logger

# 2. 执行数据库迁移
python backend/zhi_scripts/manage_projects.py migrate zhi_logger

# 3. 启动开发服务器
python backend/zhi_scripts/start_logger.py --debug --reload

# 4. 查看项目状态
python backend/zhi_scripts/manage_projects.py status
```

## 📚 相关文档

- **脚本使用说明**: `backend/zhi_scripts/README.md`
- **日志SDK文档**: `backend/zhi_common/zhi_logger/README.md`
- **API文档**: 启动服务后访问 `/docs` 端点

## 🔗 架构优势

这种架构设计提供了：

1. **模块化**: 每个子项目独立开发和部署
2. **可扩展性**: 易于添加新的子项目
3. **统一管理**: 通过 `application` 统一配置管理
4. **灵活部署**: 可以选择性部署需要的服务
5. **开发友好**: 丰富的管理脚本和工具
6. **维护性**: 清晰的目录结构和文档

适合大型项目的开发和部署，支持微服务化的发展方向。
