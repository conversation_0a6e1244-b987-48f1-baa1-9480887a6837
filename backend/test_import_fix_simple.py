"""
简化的认证导入路径修复验证 - 只检查文件内容
"""

import os

def test_import_fix_simple():
    """简化的导入修复验证"""
    
    print("=== 认证导入路径修复验证（文件检查） ===")
    
    # 检查需要修复的文件
    files_to_check = [
        {
            "path": "zhi_common/zhi_services/enhanced_api_expose.py",
            "name": "API暴露装饰器",
            "checks": [
                ("from zhi_common.zhi_auth.core_auth import GlobalOAuth2", "正确导入GlobalOAuth2"),
                ("from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager", "正确导入权限管理器"),
                ("PERMISSION_SYSTEM_AVAILABLE = True", "权限系统已启用"),
                ("global_oauth2 = GlobalOAuth2()", "创建OAuth2实例"),
                ("auth=global_oauth2", "使用正确的认证实例"),
            ]
        },
        {
            "path": "zhi_common/zhi_services/enhanced_model_service.py", 
            "name": "增强型模型服务",
            "checks": [
                ("from zhi_common.zhi_auth.core_auth import GlobalOAuth2", "正确导入GlobalOAuth2"),
                ("from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager", "正确导入权限管理器"),
                ("from zhi_common.zhi_auth.permission_audit_system import permission_audit_logger", "正确导入审计日志"),
                ("PERMISSION_SYSTEM_AVAILABLE = True", "权限系统已启用"),
            ]
        },
        {
            "path": "zhi_oauth/services/example_product.py",
            "name": "示例产品服务", 
            "checks": [
                ("from zhi_common.zhi_auth.core_auth import GlobalOAuth2", "正确导入GlobalOAuth2"),
                ("from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager", "正确导入权限管理器"),
                ("PERMISSION_SYSTEM_AVAILABLE = True", "权限系统已启用"),
            ]
        },
        {
            "path": "zhi_oauth/controllers/example_product.py",
            "name": "示例产品控制器",
            "checks": [
                ("from zhi_common.zhi_auth.core_auth import GlobalOAuth2", "正确导入GlobalOAuth2"),
                ("from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager", "正确导入权限管理器"),
                ("PERMISSION_SYSTEM_AVAILABLE = True", "权限系统已启用"),
                ("def require_permission(permission_code):", "权限装饰器定义"),
                ("_permission_required", "权限标记设置"),
            ]
        }
    ]
    
    total_checks = 0
    passed_checks = 0
    
    for file_info in files_to_check:
        file_path = file_info["path"]
        file_name = file_info["name"]
        checks = file_info["checks"]
        
        print(f"\n--- 检查 {file_name} ({file_path}) ---")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_passed = 0
            for check_text, check_desc in checks:
                total_checks += 1
                if check_text in content:
                    print(f"   ✅ {check_desc}")
                    passed_checks += 1
                    file_passed += 1
                else:
                    print(f"   ❌ {check_desc} - 未找到: {check_text}")
            
            print(f"   文件检查: {file_passed}/{len(checks)} ({file_passed/len(checks)*100:.1f}%)")
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
    
    print(f"\n=== 总体检查结果 ===")
    print(f"通过检查: {passed_checks}/{total_checks} ({passed_checks/total_checks*100:.1f}%)")
    
    # 检查是否移除了try-except兼容性代码
    print(f"\n=== 兼容性代码移除检查 ===")
    
    removed_patterns = [
        "try:",
        "except ImportError:",
        "zhi_logger.warning(\"权限管理系统",
        "PERMISSION_SYSTEM_AVAILABLE = False"
    ]
    
    compatibility_removed = True
    for file_info in files_to_check:
        file_path = file_info["path"]
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for pattern in removed_patterns:
                    if pattern in content and "权限管理系统" in pattern:
                        print(f"⚠️  {file_info['name']}: 仍包含兼容性代码")
                        compatibility_removed = False
                        break
            except:
                pass
    
    if compatibility_removed:
        print("✅ 兼容性代码已移除")
    
    print(f"\n=== 修复效果分析 ===")
    
    if passed_checks >= total_checks * 0.9:
        print("🎉 认证导入路径修复成功！")
        print("✅ 所有文件都使用正确的导入路径")
        print("✅ 权限系统已正确启用")
        print("✅ 移除了try-except兼容性代码")
        
        print(f"\n📋 修复内容总结:")
        print("1. ✅ 修复导入路径: zhi_common.zhi_auth.core_auth")
        print("2. ✅ 启用权限系统: PERMISSION_SYSTEM_AVAILABLE = True")
        print("3. ✅ 移除兼容性代码: 不再使用try-except")
        print("4. ✅ 统一认证实例: 使用GlobalOAuth2()")
        
        print(f"\n🔒 预期效果:")
        print("- API文档中应该显示🔒锁标识")
        print("- 权限检查功能应该正常工作")
        print("- require_permission装饰器应该生效")
        print("- 认证和授权流程应该完整")
        
        print(f"\n🔧 下一步操作:")
        print("1. 重启Django开发服务器")
        print("2. 访问 http://127.0.0.1:8000/api/docs")
        print("3. 检查API端点是否显示🔒锁标识")
        print("4. 测试API调用是否需要认证")
        
        return True
    else:
        print("⚠️  认证导入路径修复不完整")
        print(f"需要检查未通过的项目并进一步修复")
        return False

if __name__ == "__main__":
    success = test_import_fix_simple()
    if success:
        print("\n✅ 验证通过 - 🔒锁标识显示问题应该已解决")
    else:
        print("\n❌ 验证失败 - 需要进一步修复导入问题")
