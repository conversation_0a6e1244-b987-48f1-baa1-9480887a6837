#!/usr/bin/env python
"""
测试 Django Channels 配置
"""
import os
import sys
import django

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.interval_admin')
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

django.setup()

def test_channels_installation():
    """测试 Channels 安装"""
    try:
        import channels
        print(f"✓ Django Channels 已安装，版本: {channels.__version__}")
        return True
    except ImportError as e:
        print(f"✗ Django Channels 未安装: {e}")
        return False

def test_channels_redis():
    """测试 Channels Redis 后端"""
    try:
        import channels_redis
        print(f"✓ Channels Redis 已安装，版本: {channels_redis.__version__}")
        return True
    except ImportError as e:
        print(f"✗ Channels Redis 未安装: {e}")
        return False

def test_redis_connection():
    """测试 Redis 连接"""
    try:
        import redis
        from django.conf import settings
        
        # 从 Django 设置获取 Redis 配置
        redis_url = getattr(settings, 'REDIS_URL', None)
        if not redis_url:
            print("✗ REDIS_URL 未配置")
            return False
            
        print(f"Redis URL: {redis_url}")
        
        # 测试连接
        r = redis.from_url(redis_url)
        r.ping()
        print("✓ Redis 连接成功")
        return True
    except Exception as e:
        print(f"✗ Redis 连接失败: {e}")
        return False

def test_channel_layer():
    """测试 Channel Layer"""
    try:
        from channels.layers import get_channel_layer
        
        channel_layer = get_channel_layer()
        if channel_layer is None:
            print("✗ Channel Layer 未配置")
            return False
            
        print(f"✓ Channel Layer 配置成功: {type(channel_layer).__name__}")
        return True
    except Exception as e:
        print(f"✗ Channel Layer 配置失败: {e}")
        return False

def test_asgi_application():
    """测试 ASGI 应用"""
    try:
        from django.conf import settings
        asgi_app = getattr(settings, 'ASGI_APPLICATION', None)
        if not asgi_app:
            print("✗ ASGI_APPLICATION 未配置")
            return False
            
        print(f"✓ ASGI_APPLICATION 配置: {asgi_app}")
        
        # 尝试导入 ASGI 应用
        from application.asgi import application
        print("✓ ASGI 应用导入成功")
        return True
    except Exception as e:
        print(f"✗ ASGI 应用导入失败: {e}")
        return False

def test_websocket_routing():
    """测试 WebSocket 路由"""
    try:
        from application.routing import websocket_urlpatterns
        print(f"✓ WebSocket 路由配置成功，共 {len(websocket_urlpatterns)} 个路由")
        
        for pattern in websocket_urlpatterns:
            print(f"  - {pattern.pattern}")
        return True
    except Exception as e:
        print(f"✗ WebSocket 路由配置失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== Django Channels 配置测试 ===\n")
    
    tests = [
        ("Channels 安装", test_channels_installation),
        ("Channels Redis", test_channels_redis),
        ("Redis 连接", test_redis_connection),
        ("Channel Layer", test_channel_layer),
        ("ASGI 应用", test_asgi_application),
        ("WebSocket 路由", test_websocket_routing),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n=== 测试结果汇总 ===")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！Django Channels 配置正确。")
    else:
        print("\n⚠️  部分测试失败，请检查配置。")

if __name__ == '__main__':
    main()
