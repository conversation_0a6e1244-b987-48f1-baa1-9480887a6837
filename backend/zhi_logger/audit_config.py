"""
Z<PERSON><PERSON>ogger 子项目审计配置

每个子项目可以独立配置自己的审计日志，不依赖全局配置
配置格式与全局配置保持一致，但只影响当前子项目
"""

# ZhiLogger 子项目的审计日志配置
AUDIT_LOG_CONFIG = {
    # SystemLog 模型配置
    'SystemLog': {
        'is_enabled': True,
        'using_fields': {
            'level': {
                'field_name': '日志级别',
                'is_enabled': True,
                'is_important': True,
            },
            'message': {
                'field_name': '日志消息',
                'is_enabled': True,
                'is_important': True,
            },
            'category': {
                'field_name': '日志分类',
                'is_enabled': True,
                'is_important': True,
            },
            'module_name': {
                'field_name': '模块名称',
                'is_enabled': True,
                'is_important': False,
            },
            'user_id': {
                'field_name': '用户ID',
                'is_enabled': True,
                'is_important': True,
            },
            'trace_id': {
                'field_name': '追踪ID',
                'is_enabled': True,
                'is_important': False,
            },
            'extra_data': {
                'field_name': '额外数据',
                'is_enabled': True,
                'is_important': False,
            },
            'ip_address': {
                'field_name': 'IP地址',
                'is_enabled': True,
                'is_important': False,
            },
            'user_agent': {
                'field_name': '用户代理',
                'is_enabled': False,  # 不记录用户代理变更
                'is_important': False,
            },
        }
    },
    
    # LogCategory 模型配置
    'LogCategory': {
        'is_enabled': True,
        'using_fields': {
            'name': {
                'field_name': '分类名称',
                'is_enabled': True,
                'is_important': True,
            },
            'description': {
                'field_name': '分类描述',
                'is_enabled': True,
                'is_important': False,
            },
            'is_active': {
                'field_name': '是否激活',
                'is_enabled': True,
                'is_important': True,
            },
            'retention_days': {
                'field_name': '保留天数',
                'is_enabled': True,
                'is_important': True,
            },
        }
    },
    
    # LogStatistics 模型配置
    'LogStatistics': {
        'is_enabled': False,  # 统计数据不需要审计
        'using_fields': {}
    },
    
    # WebSocketConnection 模型配置
    'WebSocketConnection': {
        'is_enabled': True,
        'using_fields': {
            'channel_name': {
                'field_name': '频道名称',
                'is_enabled': True,
                'is_important': True,
            },
            'user_id': {
                'field_name': '用户ID',
                'is_enabled': True,
                'is_important': True,
            },
            'connected_at': {
                'field_name': '连接时间',
                'is_enabled': True,
                'is_important': False,
            },
            'disconnected_at': {
                'field_name': '断开时间',
                'is_enabled': True,
                'is_important': False,
            },
            'is_active': {
                'field_name': '是否活跃',
                'is_enabled': True,
                'is_important': True,
            },
        }
    },
}

# 审计配置的元数据
AUDIT_CONFIG_META = {
    'version': '1.0.0',
    'project': 'zhi_logger',
    'description': 'ZhiLogger 子项目审计配置',
    'last_updated': '2025-07-22',
    'maintainer': 'ZhiAdmin Team',
    
    # 配置选项
    'options': {
        # 是否启用批量操作审计
        'enable_bulk_audit': True,
        
        # 是否记录查询操作
        'enable_query_audit': False,
        
        # 审计日志保留天数
        'audit_retention_days': 90,
        
        # 是否异步记录审计日志
        'async_audit': True,
        
        # 是否使用Celery任务
        'use_celery': True,
        
        # 审计日志级别
        'audit_log_level': 'INFO',
        
        # 是否记录敏感字段变更
        'log_sensitive_fields': False,
        
        # 敏感字段列表
        'sensitive_fields': [
            'password',
            'token',
            'secret',
            'key',
        ],
    }
}

# 动态配置函数
def get_audit_config_for_model(model_name: str) -> dict:
    """
    获取指定模型的审计配置
    
    Args:
        model_name: 模型名称
        
    Returns:
        模型的审计配置字典
    """
    return AUDIT_LOG_CONFIG.get(model_name, {})


def is_field_audit_enabled(model_name: str, field_name: str) -> bool:
    """
    检查指定字段是否启用审计
    
    Args:
        model_name: 模型名称
        field_name: 字段名称
        
    Returns:
        是否启用审计
    """
    model_config = get_audit_config_for_model(model_name)
    using_fields = model_config.get('using_fields', {})
    field_config = using_fields.get(field_name, {})
    return field_config.get('is_enabled', False)


def is_field_important(model_name: str, field_name: str) -> bool:
    """
    检查指定字段是否为重要字段
    
    Args:
        model_name: 模型名称
        field_name: 字段名称
        
    Returns:
        是否为重要字段
    """
    model_config = get_audit_config_for_model(model_name)
    using_fields = model_config.get('using_fields', {})
    field_config = using_fields.get(field_name, {})
    return field_config.get('is_important', False)


def get_field_display_name(model_name: str, field_name: str) -> str:
    """
    获取字段的显示名称
    
    Args:
        model_name: 模型名称
        field_name: 字段名称
        
    Returns:
        字段的显示名称
    """
    model_config = get_audit_config_for_model(model_name)
    using_fields = model_config.get('using_fields', {})
    field_config = using_fields.get(field_name, {})
    return field_config.get('field_name', field_name)


# 配置验证函数
def validate_audit_config() -> bool:
    """
    验证审计配置的有效性
    
    Returns:
        配置是否有效
    """
    try:
        # 检查配置结构
        if not isinstance(AUDIT_LOG_CONFIG, dict):
            return False
        
        # 检查每个模型配置
        for model_name, model_config in AUDIT_LOG_CONFIG.items():
            if not isinstance(model_config, dict):
                return False
            
            # 检查必需字段
            if 'is_enabled' not in model_config:
                return False
            
            if 'using_fields' not in model_config:
                return False
            
            # 检查字段配置
            using_fields = model_config['using_fields']
            if not isinstance(using_fields, dict):
                return False
            
            for field_name, field_config in using_fields.items():
                if not isinstance(field_config, dict):
                    return False
                
                # 检查字段配置的必需属性
                required_attrs = ['field_name', 'is_enabled', 'is_important']
                for attr in required_attrs:
                    if attr not in field_config:
                        return False
        
        return True
        
    except Exception:
        return False


# 在模块加载时验证配置
if not validate_audit_config():
    import warnings
    warnings.warn(
        "ZhiLogger 审计配置验证失败，请检查配置格式",
        UserWarning
    )
