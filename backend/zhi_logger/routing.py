"""
WebSocket路由配置 - 日志实时推送
"""
from django.urls import re_path
from . import consumers

websocket_urlpatterns = [
    # 通用日志WebSocket
    re_path(r'ws/logs/$', consumers.LogConsumer.as_asgi()),
    
    # 系统日志WebSocket (仅管理员)
    re_path(r'ws/logs/system/$', consumers.SystemLogConsumer.as_asgi()),
    
    # 模块特定日志WebSocket
    re_path(r'ws/logs/module/(?P<module_name>\w+)/$', consumers.ModuleLogConsumer.as_asgi()),
]
