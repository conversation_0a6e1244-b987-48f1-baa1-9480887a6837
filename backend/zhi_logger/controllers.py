"""
日志管理API控制器
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List
from ninja_extra import api_controller, http_get, http_post, http_delete
from ninja_extra.permissions import IsAuthenticated
from ninja import Schema, Query
from django.http import HttpRequest
from django.utils import timezone

from .services import log_service
from zhi_common.zhi_logger.base import LogLevel, LogCategory


class LogQuerySchema(Schema):
    """日志查询参数"""
    level: Optional[str] = None
    category: Optional[str] = None
    module_name: Optional[str] = None
    user_id: Optional[str] = None
    trace_id: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    search: Optional[str] = None
    page: int = 1
    page_size: int = 50


class ApiLogQuerySchema(Schema):
    """API日志查询参数"""
    method: Optional[str] = None
    path: Optional[str] = None
    status_code: Optional[int] = None
    user_id: Optional[str] = None
    ip_address: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    page: int = 1
    page_size: int = 50


class LogCreateSchema(Schema):
    """创建日志参数"""
    level: str
    message: str
    module_name: str
    category: str = LogCategory.SYSTEM
    extra_data: Optional[dict] = None


class LogResponseSchema(Schema):
    """日志响应"""
    id: str
    level: str
    category: str
    module_name: str
    message: str
    trace_id: Optional[str]
    user_id: Optional[str]
    ip_address: Optional[str]
    created_at: datetime
    extra_data: Optional[dict]


class ApiLogResponseSchema(Schema):
    """API日志响应"""
    id: str
    method: str
    path: str
    response_status: int
    response_time: Optional[float]
    user_id: Optional[str]
    ip_address: Optional[str]
    created_at: datetime
    trace_id: Optional[str]


class LogListResponseSchema(Schema):
    """日志列表响应"""
    logs: List[LogResponseSchema]
    total: int
    page: int
    page_size: int
    total_pages: int


class ApiLogListResponseSchema(Schema):
    """API日志列表响应"""
    logs: List[ApiLogResponseSchema]
    total: int
    page: int
    page_size: int
    total_pages: int


class LogStatisticsSchema(Schema):
    """日志统计响应"""
    system_logs: dict
    api_logs: dict
    module_logs: dict
    period: str
    start_time: datetime
    end_time: datetime


@api_controller('/logs', tags=['日志管理'], permissions=[IsAuthenticated])
class LogController:
    """日志管理控制器"""
    
    @http_get('/system', response=LogListResponseSchema)
    def get_system_logs(self, request: HttpRequest, query: LogQuerySchema = Query(...)):
        """获取系统日志列表"""
        result = log_service.query_system_logs(
            level=query.level,
            category=query.category,
            module_name=query.module_name,
            user_id=query.user_id,
            trace_id=query.trace_id,
            start_time=query.start_time,
            end_time=query.end_time,
            search=query.search,
            page=query.page,
            page_size=query.page_size
        )
        return result
    
    @http_get('/api', response=ApiLogListResponseSchema)
    def get_api_logs(self, request: HttpRequest, query: ApiLogQuerySchema = Query(...)):
        """获取API访问日志列表"""
        result = log_service.query_api_logs(
            method=query.method,
            path=query.path,
            status_code=query.status_code,
            user_id=query.user_id,
            ip_address=query.ip_address,
            start_time=query.start_time,
            end_time=query.end_time,
            page=query.page,
            page_size=query.page_size
        )
        return result
    
    @http_get('/statistics', response=LogStatisticsSchema)
    def get_log_statistics(self, request: HttpRequest, days: int = Query(7, description="统计天数")):
        """获取日志统计信息"""
        return log_service.get_log_statistics(days=days)
    
    @http_post('/system')
    async def create_system_log(self, request: HttpRequest, data: LogCreateSchema):
        """创建系统日志"""
        user_id = getattr(request.user, 'id', None) if hasattr(request, 'user') else None
        
        log = await log_service.create_system_log(
            level=data.level,
            message=data.message,
            module_name=data.module_name,
            category=data.category,
            user_id=user_id,
            extra_data=data.extra_data
        )
        
        return {
            'success': True,
            'message': '日志创建成功',
            'log_id': log.id
        }
    
    @http_get('/levels')
    def get_log_levels(self, request: HttpRequest):
        """获取日志级别列表"""
        return {
            'levels': [
                {'value': choice[0], 'label': choice[1]}
                for choice in LogLevel.choices
            ]
        }
    
    @http_get('/categories')
    def get_log_categories(self, request: HttpRequest):
        """获取日志分类列表"""
        return {
            'categories': [
                {'value': choice[0], 'label': choice[1]}
                for choice in LogCategory.choices
            ]
        }
    
    @http_get('/modules')
    def get_log_modules(self, request: HttpRequest):
        """获取日志模块列表"""
        from .models import SystemLog
        
        modules = SystemLog.objects.values_list('module_name', flat=True).distinct()
        return {
            'modules': list(modules)
        }
    
    @http_delete('/cleanup')
    async def cleanup_old_logs(self, request: HttpRequest, days: int = Query(30, description="保留天数")):
        """清理旧日志"""
        # 检查权限
        if not request.user.is_superuser:
            return {
                'success': False,
                'message': '权限不足，只有超级管理员可以执行此操作'
            }
        
        result = await log_service.cleanup_old_logs(days=days)
        
        return {
            'success': True,
            'message': f'日志清理完成',
            'result': result
        }


@api_controller('/logs/realtime', tags=['实时日志'], permissions=[IsAuthenticated])
class RealtimeLogController:
    """实时日志控制器"""
    
    @http_get('/token')
    def get_websocket_token(self, request: HttpRequest):
        """获取WebSocket连接令牌"""
        # 生成WebSocket连接令牌
        from django.core.signing import Signer
        
        signer = Signer()
        token_data = {
            'user_id': request.user.id,
            'timestamp': timezone.now().timestamp()
        }
        token = signer.sign_object(token_data)
        
        return {
            'success': True,
            'token': token,
            'websocket_url': '/ws/logs/',
            'expires_in': 3600  # 1小时
        }
    
    @http_get('/channels')
    def get_available_channels(self, request: HttpRequest):
        """获取可用的日志频道"""
        channels = ['system', 'api', 'error']
        
        # 根据用户权限过滤频道
        if request.user.has_perm('zhi_logger.view_security_logs'):
            channels.append('security')
        
        if request.user.is_superuser:
            channels.extend(['admin', 'debug'])
        
        return {
            'success': True,
            'channels': channels
        }
