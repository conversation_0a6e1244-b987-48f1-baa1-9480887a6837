"""
审计日志 API

提供审计日志的查询、统计和管理功能
"""

from typing import List, Optional
from datetime import datetime, timedelta
from django.db.models import Q, Count
from django.utils import timezone
from ninja import Query
from ninja_extra import api_controller, http_get, paginate
from ninja_extra.pagination import PageNumberPaginationExtra

from zhi_common.zhi_logger import get_logger
from zhi_logger.models import AuditLog
from zhi_logger.schemas.audit_log_schemas import (
    AuditLogListSchema,
    AuditLogDetailSchema,
    AuditLogStatsSchema,
    AuditLogFilterSchema
)

logger = get_logger("audit_log_api")


@api_controller('/audit-logs', tags=['审计日志'])
class AuditLogController:
    """审计日志控制器"""
    
    @http_get('/', response=List[AuditLogListSchema])
    @paginate(PageNumberPaginationExtra, page_size=20)
    def list_audit_logs(
        self,
        request,
        filters: AuditLogFilterSchema = Query(...)
    ):
        """
        获取审计日志列表
        
        支持按资源ID、操作类型、用户、时间范围等条件过滤
        """
        try:
            queryset = AuditLog.objects.all()
            
            # 应用过滤条件
            if filters.resource_id:
                queryset = queryset.filter(resource_id__icontains=filters.resource_id)
            
            if filters.action:
                queryset = queryset.filter(action=filters.action)
            
            if filters.creator_id:
                queryset = queryset.filter(creator_id=filters.creator_id)
            
            if filters.creator_name:
                queryset = queryset.filter(creator_name__icontains=filters.creator_name)
            
            if filters.start_date:
                queryset = queryset.filter(created_at__gte=filters.start_date)
            
            if filters.end_date:
                queryset = queryset.filter(created_at__lte=filters.end_date)
            
            if filters.is_sensitive is not None:
                # 过滤敏感操作
                if filters.is_sensitive:
                    sensitive_actions = ['删除', '硬删除', '权限变更']
                    queryset = queryset.filter(action__in=sensitive_actions)
                else:
                    sensitive_actions = ['删除', '硬删除', '权限变更']
                    queryset = queryset.exclude(action__in=sensitive_actions)
            
            # 按创建时间倒序排列
            queryset = queryset.order_by('-created_at')
            
            logger.info(
                f"查询审计日志列表",
                category="audit",
                user_id=getattr(request.user, 'id', None),
                extra_data={
                    'filters': filters.dict(),
                    'count': queryset.count()
                }
            )
            
            return queryset
            
        except Exception as e:
            logger.error(
                f"查询审计日志列表失败: {str(e)}",
                category="audit",
                extra_data={'error': str(e)}
            )
            raise
    
    @http_get('/{audit_log_id}', response=AuditLogDetailSchema)
    def get_audit_log_detail(self, request, audit_log_id: str):
        """获取审计日志详情"""
        try:
            audit_log = AuditLog.objects.get(id=audit_log_id)
            
            logger.info(
                f"查询审计日志详情: {audit_log_id}",
                category="audit",
                user_id=getattr(request.user, 'id', None),
                extra_data={'audit_log_id': audit_log_id}
            )
            
            return audit_log
            
        except AuditLog.DoesNotExist:
            logger.warning(
                f"审计日志不存在: {audit_log_id}",
                category="audit",
                user_id=getattr(request.user, 'id', None)
            )
            raise
    
    @http_get('/resource/{resource_id}', response=List[AuditLogListSchema])
    @paginate(PageNumberPaginationExtra, page_size=20)
    def get_resource_history(self, request, resource_id: str):
        """获取指定资源的变更历史"""
        try:
            queryset = AuditLog.objects.get_resource_history(resource_id)
            
            logger.info(
                f"查询资源变更历史: {resource_id}",
                category="audit",
                user_id=getattr(request.user, 'id', None),
                extra_data={
                    'resource_id': resource_id,
                    'count': queryset.count()
                }
            )
            
            return queryset
            
        except Exception as e:
            logger.error(
                f"查询资源变更历史失败: {str(e)}",
                category="audit",
                extra_data={
                    'resource_id': resource_id,
                    'error': str(e)
                }
            )
            raise
    
    @http_get('/user/{creator_id}', response=List[AuditLogListSchema])
    @paginate(PageNumberPaginationExtra, page_size=20)
    def get_user_actions(self, request, creator_id: str):
        """获取指定用户的操作历史"""
        try:
            queryset = AuditLog.objects.get_user_actions(creator_id)
            
            logger.info(
                f"查询用户操作历史: {creator_id}",
                category="audit",
                user_id=getattr(request.user, 'id', None),
                extra_data={
                    'target_user_id': creator_id,
                    'count': queryset.count()
                }
            )
            
            return queryset
            
        except Exception as e:
            logger.error(
                f"查询用户操作历史失败: {str(e)}",
                category="audit",
                extra_data={
                    'target_user_id': creator_id,
                    'error': str(e)
                }
            )
            raise
    
    @http_get('/stats', response=AuditLogStatsSchema)
    def get_audit_log_stats(
        self,
        request,
        days: int = Query(7, description="统计天数，默认7天")
    ):
        """获取审计日志统计信息"""
        try:
            # 计算时间范围
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # 基础查询集
            queryset = AuditLog.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            )
            
            # 总数统计
            total_count = queryset.count()
            
            # 按操作类型统计
            action_stats = list(
                queryset.values('action')
                .annotate(count=Count('id'))
                .order_by('-count')
            )
            
            # 按用户统计
            user_stats = list(
                queryset.values('creator_id', 'creator_name')
                .annotate(count=Count('id'))
                .order_by('-count')[:10]  # 只取前10个用户
            )
            
            # 敏感操作统计
            sensitive_actions = ['删除', '硬删除', '权限变更']
            sensitive_count = queryset.filter(action__in=sensitive_actions).count()
            
            # 按日期统计
            daily_stats = []
            for i in range(days):
                day_start = start_date + timedelta(days=i)
                day_end = day_start + timedelta(days=1)
                day_count = queryset.filter(
                    created_at__gte=day_start,
                    created_at__lt=day_end
                ).count()
                daily_stats.append({
                    'date': day_start.date(),
                    'count': day_count
                })
            
            stats = {
                'total_count': total_count,
                'sensitive_count': sensitive_count,
                'action_stats': action_stats,
                'user_stats': user_stats,
                'daily_stats': daily_stats,
                'start_date': start_date,
                'end_date': end_date,
                'days': days
            }
            
            logger.info(
                f"查询审计日志统计信息",
                category="audit",
                user_id=getattr(request.user, 'id', None),
                extra_data={
                    'days': days,
                    'total_count': total_count,
                    'sensitive_count': sensitive_count
                }
            )
            
            return stats
            
        except Exception as e:
            logger.error(
                f"查询审计日志统计失败: {str(e)}",
                category="audit",
                extra_data={
                    'days': days,
                    'error': str(e)
                }
            )
            raise
    
    @http_get('/sensitive', response=List[AuditLogListSchema])
    @paginate(PageNumberPaginationExtra, page_size=20)
    def get_sensitive_operations(self, request):
        """获取敏感操作日志"""
        try:
            sensitive_actions = ['删除', '硬删除', '权限变更']
            queryset = AuditLog.objects.filter(
                action__in=sensitive_actions
            ).order_by('-created_at')
            
            logger.info(
                f"查询敏感操作日志",
                category="audit",
                user_id=getattr(request.user, 'id', None),
                extra_data={'count': queryset.count()}
            )
            
            return queryset
            
        except Exception as e:
            logger.error(
                f"查询敏感操作日志失败: {str(e)}",
                category="audit",
                extra_data={'error': str(e)}
            )
            raise
