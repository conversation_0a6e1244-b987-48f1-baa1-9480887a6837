"""
日志服务层 - 提供日志管理和查询功能
"""
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.utils import timezone
try:
    from channels.layers import get_channel_layer
    from asgiref.sync import async_to_sync
    CHANNELS_AVAILABLE = True
except ImportError:
    get_channel_layer = None
    async_to_sync = None
    CHANNELS_AVAILABLE = False

from zhi_common.zhi_logger.base import LogLevel, LogCategory
from .models import SystemLog, ApiAccessLog


class LogService:
    """日志服务类"""
    
    def __init__(self):
        self.channel_layer = get_channel_layer() if CHANNELS_AVAILABLE else None
    
    async def create_system_log(
        self,
        level: str,
        message: str,
        module_name: str,
        category: str = LogCategory.SYSTEM,
        user_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        extra_data: Optional[Dict] = None,
        content_object: Optional[Any] = None
    ) -> SystemLog:
        """创建系统日志"""
        log_data = {
            'level': level,
            'message': message,
            'module_name': module_name,
            'category': category,
            'user_id': user_id,
            'trace_id': trace_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'extra_data': extra_data or {},
        }
        
        if content_object:
            log_data['content_object'] = content_object
            
        log = await SystemLog.objects.acreate(**log_data)
        
        # 发送WebSocket通知
        await self._send_log_notification(log)
        
        return log
    
    async def create_api_log(
        self,
        method: str,
        path: str,
        response_status: int,
        user_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        query_params: Optional[Dict] = None,
        request_body: Optional[str] = None,
        response_body: Optional[str] = None,
        response_time: Optional[float] = None
    ) -> ApiAccessLog:
        """创建API访问日志"""
        log_data = {
            'method': method,
            'path': path,
            'response_status': response_status,
            'user_id': user_id,
            'trace_id': trace_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'query_params': query_params or {},
            'request_body': request_body,
            'response_body': response_body,
            'response_time': response_time,
        }
        
        log = await ApiAccessLog.objects.acreate(**log_data)
        
        # 发送WebSocket通知
        await self._send_api_log_notification(log)
        
        return log
    
    async def _send_log_notification(self, log: SystemLog):
        """发送系统日志WebSocket通知"""
        if not self.channel_layer:
            return
            
        log_data = {
            'id': log.id,
            'level': log.level,
            'category': log.category,
            'module_name': log.module_name,
            'message': log.message,
            'trace_id': log.trace_id,
            'user_id': log.user_id,
            'timestamp': log.created_at.isoformat(),
            'extra_data': log.extra_data,
        }
        
        # 发送到通用日志频道
        await self.channel_layer.group_send(
            "logs",
            {
                "type": "log_message",
                "data": log_data
            }
        )
        
        # 发送到模块特定频道
        await self.channel_layer.group_send(
            f"logs_{log.module_name}",
            {
                "type": "log_message",
                "data": log_data
            }
        )
        
        # 错误日志发送到错误频道
        if log.level in [LogLevel.ERROR, LogLevel.CRITICAL]:
            await self.channel_layer.group_send(
                "logs_error",
                {
                    "type": "log_message",
                    "data": log_data
                }
            )
    
    async def _send_api_log_notification(self, log: ApiAccessLog):
        """发送API日志WebSocket通知"""
        if not self.channel_layer:
            return
            
        log_data = {
            'id': log.id,
            'method': log.method,
            'path': log.path,
            'response_status': log.response_status,
            'response_time': log.response_time,
            'user_id': log.user_id,
            'ip_address': str(log.ip_address) if log.ip_address else None,
            'timestamp': log.created_at.isoformat(),
            'trace_id': log.trace_id,
        }
        
        # 发送到API日志频道
        await self.channel_layer.group_send(
            "logs_api",
            {
                "type": "log_message",
                "data": log_data
            }
        )
    
    def query_system_logs(
        self,
        level: Optional[str] = None,
        category: Optional[str] = None,
        module_name: Optional[str] = None,
        user_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        search: Optional[str] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """查询系统日志"""
        queryset = SystemLog.objects.all()
        
        # 构建过滤条件
        filters = Q()
        
        if level:
            filters &= Q(level=level)
        if category:
            filters &= Q(category=category)
        if module_name:
            filters &= Q(module_name=module_name)
        if user_id:
            filters &= Q(user_id=user_id)
        if trace_id:
            filters &= Q(trace_id=trace_id)
        if start_time:
            filters &= Q(created_at__gte=start_time)
        if end_time:
            filters &= Q(created_at__lte=end_time)
        if search:
            filters &= Q(message__icontains=search)
            
        queryset = queryset.filter(filters)
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        logs = []
        for log in page_obj:
            logs.append({
                'id': log.id,
                'level': log.level,
                'category': log.category,
                'module_name': log.module_name,
                'message': log.message,
                'trace_id': log.trace_id,
                'user_id': log.user_id,
                'ip_address': str(log.ip_address) if log.ip_address else None,
                'created_at': log.created_at.isoformat(),
                'extra_data': log.extra_data,
            })
        
        return {
            'logs': logs,
            'total': paginator.count,
            'page': page,
            'page_size': page_size,
            'total_pages': paginator.num_pages,
        }
    
    def query_api_logs(
        self,
        method: Optional[str] = None,
        path: Optional[str] = None,
        status_code: Optional[int] = None,
        user_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """查询API访问日志"""
        queryset = ApiAccessLog.objects.all()
        
        # 构建过滤条件
        filters = Q()
        
        if method:
            filters &= Q(method=method)
        if path:
            filters &= Q(path__icontains=path)
        if status_code:
            filters &= Q(response_status=status_code)
        if user_id:
            filters &= Q(user_id=user_id)
        if ip_address:
            filters &= Q(ip_address=ip_address)
        if start_time:
            filters &= Q(created_at__gte=start_time)
        if end_time:
            filters &= Q(created_at__lte=end_time)
            
        queryset = queryset.filter(filters)
        
        # 分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 序列化数据
        logs = []
        for log in page_obj:
            logs.append({
                'id': log.id,
                'method': log.method,
                'path': log.path,
                'response_status': log.response_status,
                'response_time': log.response_time,
                'user_id': log.user_id,
                'ip_address': str(log.ip_address) if log.ip_address else None,
                'created_at': log.created_at.isoformat(),
                'trace_id': log.trace_id,
            })
        
        return {
            'logs': logs,
            'total': paginator.count,
            'page': page,
            'page_size': page_size,
            'total_pages': paginator.num_pages,
        }
    
    def get_log_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取日志统计信息"""
        end_time = timezone.now()
        start_time = end_time - timedelta(days=days)
        
        # 系统日志统计
        system_stats = SystemLog.objects.filter(
            created_at__gte=start_time
        ).values('level').annotate(count=Count('id'))
        
        # API日志统计
        api_stats = ApiAccessLog.objects.filter(
            created_at__gte=start_time
        ).values('response_status').annotate(count=Count('id'))
        
        # 模块日志统计
        module_stats = SystemLog.objects.filter(
            created_at__gte=start_time
        ).values('module_name').annotate(count=Count('id'))
        
        return {
            'system_logs': {item['level']: item['count'] for item in system_stats},
            'api_logs': {str(item['response_status']): item['count'] for item in api_stats},
            'module_logs': {item['module_name']: item['count'] for item in module_stats},
            'period': f'{days} days',
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
        }
    
    async def cleanup_old_logs(self, days: int = 30):
        """清理旧日志"""
        cutoff_time = timezone.now() - timedelta(days=days)
        
        # 删除旧的系统日志
        system_deleted = await SystemLog.objects.filter(
            created_at__lt=cutoff_time
        ).adelete()
        
        # 删除旧的API日志
        api_deleted = await ApiAccessLog.objects.filter(
            created_at__lt=cutoff_time
        ).adelete()
        
        return {
            'system_logs_deleted': system_deleted[0],
            'api_logs_deleted': api_deleted[0],
            'cutoff_time': cutoff_time.isoformat(),
        }


# 全局日志服务实例
log_service = LogService()
