from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey

from zhi_common.zhi_model.core_model import ZhiCoreModel


table_prefix = 'zhi_log_'


class LogLevel(models.TextChoices):
    """日志级别"""
    DEBUG = 'DEBUG', 'Debug'
    INFO = 'INFO', 'Info'
    WARNING = 'WARNING', 'Warning'
    ERROR = 'ERROR', 'Error'
    CRITICAL = 'CRITICAL', 'Critical'


class LogCategory(models.TextChoices):
    """日志分类"""
    SYSTEM = 'system', '系统'
    API = 'api', 'API'
    AUTH = 'auth', '认证'
    DATABASE = 'database', '数据库'
    BUSINESS = 'business', '业务'
    SECURITY = 'security', '安全'
    PERFORMANCE = 'performance', '性能'


class SystemLog(ZhiCoreModel):
    """系统日志模型 - 统一的日志记录"""

    level = models.CharField(
        max_length=20, choices=LogLevel.choices, default=LogLevel.INFO,
        verbose_name='日志级别', help_text='日志级别', db_comment='日志级别'
    )

    def is_audit_log_enabled(self):
        """禁用SystemLog的审计日志，防止循环"""
        return False
    category = models.CharField(
        max_length=50, choices=LogCategory.choices, default=LogCategory.SYSTEM,
        verbose_name='日志分类', help_text='日志分类', db_comment='日志分类'
    )
    module_name = models.CharField(
        max_length=100, verbose_name='模块名称', help_text='产生日志的模块名称',
        db_comment='模块名称', db_index=True
    )
    message = models.TextField(
        verbose_name='日志消息', help_text='日志消息内容', db_comment='日志消息'
    )
    trace_id = models.CharField(
        max_length=64, verbose_name='追踪ID', null=True, blank=True,
        help_text='请求追踪ID', db_comment='追踪ID', db_index=True
    )
    user_id = models.CharField(
        max_length=63, verbose_name='用户ID', null=True, blank=True,
        help_text='操作用户ID', db_comment='用户ID', db_index=True
    )
    ip_address = models.GenericIPAddressField(
        verbose_name='IP地址', null=True, blank=True,
        help_text='客户端IP地址', db_comment='IP地址'
    )
    user_agent = models.TextField(
        verbose_name='用户代理', null=True, blank=True,
        help_text='客户端用户代理', db_comment='用户代理'
    )
    extra_data = models.JSONField(
        default=dict, verbose_name='额外数据', null=True, blank=True,
        help_text='额外的日志数据', db_comment='额外数据'
    )

    # 关联对象 (可选)
    content_type = models.ForeignKey(
        ContentType, on_delete=models.CASCADE, null=True, blank=True,
        verbose_name='内容类型', help_text='关联对象类型'
    )
    object_id = models.CharField(
        max_length=63, null=True, blank=True,
        verbose_name='对象ID', help_text='关联对象ID'
    )
    content_object = GenericForeignKey('content_type', 'object_id')

    class Meta:
        db_table = table_prefix + 'system_log'
        verbose_name = '系统日志'
        verbose_name_plural = verbose_name
        ordering = ('-created_at',)
        indexes = [
            models.Index(fields=['level', 'created_at']),
            models.Index(fields=['category', 'created_at']),
            models.Index(fields=['module_name', 'created_at']),
            models.Index(fields=['trace_id']),
            models.Index(fields=['user_id', 'created_at']),
        ]

    def __str__(self):
        return f"[{self.level}] {self.module_name}: {self.message[:50]}"


class ApiAccessLog(ZhiCoreModel):
    """API访问日志 - 专门记录API请求"""

    method = models.CharField(
        max_length=10, verbose_name='请求方法', help_text='HTTP请求方法',
        db_comment='请求方法', db_index=True
    )
    path = models.CharField(
        max_length=500, verbose_name='请求路径', help_text='API请求路径',
        db_comment='请求路径', db_index=True
    )
    query_params = models.JSONField(
        default=dict, verbose_name='查询参数', null=True, blank=True,
        help_text='URL查询参数', db_comment='查询参数'
    )
    request_body = models.TextField(
        verbose_name='请求体', null=True, blank=True,
        help_text='请求体内容', db_comment='请求体'
    )
    response_status = models.IntegerField(
        verbose_name='响应状态码', help_text='HTTP响应状态码',
        db_comment='响应状态码', db_index=True
    )
    response_body = models.TextField(
        verbose_name='响应体', null=True, blank=True,
        help_text='响应体内容', db_comment='响应体'
    )
    response_time = models.FloatField(
        verbose_name='响应时间', help_text='请求处理时间(秒)',
        db_comment='响应时间', null=True, blank=True
    )
    ip_address = models.GenericIPAddressField(
        verbose_name='客户端IP', help_text='客户端IP地址',
        db_comment='客户端IP', db_index=True
    )
    user_agent = models.TextField(
        verbose_name='用户代理', null=True, blank=True,
        help_text='客户端用户代理', db_comment='用户代理'
    )
    trace_id = models.CharField(
        max_length=64, verbose_name='追踪ID', null=True, blank=True,
        help_text='请求追踪ID', db_comment='追踪ID', db_index=True
    )
    user_id = models.CharField(
        max_length=63, verbose_name='用户ID', null=True, blank=True,
        help_text='请求用户ID', db_comment='用户ID', db_index=True
    )

    class Meta:
        db_table = table_prefix + 'api_access_log'
        verbose_name = 'API访问日志'
        verbose_name_plural = verbose_name
        ordering = ('-created_at',)
        indexes = [
            models.Index(fields=['method', 'path']),
            models.Index(fields=['response_status', 'created_at']),
            models.Index(fields=['user_id', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
            models.Index(fields=['trace_id']),
        ]

    def __str__(self):
        return f"{self.method} {self.path} - {self.response_status}"


class LoginLog(ZhiCoreModel):
    LOGIN_TYPE_CHOICES = (
        (1, '普通登录'),
        )
    username = models.CharField(
        max_length=32, verbose_name="登录用户名", null=True, blank=True, help_text="登录用户名",
        db_comment="登录用户名"
        )
    ip = models.CharField(
        max_length=32, verbose_name="登录ip", null=True, blank=True, help_text="登录ip",
        db_comment="登录ip"
        )
    agent = models.TextField(
        verbose_name="agent信息", null=True, blank=True, help_text="agent信息",
        db_comment="agent信息"
        )
    browser = models.CharField(
        max_length=200, verbose_name="浏览器名", null=True, blank=True, help_text="浏览器名",
        db_comment="浏览器名"
        )
    os = models.CharField(
        max_length=200, verbose_name="操作系统", null=True, blank=True, help_text="操作系统",
        db_comment="操作系统"
        )
    login_type = models.IntegerField(
        default=1, choices=LOGIN_TYPE_CHOICES, verbose_name="登录类型",
        help_text="登录类型", db_comment="登录类型"
        )

    class Meta:
        db_table = table_prefix + 'system_login_log'
        verbose_name = '登录日志'
        verbose_name_plural = verbose_name
        ordering = ('-created_at',)


class AuditLogManager(models.Manager):
    """AuditLog 管理器"""

    def create_audit_log(self, resource_id: str, action: str, old_values=None, new_values=None,
                        creator_id=None, creator_name=None):
        """
        创建审计日志记录

        Args:
            resource_id: 资源ID
            action: 操作类型
            old_values: 旧值
            new_values: 新值
            creator_id: 创建者ID
            creator_name: 创建者名称
        """
        return self.create(
            resource_id=resource_id,
            action=action,
            old_values=old_values,
            new_values=new_values,
            creator_id=creator_id,
            creator_name=creator_name or ''
        )

    def get_resource_history(self, resource_id: str):
        """获取资源的变更历史"""
        return self.filter(resource_id=resource_id).order_by('-created_at')

    def get_user_actions(self, creator_id: str):
        """获取用户的操作历史"""
        return self.filter(creator_id=creator_id).order_by('-created_at')

    def get_actions_by_type(self, action: str):
        """根据操作类型获取审计日志"""
        return self.filter(action=action).order_by('-created_at')


class AuditLog(ZhiCoreModel):
    """资源重要信息变动日志"""
    resource_id = models.CharField(
        max_length=63, default=None, blank=False, db_index=True,
        verbose_name='资源ID', help_text='资源ID', db_comment='资源ID'
        )
    old_values = models.JSONField(
        default=None, null=True, blank=True, verbose_name='变更信息旧值',
        help_text='变更信息旧值', db_comment='变更信息旧值'
        )
    new_values = models.JSONField(
        default=None, null=True, blank=True, verbose_name='变更信息新值',
        help_text='变更信息新值', db_comment='变更信息新值'
        )
    ACTION_CHOICES = (
        ('删除', '删除'),
        ('创建', '创建'),
        ('修改', '修改'),
        ('软删除', '软删除'),
        ('硬删除', '硬删除'),
        ('登录', '登录'),
        ('登出', '登出'),
        ('权限变更', '权限变更'),
        ('其他', '其他'),
        )
    action = models.CharField(
        max_length=63, default=None, blank=True, choices=ACTION_CHOICES,
        verbose_name='动作', help_text='动作', db_comment='动作'
        )

    # 使用自定义管理器
    objects = AuditLogManager()

    class Meta:
        db_table = table_prefix + "system_audit_log"
        verbose_name = "资源变更日志表"
        verbose_name_plural = verbose_name
        ordering = ("-created_at",)
        indexes = [
            models.Index(fields=['resource_id', '-created_at']),
            models.Index(fields=['creator_id', '-created_at']),
            models.Index(fields=['action', '-created_at']),
        ]

    def __str__(self):
        return f"<resource_id:{self.resource_id}><action:{self.action}> at <create_datetime:{self.created_at}>"

    def is_audit_log_enabled(self):
        """禁用AuditLog模型自身的审计日志记录，避免无限递归"""
        return False

    def get_changes_summary(self):
        """获取变更摘要"""
        if not self.old_values or not self.new_values:
            return f"{self.action}操作"

        changes = []
        old_dict = self.old_values or {}
        new_dict = self.new_values or {}

        # 找出变更的字段
        all_keys = set(old_dict.keys()) | set(new_dict.keys())
        for key in all_keys:
            old_val = old_dict.get(key)
            new_val = new_dict.get(key)

            if old_val != new_val:
                changes.append(f"{key}: {old_val} → {new_val}")

        return f"{self.action}: " + ", ".join(changes[:3])  # 只显示前3个变更

    def is_sensitive_change(self):
        """判断是否为敏感变更"""
        sensitive_actions = ['删除', '硬删除', '权限变更']
        if self.action in sensitive_actions:
            return True

        # 检查是否涉及敏感字段
        sensitive_fields = ['password', 'token', 'secret', 'key', 'is_superuser', 'is_staff']
        if self.new_values:
            for field in sensitive_fields:
                if field in self.new_values:
                    return True

        return False

    @classmethod
    async def async_create_audit_log(cls, resource_id: str, action: str, old_values=None,
                                   new_values=None, creator_id=None, creator_name=None):
        """
        异步创建审计日志记录

        Args:
            resource_id: 资源ID
            action: 操作类型
            old_values: 旧值
            new_values: 新值
            creator_id: 创建者ID
            creator_name: 创建者名称
        """
        from asgiref.sync import sync_to_async

        audit_log = cls(
            resource_id=resource_id,
            action=action,
            old_values=old_values,
            new_values=new_values,
            creator_id=creator_id,
            creator_name=creator_name or ''
        )

        await sync_to_async(audit_log.save)()
        return audit_log


class OperationLog(ZhiCoreModel):
    request_username = models.CharField(
        max_length=50, blank=True, null=True, verbose_name="请求用户",
        help_text="请求用户", db_comment="请求用户"
        )
    request_path = models.CharField(
        max_length=400, verbose_name="请求地址", null=True, blank=True,
        help_text="请求地址", db_comment="请求地址"
        )
    request_body = models.TextField(
        verbose_name="请求参数", null=True, blank=True, help_text="请求参数",
        db_comment="请求参数"
        )
    request_method = models.CharField(
        max_length=8, verbose_name="请求方式", null=True, blank=True,
        help_text="请求方式", db_comment="请求方式"
        )
    response_msg = models.TextField(
        verbose_name="响应说明", null=True, blank=True, help_text="响应说明",
        db_comment="响应说明"
        )
    request_ip = models.CharField(
        max_length=32, verbose_name="请求ip地址", null=True, blank=True,
        help_text="请求ip地址", db_comment="请求ip地址"
        )
    request_browser = models.CharField(
        max_length=64, verbose_name="请求浏览器", null=True, blank=True,
        help_text="请求浏览器", db_comment="请求浏览器"
        )
    response_code = models.CharField(
        max_length=32, verbose_name="响应状态码", null=True, blank=True,
        help_text="响应状态码", db_comment="响应状态码"
        )
    request_os = models.CharField(
        max_length=64, verbose_name="操作系统", null=True, blank=True, help_text="操作系统",
        db_comment="操作系统"
        )
    json_result = models.TextField(
        verbose_name="返回信息", null=True, blank=True, help_text="返回信息",
        db_comment="返回信息"
        )
    status = models.BooleanField(default=False, verbose_name="响应状态", help_text="响应状态", db_comment="响应状态")
    trace_id = models.CharField(
        max_length=64, verbose_name="trace_id", null=True, blank=True,
        help_text="trace_id", db_comment="trace_id"
        )

    class Meta:
        db_table = table_prefix + 'operat_log'
        verbose_name = '操作日志'
        verbose_name_plural = verbose_name
        ordering = ('-created_at',)


class OAuthAuditLog(ZhiCoreModel):
    """
    OAuth2审计日志
    记录OAuth2相关的操作日志
    """
    ACTION_CHOICES = [
        ('login', '用户登录'),
        ('logout', '用户登出'),
        ('authorize', '授权'),
        ('token_issued', '令牌颁发'),
        ('token_refreshed', '令牌刷新'),
        ('token_revoked', '令牌撤销'),
        ('consent_granted', '授权同意'),
        ('consent_revoked', '撤销同意'),
        ('application_created', '应用创建'),
        ('application_updated', '应用更新'),
        ('application_deleted', '应用删除'),
    ]

    # 用户信息
    user_id = models.CharField(
        max_length=63,
        blank=True,
        db_index=True,
        verbose_name="操作用户ID",
        help_text="执行操作的用户ID",
        db_comment="执行操作的用户ID"
    )
    user_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="操作用户名",
        help_text="执行操作的用户名",
        db_comment="执行操作的用户名"
    )

    # 应用信息
    application_id = models.CharField(
        max_length=63,
        blank=True,
        db_index=True,
        verbose_name="应用ID",
        help_text="相关的OAuth2应用ID",
        db_comment="相关的OAuth2应用ID"
    )
    application_name = models.CharField(
        max_length=100,
        blank=True,
        verbose_name="应用名称",
        help_text="相关的OAuth2应用名称",
        db_comment="相关的OAuth2应用名称"
    )

    # 操作信息
    action = models.CharField(
        max_length=50,
        choices=ACTION_CHOICES,
        verbose_name="操作类型",
        help_text="执行的操作",
        db_comment="执行的操作"
    )
    resource_type = models.CharField(
        max_length=50,
        blank=True,
        verbose_name="资源类型",
        help_text="操作的资源类型",
        db_comment="操作的资源类型"
    )
    resource_id = models.CharField(
        max_length=63,
        blank=True,
        verbose_name="资源ID",
        help_text="操作的资源ID",
        db_comment="操作的资源ID"
    )

    # 请求信息
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="IP地址",
        help_text="操作来源IP",
        db_comment="操作来源IP"
    )
    user_agent = models.TextField(
        blank=True,
        verbose_name="用户代理",
        help_text="客户端信息",
        db_comment="客户端信息"
    )
    trace_id = models.CharField(
        max_length=64,
        blank=True,
        db_index=True,
        verbose_name="追踪ID",
        help_text="请求追踪标识",
        db_comment="请求追踪标识"
    )

    # 详细信息
    details = models.JSONField(
        default=dict,
        verbose_name="详细信息",
        help_text="操作详细信息",
        db_comment="操作详细信息"
    )
    scopes = models.JSONField(
        default=list,
        verbose_name="权限范围",
        help_text="请求或授予的权限范围",
        db_comment="请求或授予的权限范围"
    )

    # 结果信息
    is_success = models.BooleanField(
        default=True,
        verbose_name="是否成功",
        help_text="操作是否成功",
        db_comment="操作是否成功"
    )
    error_message = models.TextField(
        blank=True,
        verbose_name="错误信息",
        help_text="操作失败时的错误信息",
        db_comment="操作失败时的错误信息"
    )

    class Meta:
        db_table = table_prefix + 'oauth_audit_log'
        verbose_name = 'OAuth2审计日志'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user_id', 'action']),
            models.Index(fields=['application_id', 'action']),
            models.Index(fields=['created_at']),
            models.Index(fields=['ip_address']),
            models.Index(fields=['trace_id']),
            models.Index(fields=['action', 'is_success']),
        ]

    def __str__(self):
        return f"OAuth: {self.user_name} {self.action} {self.application_name}"