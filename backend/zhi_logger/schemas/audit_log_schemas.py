"""
审计日志相关的 Schema 定义
"""

from typing import Optional, List, Dict, Any
from datetime import datetime, date
from ninja import Schema


class AuditLogListSchema(Schema):
    """审计日志列表 Schema"""
    id: str
    resource_id: str
    action: str
    creator_id: Optional[str] = None
    creator_name: Optional[str] = None
    created_at: datetime
    
    @staticmethod
    def resolve_changes_summary(obj):
        """获取变更摘要"""
        return obj.get_changes_summary()
    
    changes_summary: Optional[str] = None


class AuditLogDetailSchema(Schema):
    """审计日志详情 Schema"""
    id: str
    resource_id: str
    action: str
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    creator_id: Optional[str] = None
    creator_name: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    @staticmethod
    def resolve_changes_summary(obj):
        """获取变更摘要"""
        return obj.get_changes_summary()
    
    @staticmethod
    def resolve_is_sensitive(obj):
        """是否为敏感变更"""
        return obj.is_sensitive_change()
    
    changes_summary: Optional[str] = None
    is_sensitive: bool = False


class AuditLogFilterSchema(Schema):
    """审计日志过滤 Schema"""
    resource_id: Optional[str] = None
    action: Optional[str] = None
    creator_id: Optional[str] = None
    creator_name: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_sensitive: Optional[bool] = None


class ActionStatsSchema(Schema):
    """操作统计 Schema"""
    action: str
    count: int


class UserStatsSchema(Schema):
    """用户统计 Schema"""
    creator_id: Optional[str] = None
    creator_name: Optional[str] = None
    count: int


class DailyStatsSchema(Schema):
    """每日统计 Schema"""
    date: date
    count: int


class AuditLogStatsSchema(Schema):
    """审计日志统计 Schema"""
    total_count: int
    sensitive_count: int
    action_stats: List[ActionStatsSchema]
    user_stats: List[UserStatsSchema]
    daily_stats: List[DailyStatsSchema]
    start_date: datetime
    end_date: datetime
    days: int


class AuditLogCreateSchema(Schema):
    """创建审计日志 Schema"""
    resource_id: str
    action: str
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    creator_id: Optional[str] = None
    creator_name: Optional[str] = None


class AuditLogBatchCreateSchema(Schema):
    """批量创建审计日志 Schema"""
    audit_logs: List[AuditLogCreateSchema]


class AuditLogExportSchema(Schema):
    """审计日志导出 Schema"""
    format: str = "csv"  # csv, excel, json
    filters: Optional[AuditLogFilterSchema] = None
    fields: Optional[List[str]] = None  # 要导出的字段


class AuditLogSearchSchema(Schema):
    """审计日志搜索 Schema"""
    keyword: str
    search_fields: Optional[List[str]] = ["resource_id", "action", "creator_name"]
    filters: Optional[AuditLogFilterSchema] = None


class AuditLogCompareSchema(Schema):
    """审计日志对比 Schema"""
    audit_log_id_1: str
    audit_log_id_2: str


class AuditLogCompareResultSchema(Schema):
    """审计日志对比结果 Schema"""
    audit_log_1: AuditLogDetailSchema
    audit_log_2: AuditLogDetailSchema
    differences: Dict[str, Any]
    similarity: float  # 相似度百分比


class AuditLogTimelineSchema(Schema):
    """审计日志时间线 Schema"""
    resource_id: str
    timeline: List[AuditLogListSchema]
    total_changes: int
    first_change: Optional[datetime] = None
    last_change: Optional[datetime] = None


class AuditLogAlertSchema(Schema):
    """审计日志告警 Schema"""
    id: str
    resource_id: str
    action: str
    alert_type: str  # "sensitive_operation", "bulk_changes", "unusual_activity"
    alert_level: str  # "low", "medium", "high", "critical"
    message: str
    created_at: datetime
    is_resolved: bool = False


class AuditLogMetricsSchema(Schema):
    """审计日志指标 Schema"""
    total_operations: int
    operations_today: int
    operations_this_week: int
    operations_this_month: int
    sensitive_operations_today: int
    most_active_users: List[UserStatsSchema]
    most_common_actions: List[ActionStatsSchema]
    peak_activity_hour: Optional[int] = None
    average_operations_per_day: float


class AuditLogRetentionSchema(Schema):
    """审计日志保留策略 Schema"""
    retention_days: int
    auto_cleanup: bool = True
    archive_before_delete: bool = True
    archive_location: Optional[str] = None


class AuditLogCleanupResultSchema(Schema):
    """审计日志清理结果 Schema"""
    deleted_count: int
    archived_count: int
    cleanup_date: datetime
    retention_days: int
    success: bool
    message: str
