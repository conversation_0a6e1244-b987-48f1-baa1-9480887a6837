# Generated by Django 5.2 on 2025-07-23 15:14

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.Char<PERSON>ield(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.Char<PERSON><PERSON>(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('username', models.CharField(blank=True, db_comment='登录用户名', help_text='登录用户名', max_length=32, null=True, verbose_name='登录用户名')),
                ('ip', models.CharField(blank=True, db_comment='登录ip', help_text='登录ip', max_length=32, null=True, verbose_name='登录ip')),
                ('agent', models.TextField(blank=True, db_comment='agent信息', help_text='agent信息', null=True, verbose_name='agent信息')),
                ('browser', models.CharField(blank=True, db_comment='浏览器名', help_text='浏览器名', max_length=200, null=True, verbose_name='浏览器名')),
                ('os', models.CharField(blank=True, db_comment='操作系统', help_text='操作系统', max_length=200, null=True, verbose_name='操作系统')),
                ('login_type', models.IntegerField(choices=[(1, '普通登录')], db_comment='登录类型', default=1, help_text='登录类型', verbose_name='登录类型')),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'db_table': 'zhi_log_system_login_log',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='OperationLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('request_username', models.CharField(blank=True, db_comment='请求用户', help_text='请求用户', max_length=50, null=True, verbose_name='请求用户')),
                ('request_path', models.CharField(blank=True, db_comment='请求地址', help_text='请求地址', max_length=400, null=True, verbose_name='请求地址')),
                ('request_body', models.TextField(blank=True, db_comment='请求参数', help_text='请求参数', null=True, verbose_name='请求参数')),
                ('request_method', models.CharField(blank=True, db_comment='请求方式', help_text='请求方式', max_length=8, null=True, verbose_name='请求方式')),
                ('response_msg', models.TextField(blank=True, db_comment='响应说明', help_text='响应说明', null=True, verbose_name='响应说明')),
                ('request_ip', models.CharField(blank=True, db_comment='请求ip地址', help_text='请求ip地址', max_length=32, null=True, verbose_name='请求ip地址')),
                ('request_browser', models.CharField(blank=True, db_comment='请求浏览器', help_text='请求浏览器', max_length=64, null=True, verbose_name='请求浏览器')),
                ('response_code', models.CharField(blank=True, db_comment='响应状态码', help_text='响应状态码', max_length=32, null=True, verbose_name='响应状态码')),
                ('request_os', models.CharField(blank=True, db_comment='操作系统', help_text='操作系统', max_length=64, null=True, verbose_name='操作系统')),
                ('json_result', models.TextField(blank=True, db_comment='返回信息', help_text='返回信息', null=True, verbose_name='返回信息')),
                ('status', models.BooleanField(db_comment='响应状态', default=False, help_text='响应状态', verbose_name='响应状态')),
                ('trace_id', models.CharField(blank=True, db_comment='trace_id', help_text='trace_id', max_length=64, null=True, verbose_name='trace_id')),
            ],
            options={
                'verbose_name': '操作日志',
                'verbose_name_plural': '操作日志',
                'db_table': 'zhi_log_operat_log',
                'ordering': ('-created_at',),
            },
        ),
        migrations.CreateModel(
            name='ApiAccessLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('method', models.CharField(db_comment='请求方法', db_index=True, help_text='HTTP请求方法', max_length=10, verbose_name='请求方法')),
                ('path', models.CharField(db_comment='请求路径', db_index=True, help_text='API请求路径', max_length=500, verbose_name='请求路径')),
                ('query_params', models.JSONField(blank=True, db_comment='查询参数', default=dict, help_text='URL查询参数', null=True, verbose_name='查询参数')),
                ('request_body', models.TextField(blank=True, db_comment='请求体', help_text='请求体内容', null=True, verbose_name='请求体')),
                ('response_status', models.IntegerField(db_comment='响应状态码', db_index=True, help_text='HTTP响应状态码', verbose_name='响应状态码')),
                ('response_body', models.TextField(blank=True, db_comment='响应体', help_text='响应体内容', null=True, verbose_name='响应体')),
                ('response_time', models.FloatField(blank=True, db_comment='响应时间', help_text='请求处理时间(秒)', null=True, verbose_name='响应时间')),
                ('ip_address', models.GenericIPAddressField(db_comment='客户端IP', db_index=True, help_text='客户端IP地址', verbose_name='客户端IP')),
                ('user_agent', models.TextField(blank=True, db_comment='用户代理', help_text='客户端用户代理', null=True, verbose_name='用户代理')),
                ('trace_id', models.CharField(blank=True, db_comment='追踪ID', db_index=True, help_text='请求追踪ID', max_length=64, null=True, verbose_name='追踪ID')),
                ('user_id', models.CharField(blank=True, db_comment='用户ID', db_index=True, help_text='请求用户ID', max_length=63, null=True, verbose_name='用户ID')),
            ],
            options={
                'verbose_name': 'API访问日志',
                'verbose_name_plural': 'API访问日志',
                'db_table': 'zhi_log_api_access_log',
                'ordering': ('-created_at',),
                'indexes': [models.Index(fields=['method', 'path'], name='zhi_log_api_method_d06f12_idx'), models.Index(fields=['response_status', 'created_at'], name='zhi_log_api_respons_b98fa9_idx'), models.Index(fields=['user_id', 'created_at'], name='zhi_log_api_user_id_4638da_idx'), models.Index(fields=['ip_address', 'created_at'], name='zhi_log_api_ip_addr_afbeb0_idx'), models.Index(fields=['trace_id'], name='zhi_log_api_trace_i_e65291_idx')],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('resource_id', models.CharField(db_comment='资源ID', db_index=True, default=None, help_text='资源ID', max_length=63, verbose_name='资源ID')),
                ('old_values', models.JSONField(blank=True, db_comment='变更信息旧值', default=None, help_text='变更信息旧值', null=True, verbose_name='变更信息旧值')),
                ('new_values', models.JSONField(blank=True, db_comment='变更信息新值', default=None, help_text='变更信息新值', null=True, verbose_name='变更信息新值')),
                ('action', models.CharField(blank=True, choices=[('删除', '删除'), ('创建', '创建'), ('修改', '修改'), ('软删除', '软删除'), ('硬删除', '硬删除'), ('登录', '登录'), ('登出', '登出'), ('权限变更', '权限变更'), ('其他', '其他')], db_comment='动作', default=None, help_text='动作', max_length=63, verbose_name='动作')),
            ],
            options={
                'verbose_name': '资源变更日志表',
                'verbose_name_plural': '资源变更日志表',
                'db_table': 'zhi_log_system_audit_log',
                'ordering': ('-created_at',),
                'indexes': [models.Index(fields=['resource_id', '-created_at'], name='zhi_log_sys_resourc_1f399a_idx'), models.Index(fields=['creator_id', '-created_at'], name='zhi_log_sys_creator_a8d54e_idx'), models.Index(fields=['action', '-created_at'], name='zhi_log_sys_action_571e97_idx')],
            },
        ),
        migrations.CreateModel(
            name='OAuthAuditLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('user_id', models.CharField(blank=True, db_comment='执行操作的用户ID', db_index=True, help_text='执行操作的用户ID', max_length=63, verbose_name='操作用户ID')),
                ('user_name', models.CharField(blank=True, db_comment='执行操作的用户名', help_text='执行操作的用户名', max_length=100, verbose_name='操作用户名')),
                ('application_id', models.CharField(blank=True, db_comment='相关的OAuth2应用ID', db_index=True, help_text='相关的OAuth2应用ID', max_length=63, verbose_name='应用ID')),
                ('application_name', models.CharField(blank=True, db_comment='相关的OAuth2应用名称', help_text='相关的OAuth2应用名称', max_length=100, verbose_name='应用名称')),
                ('action', models.CharField(choices=[('login', '用户登录'), ('logout', '用户登出'), ('authorize', '授权'), ('token_issued', '令牌颁发'), ('token_refreshed', '令牌刷新'), ('token_revoked', '令牌撤销'), ('consent_granted', '授权同意'), ('consent_revoked', '撤销同意'), ('application_created', '应用创建'), ('application_updated', '应用更新'), ('application_deleted', '应用删除')], db_comment='执行的操作', help_text='执行的操作', max_length=50, verbose_name='操作类型')),
                ('resource_type', models.CharField(blank=True, db_comment='操作的资源类型', help_text='操作的资源类型', max_length=50, verbose_name='资源类型')),
                ('resource_id', models.CharField(blank=True, db_comment='操作的资源ID', help_text='操作的资源ID', max_length=63, verbose_name='资源ID')),
                ('ip_address', models.GenericIPAddressField(blank=True, db_comment='操作来源IP', help_text='操作来源IP', null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, db_comment='客户端信息', help_text='客户端信息', verbose_name='用户代理')),
                ('trace_id', models.CharField(blank=True, db_comment='请求追踪标识', db_index=True, help_text='请求追踪标识', max_length=64, verbose_name='追踪ID')),
                ('details', models.JSONField(db_comment='操作详细信息', default=dict, help_text='操作详细信息', verbose_name='详细信息')),
                ('scopes', models.JSONField(db_comment='请求或授予的权限范围', default=list, help_text='请求或授予的权限范围', verbose_name='权限范围')),
                ('is_success', models.BooleanField(db_comment='操作是否成功', default=True, help_text='操作是否成功', verbose_name='是否成功')),
                ('error_message', models.TextField(blank=True, db_comment='操作失败时的错误信息', help_text='操作失败时的错误信息', verbose_name='错误信息')),
            ],
            options={
                'verbose_name': 'OAuth2审计日志',
                'verbose_name_plural': 'OAuth2审计日志',
                'db_table': 'zhi_log_oauth_audit_log',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user_id', 'action'], name='zhi_log_oau_user_id_9b69cb_idx'), models.Index(fields=['application_id', 'action'], name='zhi_log_oau_applica_4939b2_idx'), models.Index(fields=['created_at'], name='zhi_log_oau_created_59e32c_idx'), models.Index(fields=['ip_address'], name='zhi_log_oau_ip_addr_d6f718_idx'), models.Index(fields=['trace_id'], name='zhi_log_oau_trace_i_317919_idx'), models.Index(fields=['action', 'is_success'], name='zhi_log_oau_action_ad47d5_idx')],
            },
        ),
        migrations.CreateModel(
            name='SystemLog',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('level', models.CharField(choices=[('DEBUG', 'Debug'), ('INFO', 'Info'), ('WARNING', 'Warning'), ('ERROR', 'Error'), ('CRITICAL', 'Critical')], db_comment='日志级别', default='INFO', help_text='日志级别', max_length=20, verbose_name='日志级别')),
                ('category', models.CharField(choices=[('system', '系统'), ('api', 'API'), ('auth', '认证'), ('database', '数据库'), ('business', '业务'), ('security', '安全'), ('performance', '性能')], db_comment='日志分类', default='system', help_text='日志分类', max_length=50, verbose_name='日志分类')),
                ('module_name', models.CharField(db_comment='模块名称', db_index=True, help_text='产生日志的模块名称', max_length=100, verbose_name='模块名称')),
                ('message', models.TextField(db_comment='日志消息', help_text='日志消息内容', verbose_name='日志消息')),
                ('trace_id', models.CharField(blank=True, db_comment='追踪ID', db_index=True, help_text='请求追踪ID', max_length=64, null=True, verbose_name='追踪ID')),
                ('user_id', models.CharField(blank=True, db_comment='用户ID', db_index=True, help_text='操作用户ID', max_length=63, null=True, verbose_name='用户ID')),
                ('ip_address', models.GenericIPAddressField(blank=True, db_comment='IP地址', help_text='客户端IP地址', null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, db_comment='用户代理', help_text='客户端用户代理', null=True, verbose_name='用户代理')),
                ('extra_data', models.JSONField(blank=True, db_comment='额外数据', default=dict, help_text='额外的日志数据', null=True, verbose_name='额外数据')),
                ('object_id', models.CharField(blank=True, help_text='关联对象ID', max_length=63, null=True, verbose_name='对象ID')),
                ('content_type', models.ForeignKey(blank=True, help_text='关联对象类型', null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype', verbose_name='内容类型')),
            ],
            options={
                'verbose_name': '系统日志',
                'verbose_name_plural': '系统日志',
                'db_table': 'zhi_log_system_log',
                'ordering': ('-created_at',),
                'indexes': [models.Index(fields=['level', 'created_at'], name='zhi_log_sys_level_2c5484_idx'), models.Index(fields=['category', 'created_at'], name='zhi_log_sys_categor_746062_idx'), models.Index(fields=['module_name', 'created_at'], name='zhi_log_sys_module__279ea8_idx'), models.Index(fields=['trace_id'], name='zhi_log_sys_trace_i_a51d39_idx'), models.Index(fields=['user_id', 'created_at'], name='zhi_log_sys_user_id_c561b0_idx')],
            },
        ),
    ]
