"""
WebSocket消费者 - 实时日志推送
"""
import json
from typing import Dict, Any
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser

from zhi_oauth.auth.oauth_backend import OAuth2Backend
from zhi_common.zhi_logger.base import LogLevel


class LogConsumer(AsyncWebsocketConsumer):
    """日志WebSocket消费者"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.groups = []
        
    async def connect(self):
        """WebSocket连接处理"""
        # 获取查询参数
        query_string = self.scope.get('query_string', b'').decode()
        query_params = dict(param.split('=') for param in query_string.split('&') if '=' in param)
        token = query_params.get('token')
        
        # 验证用户身份
        self.user = await self.authenticate_user(token)
        if isinstance(self.user, AnonymousUser) or not self.user:
            await self.close(code=4001)  # 未授权
            return
            
        # 检查权限
        if not await self.check_log_permission():
            await self.close(code=4003)  # 权限不足
            return
            
        # 加入日志组
        await self.join_log_groups()
        await self.accept()
        
        # 发送连接成功消息
        await self.send(text_data=json.dumps({
            'type': 'connection',
            'status': 'connected',
            'message': '日志连接已建立'
        }))

    async def disconnect(self, close_code):
        """WebSocket断开处理"""
        # 离开所有组
        for group in self.groups:
            await self.channel_layer.group_discard(group, self.channel_name)

    async def receive(self, text_data):
        """接收客户端消息"""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            if message_type == 'subscribe':
                await self.handle_subscribe(data)
            elif message_type == 'unsubscribe':
                await self.handle_unsubscribe(data)
            elif message_type == 'filter':
                await self.handle_filter_change(data)
            else:
                await self.send_error('未知的消息类型')
                
        except json.JSONDecodeError:
            await self.send_error('无效的JSON格式')
        except Exception as e:
            await self.send_error(f'处理消息时出错: {str(e)}')

    async def log_message(self, event):
        """处理日志消息"""
        log_data = event['data']
        
        # 应用过滤器
        if not await self.should_send_log(log_data):
            return
            
        # 发送日志到客户端
        await self.send(text_data=json.dumps({
            'type': 'log',
            'data': log_data
        }))

    async def handle_subscribe(self, data):
        """处理订阅请求"""
        channels = data.get('channels', [])
        
        for channel in channels:
            if await self.can_subscribe_channel(channel):
                group_name = f"logs_{channel}"
                await self.channel_layer.group_add(group_name, self.channel_name)
                self.groups.append(group_name)
                
        await self.send(text_data=json.dumps({
            'type': 'subscribe_response',
            'status': 'success',
            'channels': channels
        }))

    async def handle_unsubscribe(self, data):
        """处理取消订阅请求"""
        channels = data.get('channels', [])
        
        for channel in channels:
            group_name = f"logs_{channel}"
            await self.channel_layer.group_discard(group_name, self.channel_name)
            if group_name in self.groups:
                self.groups.remove(group_name)
                
        await self.send(text_data=json.dumps({
            'type': 'unsubscribe_response',
            'status': 'success',
            'channels': channels
        }))

    async def handle_filter_change(self, data):
        """处理过滤器变更"""
        # 存储用户的过滤器设置
        self.log_filters = data.get('filters', {})
        
        await self.send(text_data=json.dumps({
            'type': 'filter_response',
            'status': 'success',
            'filters': self.log_filters
        }))

    async def should_send_log(self, log_data: Dict[str, Any]) -> bool:
        """判断是否应该发送日志"""
        if not hasattr(self, 'log_filters'):
            return True
            
        filters = self.log_filters
        
        # 级别过滤
        if 'levels' in filters:
            if log_data.get('level') not in filters['levels']:
                return False
                
        # 模块过滤
        if 'modules' in filters:
            if log_data.get('module') not in filters['modules']:
                return False
                
        # 分类过滤
        if 'categories' in filters:
            if log_data.get('category') not in filters['categories']:
                return False
                
        return True

    async def can_subscribe_channel(self, channel: str) -> bool:
        """检查是否可以订阅指定频道"""
        # 基础权限检查
        if not await self.check_log_permission():
            return False
            
        # 特殊频道权限检查
        if channel == 'error' and not await self.check_error_log_permission():
            return False
            
        if channel == 'security' and not await self.check_security_log_permission():
            return False
            
        return True

    async def join_log_groups(self):
        """加入默认日志组"""
        # 加入基础日志组
        await self.channel_layer.group_add("logs", self.channel_name)
        self.groups.append("logs")
        
        # 根据权限加入特殊组
        if await self.check_error_log_permission():
            await self.channel_layer.group_add("logs_error", self.channel_name)
            self.groups.append("logs_error")

    @database_sync_to_async
    def authenticate_user(self, token: str):
        """验证用户身份"""
        if not token:
            return AnonymousUser()
            
        try:
            # 使用OAuth2后端验证token
            oauth_backend = OAuth2Backend()
            user = oauth_backend.authenticate_token(token)
            return user
        except Exception:
            return AnonymousUser()

    @database_sync_to_async
    def check_log_permission(self) -> bool:
        """检查日志查看权限"""
        if not self.user or isinstance(self.user, AnonymousUser):
            return False
            
        # 检查用户是否有日志查看权限
        return self.user.has_perm('zhi_logger.view_logs') or self.user.is_superuser

    @database_sync_to_async
    def check_error_log_permission(self) -> bool:
        """检查错误日志查看权限"""
        if not self.user or isinstance(self.user, AnonymousUser):
            return False
            
        return self.user.has_perm('zhi_logger.view_error_logs') or self.user.is_superuser

    @database_sync_to_async
    def check_security_log_permission(self) -> bool:
        """检查安全日志查看权限"""
        if not self.user or isinstance(self.user, AnonymousUser):
            return False
            
        return self.user.has_perm('zhi_logger.view_security_logs') or self.user.is_superuser

    async def send_error(self, message: str):
        """发送错误消息"""
        await self.send(text_data=json.dumps({
            'type': 'error',
            'message': message
        }))


class SystemLogConsumer(LogConsumer):
    """系统日志消费者 - 仅系统管理员可访问"""
    
    @database_sync_to_async
    def check_log_permission(self) -> bool:
        """系统日志需要管理员权限"""
        if not self.user or isinstance(self.user, AnonymousUser):
            return False
            
        return self.user.is_superuser


class ModuleLogConsumer(LogConsumer):
    """模块日志消费者 - 按模块权限控制"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.module_name = None
        
    async def connect(self):
        """连接时获取模块名称"""
        self.module_name = self.scope['url_route']['kwargs'].get('module_name')
        if not self.module_name:
            await self.close(code=4000)  # 缺少模块名称
            return
            
        await super().connect()

    @database_sync_to_async
    def check_log_permission(self) -> bool:
        """检查模块日志权限"""
        if not self.user or isinstance(self.user, AnonymousUser):
            return False
            
        # 检查用户是否有特定模块的日志查看权限
        perm_name = f'zhi_logger.view_{self.module_name}_logs'
        return (self.user.has_perm(perm_name) or 
                self.user.has_perm('zhi_logger.view_all_logs') or 
                self.user.is_superuser)

    async def join_log_groups(self):
        """加入模块特定的日志组"""
        group_name = f"logs_{self.module_name}"
        await self.channel_layer.group_add(group_name, self.channel_name)
        self.groups.append(group_name)
