"""
日志相关的异步任务
"""
from datetime import datetime, timedelta
from celery import shared_task
from django.utils import timezone
from django.conf import settings

from .services import log_service
from zhi_common.zhi_logger.base import Zhi<PERSON>ogger, LogLevel, LogCategory


logger = ZhiLogger(module_name="log_tasks", enable_websocket=False)


@shared_task(bind=True, max_retries=3)
def cleanup_old_logs_task(self, days: int = 30):
    """清理旧日志的异步任务"""
    try:
        logger.info(f"开始清理 {days} 天前的日志")
        
        # 执行清理
        result = log_service.cleanup_old_logs(days=days)
        
        logger.info(
            f"日志清理完成: 系统日志删除 {result['system_logs_deleted']} 条, "
            f"API日志删除 {result['api_logs_deleted']} 条",
            category=LogCategory.SYSTEM,
            extra_data=result
        )
        
        return {
            'success': True,
            'result': result
        }
        
    except Exception as exc:
        logger.error(
            f"日志清理任务失败: {str(exc)}",
            category=LogCategory.SYSTEM,
            extra_data={'error': str(exc), 'days': days}
        )
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task
def generate_log_report_task(days: int = 7):
    """生成日志报告的异步任务"""
    try:
        logger.info(f"开始生成 {days} 天的日志报告")
        
        # 获取统计数据
        stats = log_service.get_log_statistics(days=days)
        
        # 生成报告内容
        report = {
            'period': f'{days} days',
            'generated_at': timezone.now().isoformat(),
            'statistics': stats,
            'summary': {
                'total_system_logs': sum(stats['system_logs'].values()),
                'total_api_logs': sum(stats['api_logs'].values()),
                'total_modules': len(stats['module_logs']),
                'error_rate': _calculate_error_rate(stats),
            }
        }
        
        logger.info(
            f"日志报告生成完成",
            category=LogCategory.SYSTEM,
            extra_data=report['summary']
        )
        
        return {
            'success': True,
            'report': report
        }
        
    except Exception as exc:
        logger.error(
            f"日志报告生成失败: {str(exc)}",
            category=LogCategory.SYSTEM,
            extra_data={'error': str(exc), 'days': days}
        )
        
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task
def log_health_check_task():
    """日志系统健康检查任务"""
    try:
        logger.info("开始日志系统健康检查")
        
        health_status = {
            'timestamp': timezone.now().isoformat(),
            'database_connection': True,
            'websocket_connection': True,
            'log_volume': {},
            'error_rate': 0.0,
            'warnings': []
        }
        
        # 检查最近1小时的日志量
        recent_stats = log_service.get_log_statistics(days=1)
        
        # 检查错误率
        error_rate = _calculate_error_rate(recent_stats)
        health_status['error_rate'] = error_rate
        
        # 检查异常情况
        if error_rate > 0.1:  # 错误率超过10%
            health_status['warnings'].append('错误率过高')
        
        total_logs = sum(recent_stats['system_logs'].values())
        if total_logs > 10000:  # 1小时内日志量超过1万条
            health_status['warnings'].append('日志量异常')
        
        logger.info(
            f"日志系统健康检查完成",
            category=LogCategory.SYSTEM,
            extra_data=health_status
        )
        
        return {
            'success': True,
            'health_status': health_status
        }
        
    except Exception as exc:
        logger.error(
            f"日志健康检查失败: {str(exc)}",
            category=LogCategory.SYSTEM,
            extra_data={'error': str(exc)}
        )
        
        return {
            'success': False,
            'error': str(exc)
        }


def _calculate_error_rate(stats: dict) -> float:
    """计算错误率"""
    try:
        system_logs = stats.get('system_logs', {})
        total_logs = sum(system_logs.values())
        
        if total_logs == 0:
            return 0.0
        
        error_logs = system_logs.get('ERROR', 0) + system_logs.get('CRITICAL', 0)
        return error_logs / total_logs
        
    except Exception:
        return 0.0
