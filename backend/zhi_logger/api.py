"""
ZhiLogger API 配置
"""
from ninja_extra import NinjaExtraAPI
from ninja_extra.permissions import IsAuthenticated

from .controllers import LogController, RealtimeLogController
# from .api.audit_log_api import AuditLogController  # 暂时注释掉，避免导入错误
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers


# 创建 API 实例
api = NinjaExtraAPI(
    title="ZhiLogger API",
    version="2.0.0",  # 使用不同的版本号
    description="ZhiAdmin 日志管理系统 API",
    auth=GlobalOAuth2,
    urls_namespace="logger",  # 添加唯一的命名空间
    openapi_extra={
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
    docs_url="/logger/docs",
    openapi_url="/logger/openapi.json",
)

# 注册异常处理器
register_exception_handlers(api)

# 注册控制器
api.register_controllers(LogController)
api.register_controllers(RealtimeLogController)
# api.register_controllers(AuditLogController)  # 暂时注释掉
