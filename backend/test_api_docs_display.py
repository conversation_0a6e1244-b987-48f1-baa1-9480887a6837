"""
测试API文档显示 - 验证所有端点和权限标识是否正确显示
"""

import os
import re

def test_api_docs_display():
    """测试API文档显示配置"""
    
    print("=== API文档显示验证 ===")
    
    # 检查控制器文件
    controller_file = "zhi_oauth/apis/example_product.py"
    if not os.path.exists(controller_file):
        print(f"❌ {controller_file} 文件不存在")
        return False
    
    print(f"✅ {controller_file} 文件存在")
    
    # 读取控制器文件内容
    try:
        with open(controller_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取控制器文件失败: {e}")
        return False
    
    print("\n=== API端点配置检查 ===")
    
    # 检查所有API端点的配置
    api_endpoints = [
        {
            "method": "GET",
            "path": "/",
            "function": "list_products",
            "permission": "example_product.view",
            "summary": "获取产品分页列表",
            "operation_id": "list_products"
        },
        {
            "method": "POST", 
            "path": "/",
            "function": "create_product",
            "permission": "example_product.create",
            "summary": "创建新产品",
            "operation_id": "create_product"
        },
        {
            "method": "GET",
            "path": "/{id}",
            "function": "get_product", 
            "permission": "example_product.view",
            "summary": "获取产品详情",
            "operation_id": "get_product"
        },
        {
            "method": "PUT",
            "path": "/{id}",
            "function": "update_product",
            "permission": "example_product.update", 
            "summary": "更新产品信息",
            "operation_id": "update_product"
        },
        {
            "method": "DELETE",
            "path": "/{id}",
            "function": "delete_product",
            "permission": "example_product.delete",
            "summary": "删除产品", 
            "operation_id": "delete_product"
        },
        {
            "method": "GET",
            "path": "/stats",
            "function": "get_product_stats",
            "permission": "example_product.statistics",
            "summary": "获取产品统计信息",
            "operation_id": "get_product_stats"
        },
        {
            "method": "GET", 
            "path": "/search",
            "function": "search_products",
            "permission": "example_product.search",
            "summary": "搜索产品",
            "operation_id": "search_products"
        },
        {
            "method": "POST",
            "path": "/bulk-update-names", 
            "function": "bulk_update_product_names",
            "permission": "example_product.batch_update",
            "summary": "批量更新产品名称",
            "operation_id": "bulk_update_product_names"
        },
        {
            "method": "GET",
            "path": "/mappings",
            "function": "get_product_mappings",
            "permission": "example_product.view",
            "summary": "获取产品映射列表", 
            "operation_id": "get_product_mappings"
        }
    ]
    
    passed_endpoints = 0
    
    for endpoint in api_endpoints:
        print(f"\n--- 检查端点: {endpoint['method']} {endpoint['path']} ---")
        
        # 检查权限装饰器
        permission_pattern = f"@require_permission\\('{endpoint['permission']}'\\)"
        if re.search(permission_pattern, content):
            print(f"   ✅ 权限装饰器: {endpoint['permission']}")
        else:
            print(f"   ❌ 权限装饰器缺失: {endpoint['permission']}")
            continue
        
        # 检查HTTP方法装饰器
        method_lower = endpoint['method'].lower()
        http_decorator_pattern = f"@http_{method_lower}\\("
        if re.search(http_decorator_pattern, content):
            print(f"   ✅ HTTP装饰器: @http_{method_lower}")
        else:
            print(f"   ❌ HTTP装饰器缺失: @http_{method_lower}")
            continue
        
        # 检查函数定义
        function_pattern = f"def {endpoint['function']}\\("
        if re.search(function_pattern, content):
            print(f"   ✅ 函数定义: {endpoint['function']}")
        else:
            print(f"   ❌ 函数定义缺失: {endpoint['function']}")
            continue
        
        # 检查summary
        if endpoint['summary'] in content:
            print(f"   ✅ API摘要: {endpoint['summary']}")
        else:
            print(f"   ❌ API摘要缺失: {endpoint['summary']}")
            continue
        
        # 检查operation_id
        if endpoint['operation_id'] in content:
            print(f"   ✅ 操作ID: {endpoint['operation_id']}")
        else:
            print(f"   ❌ 操作ID缺失: {endpoint['operation_id']}")
            continue
        
        # 检查锁标识（在docstring中）
        lock_pattern = r'""".*🔒.*"""'
        if re.search(lock_pattern, content):
            print(f"   ✅ 锁标识: 🔒")
        else:
            print(f"   ❌ 锁标识缺失: 🔒")
            continue
        
        passed_endpoints += 1
        print(f"   ✅ 端点配置完整")
    
    print(f"\n=== 端点检查统计 ===")
    print(f"通过: {passed_endpoints}/{len(api_endpoints)} ({passed_endpoints/len(api_endpoints)*100:.1f}%)")
    
    print("\n=== 文档显示配置检查 ===")
    
    # 检查控制器级别配置
    controller_configs = [
        ("@api_controller", "控制器装饰器"),
        ('tags=["示例产品管理"]', "标签配置"),
        ("permissions=[permissions.IsAuthenticated]", "认证配置"),
        ("summary=", "API摘要配置"),
        ("description=", "API描述配置"),
        ("response={", "响应配置"),
        ("operation_id=", "操作ID配置"),
    ]
    
    config_passed = 0
    for config, desc in controller_configs:
        if config in content:
            print(f"   ✅ {desc}")
            config_passed += 1
        else:
            print(f"   ❌ {desc} - 未找到")
    
    print(f"\n配置检查: {config_passed}/{len(controller_configs)} ({config_passed/len(controller_configs)*100:.1f}%)")
    
    print("\n=== 总结 ===")
    
    total_score = (passed_endpoints / len(api_endpoints)) * 0.8 + (config_passed / len(controller_configs)) * 0.2
    
    if total_score >= 0.9:
        print("🎉 API文档显示配置完美！")
        print("✅ 所有端点都有权限装饰器")
        print("✅ 所有端点都有完整的文档配置")
        print("✅ 所有端点都有锁标识 🔒")
        print("✅ API文档将正确显示所有端点")
        
        print("\n📋 API端点列表:")
        for endpoint in api_endpoints:
            print(f"   🔒 {endpoint['method']} {endpoint['path']} - {endpoint['summary']}")
        
        return True
    else:
        print("⚠️  API文档显示配置需要完善")
        print(f"总体评分: {total_score*100:.1f}%")
        return False

if __name__ == "__main__":
    success = test_api_docs_display()
    if success:
        print("\n✅ 验证通过 - API文档将正确显示所有端点和权限标识")
    else:
        print("\n❌ 验证失败 - 需要进一步完善API文档配置")
