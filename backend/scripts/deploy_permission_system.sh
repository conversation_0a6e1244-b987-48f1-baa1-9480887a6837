#!/bin/bash

# 权限系统部署脚本
# 使用方法: ./deploy_permission_system.sh [environment]
# 环境选项: development, staging, production

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-development}
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    log_error "无效的环境参数: $ENVIRONMENT"
    log_info "使用方法: $0 [development|staging|production]"
    exit 1
fi

log_info "开始部署权限系统到 $ENVIRONMENT 环境..."

# 设置环境变量
export ENVIRONMENT=$ENVIRONMENT
export DJANGO_SETTINGS_MODULE="config.settings.$ENVIRONMENT"

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
cd "$PROJECT_ROOT"

log_info "项目根目录: $PROJECT_ROOT"

# 1. 检查依赖
log_info "检查系统依赖..."

# 检查Python版本
PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
REQUIRED_PYTHON="3.8.0"
if ! python3 -c "import sys; exit(0 if sys.version_info >= tuple(map(int, '$REQUIRED_PYTHON'.split('.'))) else 1)"; then
    log_error "Python版本过低，需要 >= $REQUIRED_PYTHON，当前版本: $PYTHON_VERSION"
    exit 1
fi
log_success "Python版本检查通过: $PYTHON_VERSION"

# 检查Redis连接
if command -v redis-cli &> /dev/null; then
    if redis-cli ping > /dev/null 2>&1; then
        log_success "Redis连接正常"
    else
        log_warning "Redis连接失败，请检查Redis服务"
    fi
else
    log_warning "未找到redis-cli命令"
fi

# 2. 创建必要目录
log_info "创建必要目录..."
mkdir -p logs
mkdir -p static
mkdir -p media
mkdir -p backups
mkdir -p config/ssl
log_success "目录创建完成"

# 3. 安装Python依赖
log_info "安装Python依赖..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
    log_success "Python依赖安装完成"
else
    log_warning "未找到requirements.txt文件"
fi

# 4. 环境配置检查
log_info "检查环境配置..."

# 检查必需的环境变量
REQUIRED_ENV_VARS=(
    "SECRET_KEY"
    "DATABASE_URL"
    "REDIS_URL"
)

for var in "${REQUIRED_ENV_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        log_error "缺少环境变量: $var"
        exit 1
    fi
done
log_success "环境变量检查通过"

# 5. 数据库迁移
log_info "执行数据库迁移..."
python3 manage.py makemigrations --noinput
python3 manage.py migrate --noinput
log_success "数据库迁移完成"

# 6. 创建超级用户（仅开发环境）
if [ "$ENVIRONMENT" = "development" ]; then
    log_info "创建超级用户..."
    if [ -n "$DJANGO_SUPERUSER_USERNAME" ] && [ -n "$DJANGO_SUPERUSER_PASSWORD" ] && [ -n "$DJANGO_SUPERUSER_EMAIL" ]; then
        python3 manage.py createsuperuser --noinput || log_warning "超级用户可能已存在"
        log_success "超级用户创建完成"
    else
        log_warning "未设置超级用户环境变量，跳过创建"
    fi
fi

# 7. 收集静态文件
log_info "收集静态文件..."
python3 manage.py collectstatic --noinput
log_success "静态文件收集完成"

# 8. 初始化权限数据
log_info "初始化权限数据..."
python3 manage.py shell << EOF
from zhi_oauth.models import Permission, Role
from django.contrib.auth import get_user_model

User = get_user_model()

# 创建基础权限
base_permissions = [
    # 用户管理权限
    {'code': 'user.view', 'name': '查看用户', 'permission_type': 'api'},
    {'code': 'user.create', 'name': '创建用户', 'permission_type': 'api'},
    {'code': 'user.update', 'name': '更新用户', 'permission_type': 'api'},
    {'code': 'user.delete', 'name': '删除用户', 'permission_type': 'api'},
    
    # 角色管理权限
    {'code': 'role.view', 'name': '查看角色', 'permission_type': 'api'},
    {'code': 'role.create', 'name': '创建角色', 'permission_type': 'api'},
    {'code': 'role.update', 'name': '更新角色', 'permission_type': 'api'},
    {'code': 'role.delete', 'name': '删除角色', 'permission_type': 'api'},
    {'code': 'role.assign', 'name': '分配角色', 'permission_type': 'api'},
    
    # 权限管理权限
    {'code': 'permission.view', 'name': '查看权限', 'permission_type': 'api'},
    {'code': 'permission.modify', 'name': '修改权限', 'permission_type': 'api'},
    
    # 系统管理权限
    {'code': 'system.config', 'name': '系统配置', 'permission_type': 'api'},
    {'code': 'system.monitor', 'name': '系统监控', 'permission_type': 'api'},
    {'code': 'admin.access', 'name': '管理后台访问', 'permission_type': 'menu'},
    
    # 数据权限
    {'code': 'data.export', 'name': '数据导出', 'permission_type': 'api'},
    {'code': 'data.import', 'name': '数据导入', 'permission_type': 'api'},
]

for perm_data in base_permissions:
    Permission.objects.get_or_create(
        code=perm_data['code'],
        defaults=perm_data
    )

print("基础权限创建完成")

# 创建基础角色
base_roles = [
    {
        'code': 'admin',
        'name': '系统管理员',
        'description': '拥有所有权限的系统管理员',
        'data_scope': 'all'
    },
    {
        'code': 'manager',
        'name': '部门经理',
        'description': '部门管理权限',
        'data_scope': 'org_and_sub'
    },
    {
        'code': 'user',
        'name': '普通用户',
        'description': '基础用户权限',
        'data_scope': 'self'
    }
]

for role_data in base_roles:
    role, created = Role.objects.get_or_create(
        code=role_data['code'],
        defaults=role_data
    )
    if created:
        print(f"创建角色: {role.name}")

# 为管理员角色分配所有权限
admin_role = Role.objects.get(code='admin')
all_permissions = Permission.objects.all()
admin_role.permissions.set(all_permissions)
print(f"为管理员角色分配了 {all_permissions.count()} 个权限")

print("权限数据初始化完成")
EOF

log_success "权限数据初始化完成"

# 9. 预热缓存
log_info "预热权限缓存..."
python3 manage.py shell << EOF
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager

try:
    result = unified_permission_manager.warm_up_cache()
    print(f"缓存预热结果: 成功 {result['success']}, 失败 {result['failed']}")
except Exception as e:
    print(f"缓存预热失败: {e}")
EOF

log_success "缓存预热完成"

# 10. 配置文件验证
log_info "验证配置文件..."
python3 manage.py shell << EOF
from config.permission_settings import validate_permission_config

try:
    validate_permission_config()
    print("配置验证通过")
except Exception as e:
    print(f"配置验证失败: {e}")
    exit(1)
EOF

log_success "配置验证通过"

# 11. 运行测试（仅开发和测试环境）
if [ "$ENVIRONMENT" != "production" ]; then
    log_info "运行权限系统测试..."
    python3 manage.py test zhi_common.zhi_auth --verbosity=2 || log_warning "部分测试失败"
    log_success "测试完成"
fi

# 12. 生成部署报告
log_info "生成部署报告..."
REPORT_FILE="deployment_report_$(date +%Y%m%d_%H%M%S).txt"

cat > "$REPORT_FILE" << EOF
权限系统部署报告
==================

部署时间: $(date)
环境: $ENVIRONMENT
Python版本: $PYTHON_VERSION
项目路径: $PROJECT_ROOT

部署步骤:
✓ 依赖检查
✓ 目录创建
✓ Python依赖安装
✓ 环境配置检查
✓ 数据库迁移
✓ 静态文件收集
✓ 权限数据初始化
✓ 缓存预热
✓ 配置验证

数据库信息:
- 权限数量: $(python3 manage.py shell -c "from zhi_oauth.models import Permission; print(Permission.objects.count())")
- 角色数量: $(python3 manage.py shell -c "from zhi_oauth.models import Role; print(Role.objects.count())")
- 用户数量: $(python3 manage.py shell -c "from django.contrib.auth import get_user_model; User = get_user_model(); print(User.objects.count())")

服务状态:
- Django: 已配置
- Redis: $(redis-cli ping 2>/dev/null || echo "未连接")
- 数据库: 已连接

下一步操作:
1. 启动应用服务器
2. 配置反向代理
3. 设置监控告警
4. 备份数据库

EOF

log_success "部署报告已生成: $REPORT_FILE"

# 13. 启动服务（开发环境）
if [ "$ENVIRONMENT" = "development" ]; then
    log_info "启动开发服务器..."
    echo "使用以下命令启动开发服务器:"
    echo "python3 manage.py runserver 0.0.0.0:8000"
    echo ""
    echo "或使用以下命令启动生产服务器:"
    echo "gunicorn config.wsgi:application --bind 0.0.0.0:8000 --workers 4"
fi

# 14. 生产环境特殊配置
if [ "$ENVIRONMENT" = "production" ]; then
    log_info "配置生产环境..."
    
    # 设置文件权限
    chmod 600 config/settings/production.py
    chmod 700 logs/
    
    # 创建systemd服务文件
    if command -v systemctl &> /dev/null; then
        cat > /tmp/zhi-admin.service << EOF
[Unit]
Description=Zhi Admin Django Application
After=network.target

[Service]
Type=forking
User=www-data
Group=www-data
WorkingDirectory=$PROJECT_ROOT
Environment=DJANGO_SETTINGS_MODULE=config.settings.production
ExecStart=$PROJECT_ROOT/venv/bin/gunicorn config.wsgi:application --bind 127.0.0.1:8000 --workers 4 --daemon --pid /var/run/zhi-admin.pid
ExecReload=/bin/kill -s HUP \$MAINPID
PIDFile=/var/run/zhi-admin.pid
Restart=always

[Install]
WantedBy=multi-user.target
EOF
        
        log_info "systemd服务文件已创建: /tmp/zhi-admin.service"
        log_info "请手动复制到 /etc/systemd/system/ 并启用服务"
    fi
    
    # 创建nginx配置
    cat > /tmp/zhi-admin-nginx.conf << EOF
server {
    listen 80;
    server_name your-domain.com;
    
    location /static/ {
        alias $PROJECT_ROOT/static/;
        expires 30d;
    }
    
    location /media/ {
        alias $PROJECT_ROOT/media/;
        expires 30d;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF
    
    log_info "Nginx配置文件已创建: /tmp/zhi-admin-nginx.conf"
    log_info "请手动复制到nginx配置目录并重载nginx"
fi

# 15. 完成部署
log_success "权限系统部署完成！"
log_info "部署环境: $ENVIRONMENT"
log_info "部署报告: $REPORT_FILE"

if [ "$ENVIRONMENT" = "development" ]; then
    log_info "开发环境访问地址: http://localhost:8000"
    log_info "管理后台地址: http://localhost:8000/admin"
fi

log_info "请查看部署报告了解详细信息"
log_info "如有问题，请检查日志文件: logs/"

exit 0
