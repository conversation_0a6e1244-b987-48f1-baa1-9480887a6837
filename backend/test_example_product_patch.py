"""
测试ExampleProductControllerAPI是否有patch方法
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_oauth.apis.example_product import ExampleProductControllerAPI


def test_example_product_methods():
    """测试ExampleProductControllerAPI的方法"""
    print("🧪 测试ExampleProductControllerAPI的方法...")
    
    # 创建实例
    controller = ExampleProductControllerAPI()
    
    # 检查方法
    methods_to_check = [
        'create',
        'list',
        'retrieve', 
        'update',
        'patch',
        'delete',
        'partial_update'
    ]
    
    print("📋 检查控制器方法:")
    available_methods = []
    for method_name in methods_to_check:
        if hasattr(controller, method_name):
            method = getattr(controller, method_name)
            print(f"   ✅ {method_name}: {type(method)}")
            available_methods.append(method_name)
        else:
            print(f"   ❌ {method_name}: 不存在")
    
    print(f"\n📊 可用方法: {len(available_methods)}/{len(methods_to_check)}")
    print(f"可用方法列表: {available_methods}")
    
    # 检查基类方法
    print(f"\n📋 基类信息:")
    print(f"   类名: {controller.__class__.__name__}")
    print(f"   基类: {[cls.__name__ for cls in controller.__class__.__mro__]}")
    
    # 检查服务实例
    if hasattr(controller, 'service'):
        service = controller.service
        print(f"\n📋 服务实例信息:")
        print(f"   服务类型: {type(service)}")
        print(f"   服务类名: {service.__class__.__name__}")
        print(f"   服务基类: {[cls.__name__ for cls in service.__class__.__mro__]}")

        print(f"\n📋 服务实例方法:")
        service_methods = []
        for method_name in methods_to_check:
            if hasattr(service, method_name):
                method = getattr(service, method_name)
                print(f"   ✅ service.{method_name}: {type(method)} - {method}")
                service_methods.append(method_name)
            else:
                print(f"   ❌ service.{method_name}: 不存在")
        print(f"服务方法: {len(service_methods)}/{len(methods_to_check)}")

        # 检查服务实例是否有我们添加的方法
        print(f"\n📋 检查服务实例的所有方法:")
        all_methods = [name for name in dir(service) if not name.startswith('_')]
        print(f"   所有公共方法: {all_methods}")

        # 特别检查patch相关方法
        patch_related = [name for name in all_methods if 'patch' in name.lower()]
        print(f"   patch相关方法: {patch_related}")
    
    # 检查是否是自动生成的方法
    print(f"\n📋 检查自动生成的方法:")
    for method_name in ['patch', 'update']:
        if hasattr(controller, method_name):
            method = getattr(controller, method_name)
            print(f"   {method_name}: {method}")
            if hasattr(method, '__func__'):
                print(f"     函数: {method.__func__}")
            if hasattr(method, '__self__'):
                print(f"     绑定对象: {method.__self__}")
    
    return len(available_methods) >= 5  # 至少要有5个基本方法


def main():
    """主测试函数"""
    print("🚀 开始测试ExampleProductControllerAPI...")
    
    try:
        success = test_example_product_methods()
        
        if success:
            print("\n✅ 测试基本通过")
        else:
            print("\n❌ 测试失败，缺少关键方法")
            
        return success
        
    except Exception as e:
        print(f"❌ 测试出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
