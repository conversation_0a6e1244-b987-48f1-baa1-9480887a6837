{"version": "1.0", "description": "权限策略配置文件", "last_updated": "2024-01-01T00:00:00Z", "policies": [{"name": "admin_full_access", "description": "管理员全权限访问策略", "priority": 100, "effect": "allow", "conditions": [{"type": "attribute", "attribute_path": "user.is_superuser", "operator": "eq", "value": true}]}, {"name": "work_hours_access", "description": "工作时间访问策略", "priority": 50, "effect": "allow", "conditions": [{"type": "time", "start_time": "09:00", "end_time": "18:00", "weekdays": [0, 1, 2, 3, 4], "timezone": "Asia/Shanghai"}]}, {"name": "confidential_document_restriction", "description": "机密文档访问限制", "priority": 80, "effect": "deny", "conditions": [{"type": "composite", "operator": "and", "conditions": [{"type": "attribute", "attribute_path": "resource.is_confidential", "operator": "eq", "value": true}, {"type": "composite", "operator": "or", "conditions": [{"type": "time", "start_time": "22:00", "end_time": "06:00"}, {"type": "time", "weekdays": [5, 6]}]}]}]}, {"name": "department_data_access", "description": "部门数据访问策略", "priority": 30, "effect": "allow", "conditions": [{"type": "attribute", "attribute_path": "user.department_id", "operator": "eq", "value": "resource.department_id"}]}, {"name": "ip_whitelist_policy", "description": "IP白名单策略", "priority": 70, "effect": "allow", "conditions": [{"type": "ip", "allowed_ips": ["***********/24", "10.0.0.0/8", "**********/12"]}]}, {"name": "sensitive_operation_restriction", "description": "敏感操作限制策略", "priority": 90, "effect": "deny", "conditions": [{"type": "composite", "operator": "and", "conditions": [{"type": "attribute", "attribute_path": "permission_code", "operator": "regex", "value": ".*\\.(delete|modify_permission|assign_role)$"}, {"type": "composite", "operator": "or", "conditions": [{"type": "time", "start_time": "20:00", "end_time": "08:00"}, {"type": "time", "weekdays": [5, 6]}]}]}]}, {"name": "guest_user_restriction", "description": "访客用户限制策略", "priority": 60, "effect": "deny", "conditions": [{"type": "composite", "operator": "and", "conditions": [{"type": "attribute", "attribute_path": "user.user_type", "operator": "eq", "value": "guest"}, {"type": "attribute", "attribute_path": "permission_code", "operator": "regex", "value": ".*\\.(create|update|delete)$"}]}]}, {"name": "trial_user_limitation", "description": "试用用户限制策略", "priority": 40, "effect": "deny", "conditions": [{"type": "composite", "operator": "and", "conditions": [{"type": "attribute", "attribute_path": "user.is_trial", "operator": "eq", "value": true}, {"type": "attribute", "attribute_path": "permission_code", "operator": "in", "value": ["user.delete", "system.config", "data.export_all", "admin.access"]}]}]}, {"name": "api_rate_limit_policy", "description": "API频率限制策略", "priority": 20, "effect": "deny", "conditions": [{"type": "attribute", "attribute_path": "context.request_count_per_minute", "operator": "gt", "value": 100}]}, {"name": "emergency_access_policy", "description": "紧急访问策略", "priority": 95, "effect": "allow", "conditions": [{"type": "composite", "operator": "and", "conditions": [{"type": "attribute", "attribute_path": "user.roles", "operator": "contains", "value": "emergency_admin"}, {"type": "attribute", "attribute_path": "context.emergency_mode", "operator": "eq", "value": true}]}]}], "field_permissions": {"document": {"public_fields": ["id", "title", "category", "created_at"], "restricted_fields": {"content": {"required_permission": "document.view_content", "roles": ["editor", "admin"]}, "is_confidential": {"required_permission": "document.view_confidential_flag", "roles": ["manager", "admin"]}, "creator_id": {"required_permission": "document.view_creator", "roles": ["manager", "admin"]}}}, "user": {"public_fields": ["id", "username", "display_name"], "restricted_fields": {"email": {"required_permission": "user.view_email", "roles": ["hr", "admin"]}, "phone": {"required_permission": "user.view_phone", "roles": ["hr", "admin"]}, "salary": {"required_permission": "user.view_salary", "roles": ["hr_manager", "admin"]}, "last_login": {"required_permission": "user.view_login_info", "roles": ["security", "admin"]}}}}, "data_scope_rules": {"tenant": {"filter_expression": "tenant_id = user.tenant_id", "description": "按租户过滤数据"}, "org": {"filter_expression": "organization_id = user.organization_id", "description": "按组织过滤数据"}, "org_and_sub": {"filter_expression": "organization_id IN (SELECT id FROM organizations WHERE path LIKE CONCAT(user.organization.path, '%'))", "description": "按组织及子组织过滤数据"}, "self": {"filter_expression": "creator_id = user.id", "description": "只显示自己创建的数据"}, "department": {"filter_expression": "department_id = user.department_id", "description": "按部门过滤数据"}, "custom": {"filter_expression": "custom_filter_function(user, resource)", "description": "自定义过滤规则"}}, "risk_assessment_rules": {"high_risk_permissions": ["user.delete", "role.assign", "permission.modify", "system.config", "data.export_all", "admin.access"], "sensitive_resources": ["confidential_document", "financial_data", "personal_info", "system_config"], "unusual_access_patterns": {"off_hours_access": {"description": "非工作时间访问", "risk_score": 3, "conditions": {"time_range": ["22:00", "06:00"], "weekdays": [5, 6]}}, "multiple_failed_attempts": {"description": "多次失败尝试", "risk_score": 4, "conditions": {"failed_attempts": ">= 3", "time_window": "5 minutes"}}, "unusual_ip_access": {"description": "异常IP访问", "risk_score": 3, "conditions": {"ip_not_in_history": true, "geographic_distance": "> 1000km"}}, "privilege_escalation": {"description": "权限提升尝试", "risk_score": 5, "conditions": {"permission_level_increase": true, "time_since_last_promotion": "< 24 hours"}}}}, "compliance_rules": {"gdpr": {"description": "GDPR合规规则", "rules": [{"name": "personal_data_access_log", "description": "个人数据访问必须记录", "applies_to": ["user.view", "user.export"], "action": "log_access"}, {"name": "data_retention_limit", "description": "数据保留期限制", "applies_to": ["audit_log"], "retention_days": 1095}]}, "sox": {"description": "SOX合规规则", "rules": [{"name": "financial_data_segregation", "description": "财务数据职责分离", "applies_to": ["financial.*"], "requires_dual_approval": true}, {"name": "audit_trail_integrity", "description": "审计轨迹完整性", "applies_to": ["*"], "immutable_logs": true}]}}, "alert_rules": {"security_alerts": [{"name": "multiple_permission_denials", "description": "多次权限拒绝告警", "condition": "permission_denied_count > 10 in 5 minutes", "severity": "medium", "action": "send_alert"}, {"name": "admin_privilege_usage", "description": "管理员权限使用告警", "condition": "admin_permission_used AND hour NOT IN (9-17)", "severity": "high", "action": "send_alert"}, {"name": "suspicious_login_pattern", "description": "可疑登录模式告警", "condition": "login_from_new_location AND failed_attempts > 2", "severity": "high", "action": "lock_account"}], "performance_alerts": [{"name": "permission_check_slow", "description": "权限检查缓慢告警", "condition": "avg_permission_check_time > 1000ms", "severity": "medium", "action": "optimize_cache"}, {"name": "cache_hit_rate_low", "description": "缓存命中率低告警", "condition": "cache_hit_rate < 80%", "severity": "low", "action": "warm_cache"}]}, "integration_settings": {"ldap": {"enabled": false, "server": "ldap://ldap.example.com", "base_dn": "dc=example,dc=com", "user_dn_template": "uid=%(user)s,ou=users,dc=example,dc=com", "group_mapping": {"admin": "cn=admins,ou=groups,dc=example,dc=com", "user": "cn=users,ou=groups,dc=example,dc=com"}}, "oauth2": {"enabled": true, "providers": {"google": {"client_id": "your-google-client-id", "client_secret": "your-google-client-secret", "scope": ["openid", "email", "profile"]}, "microsoft": {"client_id": "your-microsoft-client-id", "client_secret": "your-microsoft-client-secret", "tenant": "your-tenant-id"}}}, "saml": {"enabled": false, "entity_id": "your-entity-id", "sso_url": "https://your-idp.com/sso", "x509_cert": "your-x509-certificate"}}}