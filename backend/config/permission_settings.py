"""
权限系统配置文件
"""

import os
from datetime import timedelta

# ==================== 基础配置 ====================

# 权限管理功能开关
PERMISSION_MANAGEMENT = {
    # 核心功能开关
    'ENABLE_CACHING': True,                    # 启用权限缓存
    'ENABLE_DYNAMIC_POLICIES': True,           # 启用动态权限策略
    'ENABLE_AUDITING': True,                   # 启用权限审计
    'ENABLE_MONITORING': True,                 # 启用权限监控
    'ENABLE_FIELD_PERMISSIONS': True,          # 启用字段级权限
    
    # 缓存配置
    'CACHE_BACKEND': 'redis',                  # 缓存后端: redis/django/memory
    'CACHE_TIMEOUT': 300,                      # 默认缓存时间(秒)
    'CACHE_PREFIX': 'perm_cache',              # 缓存键前缀
    'CACHE_VERSION': 1,                        # 缓存版本
    
    # 审计配置
    'AUDIT_LOG_LEVEL': 'INFO',                 # 审计日志级别
    'AUDIT_ASYNC_LOGGING': True,               # 异步审计日志
    'AUDIT_RETENTION_DAYS': 90,                # 审计日志保留天数
    'AUDIT_SENSITIVE_FIELDS': [                # 敏感字段列表
        'password', 'token', 'secret', 'key'
    ],
    
    # 监控配置
    'MONITOR_ALERT_THRESHOLD': {               # 告警阈值
        'failed_attempts': 5,                  # 失败尝试次数
        'unusual_access_count': 10,            # 异常访问次数
        'high_risk_events': 3,                 # 高风险事件次数
    },
    'MONITOR_REPORT_INTERVAL': 3600,           # 监控报告间隔(秒)
    
    # 动态策略配置
    'POLICY_EVALUATION_TIMEOUT': 5,            # 策略评估超时(秒)
    'POLICY_CACHE_TIMEOUT': 600,               # 策略缓存时间(秒)
    'POLICY_CONFIG_FILE': 'config/permission_policies.json',  # 策略配置文件
}

# ==================== 缓存配置 ====================

# Redis缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://127.0.0.1:6379/1'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 50,
                'retry_on_timeout': True,
            },
            'SERIALIZER': 'django_redis.serializers.json.JSONSerializer',
            'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        },
        'KEY_PREFIX': 'zhi_admin',
        'VERSION': 1,
        'TIMEOUT': 300,
    },
    
    # 权限专用缓存
    'permissions': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': os.getenv('REDIS_URL', 'redis://127.0.0.1:6379/2'),
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {
                'max_connections': 30,
                'retry_on_timeout': True,
            },
        },
        'KEY_PREFIX': 'permissions',
        'VERSION': 1,
        'TIMEOUT': 600,  # 权限缓存时间更长
    }
}

# ==================== 日志配置 ====================

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'audit': {
            'format': '[AUDIT] {asctime} {levelname} {message}',
            'style': '{',
        },
        'security': {
            'format': '[SECURITY] {asctime} {levelname} {message} | User: {user_id} | IP: {client_ip}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        },
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/django.log',
            'maxBytes': 1024*1024*10,  # 10MB
            'backupCount': 5,
            'formatter': 'verbose',
        },
        'audit_file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/audit.log',
            'maxBytes': 1024*1024*50,  # 50MB
            'backupCount': 10,
            'formatter': 'audit',
        },
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/security.log',
            'maxBytes': 1024*1024*20,  # 20MB
            'backupCount': 10,
            'formatter': 'security',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'zhi_auth.audit': {
            'handlers': ['audit_file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'zhi_auth.security': {
            'handlers': ['security_file', 'console'],
            'level': 'WARNING',
            'propagate': False,
        },
        'zhi_auth.permission': {
            'handlers': ['console', 'file'],
            'level': 'DEBUG' if os.getenv('DEBUG') == 'True' else 'INFO',
            'propagate': True,
        },
    },
}

# ==================== 安全配置 ====================

# 权限安全配置
PERMISSION_SECURITY = {
    # 会话安全
    'SESSION_TIMEOUT': 3600,                   # 会话超时时间(秒)
    'SESSION_REFRESH_THRESHOLD': 300,          # 会话刷新阈值(秒)
    'MAX_CONCURRENT_SESSIONS': 3,              # 最大并发会话数
    
    # 登录安全
    'MAX_LOGIN_ATTEMPTS': 5,                   # 最大登录尝试次数
    'LOGIN_ATTEMPT_TIMEOUT': 300,              # 登录尝试超时(秒)
    'ACCOUNT_LOCKOUT_DURATION': 1800,          # 账户锁定时长(秒)
    
    # IP安全
    'ENABLE_IP_WHITELIST': False,              # 启用IP白名单
    'IP_WHITELIST': [],                        # IP白名单
    'ENABLE_IP_BLACKLIST': True,               # 启用IP黑名单
    'IP_BLACKLIST': [],                        # IP黑名单
    
    # 权限检查安全
    'PERMISSION_CHECK_TIMEOUT': 5,             # 权限检查超时(秒)
    'MAX_PERMISSION_CHECKS_PER_MINUTE': 100,   # 每分钟最大权限检查次数
    'ENABLE_PERMISSION_RATE_LIMIT': True,      # 启用权限检查频率限制
    
    # 敏感操作
    'SENSITIVE_OPERATIONS': [                  # 敏感操作列表
        'user.delete',
        'role.assign',
        'permission.modify',
        'system.config',
        'data.export',
    ],
    'REQUIRE_ADDITIONAL_AUTH': True,           # 敏感操作需要额外认证
}

# ==================== 数据权限配置 ====================

# 数据权限范围配置
DATA_PERMISSION_SCOPES = {
    'all': {
        'name': '全部数据',
        'description': '可以访问所有数据',
        'filter_func': None,
    },
    'tenant': {
        'name': '租户数据',
        'description': '只能访问本租户数据',
        'filter_func': 'filter_by_tenant',
    },
    'org': {
        'name': '本部门数据',
        'description': '只能访问本部门数据',
        'filter_func': 'filter_by_organization',
    },
    'org_and_sub': {
        'name': '本部门及子部门数据',
        'description': '可以访问本部门及其子部门数据',
        'filter_func': 'filter_by_organization_tree',
    },
    'self': {
        'name': '仅本人数据',
        'description': '只能访问自己创建的数据',
        'filter_func': 'filter_by_creator',
    },
    'custom': {
        'name': '自定义数据范围',
        'description': '根据自定义规则过滤数据',
        'filter_func': 'filter_by_custom_rules',
    },
}

# ==================== 监控告警配置 ====================

# 告警配置
ALERT_SETTINGS = {
    'ENABLE_EMAIL_ALERTS': True,               # 启用邮件告警
    'ENABLE_SMS_ALERTS': False,                # 启用短信告警
    'ENABLE_WEBHOOK_ALERTS': True,             # 启用Webhook告警
    
    # 邮件告警配置
    'EMAIL_ALERT_RECIPIENTS': [
        '<EMAIL>',
        '<EMAIL>',
    ],
    'EMAIL_ALERT_SUBJECT_PREFIX': '[权限告警]',
    
    # Webhook告警配置
    'WEBHOOK_ALERT_URL': os.getenv('ALERT_WEBHOOK_URL'),
    'WEBHOOK_ALERT_TIMEOUT': 10,
    
    # 告警规则
    'ALERT_RULES': {
        'high_risk_events': {
            'threshold': 5,
            'window': 300,  # 5分钟内
            'severity': 'high',
        },
        'failed_login_attempts': {
            'threshold': 10,
            'window': 600,  # 10分钟内
            'severity': 'medium',
        },
        'permission_denied_spike': {
            'threshold': 50,
            'window': 300,  # 5分钟内
            'severity': 'medium',
        },
        'unusual_access_pattern': {
            'threshold': 20,
            'window': 1800,  # 30分钟内
            'severity': 'low',
        },
    },
}

# ==================== 性能优化配置 ====================

# 性能配置
PERFORMANCE_SETTINGS = {
    # 查询优化
    'ENABLE_QUERY_OPTIMIZATION': True,         # 启用查询优化
    'MAX_QUERY_RESULTS': 1000,                 # 最大查询结果数
    'ENABLE_PAGINATION': True,                 # 启用分页
    'DEFAULT_PAGE_SIZE': 20,                   # 默认分页大小
    
    # 缓存优化
    'ENABLE_QUERY_CACHE': True,                # 启用查询缓存
    'QUERY_CACHE_TIMEOUT': 300,                # 查询缓存超时
    'ENABLE_RESULT_CACHE': True,               # 启用结果缓存
    'RESULT_CACHE_TIMEOUT': 600,               # 结果缓存超时
    
    # 并发控制
    'MAX_CONCURRENT_REQUESTS': 100,            # 最大并发请求数
    'REQUEST_TIMEOUT': 30,                     # 请求超时时间
    'ENABLE_REQUEST_THROTTLING': True,         # 启用请求限流
    
    # 数据库优化
    'ENABLE_DB_CONNECTION_POOLING': True,      # 启用数据库连接池
    'DB_CONNECTION_POOL_SIZE': 20,             # 连接池大小
    'DB_CONNECTION_MAX_OVERFLOW': 10,          # 连接池最大溢出
}

# ==================== 开发环境配置 ====================

if os.getenv('ENVIRONMENT') == 'development':
    # 开发环境特殊配置
    PERMISSION_MANAGEMENT.update({
        'ENABLE_CACHING': False,               # 开发环境禁用缓存
        'CACHE_TIMEOUT': 60,                   # 短缓存时间
        'AUDIT_LOG_LEVEL': 'DEBUG',            # 详细日志
    })
    
    PERMISSION_SECURITY.update({
        'MAX_LOGIN_ATTEMPTS': 10,              # 更宽松的登录限制
        'ACCOUNT_LOCKOUT_DURATION': 300,       # 更短的锁定时间
    })

# ==================== 生产环境配置 ====================

if os.getenv('ENVIRONMENT') == 'production':
    # 生产环境特殊配置
    PERMISSION_MANAGEMENT.update({
        'CACHE_TIMEOUT': 600,                  # 更长的缓存时间
        'AUDIT_RETENTION_DAYS': 365,           # 更长的审计日志保留
    })
    
    PERMISSION_SECURITY.update({
        'MAX_LOGIN_ATTEMPTS': 3,               # 更严格的登录限制
        'ACCOUNT_LOCKOUT_DURATION': 3600,      # 更长的锁定时间
        'ENABLE_IP_WHITELIST': True,           # 启用IP白名单
    })
    
    ALERT_SETTINGS.update({
        'ENABLE_SMS_ALERTS': True,             # 生产环境启用短信告警
    })

# ==================== 导出配置 ====================

# 将配置添加到Django settings
def apply_permission_settings(settings_module):
    """将权限配置应用到Django settings"""
    
    # 添加权限管理配置
    settings_module.PERMISSION_MANAGEMENT = PERMISSION_MANAGEMENT
    settings_module.PERMISSION_SECURITY = PERMISSION_SECURITY
    settings_module.DATA_PERMISSION_SCOPES = DATA_PERMISSION_SCOPES
    settings_module.ALERT_SETTINGS = ALERT_SETTINGS
    settings_module.PERFORMANCE_SETTINGS = PERFORMANCE_SETTINGS
    
    # 更新缓存配置
    if hasattr(settings_module, 'CACHES'):
        settings_module.CACHES.update(CACHES)
    else:
        settings_module.CACHES = CACHES
    
    # 更新日志配置
    if hasattr(settings_module, 'LOGGING'):
        # 合并日志配置
        existing_logging = settings_module.LOGGING
        existing_logging['formatters'].update(LOGGING['formatters'])
        existing_logging['handlers'].update(LOGGING['handlers'])
        existing_logging['loggers'].update(LOGGING['loggers'])
    else:
        settings_module.LOGGING = LOGGING
    
    # 添加中间件
    if hasattr(settings_module, 'MIDDLEWARE'):
        permission_middleware = [
            'zhi_common.zhi_auth.middleware.PermissionAuditMiddleware',
            'zhi_common.zhi_auth.middleware.SecurityMiddleware',
        ]
        for middleware in permission_middleware:
            if middleware not in settings_module.MIDDLEWARE:
                settings_module.MIDDLEWARE.append(middleware)
    
    # 添加应用
    if hasattr(settings_module, 'INSTALLED_APPS'):
        permission_apps = [
            'zhi_common.zhi_auth',
        ]
        for app in permission_apps:
            if app not in settings_module.INSTALLED_APPS:
                settings_module.INSTALLED_APPS.append(app)

# ==================== 配置验证 ====================

def validate_permission_config():
    """验证权限配置"""
    errors = []
    
    # 检查必需的环境变量
    required_env_vars = ['REDIS_URL', 'SECRET_KEY']
    for var in required_env_vars:
        if not os.getenv(var):
            errors.append(f"缺少环境变量: {var}")
    
    # 检查缓存配置
    if PERMISSION_MANAGEMENT['ENABLE_CACHING']:
        if PERMISSION_MANAGEMENT['CACHE_BACKEND'] == 'redis':
            try:
                import redis
                import django_redis
            except ImportError:
                errors.append("启用Redis缓存但未安装redis或django-redis")
    
    # 检查日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except OSError:
            errors.append(f"无法创建日志目录: {log_dir}")
    
    # 检查策略配置文件
    policy_file = PERMISSION_MANAGEMENT['POLICY_CONFIG_FILE']
    if not os.path.exists(policy_file):
        errors.append(f"策略配置文件不存在: {policy_file}")
    
    if errors:
        raise ValueError(f"权限配置验证失败: {'; '.join(errors)}")
    
    return True
