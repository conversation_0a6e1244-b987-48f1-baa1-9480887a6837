"""
动态创建类来测试patch方法继承
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_common.zhi_services.base_model_service import BaseModelService
from zhi_oauth.models import ExampleProduct


def test_dynamic_class_creation():
    """动态创建类来测试patch方法继承"""
    print("🧪 动态创建类来测试patch方法继承...")
    
    # 首先验证BaseModelService有patch方法
    print("📋 验证BaseModelService:")
    base_service = BaseModelService(model_class=ExampleProduct)
    
    methods_to_check = ['update', 'patch', 'partial_update', '_should_update_field']
    base_has_patch = False
    
    for method_name in methods_to_check:
        if hasattr(base_service, method_name):
            print(f"   ✅ BaseModelService.{method_name}: 存在")
            if method_name == 'patch':
                base_has_patch = True
        else:
            print(f"   ❌ BaseModelService.{method_name}: 不存在")
    
    if not base_has_patch:
        print("❌ BaseModelService没有patch方法，无法继续测试")
        return False
    
    # 动态创建一个新的服务类
    print("\n📋 动态创建新的服务类:")
    
    class DynamicExampleService(BaseModelService):
        """动态创建的示例服务类"""
        model = ExampleProduct
        model_exclude = ['created_at', 'updated_at', 'is_deleted', 'deleted_at']
    
    # 测试动态创建的类
    dynamic_service = DynamicExampleService(model_class=ExampleProduct)
    
    print("📋 测试动态创建的服务类:")
    dynamic_has_patch = False
    
    for method_name in methods_to_check:
        if hasattr(dynamic_service, method_name):
            print(f"   ✅ DynamicExampleService.{method_name}: 存在")
            if method_name == 'patch':
                dynamic_has_patch = True
        else:
            print(f"   ❌ DynamicExampleService.{method_name}: 不存在")
    
    # 检查所有公共方法
    all_methods = [name for name in dir(dynamic_service) if not name.startswith('_')]
    patch_related = [name for name in all_methods if 'patch' in name.lower()]
    print(f"\n📋 DynamicExampleService的patch相关方法: {patch_related}")
    
    # 尝试调用patch方法
    if dynamic_has_patch:
        print("\n📋 尝试调用patch方法:")
        try:
            import inspect
            patch_signature = inspect.signature(dynamic_service.patch)
            print(f"   patch方法签名: {patch_signature}")
            print(f"   ✅ patch方法可调用")
        except Exception as e:
            print(f"   ❌ patch方法调用失败: {e}")
    
    return dynamic_has_patch


def test_existing_class_patch():
    """测试现有的ExampleProductControllerAPI类"""
    print("\n🧪 测试现有的ExampleProductControllerAPI类...")
    
    # 导入现有的类
    from zhi_oauth.apis.example_product import ExampleProductControllerAPI
    
    # 检查类的方法解析顺序(MRO)
    print("📋 ExampleProductControllerAPI的MRO:")
    for i, cls in enumerate(ExampleProductControllerAPI.__mro__):
        print(f"   {i}: {cls}")
    
    # 检查BaseModelService在MRO中的位置
    base_service_in_mro = BaseModelService in ExampleProductControllerAPI.__mro__
    print(f"\n📋 BaseModelService在MRO中: {base_service_in_mro}")
    
    # 检查类级别的方法
    print("\n📋 检查类级别的方法:")
    methods_to_check = ['update', 'patch', 'partial_update', '_should_update_field']
    
    for method_name in methods_to_check:
        if hasattr(ExampleProductControllerAPI, method_name):
            method = getattr(ExampleProductControllerAPI, method_name)
            print(f"   ✅ ExampleProductControllerAPI.{method_name}: {type(method)} - {method}")
        else:
            print(f"   ❌ ExampleProductControllerAPI.{method_name}: 不存在")
    
    # 检查BaseModelService的方法是否在ExampleProductControllerAPI中
    print("\n📋 检查BaseModelService的方法是否被继承:")
    for method_name in methods_to_check:
        if hasattr(BaseModelService, method_name):
            base_method = getattr(BaseModelService, method_name)
            if hasattr(ExampleProductControllerAPI, method_name):
                example_method = getattr(ExampleProductControllerAPI, method_name)
                is_same = base_method == example_method
                print(f"   {method_name}: BaseModelService有 -> ExampleProductControllerAPI{'有' if hasattr(ExampleProductControllerAPI, method_name) else '无'} (相同: {is_same})")
            else:
                print(f"   {method_name}: BaseModelService有 -> ExampleProductControllerAPI无")
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始动态类创建测试...")
    
    try:
        test1_success = test_dynamic_class_creation()
        test2_success = test_existing_class_patch()
        
        if test1_success:
            print("\n✅ 动态创建的类有patch方法")
        else:
            print("\n❌ 动态创建的类没有patch方法")
        
        if test2_success:
            print("✅ 现有类分析完成")
        
        return test1_success and test2_success
        
    except Exception as e:
        print(f"❌ 测试出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
