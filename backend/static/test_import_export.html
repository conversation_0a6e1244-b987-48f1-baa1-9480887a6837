<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ExampleProduct 导入导出测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .section {
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button.success {
            background-color: #28a745;
        }
        button.warning {
            background-color: #ffc107;
            color: #212529;
        }
        button.danger {
            background-color: #dc3545;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
        .file-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 ExampleProduct 导入导出功能测试</h1>
        
        <div class="grid">
            <!-- 导出功能 -->
            <div class="section">
                <h2>📤 数据导出</h2>
                
                <div class="form-group">
                    <label for="exportFormat">导出格式:</label>
                    <select id="exportFormat">
                        <option value="excel">Excel (.xlsx)</option>
                        <option value="csv">CSV (.csv)</option>
                        <option value="json">JSON (.json)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="exportFilename">文件名 (可选):</label>
                    <input type="text" id="exportFilename" placeholder="example_products">
                </div>
                
                <div class="form-group">
                    <label for="exportFields">导出字段 (可选，逗号分隔):</label>
                    <input type="text" id="exportFields" placeholder="name,description">
                </div>
                
                <div class="form-group">
                    <label for="exportFilter">名称过滤 (可选):</label>
                    <input type="text" id="exportFilter" placeholder="测试">
                </div>
                
                <button onclick="exportData()">导出数据</button>
                <button class="success" onclick="getExportFields()">获取字段信息</button>
                
                <div id="exportResult" class="result" style="display: none;"></div>
            </div>
            
            <!-- 导入功能 -->
            <div class="section">
                <h2>📥 数据导入</h2>
                
                <div class="form-group">
                    <label for="importFile">选择文件:</label>
                    <input type="file" id="importFile" accept=".csv,.xlsx,.xls">
                    <div class="file-info">支持 CSV 和 Excel 格式，最大 2MB</div>
                </div>
                
                <button onclick="previewImport()">预览导入</button>
                <button class="warning" onclick="importData()">执行导入</button>
                <button class="success" onclick="downloadTemplate('excel')">下载Excel模板</button>
                <button class="success" onclick="downloadTemplate('csv')">下载CSV模板</button>
                
                <div id="importResult" class="result" style="display: none;"></div>
            </div>
        </div>
        
        <!-- 测试数据 -->
        <div class="section">
            <h2>🧪 测试数据</h2>
            <p>你可以创建一个包含以下内容的CSV文件进行测试：</p>
            <textarea readonly rows="6">产品名称,产品描述
测试产品A,这是测试产品A的描述
测试产品B,这是测试产品B的描述
测试产品C,这是测试产品C的描述
测试产品D,这是测试产品D的描述
测试产品E,这是一个很长的描述用来测试字段长度</textarea>
            <button onclick="createTestFile()">创建测试CSV文件</button>
        </div>
    </div>

    <script>
        const API_BASE = '/api/ExampleProduct';
        
        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
        
        // 导出数据
        async function exportData() {
            try {
                const format = document.getElementById('exportFormat').value;
                const filename = document.getElementById('exportFilename').value;
                const fields = document.getElementById('exportFields').value;
                const filter = document.getElementById('exportFilter').value;
                
                let url = `${API_BASE}/export?format=${format}`;
                if (filename) url += `&filename=${filename}`;
                if (fields) url += `&fields=${fields}`;
                if (filter) url += `&name=${filter}`;
                
                showResult('exportResult', '正在导出...', 'info');
                
                const response = await fetch(url);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = `${filename || 'example_products'}.${format === 'excel' ? 'xlsx' : format}`;
                    a.click();
                    
                    showResult('exportResult', `导出成功！文件已下载。`, 'success');
                } else {
                    const error = await response.text();
                    showResult('exportResult', `导出失败: ${error}`, 'error');
                }
            } catch (error) {
                showResult('exportResult', `导出错误: ${error.message}`, 'error');
            }
        }
        
        // 获取导出字段信息
        async function getExportFields() {
            try {
                showResult('exportResult', '正在获取字段信息...', 'info');
                
                const response = await fetch(`${API_BASE}/export/fields`);
                const result = await response.json();
                
                if (result.success) {
                    const fields = result.data.map(field => 
                        `${field.name} (${field.verbose_name}) - ${field.type}${field.required ? ' [必填]' : ''}`
                    ).join('\n');
                    
                    showResult('exportResult', `可导出字段:\n${fields}`, 'success');
                } else {
                    showResult('exportResult', `获取字段信息失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('exportResult', `获取字段信息错误: ${error.message}`, 'error');
            }
        }
        
        // 预览导入
        async function previewImport() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('importResult', '请先选择文件', 'error');
                return;
            }
            
            try {
                showResult('importResult', '正在预览...', 'info');
                
                const formData = new FormData();
                formData.append('file', file);
                
                const response = await fetch(`${API_BASE}/import/preview`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const preview = result.data;
                    const message = `预览结果:
总行数: ${preview.total_rows}
表头: ${preview.headers.join(', ')}
示例数据: ${JSON.stringify(preview.sample_data.slice(0, 3), null, 2)}
${preview.errors.length > 0 ? `错误: ${preview.errors.join(', ')}` : ''}`;
                    
                    showResult('importResult', message, 'success');
                } else {
                    showResult('importResult', `预览失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('importResult', `预览错误: ${error.message}`, 'error');
            }
        }
        
        // 执行导入
        async function importData() {
            const fileInput = document.getElementById('importFile');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('importResult', '请先选择文件', 'error');
                return;
            }
            
            try {
                showResult('importResult', '正在导入...', 'info');
                
                const formData = new FormData();
                formData.append('file', file);
                formData.append('preview_only', 'false');
                
                const response = await fetch(`${API_BASE}/import`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const importResult = result.data;
                    const message = `导入完成:
成功: ${importResult.success_rows} 条
失败: ${importResult.error_rows} 条
总计: ${importResult.total_rows} 条
${importResult.errors.length > 0 ? `错误信息: ${importResult.errors.join(', ')}` : ''}`;
                    
                    showResult('importResult', message, importResult.success ? 'success' : 'error');
                } else {
                    showResult('importResult', `导入失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showResult('importResult', `导入错误: ${error.message}`, 'error');
            }
        }
        
        // 下载模板
        async function downloadTemplate(format) {
            try {
                showResult('importResult', `正在生成${format.toUpperCase()}模板...`, 'info');
                
                const response = await fetch(`${API_BASE}/import/template?format=${format}`);
                
                if (response.ok) {
                    const blob = await response.blob();
                    const downloadUrl = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = downloadUrl;
                    a.download = `example_product_template.${format === 'excel' ? 'xlsx' : 'csv'}`;
                    a.click();
                    
                    showResult('importResult', `${format.toUpperCase()}模板下载成功！`, 'success');
                } else {
                    const error = await response.text();
                    showResult('importResult', `模板下载失败: ${error}`, 'error');
                }
            } catch (error) {
                showResult('importResult', `模板下载错误: ${error.message}`, 'error');
            }
        }
        
        // 创建测试CSV文件
        function createTestFile() {
            const csvContent = `产品名称,产品描述
测试产品A,这是测试产品A的描述
测试产品B,这是测试产品B的描述
测试产品C,这是测试产品C的描述
测试产品D,这是测试产品D的描述
测试产品E,这是一个很长的描述用来测试字段长度`;
            
            const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
            const downloadUrl = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = 'test_products.csv';
            a.click();
            
            showResult('importResult', '测试CSV文件已创建并下载！', 'success');
        }
    </script>
</body>
</html>
