#!/usr/bin/env python
"""
API 文档访问修复脚本
用于快速修复 API 文档无法访问的问题
"""

import os
import sys
import re

def backup_settings():
    """备份当前设置文件"""
    settings_file = "application/settings/base.py"
    backup_file = "application/settings/base.py.backup"
    
    if os.path.exists(settings_file):
        with open(settings_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已备份设置文件到: {backup_file}")
        return True
    else:
        print(f"❌ 找不到设置文件: {settings_file}")
        return False

def check_current_whitelist():
    """检查当前白名单配置"""
    settings_file = "application/settings/base.py"
    
    if not os.path.exists(settings_file):
        print(f"❌ 找不到设置文件: {settings_file}")
        return False
    
    with open(settings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("=== 当前白名单配置 ===")
    
    # 查找白名单配置
    whitelist_pattern = r"'WHITELIST_PATTERNS':\s*\[(.*?)\]"
    match = re.search(whitelist_pattern, content, re.DOTALL)
    
    if match:
        whitelist_content = match.group(1)
        patterns = re.findall(r"r'([^']+)'", whitelist_content)
        
        print("当前白名单模式:")
        for i, pattern in enumerate(patterns, 1):
            print(f"  {i}. {pattern}")
        
        # 检查是否包含文档路径（正则表达式模式）
        doc_patterns = [
            r'^/docs/?$',                                    # 项目文档入口
            r'^/api/docs/?$',                                # 通用文档入口
            r'^/api/(?P<project>\w+)/docs/?$',               # 子项目文档
            r'^/api/(?P<project>\w+)/openapi\.json/?$',      # 子项目OpenAPI
            r'^/api/(?P<project>\w+)/redoc/?$',              # 子项目ReDoc
        ]
        
        missing_patterns = []
        for doc_pattern in doc_patterns:
            if doc_pattern not in patterns:
                missing_patterns.append(doc_pattern)
        
        if missing_patterns:
            print("\n⚠️ 缺少的文档路径模式:")
            for pattern in missing_patterns:
                print(f"  - {pattern}")
            return False
        else:
            print("\n✅ 文档路径已在白名单中")
            return True
    else:
        print("❌ 找不到白名单配置")
        return False

def add_missing_doc_patterns():
    """添加缺少的文档路径模式"""
    settings_file = "application/settings/base.py"
    
    with open(settings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找并替换白名单配置
    old_pattern = r"(# API 文档\s*\n)(.*?)(# 开发调试)"
    
    new_doc_config = """        # API 文档 - 使用正则表达式匹配所有子项目
        r'^/docs/?$',                                    # 项目文档入口
        r'^/api/docs/?$',                                # 通用文档入口
        r'^/api/(?P<project>\w+)/docs/?$',               # 子项目文档: /api/{project}/docs
        r'^/api/(?P<project>\w+)/openapi\.json/?$',      # 子项目OpenAPI: /api/{project}/openapi.json
        r'^/api/(?P<project>\w+)/redoc/?$',              # 子项目ReDoc: /api/{project}/redoc
        r'^/api/openapi\.json/?$',                       # 通用 OpenAPI 规范
        r'^/api/redoc/?$',                               # 通用 ReDoc 文档

        """
    
    def replace_func(match):
        return match.group(1) + new_doc_config + match.group(3)
    
    new_content = re.sub(old_pattern, replace_func, content, flags=re.DOTALL)
    
    if new_content != content:
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        print("✅ 已更新文档路径配置")
        return True
    else:
        print("⚠️ 配置未发生变化")
        return False

def toggle_middleware(enable=True):
    """启用或禁用权限中间件"""
    settings_file = "application/settings/base.py"
    
    with open(settings_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    middleware_line = "'zhi_oauth.middleware.global_permission_middleware.GlobalPermissionMiddleware',"
    
    if enable:
        # 启用中间件
        commented_line = f"    # {middleware_line}"
        if commented_line in content:
            new_content = content.replace(commented_line, f"    {middleware_line}")
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print("✅ 已启用权限中间件")
            return True
        else:
            print("⚠️ 权限中间件已经启用")
            return False
    else:
        # 禁用中间件
        active_line = f"    {middleware_line}"
        if active_line in content:
            new_content = content.replace(active_line, f"    # {middleware_line}")
            with open(settings_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print("✅ 已禁用权限中间件")
            return True
        else:
            print("⚠️ 权限中间件已经禁用")
            return False

def restore_settings():
    """恢复设置文件"""
    settings_file = "application/settings/base.py"
    backup_file = "application/settings/base.py.backup"
    
    if os.path.exists(backup_file):
        with open(backup_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        with open(settings_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已从备份恢复设置文件")
        return True
    else:
        print(f"❌ 找不到备份文件: {backup_file}")
        return False

def main():
    """主修复流程"""
    print("API 文档访问修复工具")
    print("=" * 40)
    
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python fix_api_docs_access.py check     # 检查当前配置")
        print("  python fix_api_docs_access.py fix       # 修复文档访问")
        print("  python fix_api_docs_access.py disable   # 临时禁用中间件")
        print("  python fix_api_docs_access.py enable    # 启用中间件")
        print("  python fix_api_docs_access.py restore   # 恢复备份")
        return
    
    command = sys.argv[1].lower()
    
    if command == "check":
        print("检查当前配置...")
        check_current_whitelist()
        
    elif command == "fix":
        print("修复文档访问...")
        if backup_settings():
            if not check_current_whitelist():
                add_missing_doc_patterns()
                print("\n✅ 修复完成！请重启 Django 服务器")
                print("   python manage.py runserver")
            else:
                print("\n✅ 配置正确，无需修复")
        
    elif command == "disable":
        print("临时禁用权限中间件...")
        backup_settings()
        toggle_middleware(False)
        print("\n⚠️ 权限中间件已禁用，请重启服务器")
        print("   python manage.py runserver")
        print("   测试完成后请运行: python fix_api_docs_access.py enable")
        
    elif command == "enable":
        print("启用权限中间件...")
        toggle_middleware(True)
        print("\n✅ 权限中间件已启用，请重启服务器")
        print("   python manage.py runserver")
        
    elif command == "restore":
        print("恢复设置文件...")
        restore_settings()
        print("\n✅ 设置文件已恢复，请重启服务器")
        
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()
