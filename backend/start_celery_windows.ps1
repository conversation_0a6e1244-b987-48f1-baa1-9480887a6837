# Windows PowerShell Celery 启动脚本
# 基于您提供的启动命令适配Windows环境

Write-Host "========================================" -ForegroundColor Green
Write-Host "    ZhiAdmin Celery Windows 启动脚本" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# 激活虚拟环境
Write-Host "激活虚拟环境..." -ForegroundColor Yellow
& ".\.venv\Scripts\Activate.ps1"

# 设置环境变量
$env:DJANGO_SETTINGS_MODULE = "application.settings.base"
$env:PYTHONPATH = Get-Location

# 检查是否安装了gevent
Write-Host "检查依赖包..." -ForegroundColor Yellow
try {
    python -c "import gevent" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "警告: gevent 未安装，Celery在Windows上需要gevent支持" -ForegroundColor Red
        Write-Host "正在安装gevent..." -ForegroundColor Yellow
        pip install gevent
    }
} catch {
    Write-Host "安装gevent时出错: $_" -ForegroundColor Red
}

# 检查Redis连接
Write-Host "检查Redis连接..." -ForegroundColor Yellow
try {
    python -c "import redis; r=redis.Redis(); r.ping()" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "警告: 无法连接到Redis，请确保Redis服务正在运行" -ForegroundColor Red
        Write-Host "您可以：" -ForegroundColor Yellow
        Write-Host "1. 安装并启动Redis服务" -ForegroundColor Yellow
        Write-Host "2. 或使用Docker: docker run -d -p 6379:6379 redis:alpine" -ForegroundColor Yellow
        Read-Host "按Enter继续..."
    }
} catch {
    Write-Host "Redis连接检查失败: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "选择启动模式:" -ForegroundColor Cyan
Write-Host "1. 启动Celery Worker（异步任务处理）" -ForegroundColor White
Write-Host "2. 启动Celery Beat（定时任务调度）" -ForegroundColor White  
Write-Host "3. 同时启动Worker和Beat" -ForegroundColor White
Write-Host "4. 启动Django开发服务器" -ForegroundColor White
Write-Host "5. 运行完整测试" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请选择 (1-5)"

switch ($choice) {
    "1" {
        Write-Host "启动Celery Worker..." -ForegroundColor Green
        Write-Host "命令: celery -A application.celery worker -l info -c 1 -P gevent" -ForegroundColor Gray
        celery -A application.celery worker -l info -c 1 -P gevent
    }
    "2" {
        Write-Host "启动Celery Beat..." -ForegroundColor Green
        Write-Host "命令: celery -A application.celery beat --loglevel=info" -ForegroundColor Gray
        celery -A application.celery beat --loglevel=info
    }
    "3" {
        Write-Host "同时启动Celery Worker和Beat..." -ForegroundColor Green
        Write-Host "注意: 在生产环境中建议分别启动Worker和Beat" -ForegroundColor Yellow
        Write-Host "命令: celery -A application.celery worker --loglevel=info --beat -c 1 -P gevent" -ForegroundColor Gray
        celery -A application.celery worker --loglevel=info --beat -c 1 -P gevent
    }
    "4" {
        Write-Host "启动Django开发服务器..." -ForegroundColor Green
        Write-Host "命令: python manage.py runserver 0.0.0.0:8001" -ForegroundColor Gray
        python manage.py runserver 0.0.0.0:8001
    }
    "5" {
        Write-Host "运行完整测试..." -ForegroundColor Green
        Write-Host "首先启动Django服务器（后台）..." -ForegroundColor Yellow
        
        # 启动Django服务器作为后台任务
        $djangoJob = Start-Job -ScriptBlock {
            Set-Location $using:PWD
            & ".\.venv\Scripts\Activate.ps1"
            python manage.py runserver 0.0.0.0:8001
        }
        
        Write-Host "等待服务器启动..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
        
        Write-Host "运行OAuth API测试..." -ForegroundColor Yellow
        python test_oauth_complete.py
        
        Write-Host "停止Django服务器..." -ForegroundColor Yellow
        Stop-Job $djangoJob
        Remove-Job $djangoJob
    }
    default {
        Write-Host "无效选择，请重新运行脚本" -ForegroundColor Red
        Read-Host "按Enter退出..."
        exit
    }
}

Write-Host ""
Write-Host "服务已停止" -ForegroundColor Yellow
Read-Host "按Enter退出..."
