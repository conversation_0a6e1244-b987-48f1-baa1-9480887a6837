#!/usr/bin/env python
"""
ZhiFiles 项目简化测试脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_files')

def test_basic_imports():
    """测试基础导入"""
    print("\n=== 测试基础导入 ===")
    
    try:
        # 测试Django设置
        from django.conf import settings
        print("✅ Django设置导入成功")
        print(f"   ROOT_URLCONF: {settings.ROOT_URLCONF}")
        
        # 测试Django应用
        import django
        django.setup()
        print("✅ Django环境初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础导入测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_zhi_files_structure():
    """测试ZhiFiles项目结构"""
    print("\n=== 测试ZhiFiles项目结构 ===")
    
    try:
        # 检查文件存在性
        files_to_check = [
            'zhi_files/__init__.py',
            'zhi_files/apps.py',
            'zhi_files/models.py',
            'zhi_files/api.py',
            'zhi_files/apis/__init__.py',
            'zhi_files/apis/file_management.py',
            'application/settings/zhi_files.py',
            'application/conf_urls/zhi_files.py',
            'zhi_scripts/start_files.py'
        ]
        
        for file_path in files_to_check:
            full_path = project_root / file_path
            if full_path.exists():
                print(f"✅ 文件存在: {file_path}")
            else:
                print(f"❌ 文件缺失: {file_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 项目结构测试失败: {str(e)}")
        return False

def test_settings_loading():
    """测试设置加载"""
    print("\n=== 测试设置加载 ===")
    
    try:
        from django.conf import settings
        
        print("✅ Django设置加载成功")
        
        # 检查关键设置
        key_settings = [
            'INSTALLED_APPS',
            'MIDDLEWARE', 
            'ROOT_URLCONF',
            'DATABASES',
            'MEDIA_ROOT',
            'MEDIA_URL'
        ]
        
        for setting_name in key_settings:
            if hasattr(settings, setting_name):
                setting_value = getattr(settings, setting_name)
                print(f"✅ 设置存在: {setting_name}")
                if setting_name == 'INSTALLED_APPS':
                    print(f"   应用数量: {len(setting_value)}")
                    if 'zhi_files' in setting_value:
                        print("   ✅ zhi_files应用已注册")
                    else:
                        print("   ❌ zhi_files应用未注册")
            else:
                print(f"❌ 设置缺失: {setting_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置加载测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始ZhiFiles项目简化测试")
    
    tests = [
        ("项目结构测试", test_zhi_files_structure),
        ("设置加载测试", test_settings_loading),
        ("基础导入测试", test_basic_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"执行测试: {test_name}")
            print(f"{'='*50}")
            
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"{'='*50}")
    
    if passed >= 2:  # 至少2个测试通过就算成功
        print("🎉 ZhiFiles项目基础结构测试通过！")
        print("💡 下一步:")
        print("   - 运行 'python zhi_scripts/start_files.py' 启动服务")
        print("   - 检查数据库迁移")
        return True
    else:
        print("⚠️  基础结构测试失败，请检查项目配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
