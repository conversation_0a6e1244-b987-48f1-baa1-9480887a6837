@echo off
REM Windows Celery 启动脚本
REM 基于您提供的启动命令适配Windows环境

echo ========================================
echo    ZhiAdmin Celery Windows 启动脚本
echo ========================================

REM 激活虚拟环境
echo 激活虚拟环境...
call .venv\Scripts\activate.bat

REM 设置环境变量
set DJANGO_SETTINGS_MODULE=application.settings.base
set PYTHONPATH=%CD%

REM 检查是否安装了gevent
echo 检查依赖包...
python -c "import gevent" 2>nul
if errorlevel 1 (
    echo 警告: gevent 未安装，Celery在Windows上需要gevent支持
    echo 正在安装gevent...
    pip install gevent
)

REM 检查Redis是否运行（Celery需要消息代理）
echo 检查Redis连接...
python -c "import redis; r=redis.Redis(); r.ping()" 2>nul
if errorlevel 1 (
    echo 警告: 无法连接到Redis，请确保Redis服务正在运行
    echo 您可以：
    echo 1. 安装并启动Redis服务
    echo 2. 或使用Docker: docker run -d -p 6379:6379 redis:alpine
    pause
)

echo.
echo 选择启动模式:
echo 1. 启动Celery Worker（异步任务处理）
echo 2. 启动Celery Beat（定时任务调度）
echo 3. 同时启动Worker和Beat
echo 4. 启动Django开发服务器
echo.
set /p choice=请选择 (1-4): 

if "%choice%"=="1" goto start_worker
if "%choice%"=="2" goto start_beat
if "%choice%"=="3" goto start_both
if "%choice%"=="4" goto start_django
goto invalid_choice

:start_worker
echo 启动Celery Worker...
echo 命令: celery -A application.celery worker -l info -c 1 -P gevent
celery -A application.celery worker -l info -c 1 -P gevent
goto end

:start_beat
echo 启动Celery Beat...
echo 命令: celery -A application.celery beat --loglevel=info
celery -A application.celery beat --loglevel=info
goto end

:start_both
echo 同时启动Celery Worker和Beat...
echo 注意: 在生产环境中建议分别启动Worker和Beat
echo 命令: celery -A application.celery worker --loglevel=info --beat -c 1 -P gevent
celery -A application.celery worker --loglevel=info --beat -c 1 -P gevent
goto end

:start_django
echo 启动Django开发服务器...
echo 命令: python manage.py runserver 0.0.0.0:8001
python manage.py runserver 0.0.0.0:8001
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
pause
goto end

:end
echo.
echo 服务已停止
pause
