#!/usr/bin/env python
"""
ZhiFiles 增强测试脚本
测试优化后的文件管理功能
"""

import os
import sys
import tempfile
from pathlib import Path
from io import BytesIO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_files')

def test_django_setup():
    """测试Django环境设置"""
    print("\n=== 测试Django环境设置 ===")
    
    try:
        import django
        from django.conf import settings
        
        django.setup()
        print("✅ Django环境初始化成功")
        
        # 检查应用注册
        if 'zhi_files' in settings.INSTALLED_APPS:
            print("✅ zhi_files应用已注册")
        else:
            print("❌ zhi_files应用未注册")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Django环境设置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_imports():
    """测试模型导入"""
    print("\n=== 测试模型导入 ===")
    
    try:
        from zhi_files.models import FileStorage, FileAccessLog
        print("✅ 模型导入成功")
        
        # 检查模型字段
        file_storage_fields = [field.name for field in FileStorage._meta.fields]
        required_fields = ['file_id', 'original_name', 'file_path', 'file_size', 'created_at']
        
        missing_fields = [field for field in required_fields if field not in file_storage_fields]
        if missing_fields:
            print(f"❌ FileStorage模型缺少字段: {missing_fields}")
            return False
        
        print("✅ FileStorage模型字段检查通过")
        print("✅ FileAccessLog模型导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_imports():
    """测试API导入"""
    print("\n=== 测试API导入 ===")
    
    try:
        from zhi_files.apis.file_management import FileStorageControllerAPI
        print("✅ 基础文件管理API导入成功")
        
        from zhi_files.apis.enhanced_file_api import EnhancedFileAPIController
        print("✅ 增强文件管理API导入成功")
        
        from zhi_files.api import api
        print("✅ API实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ API导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_manager():
    """测试文件管理器"""
    print("\n=== 测试文件管理器 ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig
        
        # 创建配置
        config = FileUploadConfig(
            max_file_size=10 * 1024 * 1024,  # 10MB
            allowed_types=['image', 'document'],
            upload_path='test_uploads',
            organize_by_date=True,
            generate_unique_name=True
        )
        print("✅ 文件上传配置创建成功")
        
        # 创建文件管理器
        file_manager = FileManager(config)
        print("✅ 文件管理器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n=== 测试数据库操作 ===")
    
    try:
        from django.db import connection
        from zhi_files.models import FileStorage
        
        # 测试数据库连接
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            if result[0] == 1:
                print("✅ 数据库连接正常")
            else:
                print("❌ 数据库连接异常")
                return False
        
        # 测试模型查询
        try:
            count = FileStorage.objects.count()
            print(f"✅ 文件记录查询成功，当前记录数: {count}")
        except Exception as e:
            print(f"⚠️  文件记录查询失败（可能是表未创建）: {e}")
            # 这不算错误，因为可能还没有运行迁移
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_management_commands():
    """测试管理命令"""
    print("\n=== 测试管理命令 ===")
    
    try:
        from django.core.management import get_commands
        
        commands = get_commands()
        
        # 检查自定义管理命令
        custom_commands = ['cleanup_files', 'file_stats']
        found_commands = []
        
        for cmd in custom_commands:
            if cmd in commands:
                found_commands.append(cmd)
                print(f"✅ 管理命令 {cmd} 已注册")
            else:
                print(f"⚠️  管理命令 {cmd} 未找到")
        
        if found_commands:
            print(f"✅ 找到 {len(found_commands)} 个自定义管理命令")
            return True
        else:
            print("⚠️  未找到自定义管理命令，但这不影响基本功能")
            return True
        
    except Exception as e:
        print(f"❌ 管理命令测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置"""
    print("\n=== 测试配置 ===")
    
    try:
        from django.conf import settings
        
        # 检查ZhiFiles配置
        if hasattr(settings, 'ZHI_FILES_CONFIG'):
            config = settings.ZHI_FILES_CONFIG
            print("✅ ZHI_FILES_CONFIG配置存在")
            
            # 检查关键配置项
            key_configs = [
                'DEFAULT_MAX_FILE_SIZE',
                'DEFAULT_ALLOWED_TYPES',
                'DEFAULT_UPLOAD_PATH',
                'ENABLE_ACCESS_LOG',
                'MICROSERVICE_MODE'
            ]
            
            for key in key_configs:
                if key in config:
                    print(f"  ✅ {key}: {config[key]}")
                else:
                    print(f"  ❌ 缺少配置项: {key}")
        else:
            print("❌ ZHI_FILES_CONFIG配置不存在")
            return False
        
        # 检查媒体文件配置
        if hasattr(settings, 'MEDIA_ROOT') and hasattr(settings, 'MEDIA_URL'):
            print(f"✅ 媒体文件配置: MEDIA_ROOT={settings.MEDIA_ROOT}")
            print(f"✅ 媒体文件配置: MEDIA_URL={settings.MEDIA_URL}")
        else:
            print("❌ 媒体文件配置缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始ZhiFiles增强功能测试")
    print("=" * 60)
    
    tests = [
        ("Django环境设置", test_django_setup),
        ("模型导入", test_model_imports),
        ("API导入", test_api_imports),
        ("文件管理器", test_file_manager),
        ("数据库操作", test_database_operations),
        ("管理命令", test_management_commands),
        ("配置检查", test_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"执行测试: {test_name}")
            print(f"{'='*50}")
            
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"{'='*60}")
    
    if passed >= total - 1:  # 允许1个测试失败
        print("🎉 ZhiFiles增强功能测试基本通过！")
        print("\n💡 下一步操作:")
        print("   1. 运行数据库迁移: python manage.py makemigrations zhi_files")
        print("   2. 应用迁移: python manage.py migrate")
        print("   3. 启动服务: python zhi_scripts/start_files.py")
        print("   4. 访问API文档: http://127.0.0.1:8000/api/docs/")
        print("\n🔧 管理命令:")
        print("   - 文件统计: python manage.py file_stats --detailed")
        print("   - 清理文件: python manage.py cleanup_files --expired --dry-run")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和依赖")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
