# Generated by Django 5.2 on 2025-08-04 05:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FileStorage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_id', models.CharField(db_index=True, help_text='文件的唯一标识符', max_length=64, unique=True, verbose_name='文件唯一标识')),
                ('original_name', models.CharField(help_text='用户上传时的原始文件名', max_length=255, verbose_name='原始文件名')),
                ('file_name', models.CharField(help_text='在服务器上存储的文件名', max_length=255, verbose_name='存储文件名')),
                ('file_path', models.CharField(help_text='文件在服务器上的相对路径', max_length=500, verbose_name='文件路径')),
                ('file_url', models.URLField(help_text='文件的访问URL', max_length=500, verbose_name='文件访问URL')),
                ('file_size', models.BigIntegerField(help_text='文件大小，单位：字节', verbose_name='文件大小')),
                ('file_type', models.CharField(choices=[('image', '图片'), ('document', '文档'), ('archive', '压缩包'), ('video', '视频'), ('audio', '音频'), ('other', '其他')], default='other', help_text='文件的分类类型', max_length=20, verbose_name='文件类型')),
                ('content_type', models.CharField(help_text='文件的MIME类型', max_length=100, verbose_name='MIME类型')),
                ('file_extension', models.CharField(help_text='文件的扩展名', max_length=20, verbose_name='文件扩展名')),
                ('file_hash', models.CharField(db_index=True, help_text='文件的MD5哈希值，用于去重', max_length=64, verbose_name='文件哈希值')),
                ('upload_ip', models.GenericIPAddressField(blank=True, help_text='文件上传时的客户端IP地址', null=True, verbose_name='上传IP')),
                ('user_agent', models.TextField(blank=True, help_text='文件上传时的用户代理信息', null=True, verbose_name='用户代理')),
                ('status', models.CharField(choices=[('active', '正常'), ('deleted', '已删除'), ('expired', '已过期')], db_index=True, default='active', help_text='文件的当前状态', max_length=20, verbose_name='文件状态')),
                ('download_count', models.PositiveIntegerField(default=0, help_text='文件被下载的次数', verbose_name='下载次数')),
                ('last_download_at', models.DateTimeField(blank=True, help_text='文件最后一次被下载的时间', null=True, verbose_name='最后下载时间')),
                ('expires_at', models.DateTimeField(blank=True, help_text='文件的过期时间，为空表示永不过期', null=True, verbose_name='过期时间')),
                ('tags', models.JSONField(blank=True, default=list, help_text='文件的标签列表，用于分类和搜索', verbose_name='文件标签')),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='文件的额外元数据信息', verbose_name='文件元数据')),
                ('description', models.TextField(blank=True, help_text='文件的描述信息', verbose_name='文件描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('creator_id', models.CharField(blank=True, max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='创建人姓名')),
                ('is_deleted', models.BooleanField(default=False, verbose_name='是否删除')),
                ('deleted_at', models.DateTimeField(blank=True, null=True, verbose_name='删除时间')),
            ],
            options={
                'verbose_name': '文件存储',
                'verbose_name_plural': '文件存储',
                'db_table': 'zhi_files_storage',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['file_id'], name='zhi_files_s_file_id_899ee5_idx'), models.Index(fields=['file_hash'], name='zhi_files_s_file_ha_cc5bf6_idx'), models.Index(fields=['file_type'], name='zhi_files_s_file_ty_cbbd5d_idx'), models.Index(fields=['status'], name='zhi_files_s_status_f147dd_idx'), models.Index(fields=['created_at'], name='zhi_files_s_created_2778e9_idx'), models.Index(fields=['creator_id'], name='zhi_files_s_creator_468b36_idx')],
            },
        ),
        migrations.CreateModel(
            name='FileAccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('upload', '上传'), ('download', '下载'), ('view', '查看'), ('delete', '删除')], max_length=20, verbose_name='操作类型')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, null=True, verbose_name='用户代理')),
                ('referer', models.URLField(blank=True, null=True, verbose_name='来源页面')),
                ('extra_data', models.JSONField(blank=True, default=dict, verbose_name='额外数据')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('creator_id', models.CharField(blank=True, max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='创建人姓名')),
                ('file', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_logs', to='zhi_files.filestorage', verbose_name='关联文件')),
            ],
            options={
                'verbose_name': '文件访问日志',
                'verbose_name_plural': '文件访问日志',
                'db_table': 'zhi_files_access_log',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['file', 'action'], name='zhi_files_a_file_id_ec4f83_idx'), models.Index(fields=['created_at'], name='zhi_files_a_created_fdf58f_idx'), models.Index(fields=['creator_id'], name='zhi_files_a_creator_27e811_idx')],
            },
        ),
    ]
