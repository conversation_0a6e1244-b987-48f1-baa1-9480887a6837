"""
文件统计管理命令
用于生成文件系统的统计报告
"""

from collections import defaultdict
from datetime import datetime, timedelta

from django.core.management.base import BaseCommand
from django.db.models import Count, Sum, Avg
from django.utils import timezone

from zhi_files.models import FileStorage, FileAccessLog


class Command(BaseCommand):
    help = '生成文件系统统计报告'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='统计最近多少天的数据（默认30天）',
        )
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='显示详细统计信息',
        )
        parser.add_argument(
            '--export',
            type=str,
            help='导出统计报告到文件',
        )
    
    def handle(self, *args, **options):
        """生成统计报告"""
        self.days = options['days']
        self.detailed = options['detailed']
        self.export_file = options.get('export')
        
        self.stdout.write(f"生成最近 {self.days} 天的文件统计报告...")
        self.stdout.write("=" * 60)
        
        # 生成各种统计
        report_lines = []
        report_lines.extend(self.get_basic_stats())
        report_lines.extend(self.get_file_type_stats())
        report_lines.extend(self.get_upload_stats())
        report_lines.extend(self.get_access_stats())
        
        if self.detailed:
            report_lines.extend(self.get_detailed_stats())
        
        # 输出报告
        for line in report_lines:
            self.stdout.write(line)
        
        # 导出到文件
        if self.export_file:
            self.export_report(report_lines)
    
    def get_basic_stats(self):
        """基础统计信息"""
        lines = []
        lines.append("\n📊 基础统计")
        lines.append("-" * 30)
        
        # 总文件数
        total_files = FileStorage.objects.filter(is_deleted=False).count()
        lines.append(f"总文件数: {total_files:,}")
        
        # 总文件大小
        total_size = FileStorage.objects.filter(is_deleted=False).aggregate(
            total=Sum('file_size')
        )['total'] or 0
        lines.append(f"总文件大小: {self.format_size(total_size)}")
        
        # 平均文件大小
        avg_size = FileStorage.objects.filter(is_deleted=False).aggregate(
            avg=Avg('file_size')
        )['avg'] or 0
        lines.append(f"平均文件大小: {self.format_size(avg_size)}")
        
        # 活跃文件数
        active_files = FileStorage.objects.filter(
            is_deleted=False, status='active'
        ).count()
        lines.append(f"活跃文件数: {active_files:,}")
        
        # 已删除文件数
        deleted_files = FileStorage.objects.filter(is_deleted=True).count()
        lines.append(f"已删除文件数: {deleted_files:,}")
        
        # 过期文件数
        now = timezone.now()
        expired_files = FileStorage.objects.filter(
            is_deleted=False,
            expires_at__lt=now
        ).count()
        lines.append(f"过期文件数: {expired_files:,}")
        
        return lines
    
    def get_file_type_stats(self):
        """文件类型统计"""
        lines = []
        lines.append("\n📁 文件类型统计")
        lines.append("-" * 30)
        
        type_stats = FileStorage.objects.filter(is_deleted=False).values(
            'file_type'
        ).annotate(
            count=Count('id'),
            total_size=Sum('file_size')
        ).order_by('-count')
        
        for stat in type_stats:
            file_type = stat['file_type']
            count = stat['count']
            size = stat['total_size'] or 0
            lines.append(f"{file_type}: {count:,} 个文件, {self.format_size(size)}")
        
        return lines
    
    def get_upload_stats(self):
        """上传统计"""
        lines = []
        lines.append("\n📤 上传统计")
        lines.append("-" * 30)
        
        cutoff_date = timezone.now() - timedelta(days=self.days)
        
        # 最近上传统计
        recent_uploads = FileStorage.objects.filter(
            created_at__gte=cutoff_date,
            is_deleted=False
        )
        
        upload_count = recent_uploads.count()
        upload_size = recent_uploads.aggregate(total=Sum('file_size'))['total'] or 0
        
        lines.append(f"最近 {self.days} 天上传: {upload_count:,} 个文件")
        lines.append(f"上传总大小: {self.format_size(upload_size)}")
        
        if upload_count > 0:
            avg_daily = upload_count / self.days
            lines.append(f"日均上传: {avg_daily:.1f} 个文件")
        
        # 按日期统计上传
        if self.detailed:
            lines.append("\n📅 每日上传统计:")
            daily_stats = defaultdict(int)
            
            for file_record in recent_uploads:
                date_key = file_record.created_at.date()
                daily_stats[date_key] += 1
            
            for date_key in sorted(daily_stats.keys(), reverse=True)[:7]:  # 最近7天
                count = daily_stats[date_key]
                lines.append(f"  {date_key}: {count} 个文件")
        
        return lines
    
    def get_access_stats(self):
        """访问统计"""
        lines = []
        lines.append("\n👁️ 访问统计")
        lines.append("-" * 30)
        
        cutoff_date = timezone.now() - timedelta(days=self.days)
        
        # 总下载次数
        total_downloads = FileStorage.objects.filter(
            is_deleted=False
        ).aggregate(total=Sum('download_count'))['total'] or 0
        lines.append(f"总下载次数: {total_downloads:,}")
        
        # 最近访问统计
        recent_access = FileAccessLog.objects.filter(
            created_at__gte=cutoff_date
        )
        
        access_count = recent_access.count()
        lines.append(f"最近 {self.days} 天访问: {access_count:,} 次")
        
        # 按操作类型统计
        action_stats = recent_access.values('action').annotate(
            count=Count('id')
        ).order_by('-count')
        
        lines.append("\n操作类型统计:")
        for stat in action_stats:
            action = stat['action']
            count = stat['count']
            lines.append(f"  {action}: {count:,} 次")
        
        # 热门文件
        if self.detailed:
            lines.append("\n🔥 热门文件 (下载次数最多):")
            popular_files = FileStorage.objects.filter(
                is_deleted=False,
                download_count__gt=0
            ).order_by('-download_count')[:10]
            
            for file_record in popular_files:
                lines.append(
                    f"  {file_record.original_name}: {file_record.download_count} 次下载"
                )
        
        return lines
    
    def get_detailed_stats(self):
        """详细统计信息"""
        lines = []
        lines.append("\n🔍 详细统计")
        lines.append("-" * 30)
        
        # 文件扩展名统计
        extension_stats = defaultdict(int)
        for file_record in FileStorage.objects.filter(is_deleted=False):
            ext = file_record.file_extension.lower()
            extension_stats[ext] += 1
        
        lines.append("\n文件扩展名统计 (前10):")
        sorted_extensions = sorted(
            extension_stats.items(), 
            key=lambda x: x[1], 
            reverse=True
        )[:10]
        
        for ext, count in sorted_extensions:
            lines.append(f"  {ext or '无扩展名'}: {count:,} 个文件")
        
        # 用户上传统计
        user_stats = FileStorage.objects.filter(
            is_deleted=False,
            creator_name__isnull=False
        ).values('creator_name').annotate(
            count=Count('id'),
            total_size=Sum('file_size')
        ).order_by('-count')[:10]
        
        if user_stats:
            lines.append("\n👤 用户上传统计 (前10):")
            for stat in user_stats:
                name = stat['creator_name']
                count = stat['count']
                size = stat['total_size'] or 0
                lines.append(f"  {name}: {count:,} 个文件, {self.format_size(size)}")
        
        return lines
    
    def export_report(self, report_lines):
        """导出报告到文件"""
        try:
            with open(self.export_file, 'w', encoding='utf-8') as f:
                f.write(f"ZhiFiles 统计报告\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"统计周期: 最近 {self.days} 天\n")
                f.write("=" * 60 + "\n")
                
                for line in report_lines:
                    f.write(line + "\n")
            
            self.stdout.write(
                self.style.SUCCESS(f"报告已导出到: {self.export_file}")
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"导出报告失败: {e}")
            )
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
