"""
文件清理管理命令
用于清理过期文件、孤儿文件等
"""

import os
from datetime import timedelta
from pathlib import Path

from django.core.management.base import BaseCommand, CommandError
from django.core.files.storage import default_storage
from django.utils import timezone
from django.conf import settings

from zhi_files.models import FileStorage
from zhi_common.zhi_logger.core_logger import zhi_logger


class Command(BaseCommand):
    help = '清理文件系统中的过期文件和孤儿文件'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--expired',
            action='store_true',
            help='清理过期文件',
        )
        parser.add_argument(
            '--orphaned',
            action='store_true',
            help='清理孤儿文件（数据库中不存在的物理文件）',
        )
        parser.add_argument(
            '--deleted',
            action='store_true',
            help='清理已标记为删除的文件',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='清理多少天前的文件（默认30天）',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='试运行，不实际删除文件',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制删除，不询问确认',
        )
    
    def handle(self, *args, **options):
        """执行清理操作"""
        self.dry_run = options['dry_run']
        self.force = options['force']
        self.days = options['days']
        
        if not any([options['expired'], options['orphaned'], options['deleted']]):
            self.stdout.write(
                self.style.WARNING('请指定至少一种清理类型: --expired, --orphaned, --deleted')
            )
            return
        
        self.stdout.write(f"开始文件清理任务...")
        if self.dry_run:
            self.stdout.write(self.style.WARNING("试运行模式，不会实际删除文件"))
        
        total_cleaned = 0
        total_size_saved = 0
        
        if options['expired']:
            cleaned, size_saved = self.cleanup_expired_files()
            total_cleaned += cleaned
            total_size_saved += size_saved
        
        if options['deleted']:
            cleaned, size_saved = self.cleanup_deleted_files()
            total_cleaned += cleaned
            total_size_saved += size_saved
        
        if options['orphaned']:
            cleaned, size_saved = self.cleanup_orphaned_files()
            total_cleaned += cleaned
            total_size_saved += size_saved
        
        self.stdout.write(
            self.style.SUCCESS(
                f"清理完成！共清理 {total_cleaned} 个文件，"
                f"释放空间 {self.format_size(total_size_saved)}"
            )
        )
    
    def cleanup_expired_files(self):
        """清理过期文件"""
        self.stdout.write("正在清理过期文件...")
        
        now = timezone.now()
        expired_files = FileStorage.objects.filter(
            expires_at__lt=now,
            is_deleted=False
        )
        
        count = expired_files.count()
        if count == 0:
            self.stdout.write("没有找到过期文件")
            return 0, 0
        
        self.stdout.write(f"找到 {count} 个过期文件")
        
        if not self.force and not self.dry_run:
            confirm = input(f"确认清理这 {count} 个过期文件吗？(y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("取消清理")
                return 0, 0
        
        total_size = 0
        cleaned_count = 0
        
        for file_record in expired_files:
            try:
                file_size = file_record.file_size
                
                if not self.dry_run:
                    # 删除物理文件
                    if default_storage.exists(file_record.file_path):
                        default_storage.delete(file_record.file_path)
                    
                    # 标记为已删除
                    file_record.status = 'expired'
                    file_record.is_deleted = True
                    file_record.save()
                
                total_size += file_size
                cleaned_count += 1
                
                self.stdout.write(f"  清理: {file_record.original_name}")
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"清理文件失败 {file_record.original_name}: {e}")
                )
                zhi_logger.error(f"清理过期文件失败: {e}", extra_data={
                    'file_id': file_record.file_id,
                    'file_path': file_record.file_path
                })
        
        self.stdout.write(
            self.style.SUCCESS(f"过期文件清理完成: {cleaned_count} 个文件")
        )
        return cleaned_count, total_size
    
    def cleanup_deleted_files(self):
        """清理已标记为删除的文件"""
        self.stdout.write("正在清理已删除文件...")
        
        cutoff_date = timezone.now() - timedelta(days=self.days)
        deleted_files = FileStorage.objects.filter(
            is_deleted=True,
            deleted_at__lt=cutoff_date
        )
        
        count = deleted_files.count()
        if count == 0:
            self.stdout.write(f"没有找到 {self.days} 天前的已删除文件")
            return 0, 0
        
        self.stdout.write(f"找到 {count} 个已删除文件")
        
        if not self.force and not self.dry_run:
            confirm = input(f"确认永久删除这 {count} 个文件吗？(y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("取消清理")
                return 0, 0
        
        total_size = 0
        cleaned_count = 0
        
        for file_record in deleted_files:
            try:
                file_size = file_record.file_size
                
                if not self.dry_run:
                    # 删除物理文件
                    if default_storage.exists(file_record.file_path):
                        default_storage.delete(file_record.file_path)
                    
                    # 删除数据库记录
                    file_record.delete()
                
                total_size += file_size
                cleaned_count += 1
                
                self.stdout.write(f"  清理: {file_record.original_name}")
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"清理文件失败 {file_record.original_name}: {e}")
                )
                zhi_logger.error(f"清理已删除文件失败: {e}", extra_data={
                    'file_id': file_record.file_id,
                    'file_path': file_record.file_path
                })
        
        self.stdout.write(
            self.style.SUCCESS(f"已删除文件清理完成: {cleaned_count} 个文件")
        )
        return cleaned_count, total_size
    
    def cleanup_orphaned_files(self):
        """清理孤儿文件"""
        self.stdout.write("正在扫描孤儿文件...")
        
        # 获取上传目录
        upload_path = getattr(settings, 'ZHI_FILES_CONFIG', {}).get(
            'DEFAULT_UPLOAD_PATH', 'zhi_files_uploads'
        )
        upload_dir = Path(settings.MEDIA_ROOT) / upload_path
        
        if not upload_dir.exists():
            self.stdout.write("上传目录不存在")
            return 0, 0
        
        # 获取数据库中所有文件路径
        db_file_paths = set(
            FileStorage.objects.values_list('file_path', flat=True)
        )
        
        orphaned_files = []
        total_size = 0
        
        # 扫描物理文件
        for file_path in upload_dir.rglob('*'):
            if file_path.is_file():
                relative_path = str(file_path.relative_to(settings.MEDIA_ROOT))
                if relative_path not in db_file_paths:
                    file_size = file_path.stat().st_size
                    orphaned_files.append((file_path, file_size))
                    total_size += file_size
        
        count = len(orphaned_files)
        if count == 0:
            self.stdout.write("没有找到孤儿文件")
            return 0, 0
        
        self.stdout.write(f"找到 {count} 个孤儿文件")
        
        if not self.force and not self.dry_run:
            confirm = input(f"确认删除这 {count} 个孤儿文件吗？(y/N): ")
            if confirm.lower() != 'y':
                self.stdout.write("取消清理")
                return 0, 0
        
        cleaned_count = 0
        cleaned_size = 0
        
        for file_path, file_size in orphaned_files:
            try:
                if not self.dry_run:
                    file_path.unlink()
                
                cleaned_count += 1
                cleaned_size += file_size
                
                self.stdout.write(f"  清理: {file_path.name}")
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"清理孤儿文件失败 {file_path}: {e}")
                )
                zhi_logger.error(f"清理孤儿文件失败: {e}", extra_data={
                    'file_path': str(file_path)
                })
        
        self.stdout.write(
            self.style.SUCCESS(f"孤儿文件清理完成: {cleaned_count} 个文件")
        )
        return cleaned_count, cleaned_size
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
