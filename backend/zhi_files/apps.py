from django.apps import AppConfig


class ZhiFilesConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'zhi_files'
    verbose_name = '<PERSON>hi<PERSON><PERSON><PERSON> 文件管理系统'

    def ready(self):
        """应用准备就绪时的初始化"""
        # 导入信号处理器
        try:
            from . import signals  # 如果有信号处理器
        except ImportError:
            pass

        # 注册日志记录
        try:
            from zhi_common.zhi_logger import get_logger
            logger = get_logger(module_name="zhi_files")
            logger.info("ZhiFiles 文件管理模块已加载")
        except ImportError:
            pass

        # 初始化文件存储目录
        self._init_storage_directories()

    def _init_storage_directories(self):
        """初始化文件存储目录"""
        try:
            import os
            from django.conf import settings
            from pathlib import Path

            # 创建必要的存储目录
            media_root = Path(settings.MEDIA_ROOT)
            storage_dirs = [
                'uploads',
                'enhanced_uploads', 
                'temp',
                'avatars',
                'documents',
                'images',
                'archives'
            ]

            for dir_name in storage_dirs:
                dir_path = media_root / dir_name
                dir_path.mkdir(parents=True, exist_ok=True)

        except Exception as e:
            # 静默处理，避免影响应用启动
            pass
