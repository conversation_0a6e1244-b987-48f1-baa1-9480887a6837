"""
ZhiFiles API 配置
"""
from ninja_extra import NinjaExtraAPI

from .apis.file_management import FileStorageControllerAPI
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers


# 创建 API 实例
api = NinjaExtraAPI(
    title="ZhiFiles API",
    version="1.0.0",
    description="ZhiAdmin 文件管理系统 API",
    auth=GlobalOAuth2,
    openapi_extra={
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
    docs_url="docs",
    openapi_url="openapi.json",
)

# 注册异常处理器
register_exception_handlers(api)

# 注册控制器
api.register_controllers(FileStorageControllerAPI)  # 文件存储管理API
