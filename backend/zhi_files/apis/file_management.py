"""
ZhiFiles 文件管理API控制器
"""

from pydantic import BaseModel
from typing import List, Any, Dict, Optional
from ninja import Field, ModelSchema
from ninja_extra import http_get, http_post, http_delete, http_put, route
from ninja_extra.pagination import paginate
from django.http import HttpResponse, FileResponse, Http404
from django.core.files.uploadedfile import UploadedFile
from django.db import transaction, models
from django.utils import timezone
from datetime import timedelta
from ninja import Query

from zhi_common.zhi_response.base import ZhiResponse, create_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import BaseResponseSchema
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import BaseModelService
from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig
from zhi_common.zhi_tools.req_util import get_client_ip, get_user_agent
from zhi_common.zhi_logger.core_logger import zhi_logger

# 延迟导入模型以避免循环导入
def get_file_models():
    from zhi_files.models import FileStorage, FileAccessLog
    return FileStorage, FileAccessLog


# Schema定义将在控制器中动态创建


class FileUploadRequestSchema(BaseModel):
    """文件上传请求Schema"""
    tags: Optional[str] = Field(None, description="文件标签,逗号分隔")
    description: Optional[str] = Field("", description="文件描述")
    expires_in_days: Optional[int] = Field(None, description="过期天数")
    custom_path: Optional[str] = Field(None, description="自定义存储路径")
    file_id: Optional[str] = Field(None, description="自定义文件ID")


class FileUploadResponseSchema(BaseModel):
    """文件上传响应Schema"""
    id: int = Field(description="数据库记录ID")
    file_id: str = Field(description="文件唯一标识")
    filename: str = Field(description="原始文件名")
    file_path: str = Field(description="文件存储路径")
    file_url: str = Field(description="文件访问URL")
    file_size: int = Field(description="文件大小(字节)")
    content_type: str = Field(description="文件MIME类型")
    upload_time: str = Field(description="上传时间")
    file_hash: str = Field(description="文件MD5哈希值")
    tags: List[str] = Field(default=[], description="文件标签")
    description: str = Field(default="", description="文件描述")
    expires_at: Optional[str] = Field(None, description="过期时间")


class FileListFilterSchema(BaseModel):
    """文件列表过滤Schema"""
    file_type: Optional[str] = Field(None, description="文件类型")
    status: Optional[str] = Field(None, description="文件状态")
    tag: Optional[str] = Field(None, description="标签")
    search: Optional[str] = Field(None, description="搜索关键词")


class FileInfoResponseSchema(BaseModel):
    """文件信息响应Schema"""
    id: int
    file_id: str
    filename: str
    file_path: str
    file_url: str
    file_size: int
    file_size_human: str
    content_type: str
    file_type: str
    tags: List[str]
    description: str
    download_count: int
    status: str
    created_at: str
    updated_at: Optional[str]
    expires_at: Optional[str]
    last_download_at: Optional[str]


class FileStorageControllerAPI(BaseModelService):
    """文件存储管理服务"""
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    def __init__(self):
        super().__init__()
        # 初始化文件管理器
        self.file_manager = FileManager(FileUploadConfig(
            max_file_size=50 * 1024 * 1024,  # 50MB
            allowed_types=['image', 'document', 'archive', 'video', 'audio'],
            upload_path='zhi_files_uploads',
            organize_by_date=True,
            generate_unique_name=True
        ))

    def _determine_file_type(self, filename: str, content_type: str) -> str:
        """确定文件类型"""
        import os
        extension = os.path.splitext(filename)[1].lower()

        if extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'] or content_type.startswith('image/'):
            return 'image'
        elif extension in ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']:
            return 'document'
        elif extension in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'archive'
        elif extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv'] or content_type.startswith('video/'):
            return 'video'
        elif extension in ['.mp3', '.wav', '.flac', '.aac', '.ogg'] or content_type.startswith('audio/'):
            return 'audio'
        else:
            return 'other'

    def _log_file_access(self, file_record, action: str, request=None, extra_data: Dict = None):
        """记录文件访问日志"""
        try:
            FileStorage, FileAccessLog = get_file_models()

            log_data = {
                'file': file_record,
                'action': action,
                'extra_data': extra_data or {}
            }

            if request:
                log_data.update({
                    'ip_address': get_client_ip(request),
                    'user_agent': get_user_agent(request),
                    'referer': request.META.get('HTTP_REFERER')
                })

            FileAccessLog.objects.create(**log_data)

        except Exception as e:
            zhi_logger.error(f"记录文件访问日志失败: {str(e)}")

    @api_route(http_post, "/upload", response={200: BaseResponseSchema[FileUploadResponseSchema]})
    def upload_file(self, request):
        """
        上传文件

        支持的文件类型：
        - image: jpg, jpeg, png, gif, bmp, webp
        - document: pdf, doc, docx, xls, xlsx, ppt, pptx, txt
        - archive: zip, rar, 7z, tar, gz
        - video: mp4, avi, mov, wmv, flv, mkv
        - audio: mp3, wav, flac, aac, ogg
        """
        try:
            # 从request.FILES获取上传的文件
            file = request.FILES.get('file')
            if not file:
                return create_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message="请上传文件",
                    success=False
                )

            # 获取表单参数
            tags_str = request.POST.get('tags', '')
            description = request.POST.get('description', '')
            expires_in_days = request.POST.get('expires_in_days')
            custom_path = request.POST.get('custom_path')
            file_id = request.POST.get('file_id')

            # 处理标签
            tags = [tag.strip() for tag in tags_str.split(',') if tag.strip()] if tags_str else []

            # 处理过期天数
            expires_in_days = int(expires_in_days) if expires_in_days else None

            with transaction.atomic():
                # 获取模型类
                FileStorage, FileAccessLog = get_file_models()

                # 使用基础文件管理器上传文件
                upload_result = self.file_manager.upload_file(file, custom_path, file_id)

                # 确定文件类型
                file_type = self._determine_file_type(upload_result.filename, upload_result.content_type)

                # 计算过期时间
                expires_at = None
                if expires_in_days:
                    expires_at = timezone.now() + timedelta(days=expires_in_days)

                # 创建数据库记录
                file_record = FileStorage.objects.create(
                    file_id=upload_result.file_id,
                    original_name=upload_result.filename,
                    file_name=upload_result.file_path.split('/')[-1],
                    file_path=upload_result.file_path,
                    file_url=upload_result.file_url,
                    file_size=upload_result.file_size,
                    file_type=file_type,
                    content_type=upload_result.content_type,
                    file_extension=upload_result.filename.split('.')[-1].lower() if '.' in upload_result.filename else '',
                    file_hash=upload_result.file_hash,
                    upload_ip=get_client_ip(request),
                    user_agent=get_user_agent(request),
                    tags=tags,
                    description=description,
                    expires_at=expires_at
                )

                # 记录访问日志
                self._log_file_access(file_record, 'upload', request)

                # 构建响应
                result = FileUploadResponseSchema(
                    id=file_record.id,
                    file_id=upload_result.file_id,
                    filename=upload_result.filename,
                    file_path=upload_result.file_path,
                    file_url=upload_result.file_url,
                    file_size=upload_result.file_size,
                    content_type=upload_result.content_type,
                    upload_time=file_record.created_at.isoformat(),
                    file_hash=upload_result.file_hash,
                    tags=tags,
                    description=description,
                    expires_at=expires_at.isoformat() if expires_at else None
                )

                return create_response(
                    data=result,
                    message="文件上传成功"
                )

        except ValueError as e:
            zhi_logger.warning(f"文件上传验证失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message=str(e),
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"文件上传失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"文件上传失败: {str(e)}",
                success=False
            )

    @api_route(http_get, "/download", response=FileResponse)
    def download_file(self, request, file_id: str = None, file_path: str = None,
                     as_attachment: bool = True, custom_filename: str = None):
        """
        下载文件

        支持通过文件ID或文件路径下载，自动记录下载日志和统计
        """
        try:
            if not file_id and not file_path:
                raise Http404("必须提供 file_id 或 file_path")

            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()

            # 获取文件记录
            if file_id:
                file_record = FileStorage.objects.get(file_id=file_id, status='active')
            else:
                file_record = FileStorage.objects.get(file_path=file_path, status='active')

            # 检查文件是否过期
            if file_record.expires_at and timezone.now() > file_record.expires_at:
                file_record.status = 'expired'
                file_record.save()
                raise Http404("文件已过期")

            # 下载文件
            response = self.file_manager.download_file(
                file_record.file_path,
                as_attachment,
                custom_filename or file_record.original_name
            )

            # 更新下载统计
            file_record.increment_download_count()

            # 记录访问日志
            self._log_file_access(file_record, 'download', request)

            return response

        except Exception as e:
            if "DoesNotExist" in str(e):
                raise Http404("文件记录不存在")
            zhi_logger.error(f"文件下载失败: {str(e)}")
            raise Http404("文件下载失败")

    @api_route(http_get, "/info", response={200: BaseResponseSchema[FileInfoResponseSchema]})
    def get_file_info(self, request, file_id: str = None, file_path: str = None):
        """
        获取文件信息

        包含标签、描述、下载统计等详细信息
        """
        try:
            if not file_id and not file_path:
                return create_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message="必须提供 file_id 或 file_path",
                    success=False
                )

            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()

            # 获取文件记录
            if file_id:
                file_record = FileStorage.objects.get(file_id=file_id, status='active')
            else:
                file_record = FileStorage.objects.get(file_path=file_path, status='active')

            # 构建响应数据
            file_data = FileInfoResponseSchema(
                id=file_record.id,
                file_id=file_record.file_id,
                filename=file_record.original_name,
                file_path=file_record.file_path,
                file_url=file_record.file_url,
                file_size=file_record.file_size,
                file_size_human=file_record.file_size_human,
                content_type=file_record.content_type,
                file_type=file_record.file_type,
                tags=file_record.tags,
                description=file_record.description,
                download_count=file_record.download_count,
                status=file_record.status,
                created_at=file_record.created_at.isoformat() if file_record.created_at else None,
                updated_at=file_record.updated_at.isoformat() if file_record.updated_at else None,
                expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None
            )

            return create_response(
                data=file_data,
                message="获取文件信息成功"
            )

        except FileStorage.DoesNotExist:
            return create_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="文件不存在",
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"获取文件信息失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取文件信息失败: {str(e)}",
                success=False
            )

    @api_route(http_get, "/list", response={200: BaseResponseSchema[List[FileInfoResponseSchema]]})
    @paginate
    def list_files(self, request, filters: FileListFilterSchema = Query(...)):
        """
        获取文件列表（支持分页和过滤）
        """
        try:
            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()

            # 构建查询
            queryset = FileStorage.objects.all()

            # 应用过滤条件
            if filters.file_type:
                queryset = queryset.filter(file_type=filters.file_type)

            if filters.status:
                queryset = queryset.filter(status=filters.status)

            if filters.tag:
                queryset = queryset.filter(tags__contains=[filters.tag])

            if filters.search:
                queryset = queryset.filter(
                    models.Q(original_name__icontains=filters.search) |
                    models.Q(description__icontains=filters.search)
                )

            # 排序
            queryset = queryset.order_by('-created_at')

            # 构建响应数据
            files_data = []
            for file_record in queryset:
                file_info = FileInfoResponseSchema(
                    id=file_record.id,
                    file_id=file_record.file_id,
                    filename=file_record.original_name,
                    file_path=file_record.file_path,
                    file_url=file_record.file_url,
                    file_size=file_record.file_size,
                    file_size_human=file_record.file_size_human,
                    content_type=file_record.content_type,
                    file_type=file_record.file_type,
                    tags=file_record.tags,
                    description=file_record.description,
                    download_count=file_record.download_count,
                    status=file_record.status,
                    created_at=file_record.created_at.isoformat() if file_record.created_at else None,
                    updated_at=file_record.updated_at.isoformat() if file_record.updated_at else None,
                    expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                    last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None
                )
                files_data.append(file_info)

            return create_response(
                data=files_data,
                message="获取文件列表成功"
            )

        except Exception as e:
            zhi_logger.error(f"获取文件列表失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"获取文件列表失败: {str(e)}",
                success=False
            )

    @api_route(http_delete, "/delete", response={200: BaseResponseSchema[None]})
    def delete_file(self, request, file_id: str = None, file_path: str = None):
        """
        删除文件（软删除）
        """
        try:
            if not file_id and not file_path:
                return create_response(
                    data=None,
                    code=ResponseCode.BAD_REQUEST,
                    message="必须提供 file_id 或 file_path",
                    success=False
                )

            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()

            # 获取文件记录
            if file_id:
                file_record = FileStorage.objects.get(file_id=file_id)
            else:
                file_record = FileStorage.objects.get(file_path=file_path)

            # 软删除文件
            file_record.mark_as_deleted()

            # 记录访问日志
            self._log_file_access(file_record, 'delete', request)

            return create_response(
                data=None,
                message="文件删除成功"
            )

        except FileStorage.DoesNotExist:
            return create_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="文件不存在",
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"删除文件失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"删除文件失败: {str(e)}",
                success=False
            )

    @api_route(http_put, "/update", response={200: BaseResponseSchema[FileInfoResponseSchema]})
    def update_file(self, request, file_id: str,
                   tags: Optional[str] = None,
                   description: Optional[str] = None):
        """
        更新文件信息（标签、描述等）
        """
        try:
            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()

            # 获取文件记录
            file_record = FileStorage.objects.get(file_id=file_id)

            # 更新标签
            if tags is not None:
                file_record.tags = [tag.strip() for tag in tags.split(',') if tag.strip()]

            # 更新描述
            if description is not None:
                file_record.description = description

            # 保存更新
            file_record.save(update_fields=['tags', 'description', 'updated_at'])

            # 构建响应数据
            file_data = FileInfoResponseSchema(
                id=file_record.id,
                file_id=file_record.file_id,
                filename=file_record.original_name,
                file_path=file_record.file_path,
                file_url=file_record.file_url,
                file_size=file_record.file_size,
                file_size_human=file_record.file_size_human,
                content_type=file_record.content_type,
                file_type=file_record.file_type,
                tags=file_record.tags,
                description=file_record.description,
                download_count=file_record.download_count,
                status=file_record.status,
                created_at=file_record.created_at.isoformat() if file_record.created_at else None,
                updated_at=file_record.updated_at.isoformat() if file_record.updated_at else None,
                expires_at=file_record.expires_at.isoformat() if file_record.expires_at else None,
                last_download_at=file_record.last_download_at.isoformat() if file_record.last_download_at else None
            )

            # 记录访问日志
            self._log_file_access(file_record, 'update', request)

            return create_response(
                data=file_data,
                message="文件信息更新成功"
            )

        except FileStorage.DoesNotExist:
            return create_response(
                data=None,
                code=ResponseCode.NOT_FOUND,
                message="文件不存在",
                success=False
            )
        except Exception as e:
            zhi_logger.error(f"更新文件信息失败: {str(e)}")
            return create_response(
                data=None,
                code=ResponseCode.INTERNAL_ERROR,
                message=f"更新文件信息失败: {str(e)}",
                success=False
            )
