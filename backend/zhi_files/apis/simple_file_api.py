"""
简化文件管理API - 不依赖复杂的认证系统
"""

import os
import mimetypes
from typing import List, Optional, Dict, Any
from pathlib import Path

from django.http import HttpResponse, Http404, FileResponse, JsonResponse
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.db import models
from django.utils import timezone
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View

import json
import uuid
import hashlib
from datetime import datetime


def get_file_models():
    """获取文件模型"""
    from zhi_files.models import FileStorage, FileAccessLog
    return FileStorage, FileAccessLog


def get_client_ip(request):
    """获取客户端IP"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def get_user_agent(request):
    """获取用户代理"""
    return request.META.get('HTTP_USER_AGENT', '')


def calculate_file_hash(file_content):
    """计算文件哈希"""
    return hashlib.md5(file_content).hexdigest()


def get_file_type(filename):
    """根据文件名获取文件类型"""
    ext = Path(filename).suffix.lower()
    
    image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
    document_exts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt']
    archive_exts = ['.zip', '.rar', '.7z', '.tar', '.gz']
    video_exts = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv']
    audio_exts = ['.mp3', '.wav', '.flac', '.aac', '.ogg']
    
    if ext in image_exts:
        return 'image'
    elif ext in document_exts:
        return 'document'
    elif ext in archive_exts:
        return 'archive'
    elif ext in video_exts:
        return 'video'
    elif ext in audio_exts:
        return 'audio'
    else:
        return 'other'


def create_response(data=None, message="操作成功", success=True, code=200):
    """创建统一响应格式"""
    return JsonResponse({
        'success': success,
        'code': code,
        'message': message,
        'data': data
    }, status=code if not success else 200)


@method_decorator(csrf_exempt, name='dispatch')
class SimpleFileUploadView(View):
    """简化文件上传视图"""
    
    def post(self, request):
        """上传文件"""
        try:
            if 'file' not in request.FILES:
                return create_response(
                    data=None,
                    message="未找到上传文件",
                    success=False,
                    code=400
                )
            
            uploaded_file = request.FILES['file']
            
            # 验证文件大小
            max_size = 100 * 1024 * 1024  # 100MB
            if uploaded_file.size > max_size:
                return create_response(
                    data=None,
                    message=f"文件大小超过限制 ({max_size // (1024*1024)}MB)",
                    success=False,
                    code=400
                )
            
            # 读取文件内容
            file_content = uploaded_file.read()
            file_hash = calculate_file_hash(file_content)
            
            # 生成文件信息
            file_id = str(uuid.uuid4())
            original_name = uploaded_file.name
            file_extension = Path(original_name).suffix.lower()
            file_type = get_file_type(original_name)
            content_type = uploaded_file.content_type or mimetypes.guess_type(original_name)[0] or 'application/octet-stream'
            
            # 生成存储路径
            now = datetime.now()
            upload_path = f"zhi_files_uploads/{now.year}/{now.month:02d}/{now.day:02d}"
            file_name = f"{file_id}{file_extension}"
            file_path = f"{upload_path}/{file_name}"
            
            # 保存文件
            saved_path = default_storage.save(file_path, ContentFile(file_content))
            file_url = f"{settings.MEDIA_URL}{saved_path}"
            
            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()
            
            # 创建文件记录
            file_record = FileStorage.objects.create(
                file_id=file_id,
                original_name=original_name,
                file_name=file_name,
                file_path=saved_path,
                file_url=file_url,
                file_size=uploaded_file.size,
                file_type=file_type,
                content_type=content_type,
                file_extension=file_extension,
                file_hash=file_hash,
                upload_ip=get_client_ip(request),
                tags=[],
                description=request.POST.get('description', ''),
                metadata={}
            )
            
            # 记录访问日志
            FileAccessLog.objects.create(
                file=file_record,
                action='upload',
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
                referer=request.META.get('HTTP_REFERER'),
                extra_data={'file_size': uploaded_file.size}
            )
            
            # 构建响应数据
            response_data = {
                'file_id': file_record.file_id,
                'original_name': file_record.original_name,
                'file_name': file_record.file_name,
                'file_path': file_record.file_path,
                'file_url': file_record.file_url,
                'file_size': file_record.file_size,
                'file_type': file_record.file_type,
                'content_type': file_record.content_type,
                'file_extension': file_record.file_extension,
                'file_hash': file_record.file_hash,
                'status': file_record.status,
                'created_at': file_record.created_at.isoformat(),
            }
            
            return create_response(
                data=response_data,
                message="文件上传成功"
            )
            
        except Exception as e:
            return create_response(
                data=None,
                message=f"文件上传失败: {str(e)}",
                success=False,
                code=500
            )


@method_decorator(csrf_exempt, name='dispatch')
class SimpleFileDownloadView(View):
    """简化文件下载视图"""
    
    def get(self, request, file_id):
        """下载文件"""
        try:
            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()
            
            # 获取文件记录
            try:
                file_record = FileStorage.objects.get(file_id=file_id, is_deleted=False)
            except FileStorage.DoesNotExist:
                raise Http404("文件不存在")
            
            # 检查文件是否存在
            if not default_storage.exists(file_record.file_path):
                return create_response(
                    data=None,
                    message="文件不存在",
                    success=False,
                    code=404
                )
            
            # 更新下载统计
            file_record.download_count += 1
            file_record.last_download_at = timezone.now()
            file_record.save(update_fields=['download_count', 'last_download_at'])
            
            # 记录访问日志
            FileAccessLog.objects.create(
                file=file_record,
                action='download',
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request),
                referer=request.META.get('HTTP_REFERER'),
                extra_data={}
            )
            
            # 返回文件响应
            file_path = default_storage.path(file_record.file_path)
            response = FileResponse(
                open(file_path, 'rb'),
                content_type=file_record.content_type,
                as_attachment=True,
                filename=file_record.original_name
            )
            
            return response
            
        except Http404:
            raise
        except Exception as e:
            return create_response(
                data=None,
                message=f"文件下载失败: {str(e)}",
                success=False,
                code=500
            )


@method_decorator(csrf_exempt, name='dispatch')
class SimpleFileListView(View):
    """简化文件列表视图"""
    
    def get(self, request):
        """获取文件列表"""
        try:
            # 获取模型类
            FileStorage, FileAccessLog = get_file_models()
            
            # 获取查询参数
            page = int(request.GET.get('page', 1))
            page_size = int(request.GET.get('page_size', 20))
            file_type = request.GET.get('file_type')
            search = request.GET.get('search')
            
            # 构建查询
            queryset = FileStorage.objects.filter(is_deleted=False)
            
            if file_type:
                queryset = queryset.filter(file_type=file_type)
            
            if search:
                queryset = queryset.filter(
                    models.Q(original_name__icontains=search) |
                    models.Q(description__icontains=search)
                )
            
            # 排序和分页
            queryset = queryset.order_by('-created_at')
            total = queryset.count()
            
            start = (page - 1) * page_size
            end = start + page_size
            files = queryset[start:end]
            
            # 构建响应数据
            file_list = []
            for file_record in files:
                file_list.append({
                    'file_id': file_record.file_id,
                    'original_name': file_record.original_name,
                    'file_size': file_record.file_size,
                    'file_type': file_record.file_type,
                    'content_type': file_record.content_type,
                    'status': file_record.status,
                    'download_count': file_record.download_count,
                    'created_at': file_record.created_at.isoformat(),
                    'description': file_record.description,
                })
            
            response_data = {
                'files': file_list,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total': total,
                    'pages': (total + page_size - 1) // page_size
                }
            }
            
            return create_response(
                data=response_data,
                message="获取文件列表成功"
            )
            
        except Exception as e:
            return create_response(
                data=None,
                message=f"获取文件列表失败: {str(e)}",
                success=False,
                code=500
            )
