"""
ZhiFiles 数据库路由器
支持微服务架构下的数据库分离
"""

from django.conf import settings


class ZhiFilesRouter:
    """
    ZhiFiles 微服务数据库路由器
    
    路由规则：
    1. zhi_files 应用的模型路由到 files_db
    2. 用户相关模型路由到 user_db
    3. 其他模型使用默认数据库
    """
    
    # ZhiFiles 相关应用
    files_apps = {'zhi_files'}
    
    # 用户相关应用
    user_apps = {'zhi_oauth', 'auth', 'contenttypes', 'sessions'}
    
    # 数据库映射
    route_app_labels = {
        'zhi_files': 'files_db',
        'zhi_oauth': 'user_db',
        'auth': 'user_db',
        'contenttypes': 'user_db',
        'sessions': 'user_db',
    }
    
    def db_for_read(self, model, **hints):
        """
        读取数据库路由
        """
        app_label = model._meta.app_label
        
        # ZhiFiles 模型路由到文件数据库
        if app_label in self.files_apps:
            return 'files_db'
        
        # 用户相关模型路由到用户数据库
        if app_label in self.user_apps:
            return 'user_db'
        
        # 其他模型使用默认数据库
        return None
    
    def db_for_write(self, model, **hints):
        """
        写入数据库路由
        """
        return self.db_for_read(model, **hints)
    
    def allow_relation(self, obj1, obj2, **hints):
        """
        允许关系检查
        """
        db_set = {'default', 'files_db', 'user_db'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        迁移控制
        """
        # ZhiFiles 应用只在 files_db 中迁移
        if app_label in self.files_apps:
            return db == 'files_db'
        
        # 用户相关应用只在 user_db 中迁移
        if app_label in self.user_apps:
            return db == 'user_db'
        
        # 其他应用在默认数据库中迁移
        if db == 'files_db' or db == 'user_db':
            return False
        
        return db == 'default'


class ZhiFilesSimpleRouter:
    """
    ZhiFiles 简化路由器
    适用于单数据库部署
    """
    
    def db_for_read(self, model, **hints):
        """所有模型使用默认数据库"""
        return None
    
    def db_for_write(self, model, **hints):
        """所有模型使用默认数据库"""
        return None
    
    def allow_relation(self, obj1, obj2, **hints):
        """允许所有关系"""
        return True
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """允许所有迁移到默认数据库"""
        return db == 'default'


def get_files_router():
    """
    获取文件路由器
    根据配置决定使用哪个路由器
    """
    # 检查是否启用了微服务模式
    microservice_mode = getattr(settings, 'ZHIFILES_MICROSERVICE_MODE', False)
    
    if microservice_mode:
        return ZhiFilesRouter()
    else:
        return ZhiFilesSimpleRouter()


# 默认路由器实例
files_router = get_files_router()
