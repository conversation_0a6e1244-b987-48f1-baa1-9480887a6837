import os
# 读取 WORKER_ENV 环境变量，默认为 development
DJANGO_ENV = os.environ.get('DJANGO_ENV', 'development')
# 公共配置项


# ================================================= #
# ****************** 功能 启停  ******************* #
# ================================================= #
DEBUG = os.environ.get('DEBUG', True)

USE_TZ = os.environ.get('USE_TZ', False)
# 开启 OpenTelemetry 日志
IS_OPEN_OTLP = os.environ.get('IS_OPEN_OTLP', False)

# 开启 API Debug 响应日志打印
API_REQUEST_INFO_LOGGER = os.environ.get('API_REQUEST_INFO_LOGGER', True)
# 登录时是否关闭验证码
LOGIN_NO_CAPTCHA_AUTH = os.environ.get('LOGIN_NO_CAPTCHA_AUTH', False)

# ================================================= #
# ****************** 密钥配置  ******************* #
# ================================================= #
SECRET_KEY = os.environ.get('SECRET_KEY', '"django-insecure--z8%exyzt7e_%i@1+#1mm=%lb5=^fx_57=1@a+_y7bg5-w%)sm"')


# ================================================= #
# ***************** 数据库配置  ******************* #
# ================================================= #
DATABASE_NAME = os.environ.get('DATABASE_NAME', '')  # mysql 时使用
# 数据库地址
DATABASE_HOST = os.environ.get('DATABASE_HOST', '')
# # 数据库端口
DATABASE_PORT = int(os.environ.get('DATABASE_PORT', 3306))
# # 数据库用户名
DATABASE_USER = os.environ.get('DATABASE_USER', '')
# # 数据库密码
DATABASE_PASSWORD = os.environ.get('DATABASE_PASSWORD', '')


# ================================================= #
# ******** redis配置，无 redis 可不进行配置  ******** #
# ================================================= #
REDIS_DB = int(os.environ.get('REDIS_DB', 1))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', '')
REDIS_HOST = os.environ.get('REDIS_HOST', '127.0.0.1')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))


# ================================================= #
# ****************** Celery 配置  ******************* #
# ================================================= #
CELERY_BROKER_DB = int(os.environ.get('CELERY_BROKER_DB', 2))

# ======================================================== #
# ****************** 数据库字段 加密配置  ******************* #
# ======================================================== #
ENCRYPTED_FIELDS_KEY = os.environ.get(
    "ENCRYPTED_FIELDS_KEY",
    "PQYn2FlA9nxzJy7C7z2Zm-Usjy-uunxKYZ72eovbIUE="
)


# 动态配置项
if DJANGO_ENV == 'development':
    # 此调用不可消除，否则会出现配置无法加载的问题
    from conf.env_development import *


