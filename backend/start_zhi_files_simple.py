#!/usr/bin/env python
"""
ZhiFiles 简化启动脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_files')

if __name__ == '__main__':
    try:
        from django.core.management import execute_from_command_line
        
        print("🚀 启动 ZhiFiles 文件管理服务...")
        print("📁 项目路径:", project_root)
        print("⚙️  配置文件: application.settings.zhi_files")
        print("🌐 API文档: http://127.0.0.1:8000/api/docs/")
        print("📊 管理后台: http://127.0.0.1:8000/admin/")
        print("-" * 50)
        
        # 直接启动开发服务器
        execute_from_command_line([
            'manage.py',
            'runserver',
            '0.0.0.0:8000'
        ])
        
    except ImportError as exc:
        raise ImportError(
            "无法导入Django。请确保已安装Django并且"
            "DJANGO_SETTINGS_MODULE环境变量已正确设置。"
        ) from exc
    except KeyboardInterrupt:
        print("\n👋 ZhiFiles 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
