"""
微服务配置示例
展示如何在各个微服务中配置数据库路由和公共组件
"""

# ================================
# 基础配置（所有微服务共用）
# ================================

# 公共应用配置
COMMON_INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',

    # 第三方应用
    'ninja',
    'ninja_extra',
    'oauth2_provider',

    # 公共应用（所有微服务都需要）
    'zhi_common',
    'zhi_oauth',
    'zhi_logger',  # 统一日志系统
]

# 数据库路由配置
DATABASE_ROUTERS = [
    'zhi_common.database_router.MicroserviceRouter',
    # 'zhi_common.database_router.ReadWriteSplitRouter',  # 可选：读写分离
    # 'zhi_common.database_router.TenantRouter',          # 可选：多租户
]

# 用户模型配置
AUTH_USER_MODEL = 'zhi_oauth.User'

# 缓存配置（Redis）
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# ================================
# 订单服务配置示例
# ================================

class OrderServiceSettings:
    """订单服务配置"""
    
    INSTALLED_APPS = COMMON_INSTALLED_APPS + [
        'order_service',           # 订单服务应用
        'order_service.orders',    # 订单模块
        'order_service.logistics', # 物流模块
    ]
    
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'order_service',
            'USER': 'root',
            'PASSWORD': 'password',
            'HOST': 'order-db-host',
            'PORT': '3306',
            'OPTIONS': {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            }
        },
        
        # 用户数据库（只读访问）
        'user_db': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'zhi_oauth',
            'USER': 'readonly_user',
            'PASSWORD': 'readonly_password',
            'HOST': 'user-db-host',
            'PORT': '3306',
            'OPTIONS': {
                'charset': 'utf8mb4',
            }
        },
        
        # 订单数据库（主库）
        'order_db': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'order_service',
            'USER': 'order_user',
            'PASSWORD': 'order_password',
            'HOST': 'order-db-host',
            'PORT': '3306',
            'OPTIONS': {
                'charset': 'utf8mb4',
            }
        },
        
        # 订单数据库（从库，可选）
        'order_db_read': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'order_service',
            'USER': 'order_readonly',
            'PASSWORD': 'order_readonly_password',
            'HOST': 'order-db-read-host',
            'PORT': '3306',
            'OPTIONS': {
                'charset': 'utf8mb4',
            }
        }
    }

# ================================
# 商品服务配置示例
# ================================

class ProductServiceSettings:
    """商品服务配置"""
    
    INSTALLED_APPS = COMMON_INSTALLED_APPS + [
        'product_service',              # 商品服务应用
        'product_service.products',     # 商品模块
        'product_service.categories',   # 分类模块
        'product_service.inventory',    # 库存模块
    ]
    
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'product_service',
            'USER': 'root',
            'PASSWORD': 'password',
            'HOST': 'product-db-host',
            'PORT': '3306',
        },
        
        # 用户数据库
        'user_db': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'zhi_oauth',
            'USER': 'readonly_user',
            'PASSWORD': 'readonly_password',
            'HOST': 'user-db-host',
            'PORT': '3306',
        },
        
        # 商品数据库
        'product_db': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'product_service',
            'USER': 'product_user',
            'PASSWORD': 'product_password',
            'HOST': 'product-db-host',
            'PORT': '3306',
        }
    }

# ================================
# 用户中心配置示例
# ================================

class UserCenterSettings:
    """用户中心配置"""
    
    INSTALLED_APPS = COMMON_INSTALLED_APPS + [
        # 用户中心不需要额外的业务应用
        # 所有用户相关功能都在zhi_oauth中
    ]
    
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'zhi_oauth',
            'USER': 'root',
            'PASSWORD': 'password',
            'HOST': 'user-db-host',
            'PORT': '3306',
        },
        
        # 用户数据库（主库）
        'user_db': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'zhi_oauth',
            'USER': 'user_admin',
            'PASSWORD': 'user_admin_password',
            'HOST': 'user-db-host',
            'PORT': '3306',
            'OPTIONS': {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            }
        },
        
        # 用户数据库（从库，可选）
        'user_db_read': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'zhi_oauth',
            'USER': 'user_readonly',
            'PASSWORD': 'user_readonly_password',
            'HOST': 'user-db-read-host',
            'PORT': '3306',
        }
    }

# ================================
# Docker Compose 配置示例
# ================================

DOCKER_COMPOSE_EXAMPLE = """
version: '3.8'

services:
  # 用户中心服务
  user-service:
    build: ./user_service
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=mysql://user_admin:password@user-db:3306/zhi_oauth
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - user-db
      - redis
  
  # 订单服务
  order-service:
    build: ./order_service
    ports:
      - "8002:8000"
    environment:
      - DATABASE_URL=mysql://order_user:password@order-db:3306/order_service
      - USER_DB_URL=mysql://readonly_user:password@user-db:3306/zhi_oauth
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - order-db
      - user-db
      - redis
  
  # 商品服务
  product-service:
    build: ./product_service
    ports:
      - "8003:8000"
    environment:
      - DATABASE_URL=mysql://product_user:password@product-db:3306/product_service
      - USER_DB_URL=mysql://readonly_user:password@user-db:3306/zhi_oauth
      - REDIS_URL=redis://redis:6379/1
    depends_on:
      - product-db
      - user-db
      - redis
  
  # 数据库服务
  user-db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: zhi_oauth
      MYSQL_USER: user_admin
      MYSQL_PASSWORD: password
    volumes:
      - user_db_data:/var/lib/mysql
    ports:
      - "3306:3306"
  
  order-db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: order_service
      MYSQL_USER: order_user
      MYSQL_PASSWORD: password
    volumes:
      - order_db_data:/var/lib/mysql
    ports:
      - "3307:3306"
  
  product-db:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: product_service
      MYSQL_USER: product_user
      MYSQL_PASSWORD: password
    volumes:
      - product_db_data:/var/lib/mysql
    ports:
      - "3308:3306"
  
  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  user_db_data:
  order_db_data:
  product_db_data:
  redis_data:
"""

# ================================
# Kubernetes 配置示例
# ================================

KUBERNETES_EXAMPLE = """
# user-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
    spec:
      containers:
      - name: user-service
        image: zhiadmin/user-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "mysql://user_admin:password@user-db-service:3306/zhi_oauth"
        - name: REDIS_URL
          value: "redis://redis-service:6379/1"

---
# order-service-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: order-service
spec:
  replicas: 2
  selector:
    matchLabels:
      app: order-service
  template:
    metadata:
      labels:
        app: order-service
    spec:
      containers:
      - name: order-service
        image: zhiadmin/order-service:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          value: "mysql://order_user:password@order-db-service:3306/order_service"
        - name: USER_DB_URL
          value: "mysql://readonly_user:password@user-db-service:3306/zhi_oauth"
        - name: REDIS_URL
          value: "redis://redis-service:6379/1"
"""

# ================================
# 使用说明
# ================================

USAGE_INSTRUCTIONS = """
# 1. 在各微服务的settings.py中使用对应配置
# order_service/settings.py
from microservice_settings_example import OrderServiceSettings
INSTALLED_APPS = OrderServiceSettings.INSTALLED_APPS
DATABASES = OrderServiceSettings.DATABASES

# 2. 在模型中使用数据库路由
from zhi_common.database_router import execute_on_user_db, execute_on_service_db

# 获取用户信息（自动路由到user_db）
from django.contrib.auth import get_user_model
User = get_user_model()
user = User.objects.get(id=user_id)  # 自动使用user_db

# 在订单服务中查询订单（自动路由到order_db）
from order_service.models import Order
orders = Order.objects.all()  # 自动使用order_db

# 手动指定数据库
user_from_user_db = execute_on_user_db(User).get(id=user_id)
orders_from_order_db = execute_on_service_db(Order, 'order').all()

# 3. 在视图中使用用户管理器
from zhi_common.zhi_oauth_sdk import user_manager

def my_view(request):
    current_user = user_manager.get_current_user(request)
    user_permissions = user_manager.get_user_permissions(current_user['id'])
    return JsonResponse({'user': current_user, 'permissions': user_permissions})
"""
