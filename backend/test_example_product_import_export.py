#!/usr/bin/env python
"""
ExampleProduct 导入导出功能测试脚本
"""

import os
import sys
import django
import json
import tempfile
from pathlib import Path
from io import BytesIO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_example_product_service():
    """测试ExampleProduct服务类"""
    print("\n=== 测试ExampleProduct服务类 ===")
    
    try:
        from zhi_oauth.apis.example_product import ExampleProductControllerAPI
        from zhi_oauth.models import ExampleProduct
        
        # 创建服务实例 - 直接使用BaseModelService
        from zhi_common.zhi_api.zhi_crud import BaseModelService

        service = BaseModelService(
            model_class=ExampleProduct,
            export_settings={
                'enabled': True,
                'max_rows': 5000,
                'fields': ['name', 'description'],
                'field_mapping': {
                    'name': '产品名称',
                    'description': '产品描述'
                }
            },
            import_settings={
                'enabled': True,
                'max_rows': 1000,
                'field_mapping': {
                    '产品名称': 'name',
                    '产品描述': 'description'
                },
                'field_validators': {
                    'name': lambda x: x.strip() if x else '',
                    'description': lambda x: x.strip() if x else ''
                }
            }
        )
        
        print("✅ ExampleProductControllerAPI 实例化成功")
        
        # 检查配置
        print(f"导出配置: {service.export_settings}")
        print(f"导入配置: {service.import_settings}")
        
        return service
        
    except Exception as e:
        print(f"❌ 服务类测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_create_sample_data():
    """创建示例数据"""
    print("\n=== 创建示例数据 ===")
    
    try:
        from zhi_oauth.models import ExampleProduct
        
        # 清理现有数据
        ExampleProduct.objects.filter(name__startswith='测试产品').delete()
        
        # 创建示例数据
        sample_products = [
            {'name': '测试产品A', 'description': '这是测试产品A的描述'},
            {'name': '测试产品B', 'description': '这是测试产品B的描述'},
            {'name': '测试产品C', 'description': '这是测试产品C的描述'},
            {'name': '测试产品D', 'description': ''},  # 空描述
            {'name': '测试产品E', 'description': '这是一个很长的描述，用来测试字段长度限制和导出功能'},
        ]
        
        created_products = []
        for product_data in sample_products:
            product = ExampleProduct.objects.create(**product_data)
            created_products.append(product)
        
        print(f"✅ 成功创建 {len(created_products)} 个示例产品")
        return created_products
        
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_export_functionality(service):
    """测试导出功能"""
    print("\n=== 测试导出功能 ===")
    
    try:
        # 准备测试数据
        test_data = [
            {'name': '产品1', 'description': '描述1'},
            {'name': '产品2', 'description': '描述2'},
            {'name': '产品3', 'description': '描述3'}
        ]
        
        # 测试不同格式的导出
        formats = ['csv', 'excel', 'json']
        
        for format_type in formats:
            try:
                response = service.export_data(
                    format_type=format_type,
                    filename=f'test_export.{format_type}',
                    filters={},
                    fields=['name', 'description']
                )
                
                if hasattr(response, 'status_code'):
                    print(f"✅ {format_type.upper()}导出测试通过 - 状态码: {response.status_code}")
                else:
                    # 如果返回的是ZhiResponse对象
                    print(f"✅ {format_type.upper()}导出测试通过 - 响应: {response.message}")
                    
            except Exception as e:
                print(f"❌ {format_type.upper()}导出测试失败: {e}")
        
        print("✅ 导出功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 导出功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_functionality(service):
    """测试导入功能"""
    print("\n=== 测试导入功能 ===")
    
    try:
        # 创建测试CSV文件
        csv_content = """产品名称,产品描述
导入产品1,这是导入的产品1
导入产品2,这是导入的产品2
导入产品3,这是导入的产品3"""
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write(csv_content)
            temp_file_path = f.name
        
        try:
            # 模拟上传文件
            class MockUploadedFile:
                def __init__(self, file_path, name):
                    self.file_path = file_path
                    self.name = name
                    with open(file_path, 'rb') as f:
                        self.content = f.read()
                    self.size = len(self.content)
                
                def read(self):
                    return self.content
            
            mock_file = MockUploadedFile(temp_file_path, 'test_import.csv')
            
            # 测试预览导入
            preview_result = service.import_data(file=mock_file, preview_only=True)
            print(f"✅ 导入预览测试通过 - 响应: {getattr(preview_result, 'message', '成功')}")

            # 测试实际导入
            import_result = service.import_data(file=mock_file, preview_only=False)
            print(f"✅ 实际导入测试通过 - 响应: {getattr(import_result, 'message', '成功')}")
            
        finally:
            # 清理临时文件
            os.unlink(temp_file_path)
        
        print("✅ 导入功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 导入功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_generation(service):
    """测试模板生成功能"""
    print("\n=== 测试模板生成功能 ===")
    
    try:
        # 测试Excel模板
        excel_template = service.get_import_template(format_type='excel')
        if hasattr(excel_template, 'status_code'):
            print(f"✅ Excel模板生成测试通过 - 状态码: {excel_template.status_code}")
        else:
            print(f"✅ Excel模板生成测试通过 - 响应: {getattr(excel_template, 'message', '成功')}")

        # 测试CSV模板
        csv_template = service.get_import_template(format_type='csv')
        if hasattr(csv_template, 'status_code'):
            print(f"✅ CSV模板生成测试通过 - 状态码: {csv_template.status_code}")
        else:
            print(f"✅ CSV模板生成测试通过 - 响应: {getattr(csv_template, 'message', '成功')}")
        
        print("✅ 模板生成功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 模板生成功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_field_info(service):
    """测试字段信息获取"""
    print("\n=== 测试字段信息获取 ===")
    
    try:
        fields_info = service.get_export_fields()
        print(f"✅ 字段信息获取测试通过 - 响应: {getattr(fields_info, 'message', '成功')}")

        if hasattr(fields_info, 'data') and fields_info.data:
            print("可导出字段信息:")
            for field in fields_info.data:
                print(f"  - {field.get('name', 'N/A')}: {field.get('verbose_name', 'N/A')} ({field.get('type', 'N/A')})")
        
        print("✅ 字段信息功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 字段信息功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始ExampleProduct导入导出功能测试")
    
    # 测试步骤
    tests = [
        ("服务类测试", test_example_product_service),
        ("创建示例数据", test_create_sample_data),
    ]
    
    passed = 0
    total = len(tests)
    service = None
    
    # 执行基础测试
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                if test_name == "服务类测试":
                    service = result
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
    
    # 如果服务创建成功，继续测试导入导出功能
    if service:
        import_export_tests = [
            ("导出功能测试", lambda: test_export_functionality(service)),
            ("导入功能测试", lambda: test_import_functionality(service)),
            ("模板生成测试", lambda: test_template_generation(service)),
            ("字段信息测试", lambda: test_field_info(service)),
        ]
        
        for test_name, test_func in import_export_tests:
            try:
                if test_func():
                    passed += 1
                total += 1
            except Exception as e:
                print(f"❌ {test_name}异常: {e}")
                total += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！ExampleProduct导入导出功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
