# dev.ps1
Set-Location "D:\MyPythonProjects\pycharm-projects\zhi_ai_admin\backend"
& .\.venv\Scripts\Activate.ps1

Write-Host "🚀 ZhiAdmin开发环境已准备就绪" -ForegroundColor Green
Write-Host "📁 当前目录: $(Get-Location)" -ForegroundColor Yellow
Write-Host "🐍 Python版本: $(python --version)" -ForegroundColor Cyan

# 显示可用命令
Write-Host "`n💡 可用命令:" -ForegroundColor Magenta
Write-Host "  python manage.py check --settings=application.settings.zhi_oauth" -ForegroundColor White
Write-Host "  python architecture_test.py" -ForegroundColor White
Write-Host "  python quick_test.py" -ForegroundColor White