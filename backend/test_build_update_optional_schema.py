"""
测试 build_update_optional_schema 功能

验证可选更新Schema的构建是否正确工作
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from django.db import models
from zhi_common.zhi_model.core_model import ZhiCoreModel
from zhi_common.zhi_api.base_config import build_update_optional_schema
from typing import Optional, Union
from datetime import datetime
from decimal import Decimal


# 创建一个测试模型
class TestProduct(ZhiCoreModel):
    """测试产品模型"""
    name = models.CharField(max_length=100, verbose_name="产品名称")
    description = models.TextField(blank=True, verbose_name="描述")
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=False, verbose_name="价格")
    category = models.CharField(max_length=50, null=True, blank=True, verbose_name="分类")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    stock = models.IntegerField(default=0, verbose_name="库存")
    email = models.EmailField(blank=True, verbose_name="联系邮箱")
    created_date = models.DateField(auto_now_add=True, verbose_name="创建日期")
    
    # 选择字段
    STATUS_CHOICES = [
        ('draft', '草稿'),
        ('published', '已发布'),
        ('archived', '已归档'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft', verbose_name="状态")
    
    class Meta:
        app_label = 'test'
        db_table = 'test_product_schema'


def test_build_update_optional_schema():
    """测试构建可选更新Schema"""
    print("🧪 测试构建可选更新Schema...")
    
    # 构建Schema
    UpdateSchema = build_update_optional_schema(TestProduct)
    
    print(f"📋 生成的Schema类名: {UpdateSchema.__name__}")
    
    # 检查Schema的字段
    schema_fields = UpdateSchema.__annotations__ if hasattr(UpdateSchema, '__annotations__') else {}
    
    print(f"📋 Schema字段数量: {len(schema_fields)}")
    
    expected_fields = [
        'name',
        'description', 
        'price',
        'category',
        'is_active',
        'stock',
        'email',
        'status'
    ]
    
    print("\n📋 字段类型检查:")
    for field_name in expected_fields:
        if field_name in schema_fields:
            field_type = schema_fields[field_name]
            print(f"   ✅ {field_name}: {field_type}")
        else:
            print(f"   ❌ {field_name}: 缺失")
    
    # 检查是否所有字段都是可选的
    print("\n📋 可选性检查:")
    all_optional = True
    for field_name, field_type in schema_fields.items():
        # 检查是否是Optional类型
        is_optional = False

        # 方法1：检查__origin__是否是Union
        if hasattr(field_type, '__origin__'):
            if field_type.__origin__ is Union:
                # Optional[T] 实际上是 Union[T, None]
                args = getattr(field_type, '__args__', ())
                if len(args) == 2 and type(None) in args:
                    is_optional = True

        # 方法2：直接检查字符串表示
        if not is_optional:
            field_type_str = str(field_type)
            if 'Optional[' in field_type_str or 'Union[' in field_type_str:
                is_optional = True

        if is_optional:
            print(f"   ✅ {field_name}: 可选")
        else:
            print(f"   ❌ {field_name}: 非可选 - {field_type}")
            all_optional = False
    
    # 测试Schema实例化
    print("\n📋 Schema实例化测试:")
    try:
        # 测试空数据
        empty_instance = UpdateSchema()
        print("   ✅ 空数据实例化成功")
        
        # 测试部分数据
        partial_data = {
            'name': '更新的产品名称',
            'price': Decimal('199.99'),
            'is_active': False
        }
        
        partial_instance = UpdateSchema(**partial_data)
        print("   ✅ 部分数据实例化成功")
        
        # 测试数据转换
        if hasattr(partial_instance, 'dict'):
            data_dict = partial_instance.dict()
            print(f"   ✅ 数据转换成功: {data_dict}")
        elif hasattr(partial_instance, 'model_dump'):
            data_dict = partial_instance.model_dump()
            print(f"   ✅ 数据转换成功: {data_dict}")
        else:
            print("   ⚠️  无法转换为字典")
        
    except Exception as e:
        print(f"   ❌ Schema实例化失败: {e}")
        all_optional = False
    
    return all_optional and len(schema_fields) >= len(expected_fields) - 2  # 允许一些字段被排除


def test_schema_with_exclude_fields():
    """测试带排除字段的Schema构建"""
    print("\n🧪 测试带排除字段的Schema构建...")
    
    exclude_fields = ['email', 'created_date']
    UpdateSchema = build_update_optional_schema(TestProduct, exclude_fields=exclude_fields)
    
    schema_fields = UpdateSchema.__annotations__ if hasattr(UpdateSchema, '__annotations__') else {}
    
    print(f"📋 排除字段后的Schema字段数量: {len(schema_fields)}")
    
    # 检查排除的字段是否真的被排除了
    excluded_found = False
    for field_name in exclude_fields:
        if field_name in schema_fields:
            print(f"   ❌ 排除字段 {field_name} 仍然存在")
            excluded_found = True
        else:
            print(f"   ✅ 排除字段 {field_name} 已被排除")
    
    return not excluded_found


def test_auto_crud_integration():
    """测试与自动CRUD装饰器的集成"""
    print("\n🧪 测试与自动CRUD装饰器的集成...")
    
    try:
        from zhi_common.zhi_api.base_config import auto_crud_api
        from zhi_common.zhi_api.zhi_crud import BaseModelService
        
        # 创建一个测试服务类
        @auto_crud_api(
            TestProduct,
            prefix="test_products",
            tags=["测试产品"],
            exclude_endpoints=['list_pagination', 'list_id_mappings']  # 排除一些端点以简化测试
        )
        class TestProductService(BaseModelService):
            model = TestProduct
        
        print("   ✅ 自动CRUD装饰器应用成功")
        
        # 检查生成的控制器是否有patch方法
        if hasattr(TestProductService, 'patch'):
            print("   ✅ 控制器有patch方法")
        else:
            print("   ❌ 控制器没有patch方法")
        
        # 检查服务实例是否有相关方法
        service_instance = TestProductService()
        
        required_methods = ['patch_instance', 'update_instance', '_should_update_field']
        methods_found = 0
        
        for method_name in required_methods:
            if hasattr(service_instance, method_name):
                print(f"   ✅ 服务实例有 {method_name} 方法")
                methods_found += 1
            else:
                print(f"   ❌ 服务实例没有 {method_name} 方法")
        
        return methods_found == len(required_methods)
        
    except Exception as e:
        print(f"   ❌ 自动CRUD集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试 build_update_optional_schema 功能...")
    print("🎯 目标：验证可选更新Schema构建功能是否正确工作")
    
    tests = [
        test_build_update_optional_schema,
        test_schema_with_exclude_fields,
        test_auto_crud_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！build_update_optional_schema 功能工作正常。")
        print("\n✅ 实现的功能特性:")
        print("   - ✅ 自动构建可选更新Schema")
        print("   - ✅ 所有字段都是Optional类型")
        print("   - ✅ 支持各种Django字段类型")
        print("   - ✅ 支持排除指定字段")
        print("   - ✅ 与自动CRUD装饰器集成")
        print("   - ✅ 支持PATCH端点的部分更新")
        print("\n💡 使用方式:")
        print("   # 构建可选更新Schema")
        print("   UpdateSchema = build_update_optional_schema(MyModel)")
        print("   ")
        print("   # 排除特定字段")
        print("   UpdateSchema = build_update_optional_schema(MyModel, exclude_fields=['field1', 'field2'])")
        print("   ")
        print("   # 在自动CRUD中自动使用")
        print("   @auto_crud_api(MyModel)")
        print("   class MyService(BaseModelService): pass")
        print("   # PATCH /api/mymodel/{id} 端点会自动使用可选Schema")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
