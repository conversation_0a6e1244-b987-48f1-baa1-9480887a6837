#!/usr/bin/env python
"""
最终测试审计日志修复

验证修复后的代码：
1. 每次操作只记录一条审计日志
2. 使用正确的精细化格式
3. AuditLog模型不会记录自身的审计日志
"""

import os
import sys
import django

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

from zhi_logger.models import AuditLog
from zhi_oauth.models import User


def test_single_audit_record():
    """测试单次操作只产生一条审计日志"""
    print("测试 1: 单次操作只产生一条审计日志")
    
    try:
        # 获取一个现有用户进行测试
        user = User.objects.first()
        if not user:
            print("  ⚠️ 没有找到用户，跳过测试")
            return False
        
        print(f"  📝 测试用户: {user.username} (ID: {user.id})")
        
        # 记录操作前的审计日志数量
        initial_count = AuditLog.objects.filter(resource_id=str(user.id)).count()
        print(f"  📊 操作前审计日志数量: {initial_count}")
        
        # 执行一次更新操作
        from django.utils import timezone
        user.last_login = timezone.now()
        user.save()
        
        print(f"  ✅ 更新用户last_login: {user.last_login}")
        
        # 等待异步任务完成
        import time
        time.sleep(3)
        
        # 检查操作后的审计日志数量
        final_count = AuditLog.objects.filter(resource_id=str(user.id)).count()
        print(f"  📊 操作后审计日志数量: {final_count}")
        
        # 计算新增的审计日志数量
        new_logs_count = final_count - initial_count
        print(f"  📈 新增审计日志数量: {new_logs_count}")
        
        if new_logs_count == 1:
            print("  ✅ 正确：单次操作只产生了1条审计日志")
            return True
        elif new_logs_count == 0:
            print("  ⚠️ 警告：没有产生审计日志，可能审计功能未启用")
            return False
        else:
            print(f"  ❌ 错误：单次操作产生了{new_logs_count}条审计日志，应该只有1条")
            
            # 显示新增的审计日志详情
            new_logs = AuditLog.objects.filter(resource_id=str(user.id)).order_by('-created_at')[:new_logs_count]
            for i, log in enumerate(new_logs, 1):
                print(f"    日志{i}: action={log.action}, old_values类型={type(log.old_values)}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {str(e)}")
        return False


def test_audit_log_format():
    """测试审计日志格式是否正确"""
    print("测试 2: 审计日志格式验证")
    
    try:
        # 获取最新的审计日志
        latest_log = AuditLog.objects.order_by('-created_at').first()
        if not latest_log:
            print("  ⚠️ 没有找到审计日志")
            return False
        
        print(f"  📝 最新审计日志ID: {latest_log.id}")
        print(f"  📝 资源ID: {latest_log.resource_id}")
        print(f"  📝 操作类型: {latest_log.action}")
        
        # 检查old_values和new_values的类型和格式
        old_values = latest_log.old_values
        new_values = latest_log.new_values
        
        print(f"  📝 old_values类型: {type(old_values)}")
        print(f"  📝 new_values类型: {type(new_values)}")
        
        # 检查是否为列表格式（正确的精细化格式）
        if isinstance(old_values, list) and isinstance(new_values, list):
            print("  ✅ 格式正确：使用了精细化的列表格式")
            
            # 检查列表中的元素格式
            if old_values and isinstance(old_values[0], dict):
                sample_item = old_values[0]
                required_keys = {'field_name', 'field_cn_name', 'value'}
                if required_keys.issubset(sample_item.keys()):
                    print("  ✅ 结构正确：包含field_name, field_cn_name, value")
                    print(f"  📝 示例变更: {sample_item['field_cn_name']} = {sample_item['value']}")
                    return True
                else:
                    print(f"  ❌ 结构错误：缺少必要字段，实际字段: {sample_item.keys()}")
                    return False
            else:
                print("  ❌ 格式错误：列表元素不是字典格式")
                return False
                
        elif isinstance(old_values, dict) and isinstance(new_values, dict):
            print("  ❌ 格式错误：使用了完整字典格式而不是精细化格式")
            print(f"  📝 old_values字段数: {len(old_values)}")
            print(f"  📝 new_values字段数: {len(new_values)}")
            return False
        else:
            print(f"  ❌ 格式未知：old_values={type(old_values)}, new_values={type(new_values)}")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {str(e)}")
        return False


def test_audit_log_self_audit_disabled():
    """测试AuditLog模型不会记录自身的审计日志"""
    print("测试 3: AuditLog模型自身审计禁用验证")
    
    try:
        # 记录创建AuditLog前的总数量
        initial_total_count = AuditLog.objects.count()
        print(f"  📊 创建前AuditLog总数量: {initial_total_count}")
        
        # 手动创建一条审计日志
        test_audit_log = AuditLog.objects.create(
            resource_id="test_resource_123",
            action="测试",
            old_values=[{"field_name": "test", "field_cn_name": "测试", "value": "old"}],
            new_values=[{"field_name": "test", "field_cn_name": "测试", "value": "new"}],
            creator_id="test_user",
            creator_name="测试用户"
        )
        
        print(f"  ✅ 创建测试审计日志: {test_audit_log.id}")
        
        # 等待可能的异步任务
        import time
        time.sleep(2)
        
        # 检查创建后的总数量
        final_total_count = AuditLog.objects.count()
        print(f"  📊 创建后AuditLog总数量: {final_total_count}")
        
        # 计算新增数量
        new_count = final_total_count - initial_total_count
        print(f"  📈 新增AuditLog数量: {new_count}")
        
        if new_count == 1:
            print("  ✅ 正确：AuditLog模型没有记录自身的审计日志")
            
            # 清理测试数据
            test_audit_log.delete()
            print("  🧹 清理测试数据完成")
            return True
        else:
            print(f"  ❌ 错误：创建1条AuditLog却新增了{new_count}条记录")
            print("  ❌ 这表明AuditLog模型记录了自身的审计日志，可能导致无限递归")
            
            # 清理测试数据
            test_audit_log.delete()
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {str(e)}")
        return False


def main():
    """运行所有测试"""
    print("开始最终审计日志修复测试...")
    print("=" * 60)
    
    tests = [
        test_single_audit_record,
        test_audit_log_format,
        test_audit_log_self_audit_disabled
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"  ❌ 测试执行异常: {str(e)}")
            print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！审计日志系统修复完成！")
        print("\n修复总结:")
        print("✅ 解决了KeyError('id')错误")
        print("✅ 解决了AuditLog参数错误")
        print("✅ 解决了重复记录问题")
        print("✅ 保持了精细化审计日志格式")
        print("✅ 正确获取字符串ID作为资源标识")
        print("✅ 防止了AuditLog模型的无限递归审计")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
