#!/usr/bin/env python
"""
所有子项目 API 文档访问测试脚本
使用正则表达式动态测试所有子项目的文档访问
"""

import requests
import json
import re
from typing import List, Dict, Tuple

# API 基础URL
BASE_URL = "http://127.0.0.1:8001"

# 子项目配置
SUBPROJECTS = [
    {
        'name': 'oauth',
        'title': 'OAuth2认证系统',
        'description': 'OAuth2认证、用户管理、权限控制',
        'expected_status': 200,  # 应该可以访问
    },
    {
        'name': 'logger',
        'title': '日志管理系统',
        'description': '日志记录、审计追踪、统计分析',
        'expected_status': [200, 404],  # 可能存在也可能不存在
    },
    {
        'name': 'system',
        'title': '系统管理模块',
        'description': '系统配置、健康检查、性能监控',
        'expected_status': [200, 404],
    },
    {
        'name': 'celery',
        'title': 'Celery任务系统',
        'description': '异步任务、定时调度、任务监控',
        'expected_status': [200, 404],
    },
    {
        'name': 'admin',
        'title': '完整管理后台',
        'description': '统一后台、完整功能、管理界面',
        'expected_status': [200, 404],
    },
]

def generate_doc_paths() -> List[Tuple[str, str, str]]:
    """生成所有可能的文档路径"""
    paths = []
    
    # 通用文档路径
    general_paths = [
        ("/docs", "项目文档入口", "general"),
        ("/api/docs", "通用API文档", "general"),
        ("/api/openapi.json", "通用OpenAPI规范", "general"),
        ("/api/redoc", "通用ReDoc文档", "general"),
    ]
    paths.extend(general_paths)
    
    # 子项目文档路径
    for project in SUBPROJECTS:
        project_name = project['name']
        project_title = project['title']
        
        project_paths = [
            (f"/api/{project_name}/docs", f"{project_title} API文档", project_name),
            (f"/api/{project_name}/openapi.json", f"{project_title} OpenAPI规范", project_name),
            (f"/api/{project_name}/redoc", f"{project_title} ReDoc文档", project_name),
        ]
        paths.extend(project_paths)
    
    return paths

def test_regex_patterns():
    """测试正则表达式模式匹配"""
    print("=== 测试正则表达式模式匹配 ===")
    
    # 从配置中获取的正则模式
    patterns = [
        r'^/docs/?$',
        r'^/api/docs/?$',
        r'^/api/(?P<project>\w+)/docs/?$',
        r'^/api/(?P<project>\w+)/openapi\.json/?$',
        r'^/api/(?P<project>\w+)/redoc/?$',
        r'^/api/openapi\.json/?$',
        r'^/api/redoc/?$',
    ]
    
    # 测试路径
    test_paths = [
        "/docs",
        "/api/docs",
        "/api/oauth/docs",
        "/api/logger/docs",
        "/api/system/openapi.json",
        "/api/celery/redoc",
        "/api/unknown/docs",
    ]
    
    print("正则模式匹配测试:")
    for path in test_paths:
        matched = False
        for pattern in patterns:
            if re.match(pattern, path):
                match = re.match(pattern, path)
                groups = match.groupdict() if match else {}
                print(f"  ✅ {path} 匹配 {pattern}")
                if groups:
                    print(f"     提取参数: {groups}")
                matched = True
                break
        
        if not matched:
            print(f"  ❌ {path} 未匹配任何模式")
    
    print()

def test_docs_access_with_categorization():
    """按分类测试文档访问"""
    print("=== 按分类测试文档访问 ===")
    
    doc_paths = generate_doc_paths()
    results = {
        'accessible': [],
        'not_found': [],
        'blocked': [],
        'error': []
    }
    
    for path, description, category in doc_paths:
        try:
            response = requests.get(f"{BASE_URL}{path}", timeout=5)
            status = response.status_code
            
            result_info = {
                'path': path,
                'description': description,
                'category': category,
                'status': status,
                'content_type': response.headers.get('content-type', ''),
            }
            
            if status == 200:
                results['accessible'].append(result_info)
                print(f"  ✅ {path}: {description} (200)")
            elif status == 404:
                results['not_found'].append(result_info)
                print(f"  ❓ {path}: {description} (404 - 未找到)")
            elif status == 401:
                results['blocked'].append(result_info)
                print(f"  🔒 {path}: {description} (401 - 被拦截)")
            elif status == 403:
                results['blocked'].append(result_info)
                print(f"  🚫 {path}: {description} (403 - 权限不足)")
            else:
                results['error'].append(result_info)
                print(f"  ⚠️ {path}: {description} ({status})")
                
        except requests.exceptions.ConnectionError:
            result_info = {
                'path': path,
                'description': description,
                'category': category,
                'status': 'connection_error',
                'content_type': '',
            }
            results['error'].append(result_info)
            print(f"  ❌ {path}: {description} (连接失败)")
        except Exception as e:
            result_info = {
                'path': path,
                'description': description,
                'category': category,
                'status': f'exception: {e}',
                'content_type': '',
            }
            results['error'].append(result_info)
            print(f"  ❌ {path}: {description} (异常: {e})")
    
    return results

def analyze_results_by_project(results):
    """按项目分析结果"""
    print("\n=== 按项目分析结果 ===")
    
    # 按项目分组
    projects = {}
    for category in results.values():
        for item in category:
            project = item['category']
            if project not in projects:
                projects[project] = {
                    'accessible': [],
                    'not_found': [],
                    'blocked': [],
                    'error': []
                }
            
            if item['status'] == 200:
                projects[project]['accessible'].append(item)
            elif item['status'] == 404:
                projects[project]['not_found'].append(item)
            elif item['status'] in [401, 403]:
                projects[project]['blocked'].append(item)
            else:
                projects[project]['error'].append(item)
    
    # 显示每个项目的状态
    for project_name, project_results in projects.items():
        if project_name == 'general':
            print(f"\n📋 通用文档:")
        else:
            project_info = next((p for p in SUBPROJECTS if p['name'] == project_name), None)
            title = project_info['title'] if project_info else project_name
            print(f"\n📦 {title} ({project_name}):")
        
        total = sum(len(v) for v in project_results.values())
        accessible = len(project_results['accessible'])
        blocked = len(project_results['blocked'])
        not_found = len(project_results['not_found'])
        error = len(project_results['error'])
        
        print(f"   总计: {total}, 可访问: {accessible}, 被拦截: {blocked}, 未找到: {not_found}, 错误: {error}")
        
        if project_results['accessible']:
            print("   ✅ 可访问的文档:")
            for item in project_results['accessible']:
                print(f"      - {item['path']}")
        
        if project_results['blocked']:
            print("   🔒 被拦截的文档:")
            for item in project_results['blocked']:
                print(f"      - {item['path']} (需要添加到白名单)")

def provide_regex_solution():
    """提供正则表达式解决方案"""
    print("\n=== 正则表达式解决方案 ===")
    
    print("当前使用的正则表达式模式可以匹配:")
    patterns_explanation = [
        ("r'^/docs/?$'", "项目文档入口"),
        ("r'^/api/docs/?$'", "通用API文档入口"),
        ("r'^/api/(?P<project>\\w+)/docs/?$'", "任意子项目的文档页面"),
        ("r'^/api/(?P<project>\\w+)/openapi\\.json/?$'", "任意子项目的OpenAPI规范"),
        ("r'^/api/(?P<project>\\w+)/redoc/?$'", "任意子项目的ReDoc文档"),
        ("r'^/api/openapi\\.json/?$'", "通用OpenAPI规范"),
        ("r'^/api/redoc/?$'", "通用ReDoc文档"),
    ]
    
    for pattern, explanation in patterns_explanation:
        print(f"  {pattern}")
        print(f"    → {explanation}")
    
    print("\n这些模式可以自动匹配所有符合规范的子项目文档路径，无需手动添加每个项目。")

def main():
    """主测试流程"""
    print("所有子项目 API 文档访问测试")
    print("=" * 60)
    
    # 测试正则表达式模式
    test_regex_patterns()
    
    # 测试文档访问
    results = test_docs_access_with_categorization()
    
    # 按项目分析结果
    analyze_results_by_project(results)
    
    # 提供解决方案
    provide_regex_solution()
    
    # 总结
    total_accessible = len(results['accessible'])
    total_blocked = len(results['blocked'])
    total_not_found = len(results['not_found'])
    total_error = len(results['error'])
    total_tested = total_accessible + total_blocked + total_not_found + total_error
    
    print(f"\n=== 测试总结 ===")
    print(f"总测试路径: {total_tested}")
    print(f"✅ 可访问: {total_accessible}")
    print(f"🔒 被拦截: {total_blocked}")
    print(f"❓ 未找到: {total_not_found}")
    print(f"❌ 错误: {total_error}")
    
    if total_blocked > 0:
        print(f"\n⚠️ 有 {total_blocked} 个文档路径被拦截，请检查白名单配置")
    elif total_accessible > 0:
        print(f"\n🎉 正则表达式白名单配置工作正常！")
    
    print("\n" + "=" * 60)
    print("测试完成！")

if __name__ == "__main__":
    main()
