#!/usr/bin/env python
"""
简化的文件管理功能测试脚本
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_basic_file_manager():
    """测试基础文件管理器"""
    print("\n=== 测试基础文件管理器 ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        # 创建配置
        config = FileUploadConfig(
            max_file_size=1024 * 1024,  # 1MB
            allowed_types=['image', 'document'],
            upload_path='test_uploads'
        )
        
        # 创建文件管理器
        file_manager = FileManager(config)
        print("✅ 基础文件管理器创建成功")
        
        # 创建测试文件
        test_content = b"This is a test file content for file manager testing."
        test_file = SimpleUploadedFile(
            name="test_document.txt",
            content=test_content,
            content_type="text/plain"
        )
        
        # 测试文件上传
        upload_result = file_manager.upload_file(test_file)
        print(f"✅ 文件上传成功: {upload_result.filename}")
        print(f"   文件路径: {upload_result.file_path}")
        print(f"   文件大小: {upload_result.file_size} 字节")
        print(f"   文件哈希: {upload_result.file_hash}")
        
        # 测试获取文件信息
        file_info = file_manager.get_file_info(upload_result.file_path)
        print(f"✅ 获取文件信息成功: 存在={file_info.exists}")
        
        # 测试文件列表
        file_list = file_manager.list_files('test_uploads')
        print(f"✅ 获取文件列表成功: 共{len(file_list)}个文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础文件管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_file_configurations():
    """测试文件配置"""
    print("\n=== 测试文件配置 ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileUploadConfig
        
        # 测试默认配置
        default_config = FileUploadConfig()
        print(f"✅ 默认配置创建成功")
        print(f"   最大文件大小: {default_config.max_file_size / 1024 / 1024:.1f}MB")
        print(f"   上传路径: {default_config.upload_path}")
        print(f"   按日期组织: {default_config.organize_by_date}")
        print(f"   生成唯一名称: {default_config.generate_unique_name}")
        
        # 测试自定义配置
        custom_config = FileUploadConfig(
            max_file_size=5 * 1024 * 1024,
            allowed_types=['image', 'document'],
            upload_path='custom_uploads',
            organize_by_date=False,
            generate_unique_name=False
        )
        print(f"✅ 自定义配置创建成功")
        print(f"   允许的扩展名数量: {len(custom_config.allowed_extensions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_utility_functions():
    """测试工具函数"""
    print("\n=== 测试工具函数 ===")
    
    try:
        from zhi_common.zhi_tools.req_util import get_client_ip, get_user_agent
        
        print("✅ 工具函数导入成功")
        
        # 测试无请求参数的情况
        ip = get_client_ip(None)
        user_agent = get_user_agent(None)
        
        print(f"✅ get_client_ip(None): {ip}")
        print(f"✅ get_user_agent(None): {user_agent}")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_import_export_utils():
    """测试导入导出工具"""
    print("\n=== 测试导入导出工具 ===")
    
    try:
        from zhi_common.zhi_tools.import_export_utils import ImportExportUtils
        
        print("✅ 导入导出工具导入成功")
        
        # 测试支持的格式
        print(f"支持的导出格式: {ImportExportUtils.SUPPORTED_EXPORT_FORMATS}")
        print(f"支持的导入格式: {ImportExportUtils.SUPPORTED_IMPORT_FORMATS}")
        
        # 测试文件格式识别
        test_files = [
            'test.csv',
            'test.xlsx', 
            'test.json',
            'test.txt'
        ]
        
        for filename in test_files:
            format_type = ImportExportUtils.get_file_format(filename)
            print(f"文件 {filename} -> 格式: {format_type}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入导出工具测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_schemas():
    """测试Schema"""
    print("\n=== 测试Schema ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileUploadSchema, FileInfoSchema
        
        print("✅ 基础Schema导入成功")
        
        # 测试Schema实例化
        upload_schema = FileUploadSchema(
            file_id="test123",
            filename="test.txt",
            file_path="uploads/test.txt",
            file_url="/media/uploads/test.txt",
            file_size=1024,
            content_type="text/plain",
            upload_time="2025-01-01T00:00:00",
            file_hash="abc123"
        )
        print(f"✅ FileUploadSchema 实例化成功: {upload_schema.filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ Schema测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始简化文件管理功能测试")
    
    tests = [
        ("文件配置测试", test_file_configurations),
        ("工具函数测试", test_utility_functions),
        ("导入导出工具测试", test_import_export_utils),
        ("Schema测试", test_schemas),
        ("基础文件管理器测试", test_basic_file_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"执行测试: {test_name}")
            print(f"{'='*50}")
            
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"{'='*50}")
    
    if passed >= 4:  # 至少4个测试通过就算成功
        print("🎉 主要功能测试通过！文件管理基础功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
