#!/usr/bin/env python
"""
完整的OAuth2认证流程测试
演示从密码登录到获取access_token的完整过程
"""

import os
import sys
import django
import requests
import json

# 设置 Django 环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()

from zhi_oauth.models import OAuthApplication
from django.contrib.auth import get_user_model

User = get_user_model()

# API 基础URL
BASE_URL = "http://127.0.0.1:8001"

def create_test_user():
    """创建测试用户"""
    print("=== 创建测试用户 ===")
    
    username = "oauth_test_user"
    password = "test_password_123"
    email = "<EMAIL>"
    
    user, created = User.objects.get_or_create(
        username=username,
        defaults={
            'email': email,
            'is_active': True,
        }
    )
    
    if created:
        user.set_password(password)
        user.save()
        print(f"✓ 创建新用户: {username}")
    else:
        # 确保密码正确
        user.set_password(password)
        user.save()
        print(f"- 用户已存在，已重置密码: {username}")
    
    return username, password

def step1_password_login(username, password):
    """步骤1: 密码登录获取session_token"""
    print("\n=== 步骤1: 密码登录 ===")
    
    login_data = {
        "username": username,
        "password": password,
        "remember_me": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/oauth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"登录响应状态码: {response.status_code}")
        result = response.json()
        print(f"登录响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            session_token = result.get('data', {}).get('session_token')
            if session_token:
                print(f"✓ 登录成功，获得session_token: {session_token[:20]}...")
                return session_token
            else:
                print("❌ 登录成功但未获得session_token")
                return None
        else:
            print(f"❌ 登录失败: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ 登录请求异常: {e}")
        return None

def step2_oauth_authorize(session_token, client_id, redirect_uri):
    """步骤2: OAuth2授权获取authorization_code"""
    print("\n=== 步骤2: OAuth2授权 ===")
    
    authorize_data = {
        "session_token": session_token,
        "client_id": client_id,
        "redirect_uri": redirect_uri,
        "response_type": "code",
        "scope": "read write",
        "state": "test_state_123"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/oauth/authorize",
            json=authorize_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"授权响应状态码: {response.status_code}")
        result = response.json()
        print(f"授权响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            auth_code = result.get('data', {}).get('authorization_code')
            if auth_code:
                print(f"✓ 授权成功，获得authorization_code: {auth_code[:20]}...")
                return auth_code
            else:
                print("❌ 授权成功但未获得authorization_code")
                return None
        else:
            print(f"❌ 授权失败: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ 授权请求异常: {e}")
        return None

def step3_token_exchange(auth_code, client_id, client_secret, redirect_uri):
    """步骤3: 交换access_token"""
    print("\n=== 步骤3: 令牌交换 ===")
    
    token_data = {
        "grant_type": "authorization_code",
        "code": auth_code,
        "client_id": client_id,
        "client_secret": client_secret,
        "redirect_uri": redirect_uri
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/oauth/token",
            json=token_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"令牌交换响应状态码: {response.status_code}")
        result = response.json()
        print(f"令牌交换响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('success'):
            access_token = result.get('data', {}).get('access_token')
            if access_token:
                print(f"✓ 令牌交换成功，获得access_token: {access_token[:20]}...")
                return access_token
            else:
                print("❌ 令牌交换成功但未获得access_token")
                return None
        else:
            print(f"❌ 令牌交换失败: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"❌ 令牌交换请求异常: {e}")
        return None

def step4_test_api_access(access_token):
    """步骤4: 测试API访问"""
    print("\n=== 步骤4: 测试API访问 ===")
    
    try:
        # 测试获取用户信息
        response = requests.get(
            f"{BASE_URL}/api/oauth/user/info",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"API访问响应状态码: {response.status_code}")
        result = response.json()
        print(f"API访问响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("✓ API访问成功")
            return True
        else:
            print(f"❌ API访问失败")
            return False
            
    except Exception as e:
        print(f"❌ API访问请求异常: {e}")
        return False

def main():
    """主测试流程"""
    print("OAuth2 完整认证流程测试")
    print("=" * 60)
    
    # 创建测试用户
    username, password = create_test_user()
    
    # 获取OAuth应用信息
    app = OAuthApplication.objects.filter(client_id="test_debug_client").first()
    if not app:
        print("❌ 找不到测试OAuth应用，请先运行 test_oauth_debug.py")
        return
    
    client_id = app.client_id
    client_secret = app.client_secret
    redirect_uri = app.redirect_uris[0]
    
    print(f"\n使用OAuth应用:")
    print(f"  client_id: {client_id}")
    print(f"  redirect_uri: {redirect_uri}")
    
    # 执行完整流程
    session_token = step1_password_login(username, password)
    if not session_token:
        print("\n❌ 流程终止：密码登录失败")
        return
    
    auth_code = step2_oauth_authorize(session_token, client_id, redirect_uri)
    if not auth_code:
        print("\n❌ 流程终止：OAuth2授权失败")
        return
    
    access_token = step3_token_exchange(auth_code, client_id, client_secret, redirect_uri)
    if not access_token:
        print("\n❌ 流程终止：令牌交换失败")
        return
    
    api_success = step4_test_api_access(access_token)
    
    print("\n" + "=" * 60)
    if api_success:
        print("🎉 完整OAuth2认证流程测试成功！")
        print(f"\n可用的凭据:")
        print(f"  用户名: {username}")
        print(f"  密码: {password}")
        print(f"  client_id: {client_id}")
        print(f"  client_secret: {client_secret}")
        print(f"  redirect_uri: {redirect_uri}")
        print(f"  access_token: {access_token}")
    else:
        print("❌ OAuth2认证流程测试失败")

if __name__ == "__main__":
    main()
