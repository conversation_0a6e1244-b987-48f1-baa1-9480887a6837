# API 文档访问问题排查指南

## 🚨 问题描述

在实施全局权限验证中间件后，API 文档无法正常加载，可能出现以下症状：
- 访问 `/api/oauth/docs` 返回 401 未授权错误
- API 文档页面显示空白或加载失败
- OpenAPI 规范文件无法访问

## 🔍 问题原因分析

### 根本原因
全局权限验证中间件拦截了所有请求，包括 API 文档相关的路径，但这些路径没有正确配置在白名单中。

### 具体原因
1. **路径映射错误**: 白名单中配置的是 `/api/docs`，但实际路径是 `/api/oauth/docs`
2. **配置不完整**: 缺少 OpenAPI 规范和 ReDoc 文档的路径
3. **中间件拦截**: 所有非白名单请求都被要求提供 OAuth2 Token

## 🛠️ 解决方案

### 方案一：更新白名单配置（推荐）

#### 1. 立即修复
已经更新了白名单配置，包含以下路径：

```python
# 在 settings/base.py 中
GLOBAL_PERMISSION_CONFIG = {
    'WHITELIST_PATTERNS': [
        # API 文档
        r'^/api/docs/?$',                    # 通用文档入口
        r'^/api/oauth/docs/?$',              # OAuth API 文档
        r'^/api/oauth/openapi\.json/?$',     # OAuth OpenAPI 规范
        r'^/api/oauth/redoc/?$',             # OAuth ReDoc 文档
        r'^/api/openapi\.json/?$',           # 通用 OpenAPI 规范
        r'^/api/redoc/?$',                   # 通用 ReDoc 文档
        r'^/docs/?$',                        # 项目文档入口
        # ... 其他配置
    ],
}
```

#### 2. 重启服务器
```bash
# 重启 Django 服务器使配置生效
python manage.py runserver
```

### 方案二：使用修复脚本

#### 1. 检查当前配置
```bash
cd backend
python fix_api_docs_access.py check
```

#### 2. 自动修复
```bash
python fix_api_docs_access.py fix
```

#### 3. 重启服务器
```bash
python manage.py runserver
```

### 方案三：临时禁用中间件

如果需要紧急访问文档：

#### 1. 临时禁用
```bash
python fix_api_docs_access.py disable
python manage.py runserver
```

#### 2. 访问文档后重新启用
```bash
python fix_api_docs_access.py enable
python manage.py runserver
```

## 🧪 验证修复效果

### 1. 运行测试脚本
```bash
cd backend
python test_api_docs_access.py
```

### 2. 手动验证
访问以下 URL 确认可以正常加载：

- **项目文档入口**: http://127.0.0.1:8001/docs
- **OAuth API 文档**: http://127.0.0.1:8001/api/oauth/docs
- **OpenAPI 规范**: http://127.0.0.1:8001/api/oauth/openapi.json

### 3. 预期结果
```bash
=== 测试 API 文档访问 ===
  /docs: ✅ 可访问 (HTML文档)
  /api/docs: ✅ 可访问 (HTML文档)
  /api/oauth/docs: ✅ 可访问 (HTML文档)
  /api/oauth/openapi.json: ✅ 可访问 (JSON规范)
  /api/oauth/redoc: ✅ 可访问 (HTML文档)
```

## 🔧 深度排查

### 1. 检查中间件日志
```python
# 启用详细日志
import logging
logging.getLogger('zhi_oauth').setLevel(logging.DEBUG)
```

### 2. 检查白名单匹配
```python
# 在 Django shell 中测试
from zhi_oauth.middleware.permission_config import permission_config_manager
import re

patterns = permission_config_manager.get_whitelist_patterns()
test_path = "/api/oauth/docs"

for pattern in patterns:
    if re.match(pattern, test_path):
        print(f"匹配成功: {pattern}")
        break
else:
    print("未找到匹配的白名单模式")
```

### 3. 检查 URL 路由
```bash
# 查看所有 URL 路由
python manage.py show_urls | grep docs
```

## 📋 预防措施

### 1. 文档化 API 路径
在添加新的 API 文档时，确保更新白名单配置：

```python
# 新增 API 文档路径模板
r'^/api/{app_name}/docs/?$',
r'^/api/{app_name}/openapi\.json/?$',
r'^/api/{app_name}/redoc/?$',
```

### 2. 自动化测试
将文档访问测试加入 CI/CD 流程：

```bash
# 在部署脚本中添加
python test_api_docs_access.py
if [ $? -ne 0 ]; then
    echo "API 文档访问测试失败"
    exit 1
fi
```

### 3. 监控告警
设置监控告警，当文档访问返回 401 时及时通知：

```python
# 监控脚本示例
def check_docs_access():
    response = requests.get('/api/oauth/docs')
    if response.status_code == 401:
        send_alert("API 文档访问被拦截")
```

## 🎯 最佳实践

### 1. 白名单配置原则
- **明确性**: 使用精确的正则表达式
- **完整性**: 包含所有相关的文档路径
- **安全性**: 避免过于宽泛的匹配规则

### 2. 开发流程
1. 添加新 API 时，同时更新白名单
2. 本地测试文档访问
3. 提交代码前运行测试脚本

### 3. 部署流程
1. 部署后立即验证文档访问
2. 检查监控告警
3. 必要时回滚配置

## 📚 相关文档

- [全局权限验证系统文档](../zhi_oauth/GLOBAL_PERMISSION_SYSTEM.md)
- [权限配置管理文档](../zhi_oauth/middleware/permission_config.py)
- [全局权限认证实施指南](global_auth_implementation_guide.md)

## 🆘 紧急联系

如果问题仍然存在，请：

1. **收集信息**:
   - 错误日志
   - 访问的具体 URL
   - 浏览器开发者工具的网络请求信息

2. **临时解决**:
   ```bash
   # 紧急情况下临时禁用中间件
   python fix_api_docs_access.py disable
   ```

3. **寻求帮助**:
   - 查看系统日志
   - 运行诊断脚本
   - 联系技术支持团队

---

**文档版本**: v1.0  
**创建日期**: 2025-07-24  
**适用场景**: API 文档访问问题  
**紧急程度**: 🔴 高优先级
