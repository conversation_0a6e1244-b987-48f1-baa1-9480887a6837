# 全局权限认证实施指南

## 📋 快速开始

本指南帮助开发者快速理解和使用已实施的全局权限认证系统。

## 🎯 系统概述

### 核心特性
- ✅ **全局保护**: 除白名单外，所有请求都需要 OAuth2 Token 认证
- ✅ **统一认证**: 避免多种认证方式混乱
- ✅ **企业级功能**: 审计日志、性能监控、权限缓存
- ✅ **灵活配置**: 支持正则表达式和动态权限映射

### 架构图
```
请求 → 中间件 → 白名单检查 → Token验证 → 权限检查 → 审计日志 → 响应
         ↓
    GlobalPermissionMiddleware
         ↓
    PermissionConfigManager
```

## ⚙️ 配置说明

### 1. 中间件配置

在 `settings/base.py` 中已启用：
```python
MIDDLEWARE = [
    # ... 其他中间件
    'zhi_oauth.middleware.global_permission_middleware.GlobalPermissionMiddleware',
]
```

### 2. 权限配置

```python
GLOBAL_PERMISSION_CONFIG = {
    # 白名单 - 不需要认证的接口
    'WHITELIST_PATTERNS': [
        r'^/api/oauth/login/?$',        # 登录接口
        r'^/api/oauth/authorize/?$',    # OAuth2授权
        r'^/api/oauth/token/?$',        # 令牌交换
        r'^/api/docs/?$',               # API文档
        r'^/static/.*',                 # 静态文件
        r'^/admin/.*',                  # 管理后台
    ],
    
    # 权限检查配置
    'PERMISSION_CHECK': {
        'ENABLE_GLOBAL_CHECK': True,           # 启用全局权限检查
        'FALLBACK_TO_CONTROLLER': True,        # 回退到控制器层面
    },
    
    # 超级管理员配置
    'SUPERUSER_CONFIG': {
        'BYPASS_PERMISSION_CHECK': True,       # 超级管理员绕过检查
        'LOG_SUPERUSER_ACCESS': True,          # 记录超级管理员访问
    },
    
    # 审计配置
    'AUDIT_CONFIG': {
        'ENABLE_ACCESS_LOG': True,             # 启用访问日志
        'ENABLE_PERMISSION_LOG': DEBUG,        # 权限检查日志
        'LOG_FAILED_ATTEMPTS': True,           # 记录失败尝试
    },
}
```

## 🔐 使用方式

### 1. API 调用示例

#### 白名单 API（无需 Token）
```bash
# 用户登录
curl -X POST http://127.0.0.1:8001/api/oauth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

#### 受保护 API（需要 Token）
```bash
# 获取产品列表
curl -X GET http://127.0.0.1:8001/api/v1/example-products/list_pagination \
  -H "Authorization: Bearer oauth2_access-xxx..."
```

### 2. 完整认证流程

```python
# 1. 密码登录获取 session_token
login_response = requests.post('/api/oauth/login', json={
    'username': 'user',
    'password': 'pass'
})
session_token = login_response.json()['data']['session_token']

# 2. OAuth2 授权获取 authorization_code
auth_response = requests.post('/api/oauth/authorize', json={
    'session_token': session_token,
    'client_id': 'your_client_id',
    'redirect_uri': 'http://localhost:3000/callback',
    'response_type': 'code'
})
auth_code = auth_response.json()['data']['authorization_code']

# 3. 令牌交换获取 access_token
token_response = requests.post('/api/oauth/token', json={
    'grant_type': 'authorization_code',
    'code': auth_code,
    'client_id': 'your_client_id',
    'client_secret': 'your_client_secret',
    'redirect_uri': 'http://localhost:3000/callback'
})
access_token = token_response.json()['data']['access_token']

# 4. 使用 access_token 访问受保护 API
api_response = requests.get('/api/v1/example-products/list_pagination', 
    headers={'Authorization': f'Bearer {access_token}'}
)
```

## 🧪 测试验证

### 运行测试脚本
```bash
cd backend
python test_global_permission.py
```

### 预期输出
```bash
=== 测试白名单 API ===
  /api/oauth/login: 状态码 405 ✓
  /api/docs: 状态码 200 ✓

=== 测试受保护的 API（无 Token）===
  /api/v1/example-products/list_pagination: 正确返回401未授权 ✓
  /api/oauth/user/info: 正确返回401未授权 ✓

=== 获取有效的 access_token ===
  ✓ 登录成功，获得 session_token
  ✓ 授权成功，获得 authorization_code
  ✓ 令牌交换成功，获得 access_token

🎉 完整OAuth2认证流程测试成功！
```

## 🔧 自定义配置

### 1. 添加新的白名单规则

```python
# 在 settings.py 中添加
GLOBAL_PERMISSION_CONFIG['WHITELIST_PATTERNS'].extend([
    r'^/api/public/.*',      # 公开 API
    r'^/api/webhook/.*',     # Webhook 接口
])
```

### 2. 配置权限映射

```python
GLOBAL_PERMISSION_CONFIG['PERMISSION_MAPPING'] = {
    # 静态权限映射
    r'^/api/v1/products/list/?$': 'product.view',
    
    # 动态权限映射
    r'^/api/v1/(?P<resource>\w+)/.*': '{resource}.{method}',
    
    # 方法特定映射
    r'^/api/v1/products/(?P<id>[^/]+)/?$': {
        'GET': 'product.view',
        'PUT': 'product.update',
        'DELETE': 'product.delete',
    },
}
```

### 3. 控制器层面权限控制

```python
from zhi_common.zhi_auth.unified_permission_manager import require_permission

@api_controller("products")
class ProductController:
    
    @require_permission('product.view')
    @http_get("/list")
    def list_products(self, request):
        # 控制器逻辑
        pass
    
    @require_permission('product.create')
    @http_post("/create")
    def create_product(self, request, data: ProductCreateSchema):
        # 控制器逻辑
        pass
```

## 📊 监控和调试

### 1. 查看日志

```bash
# 查看权限验证日志
tail -f logs/oauth2.log | grep "权限检查"

# 查看访问日志
tail -f logs/api_access.log
```

### 2. 调试权限问题

```python
# 检查权限配置
from zhi_oauth.middleware.permission_config import permission_config_manager
print(permission_config_manager.get_whitelist_patterns())

# 测试权限检查
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
result = unified_permission_manager.check_permission(user, 'product.view')
print(f"权限检查结果: {result}")
```

### 3. 性能监控

```python
# 启用详细日志
import logging
logging.getLogger('zhi_oauth').setLevel(logging.DEBUG)

# 监控中间件性能
# 查看响应时间和资源使用情况
```

## ⚠️ 常见问题

### Q1: API 返回 401 错误
**原因**: 缺少或无效的 access_token  
**解决**: 检查 Authorization 头部格式，确保 token 有效且未过期

### Q2: 白名单 API 仍然要求认证
**原因**: 正则表达式匹配问题  
**解决**: 检查白名单配置中的正则表达式是否正确

### Q3: 权限检查失败
**原因**: 用户缺少相应权限或权限配置错误  
**解决**: 检查用户权限配置和权限映射规则

### Q4: 性能影响较大
**原因**: 未启用缓存或数据库查询过多  
**解决**: 启用 Redis 缓存，优化数据库查询

## 🚀 最佳实践

### 1. 安全配置
- 定期更新白名单，移除不必要的端点
- 启用详细的审计日志
- 配置合理的 token 过期时间

### 2. 性能优化
- 启用权限结果缓存
- 使用异步处理审计日志
- 优化数据库查询和索引

### 3. 开发规范
- 新增 API 时考虑权限配置
- 使用测试脚本验证权限配置
- 定期审查权限映射规则

## 📚 相关文档

- [全局权限认证方案对比分析](global_auth_solution_comparison.md)
- [架构决策记录](architecture_decision_record_auth.md)
- [全局权限验证系统文档](../zhi_oauth/GLOBAL_PERMISSION_SYSTEM.md)
- [OAuth2 认证系统迁移指南](../zhi_oauth/MIGRATION_GUIDE.md)

## 📞 技术支持

如遇到问题，请按以下顺序排查：
1. 查看本文档的常见问题部分
2. 运行测试脚本验证系统状态
3. 检查日志文件获取详细错误信息
4. 参考相关技术文档

---

**文档版本**: v1.0  
**最后更新**: 2025-07-24  
**适用版本**: ZhiAdmin v1.0+
