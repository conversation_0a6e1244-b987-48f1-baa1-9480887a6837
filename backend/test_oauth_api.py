#!/usr/bin/env python
"""
测试OAuth API接口
"""
import requests
import json
import sys
from datetime import datetime


class OAuthAPITester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def test_login_api(self):
        """测试登录API"""
        print("🔄 测试登录API...")
        
        url = f"{self.base_url}/api/oauth/login"
        data = {
            "username": "admin",
            "password": "admin123",
            "remember_me": False
        }
        
        try:
            response = self.session.post(url, json=data)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("响应格式:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                # 检查统一响应格式
                if self.check_unified_response_format(result):
                    print("✅ 登录API响应格式正确")
                    return result
                else:
                    print("❌ 登录API响应格式不正确")
                    return None
            else:
                print(f"❌ 登录失败: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ 登录API测试异常: {e}")
            return None
    
    def test_oauth_applications_api(self):
        """测试OAuth应用列表API"""
        print("\n🔄 测试OAuth应用列表API...")

        url = f"{self.base_url}/api/oauth/applications"
        
        try:
            response = self.session.get(url)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("响应格式:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                
                if self.check_unified_response_format(result):
                    print("✅ 应用列表API响应格式正确")
                    return result
                else:
                    print("❌ 应用列表API响应格式不正确")
                    return None
            else:
                print(f"❌ 获取应用列表失败: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ 应用列表API测试异常: {e}")
            return None
    
    def check_unified_response_format(self, response_data):
        """检查统一响应格式"""
        required_fields = ['code', 'message', 'success', 'trace_id', 'timestamp']
        
        for field in required_fields:
            if field not in response_data:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        # 检查字段类型
        if not isinstance(response_data['code'], int):
            print("❌ code字段应为整数")
            return False
            
        if not isinstance(response_data['success'], bool):
            print("❌ success字段应为布尔值")
            return False
            
        if not isinstance(response_data['message'], str):
            print("❌ message字段应为字符串")
            return False
        
        # 检查是否有data字段
        if 'data' not in response_data:
            print("⚠️  建议包含data字段")
        
        return True
    
    def test_api_docs(self):
        """测试API文档是否可访问"""
        print("\n🔄 测试API文档...")
        
        url = f"{self.base_url}/api/oauth/docs"
        
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                print("✅ API文档可正常访问")
                return True
            else:
                print(f"❌ API文档访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API文档测试异常: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始OAuth API测试...")
        print(f"测试服务器: {self.base_url}")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        results = []
        
        # 测试API文档
        results.append(self.test_api_docs())
        
        # 测试登录API
        login_result = self.test_login_api()
        results.append(login_result is not None)
        
        # 测试应用列表API
        apps_result = self.test_oauth_applications_api()
        results.append(apps_result is not None)
        
        # 总结
        print("\n" + "=" * 50)
        success_count = sum(results)
        total_count = len(results)
        
        print(f"📊 测试结果: {success_count}/{total_count} 项测试通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过！OAuth API响应格式统一化成功！")
        else:
            print("⚠️  部分测试失败，请检查服务器状态")
        
        return success_count == total_count


def main():
    """主函数"""
    # 检查服务器是否运行
    base_url = "http://localhost:8001"
    
    print("检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/api/oauth/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ 服务器未正常运行，请先启动服务器:")
            print("   python manage.py runserver 0.0.0.0:8001")
            return False
    except requests.exceptions.RequestException:
        print(f"❌ 无法连接到服务器 {base_url}")
        print("   请确保服务器正在运行:")
        print("   python manage.py runserver 0.0.0.0:8001")
        return False
    
    # 运行测试
    tester = OAuthAPITester(base_url)
    return tester.run_all_tests()


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
