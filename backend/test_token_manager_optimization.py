#!/usr/bin/env python
"""
Token Manager 优化测试脚本
测试 application 和 client_id 参数的使用情况
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_oauth.utils.token_manager import oauth2_token_manager


class MockApplication:
    """模拟OAuth应用实例"""
    def __init__(self, client_id="test_client_123", name="测试应用"):
        self.client_id = client_id
        self.name = name
        self.access_token_lifetime = 7200  # 2小时
        self.refresh_token_lifetime = 3600 * 24 * 14  # 14天
        
        # 模拟令牌格式配置
        self.token_format_config = {
            'custom_prefix': True,
            'client_credentials_format': 'oauth2_client_{client_id}_{app_name}'
        }


def test_token_generation_without_application():
    """测试不带应用参数的令牌生成"""
    print("🧪 测试不带应用参数的令牌生成")
    print("-" * 50)
    
    # 生成各种类型的令牌
    access_token = oauth2_token_manager.generate_access_token()
    refresh_token = oauth2_token_manager.generate_refresh_token()
    auth_code = oauth2_token_manager.generate_authorization_code()
    
    print(f"访问令牌: {access_token}")
    print(f"刷新令牌: {refresh_token}")
    print(f"授权码: {auth_code}")
    
    # 验证令牌格式
    assert access_token.startswith('oauth2_access-'), "访问令牌格式错误"
    assert refresh_token.startswith('oauth2_refresh-'), "刷新令牌格式错误"
    assert auth_code.startswith('oauth2_code-'), "授权码格式错误"
    
    print("✅ 不带应用参数的令牌生成测试通过")
    return True


def test_token_generation_with_application():
    """测试带应用参数的令牌生成"""
    print("\n🧪 测试带应用参数的令牌生成")
    print("-" * 50)
    
    # 创建模拟应用
    app = MockApplication()
    
    # 生成各种类型的令牌
    access_token = oauth2_token_manager.generate_access_token(app)
    refresh_token = oauth2_token_manager.generate_refresh_token(app)
    auth_code = oauth2_token_manager.generate_authorization_code(app)
    
    print(f"访问令牌: {access_token}")
    print(f"刷新令牌: {refresh_token}")
    print(f"授权码: {auth_code}")
    
    # 验证令牌格式（应该包含应用标识）
    expected_prefix = f"oauth2_access_{app.client_id[:8]}-"
    assert access_token.startswith(expected_prefix), f"访问令牌格式错误，期望前缀: {expected_prefix}"
    
    expected_prefix = f"oauth2_refresh_{app.client_id[:8]}-"
    assert refresh_token.startswith(expected_prefix), f"刷新令牌格式错误，期望前缀: {expected_prefix}"
    
    expected_prefix = f"oauth2_code_{app.client_id[:8]}-"
    assert auth_code.startswith(expected_prefix), f"授权码格式错误，期望前缀: {expected_prefix}"
    
    print("✅ 带应用参数的令牌生成测试通过")
    return True


def test_client_credentials_token():
    """测试客户端凭证令牌生成"""
    print("\n🧪 测试客户端凭证令牌生成")
    print("-" * 50)
    
    client_id = "test_client_456"
    app = MockApplication(client_id=client_id, name="客户端应用")
    
    # 测试只有client_id的情况
    token1 = oauth2_token_manager.generate_client_credentials_token(client_id)
    print(f"仅client_id令牌: {token1}")
    
    # 测试有application的情况
    token2 = oauth2_token_manager.generate_client_credentials_token(client_id, app)
    print(f"带application令牌: {token2}")
    
    # 验证令牌格式
    expected_prefix1 = f"oauth2_client_{client_id[:8]}-"
    assert token1.startswith(expected_prefix1), f"客户端令牌格式错误，期望前缀: {expected_prefix1}"
    
    # 带application的令牌应该使用自定义格式
    expected_prefix2 = f"oauth2_client_{client_id[:8]}_{app.name[:4]}-"
    assert token2.startswith(expected_prefix2), f"自定义客户端令牌格式错误，期望前缀: {expected_prefix2}"
    
    print("✅ 客户端凭证令牌生成测试通过")
    return True


def test_token_expires_at():
    """测试令牌过期时间计算"""
    print("\n🧪 测试令牌过期时间计算")
    print("-" * 50)
    
    app = MockApplication()
    
    # 测试访问令牌过期时间
    access_expires = oauth2_token_manager.get_token_expires_at('access_token', app)
    print(f"访问令牌过期时间: {access_expires}")
    
    # 测试刷新令牌过期时间
    refresh_expires = oauth2_token_manager.get_token_expires_at('refresh_token', app)
    print(f"刷新令牌过期时间: {refresh_expires}")
    
    # 验证过期时间是否使用了应用配置
    from django.utils import timezone
    from datetime import timedelta
    
    expected_access_expires = timezone.now() + timedelta(seconds=app.access_token_lifetime)
    expected_refresh_expires = timezone.now() + timedelta(seconds=app.refresh_token_lifetime)
    
    # 允许1秒的误差
    assert abs((access_expires - expected_access_expires).total_seconds()) < 1, "访问令牌过期时间计算错误"
    assert abs((refresh_expires - expected_refresh_expires).total_seconds()) < 1, "刷新令牌过期时间计算错误"
    
    print("✅ 令牌过期时间计算测试通过")
    return True


def test_token_info_extraction():
    """测试令牌信息提取"""
    print("\n🧪 测试令牌信息提取")
    print("-" * 50)
    
    app = MockApplication()
    
    # 生成令牌
    access_token = oauth2_token_manager.generate_access_token(app)
    
    # 提取令牌信息
    token_info = oauth2_token_manager.extract_token_info(access_token)
    print(f"令牌信息: {token_info}")
    
    # 验证信息提取
    assert token_info['type'] == 'access_token', "令牌类型识别错误"
    assert token_info['prefix'].startswith('oauth2_access'), "令牌前缀识别错误"
    assert len(token_info['token_part']) > 0, "令牌主体部分为空"
    
    print("✅ 令牌信息提取测试通过")
    return True


def test_pkce_functionality():
    """测试PKCE功能"""
    print("\n🧪 测试PKCE功能")
    print("-" * 50)
    
    # 生成代码验证器
    code_verifier = oauth2_token_manager.generate_code_verifier()
    print(f"代码验证器: {code_verifier}")
    
    # 生成代码挑战
    code_challenge = oauth2_token_manager.generate_pkce_challenge(code_verifier)
    print(f"代码挑战: {code_challenge}")
    
    # 验证PKCE
    is_valid = oauth2_token_manager.verify_pkce_challenge(code_verifier, code_challenge)
    print(f"PKCE验证结果: {is_valid}")
    
    # 验证功能
    assert len(code_verifier) >= 43, "代码验证器长度不足"
    assert len(code_challenge) > 0, "代码挑战为空"
    assert is_valid, "PKCE验证失败"
    
    print("✅ PKCE功能测试通过")
    return True


def test_device_code_generation():
    """测试设备码生成"""
    print("\n🧪 测试设备码生成")
    print("-" * 50)
    
    # 生成设备码
    device_codes = oauth2_token_manager.generate_device_code()
    print(f"设备码: {device_codes}")
    
    # 验证设备码格式
    assert 'device_code' in device_codes, "缺少设备码"
    assert 'user_code' in device_codes, "缺少用户码"
    assert device_codes['device_code'].startswith('oauth2_device-'), "设备码格式错误"
    assert '-' in device_codes['user_code'], "用户码格式错误"
    
    print("✅ 设备码生成测试通过")
    return True


def main():
    """主测试函数"""
    print("🚀 Token Manager 优化测试")
    print("=" * 60)
    
    tests = [
        test_token_generation_without_application,
        test_token_generation_with_application,
        test_client_credentials_token,
        test_token_expires_at,
        test_token_info_extraction,
        test_pkce_functionality,
        test_device_code_generation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 测试总结")
    print("=" * 60)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Token Manager优化成功！")
        
        print("\n✅ 优化成果:")
        print("• application参数已被充分利用")
        print("• client_id参数已被正确使用")
        print("• 支持应用特定的令牌格式配置")
        print("• 支持自定义令牌前缀")
        print("• 支持应用特定的过期时间配置")
        print("• 新增令牌统计和清理功能")
        
        print("\n💡 新增功能:")
        print("1. 应用特定的令牌前缀")
        print("2. 客户端凭证令牌优化")
        print("3. 令牌统计功能")
        print("4. 过期令牌清理功能")
        
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，请检查相关实现")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
