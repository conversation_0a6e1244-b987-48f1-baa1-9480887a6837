# ZhiCelery 异步任务管理文档

## 📋 概述

ZhiCelery是ZhiAdmin系统的异步任务管理模块，统一管理所有应用的异步任务，提供任务调度、监控、管理等功能。

## 🏗️ 架构设计

### 任务组织结构

```
zhi_celery/
├── tasks/                  # 任务模块
│   ├── __init__.py        # 任务导入
│   ├── logger_tasks.py    # 日志相关任务
│   ├── oauth_tasks.py     # OAuth相关任务
│   └── system_tasks.py    # 系统维护任务
├── controllers/           # API控制器
│   └── task_controller.py # 任务管理API
├── schedules.py          # 统一调度配置
├── celery_app.py         # Celery应用配置
└── api.py                # API配置
```

### 队列设计

| 队列名称 | 优先级 | 用途 | 示例任务 |
|---------|--------|------|----------|
| urgent | 10 | 紧急任务 | 系统告警、安全事件 |
| oauth | 7 | OAuth相关 | 令牌清理、用户统计 |
| logger | 6 | 日志相关 | 日志清理、报告生成 |
| default | 5 | 默认任务 | 通用业务任务 |

## 📝 任务详解

### OAuth相关任务

#### cleanup_expired_tokens_task
**功能**: 清理过期的OAuth2令牌和授权码
**调度**: 每天凌晨2点
**参数**:
- `days` (int): 清理多少天前过期的令牌，默认7天
- `audit_days` (int): 清理多少天前的审计日志，默认90天

```python
# 手动执行
from zhi_celery.tasks.oauth_tasks import cleanup_expired_tokens_task
result = cleanup_expired_tokens_task.delay(days=7, audit_days=90)
```

#### generate_oauth_statistics_task
**功能**: 生成OAuth2统计报告
**调度**: 每周一凌晨3点
**参数**:
- `days` (int): 统计天数，默认30天

```python
# 手动执行
from zhi_celery.tasks.oauth_tasks import generate_oauth_statistics_task
result = generate_oauth_statistics_task.delay(days=30)
```

#### revoke_expired_tokens_task
**功能**: 撤销过期但未标记为撤销的令牌
**调度**: 每小时整点
**参数**: 无

```python
# 手动执行
from zhi_celery.tasks.oauth_tasks import revoke_expired_tokens_task
result = revoke_expired_tokens_task.delay()
```

### Logger相关任务

#### cleanup_old_logs_task
**功能**: 清理旧的日志记录
**调度**: 每天凌晨1点
**参数**:
- `days` (int): 清理多少天前的日志，默认30天

```python
# 手动执行
from zhi_celery.tasks.logger_tasks import cleanup_old_logs_task
result = cleanup_old_logs_task.delay(days=30)
```

#### generate_log_statistics_task
**功能**: 生成日志统计报告
**调度**: 每天凌晨4点
**参数**:
- `days` (int): 统计天数，默认7天

```python
# 手动执行
from zhi_celery.tasks.logger_tasks import generate_log_statistics_task
result = generate_log_statistics_task.delay(days=7)
```

#### log_health_check_task
**功能**: 日志系统健康检查
**调度**: 每10分钟
**参数**: 无

```python
# 手动执行
from zhi_celery.tasks.logger_tasks import log_health_check_task
result = log_health_check_task.delay()
```

## 🚀 启动和管理

### 启动Celery服务

```bash
# 启动Worker (处理任务)
python zhi_scripts/start_celery.py --worker

# 启动Beat (任务调度器)
python zhi_scripts/start_celery.py --beat

# 启动所有服务
python zhi_scripts/start_celery.py --all

# 启动Flower监控
python zhi_scripts/start_celery.py --flower
```

### 配置选项

```bash
# 自定义配置启动
python zhi_scripts/start_celery.py --worker \
  --settings=application.settings.celery_enabled \
  --loglevel=debug \
  --concurrency=4 \
  --queues=oauth,logger,default
```

## 📊 监控和管理

### API接口

#### 获取任务列表
```http
GET /api/celery/tasks/list
```

#### 执行任务
```http
POST /api/celery/tasks/execute/{task_name}
Content-Type: application/json

{
  "args": [7, 90],
  "kwargs": {}
}
```

#### 查询任务状态
```http
GET /api/celery/tasks/status/{task_id}
```

#### 获取任务统计
```http
GET /api/celery/tasks/stats
```

#### 撤销任务
```http
POST /api/celery/tasks/revoke/{task_id}
Content-Type: application/json

{
  "terminate": false
}
```

### Flower监控界面

访问 http://localhost:5555 查看：
- 实时任务状态
- Worker状态
- 任务执行历史
- 队列状态
- 任务执行图表

## 🔧 开发指南

### 添加新任务

1. **创建任务文件** (如果需要新模块)
```python
# zhi_celery/tasks/custom_tasks.py
from celery import shared_task
from zhi_common.zhi_logger import get_logger

logger = get_logger(module_name="custom_tasks")

@shared_task(bind=True, queue='custom')
def my_custom_task(self, param1, param2):
    """自定义任务"""
    try:
        # 任务逻辑
        logger.info(f"执行自定义任务: {param1}, {param2}")
        return {'success': True, 'result': 'completed'}
    except Exception as exc:
        logger.error(f"任务执行失败: {exc}")
        raise self.retry(countdown=60)
```

2. **添加调度配置**
```python
# zhi_celery/schedules.py
CELERY_BEAT_SCHEDULE.update({
    'my-custom-task': {
        'task': 'zhi_celery.tasks.custom_tasks.my_custom_task',
        'schedule': crontab(minute=0, hour='*/6'),  # 每6小时执行
        'args': ('param1', 'param2'),
        'options': {'queue': 'custom'}
    }
})
```

3. **更新导入**
```python
# zhi_celery/__init__.py
from .tasks.custom_tasks import my_custom_task

__all__.append('my_custom_task')
```

### 任务最佳实践

#### 错误处理
```python
@shared_task(bind=True, max_retries=3)
def robust_task(self, data):
    try:
        # 任务逻辑
        process_data(data)
    except Exception as exc:
        logger.error(f"任务失败: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        raise
```

#### 进度跟踪
```python
@shared_task(bind=True)
def long_running_task(self, items):
    total = len(items)
    for i, item in enumerate(items):
        # 处理项目
        process_item(item)
        
        # 更新进度
        self.update_state(
            state='PROGRESS',
            meta={'current': i + 1, 'total': total}
        )
    
    return {'status': 'completed', 'total': total}
```

#### 批量处理
```python
@shared_task
def batch_process_task(batch_size=100):
    """批量处理任务"""
    items = get_items_to_process()
    
    # 分批处理
    for i in range(0, len(items), batch_size):
        batch = items[i:i + batch_size]
        process_batch.delay(batch)
```

## 🔍 故障排除

### 常见问题

1. **Worker无法启动**
   - 检查Redis连接
   - 确认虚拟环境激活
   - 检查Django设置模块

2. **任务执行失败**
   - 查看Worker日志
   - 检查任务参数
   - 确认数据库连接

3. **Beat调度不工作**
   - 检查数据库迁移
   - 确认Beat进程运行
   - 查看调度配置

### 调试命令

```bash
# 检查注册的任务
python -c "from zhi_celery.celery_app import app; print(list(app.tasks.keys()))"

# 检查Worker状态
python -c "from zhi_celery.celery_app import app; print(app.control.inspect().stats())"

# 手动执行任务
python manage.py shell
>>> from zhi_celery.tasks.oauth_tasks import cleanup_expired_tokens_task
>>> result = cleanup_expired_tokens_task.delay()
>>> print(result.get())
```

## 📈 性能优化

### Worker配置
- 根据CPU核心数调整并发数
- 使用合适的进程池类型
- 设置任务超时时间
- 配置内存限制

### 队列优化
- 按任务类型分队列
- 设置队列优先级
- 使用专用Worker处理特定队列

### 监控指标
- 任务执行时间
- 队列长度
- Worker负载
- 错误率统计

---

**ZhiCelery** - 让异步任务管理更简单 🚀
