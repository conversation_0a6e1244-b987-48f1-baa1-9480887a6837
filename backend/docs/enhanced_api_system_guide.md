# 增强型API基类系统使用指南

## 概述

基于旧项目经验完善的增强型API基类系统，提供完整的OAuth2认证、权限管理、自动CRUD生成、缓存优化等功能。

## 系统架构

### 核心组件

1. **增强型OAuth2认证** (`zhi_oauth/auth/enhanced_global_oauth2.py`)
   - Token缓存机制
   - 数据权限服务集成
   - 用户上下文自动设置

2. **增强型API暴露装饰器** (`zhi_common/zhi_services/enhanced_api_expose.py`)
   - 自动CRUD生成
   - 权限检查装饰器
   - API版本管理

3. **增强型模型服务** (`zhi_common/zhi_services/enhanced_model_service.py`)
   - 标准化响应格式
   - 权限管理集成
   - 审计日志记录

## 快速开始

### 1. 创建模型和Schema

```python
# models.py
from django.db import models

class Product(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    creator_id = models.CharField(max_length=36)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_deleted = models.BooleanField(default=False)

# schemas.py
from ninja import Schema
from typing import Optional
from datetime import datetime

class ProductCreateSchema(Schema):
    name: str
    description: Optional[str] = None

class ProductUpdateSchema(Schema):
    name: Optional[str] = None
    description: Optional[str] = None

class ProductResponseSchema(Schema):
    id: str
    name: str
    description: Optional[str] = None
    created_at: Optional[datetime] = None
```

### 2. 创建增强型控制器

```python
from zhi_common.zhi_services.enhanced_api_expose import enhanced_auto_crud_api
from zhi_common.zhi_services.enhanced_model_service import EnhancedModelService

@enhanced_auto_crud_api(
    model_class=Product,
    prefix="products",
    tags=["产品管理"],
    schema_in=ProductCreateSchema,
    schema_out=ProductResponseSchema,
    permission_config={
        'view': 'product.view',
        'create': 'product.create',
        'update': 'product.update',
        'delete': 'product.delete',
    }
)
class ProductController(EnhancedModelService):
    """产品控制器 - 自动生成CRUD API"""
    pass
```

### 3. 添加自定义端点

```python
from zhi_common.zhi_services.enhanced_api_expose import enhanced_api_route

@enhanced_auto_crud_api(...)
class ProductController(EnhancedModelService):
    
    @enhanced_api_route(
        method="GET",
        path="/statistics",
        summary="获取产品统计",
        permission_code="product.statistics"
    )
    def get_statistics(self, request):
        """获取统计信息 🔒"""
        user = self._get_user_from_request(request)
        # 业务逻辑...
        return create_response(data=stats_data)
```

## 自动生成的API端点

使用 `@enhanced_auto_crud_api` 装饰器会自动生成以下端点：

| 方法 | 路径 | 功能 | 权限 |
|------|------|------|------|
| 🔒 GET | `/v1/products/` | 获取产品列表 | `product.view` |
| 🔒 GET | `/v1/products/{id}` | 获取产品详情 | `product.view` |
| 🔒 POST | `/v1/products/` | 创建产品 | `product.create` |
| 🔒 PUT | `/v1/products/{id}` | 更新产品 | `product.update` |
| 🔒 DELETE | `/v1/products/{id}` | 删除产品 | `product.delete` |

## 权限管理

### 权限配置

```python
permission_config = {
    'view': 'product.view',        # 查看权限
    'create': 'product.create',    # 创建权限
    'update': 'product.update',    # 更新权限
    'delete': 'product.delete',    # 删除权限
    'export': 'product.export',    # 导出权限
    'statistics': 'product.statistics'  # 统计权限
}
```

### 权限检查装饰器

```python
from zhi_common.zhi_services.enhanced_api_expose import require_permission

@require_permission('product.export')
def export_data(self, request):
    """导出数据 🔒"""
    # 只有具有export权限的用户才能访问
    pass
```

## OAuth2认证

### 认证配置

系统使用增强型OAuth2认证，支持：

- **Token缓存** - 减少数据库查询
- **自动过期检查** - 确保Token有效性
- **用户上下文设置** - 自动设置request.user和request.user_info
- **数据权限过滤** - 自动应用数据权限规则

### Token格式

```
Authorization: Bearer oauth2_your_token_here
```

### 认证流程

1. 提取Token from Authorization header
2. 检查Token格式（必须包含'oauth2'）
3. 尝试从缓存获取用户信息
4. 缓存未命中则从数据库验证
5. 检查Token是否过期
6. 设置用户上下文和权限信息

## 数据权限

### 自动数据过滤

系统会自动应用以下数据过滤规则：

```python
# 基础过滤
filters = {'is_deleted': False}

# 组织隔离
if user.org_id:
    filters['org_id'] = user.org_id

# 创建者过滤（非超级用户）
if not user.is_superuser:
    filters['creator_id'] = str(user.id)
```

### 自定义数据权限

```python
class CustomDataPermissionService(DataPermissionService):
    def get_data_filters(self):
        filters = super().get_data_filters()
        
        # 添加自定义过滤规则
        if self.user.department_id:
            filters['department_id'] = self.user.department_id
        
        return filters
```

## 缓存机制

### Token缓存

- **缓存键**: `oauth_token:{token}`
- **缓存时间**: 1小时（可配置）
- **自动清理**: Token过期时自动清除

### API响应缓存

```python
@enhanced_api_route(
    method="GET",
    path="/statistics",
    cache_ttl=600  # 10分钟缓存
)
def get_statistics(self, request):
    pass
```

## 审计日志

### 自动记录

系统会自动记录以下操作：

- 用户认证成功/失败
- CRUD操作（创建、更新、删除）
- 权限检查结果
- 数据导出操作

### 手动记录

```python
def custom_operation(self, request):
    user = self._get_user_from_request(request)
    
    # 执行业务逻辑
    result = do_something()
    
    # 记录审计日志
    self.log_audit_event(
        action='custom_operation',
        success=True,
        user=user,
        context={'result_count': len(result)}
    )
```

## API文档

### 访问地址

```
http://127.0.0.1:8000/api/docs
```

### 文档特性

- **权限标识** - 所有需要权限的API都显示🔒图标
- **完整描述** - 包含summary、description、参数说明
- **响应示例** - 标准化响应格式示例
- **权限要求** - 显示所需的权限代码

## 错误处理

### 标准错误响应

```json
{
  "code": 4003,
  "message": "权限不足，需要权限: product.create",
  "success": false,
  "trace_id": "xxx-xxx-xxx",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": null
}
```

### 常见错误码

- `2000` - 成功
- `4001` - 未授权（Token无效）
- `4003` - 权限不足
- `4004` - 资源不存在
- `4022` - 数据验证失败
- `5000` - 内部服务器错误

## 最佳实践

### 1. 控制器设计

```python
@enhanced_auto_crud_api(
    model_class=Product,
    prefix="products",
    tags=["产品管理"],
    schema_in=ProductCreateSchema,
    schema_out=ProductResponseSchema,
    enable_data_permissions=True,
    enable_field_permissions=True,
    enable_audit_logging=True
)
class ProductController(EnhancedModelService):
    """产品控制器"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 自定义初始化逻辑
```

### 2. 权限设计

- 使用层次化权限命名：`module.action`
- 合理设置数据权限过滤规则
- 记录关键操作的审计日志

### 3. 性能优化

- 启用Token缓存
- 合理设置API响应缓存
- 使用select_related优化数据库查询

### 4. 安全考虑

- 所有API都需要认证
- 敏感操作需要特殊权限
- 记录完整的审计日志

## 总结

增强型API基类系统基于旧项目经验，提供了完整的企业级API开发解决方案：

- ✅ **开箱即用** - 一个装饰器生成完整CRUD API
- ✅ **权限管理** - 完整的认证、授权、审计体系
- ✅ **性能优化** - Token缓存、API缓存、数据库优化
- ✅ **标准化** - 统一的响应格式、错误处理、文档生成
- ✅ **可扩展** - 支持自定义端点、权限规则、数据过滤

现在您可以访问 `http://127.0.0.1:8000/api/docs` 查看完整的API文档！
