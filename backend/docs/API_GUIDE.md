# ZhiAdmin API 使用指南

## 📋 概述

ZhiAdmin提供完整的RESTful API，支持OAuth2认证、日志管理、任务管理等功能。所有API都遵循OpenAPI 3.0规范，提供完整的文档和测试界面。

## 🔐 认证方式

### OAuth2 Bearer Token

所有API请求都需要在Header中携带访问令牌：

```http
Authorization: Bearer <access_token>
```

### 获取访问令牌

```http
POST /api/auth/token/
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password",
  "grant_type": "password",
  "client_id": "your_client_id",
  "client_secret": "your_client_secret"
}
```

**响应示例**:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "read write"
}
```

## 🚀 API端点详解

### OAuth认证 API

#### 用户信息
```http
GET /api/auth/user/profile/
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "first_name": "Admin",
  "last_name": "User",
  "is_active": true,
  "date_joined": "2025-01-01T00:00:00Z",
  "permissions": ["read", "write", "admin"]
}
```

#### 刷新令牌
```http
POST /api/auth/refresh/
Content-Type: application/json

{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "grant_type": "refresh_token"
}
```

#### 撤销令牌
```http
POST /api/auth/revoke/
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 日志管理 API

#### 查询日志
```http
GET /api/logger/logs/?level=ERROR&start_time=2025-01-01&limit=50
Authorization: Bearer <access_token>
```

**查询参数**:
- `level`: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `start_time`: 开始时间 (YYYY-MM-DD 或 ISO格式)
- `end_time`: 结束时间
- `module`: 模块名称
- `user_id`: 用户ID
- `limit`: 返回数量限制
- `offset`: 偏移量

**响应**:
```json
{
  "count": 100,
  "next": "/api/logger/logs/?offset=50",
  "previous": null,
  "results": [
    {
      "id": 1,
      "level": "ERROR",
      "message": "Database connection failed",
      "module": "zhi_oauth",
      "timestamp": "2025-01-01T12:00:00Z",
      "user_id": 1,
      "extra_data": {
        "error_code": "DB_CONNECTION_ERROR",
        "retry_count": 3
      }
    }
  ]
}
```

#### 创建日志
```http
POST /api/logger/logs/
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "level": "INFO",
  "message": "User login successful",
  "module": "zhi_oauth",
  "extra_data": {
    "user_id": 1,
    "ip_address": "*************"
  }
}
```

#### 日志统计
```http
GET /api/logger/stats/?days=7&group_by=level
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "period": "7 days",
  "total_logs": 1500,
  "statistics": {
    "DEBUG": 800,
    "INFO": 500,
    "WARNING": 150,
    "ERROR": 45,
    "CRITICAL": 5
  },
  "trends": [
    {"date": "2025-01-01", "count": 200},
    {"date": "2025-01-02", "count": 220}
  ]
}
```

### 任务管理 API

#### 获取任务列表
```http
GET /api/celery/tasks/list
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "total_tasks": 15,
  "task_groups": {
    "zhi_celery": [
      "zhi_celery.tasks.oauth_tasks.cleanup_expired_tokens_task",
      "zhi_celery.tasks.logger_tasks.cleanup_old_logs_task"
    ],
    "builtin": [
      "celery.backend_cleanup"
    ]
  }
}
```

#### 执行任务
```http
POST /api/celery/tasks/execute/zhi_celery.tasks.oauth_tasks.cleanup_expired_tokens_task
Content-Type: application/json
Authorization: Bearer <access_token>

{
  "args": [7, 90],
  "kwargs": {}
}
```

**响应**:
```json
{
  "success": true,
  "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "task_name": "cleanup_expired_tokens_task",
  "status": "PENDING",
  "message": "任务已提交执行"
}
```

#### 查询任务状态
```http
GET /api/celery/tasks/status/a1b2c3d4-e5f6-7890-abcd-ef1234567890
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "status": "SUCCESS",
  "result": {
    "success": true,
    "stats": {
      "access_tokens": 15,
      "refresh_tokens": 8,
      "authorization_codes": 3
    },
    "total_cleaned": 26
  },
  "timestamp": "2025-01-01T12:30:00Z"
}
```

## 🔧 错误处理

### 标准错误响应

```json
{
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Invalid or expired token",
    "details": {
      "token_expired": true,
      "expires_at": "2025-01-01T12:00:00Z"
    }
  },
  "timestamp": "2025-01-01T12:30:00Z",
  "path": "/api/auth/user/profile/"
}
```

### 常见错误码

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| AUTHENTICATION_FAILED | 401 | 认证失败 |
| PERMISSION_DENIED | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 400 | 参数验证失败 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |

## 📊 分页和过滤

### 分页参数
- `limit`: 每页数量 (默认20, 最大100)
- `offset`: 偏移量 (默认0)

### 排序参数
- `ordering`: 排序字段 (支持多字段, 用逗号分隔)
- 降序排序: `-field_name`

### 过滤参数
- 精确匹配: `field=value`
- 范围查询: `field__gte=value`, `field__lte=value`
- 模糊查询: `field__icontains=value`
- 时间范围: `created_at__date=2025-01-01`

**示例**:
```http
GET /api/logger/logs/?level=ERROR&created_at__gte=2025-01-01&ordering=-timestamp&limit=20&offset=40
```

## 🌐 WebSocket API

### 实时日志推送

```javascript
// 连接WebSocket
const ws = new WebSocket('ws://localhost:8002/ws/logs/');

// 监听消息
ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('新日志:', data);
};

// 发送过滤条件
ws.send(JSON.stringify({
    'action': 'subscribe',
    'filters': {
        'level': 'ERROR',
        'module': 'zhi_oauth'
    }
}));
```

## 🔍 API测试

### 使用curl

```bash
# 获取令牌
curl -X POST http://localhost:8001/api/auth/token/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "password",
    "grant_type": "password",
    "client_id": "your_client_id"
  }'

# 使用令牌访问API
curl -X GET http://localhost:8002/api/logger/logs/ \
  -H "Authorization: Bearer <access_token>"
```

### 使用Python requests

```python
import requests

# 获取令牌
token_response = requests.post('http://localhost:8001/api/auth/token/', json={
    'username': 'admin',
    'password': 'password',
    'grant_type': 'password',
    'client_id': 'your_client_id'
})
token = token_response.json()['access_token']

# 使用令牌
headers = {'Authorization': f'Bearer {token}'}
logs_response = requests.get('http://localhost:8002/api/logger/logs/', headers=headers)
logs = logs_response.json()
```

## 📚 SDK和客户端库

### Python SDK (推荐)

```python
from zhi_admin_sdk import ZhiAdminClient

# 初始化客户端
client = ZhiAdminClient(
    base_url='http://localhost:8001',
    client_id='your_client_id',
    client_secret='your_client_secret'
)

# 用户认证
client.authenticate('username', 'password')

# 查询日志
logs = client.logger.get_logs(level='ERROR', limit=50)

# 执行任务
task_result = client.celery.execute_task(
    'cleanup_expired_tokens_task',
    args=[7, 90]
)
```

### JavaScript SDK

```javascript
import { ZhiAdminClient } from 'zhi-admin-js-sdk';

const client = new ZhiAdminClient({
  baseURL: 'http://localhost:8001',
  clientId: 'your_client_id'
});

// 认证
await client.auth.login('username', 'password');

// 查询日志
const logs = await client.logger.getLogs({
  level: 'ERROR',
  limit: 50
});
```

---

**ZhiAdmin API** - 强大而简单的企业级API 🚀
