# Enhanced Expose 权限管理系统集成指南

## 📋 概述

本指南介绍如何在您的 `zhi_common.zhi_services.enhanced_expose` 中集成最新的权限管理系统，实现多维度权限控制、智能缓存管理、动态权限策略和实时审计监控。

## 🎯 核心功能

### 1. 多维度权限控制
- **菜单权限**: 控制界面访问
- **数据权限**: 行级数据过滤
- **字段权限**: 列级字段控制
- **API权限**: 接口访问控制

### 2. 智能缓存管理
- **多层缓存**: Redis + 内存 + 数据库
- **自动失效**: 数据变更自动清缓存
- **权限缓存**: 权限检查结果缓存

### 3. 动态权限策略
- **时间条件**: 工作时间、节假日控制
- **地理位置**: IP地址、地理位置限制
- **属性条件**: 用户属性、资源属性判断

### 4. 实时审计监控
- **操作审计**: 所有权限操作记录
- **风险评估**: 自动评估操作风险等级
- **异常检测**: 可疑行为自动识别

## 🚀 快速开始

### 1. 基础集成

```python
from zhi_common.zhi_services.enhanced_expose import (
    enhanced_auto_crud_api, 
    versioned_api_route, 
    APIVersion,
    PermissionDimension,
    PermissionAction
)
from zhi_common.zhi_services.enhanced_model_service import EnhancedModelService

# 定义服务类
class MyModelService(EnhancedModelService):
    def __init__(self, model_class, **kwargs):
        permission_config = {
            'view': 'mymodel.view',
            'create': 'mymodel.create',
            'update': 'mymodel.update',
            'delete': 'mymodel.delete',
        }
        super().__init__(
            model_class, 
            permission_config=permission_config,
            enable_data_permissions=True,
            enable_field_permissions=True,
            enable_audit_logging=True,
            **kwargs
        )

# 使用装饰器
@enhanced_auto_crud_api(
    model_class=MyModel,
    prefix="mymodels",
    version=APIVersion.V2,
    # 权限管理配置
    permission_config={
        'view': 'mymodel.view',
        'create': 'mymodel.create',
        'update': 'mymodel.update',
        'delete': 'mymodel.delete',
    },
    enable_data_permissions=True,
    enable_field_permissions=True,
    enable_audit_logging=True
)
class MyModelController(MyModelService):
    pass
```

### 2. 自定义权限路由

```python
@versioned_api_route(
    http_get,
    '/statistics',
    version=APIVersion.V2,
    permission_code='mymodel.statistics',
    permission_dimension=PermissionDimension.API,
    field_permissions=True,
    cache_strategy='redis',
    cache_ttl=600
)
def get_statistics(self, request):
    """获取统计信息 - 需要特定权限"""
    return self.get_model_statistics(request.user)
```

### 3. 权限检查装饰器

```python
from zhi_common.zhi_services.enhanced_expose import permission_required_route

@permission_required_route(
    permission_code='mymodel.sensitive_data',
    dimension=PermissionDimension.API,
    field_permissions=True
)
def get_sensitive_data(self, request):
    """获取敏感数据 - 需要特殊权限"""
    # 业务逻辑
    pass
```

## 📊 配置选项

### 1. enhanced_auto_crud_api 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `permission_config` | `Dict[str, str]` | `None` | 权限配置映射 |
| `enable_data_permissions` | `bool` | `True` | 启用数据权限过滤 |
| `enable_field_permissions` | `bool` | `True` | 启用字段权限控制 |
| `enable_audit_logging` | `bool` | `True` | 启用审计日志 |
| `permission_prefix` | `str` | `None` | 权限代码前缀 |

### 2. versioned_api_route 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `permission_code` | `str` | `None` | 权限代码 |
| `permission_dimension` | `PermissionDimension` | `API` | 权限维度 |
| `field_permissions` | `bool` | `False` | 启用字段权限 |
| `cache_strategy` | `CacheStrategy` | `NONE` | 缓存策略 |
| `cache_ttl` | `int` | `300` | 缓存时间(秒) |

### 3. 权限配置示例

```python
# 完整配置示例
@enhanced_auto_crud_api(
    model_class=Article,
    prefix="articles",
    tags=["文章管理"],
    version=APIVersion.V2,
    
    # 权限管理配置
    permission_config={
        'view': 'article.view',
        'create': 'article.create',
        'update': 'article.update',
        'delete': 'article.delete',
        'export': 'article.export'
    },
    enable_data_permissions=True,
    enable_field_permissions=True,
    enable_audit_logging=True,
    permission_prefix='article',
    
    # 缓存配置
    cache_config={
        'list_pagination': {'strategy': 'redis', 'ttl': 300},
        'retrieve': {'strategy': 'memory', 'ttl': 600}
    },
    
    # 速率限制
    rate_limit_config={
        'create': {'requests': 10, 'window': 60},
        'update': {'requests': 20, 'window': 60}
    },
    
    # 审计配置
    audit_config={
        'enable': True,
        'sensitive_fields': ['author_id', 'is_featured'],
        'async_logging': True
    }
)
class ArticleController(EnhancedModelService):
    pass
```

## 🔧 权限数据初始化

### 1. 创建权限记录

```python
from zhi_oauth.models import Permission, Role

# 创建基础权限
permissions = [
    {'code': 'article.view', 'name': '查看文章', 'permission_type': 'api'},
    {'code': 'article.create', 'name': '创建文章', 'permission_type': 'api'},
    {'code': 'article.update', 'name': '更新文章', 'permission_type': 'api'},
    {'code': 'article.delete', 'name': '删除文章', 'permission_type': 'api'},
    {'code': 'article.export', 'name': '导出文章', 'permission_type': 'api'},
]

for perm_data in permissions:
    Permission.objects.get_or_create(**perm_data)
```

### 2. 创建角色和分配权限

```python
# 创建角色
editor_role, created = Role.objects.get_or_create(
    code='editor',
    defaults={
        'name': '编辑',
        'description': '文章编辑权限',
        'data_scope': 'org'
    }
)

# 分配权限
editor_permissions = Permission.objects.filter(
    code__in=['article.view', 'article.create', 'article.update']
)
editor_role.permissions.set(editor_permissions)
```

## 📈 监控和维护

### 1. 权限统计

```bash
# 查看权限使用统计
python manage.py permission_management audit_report --days 7

# 检测权限异常
python manage.py permission_management detect_anomalies

# 预热权限缓存
python manage.py permission_management warm_cache
```

### 2. 缓存管理

```python
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager

# 清除用户缓存
unified_permission_manager.clear_user_cache(user_id)

# 预热缓存
result = unified_permission_manager.warm_up_cache()
print(f"缓存预热结果: {result}")
```

## 🛡️ 安全最佳实践

### 1. 权限设计原则
- **最小权限原则**: 用户只获得完成工作所需的最小权限
- **职责分离**: 敏感操作需要多个权限组合
- **定期审查**: 定期检查和清理不必要的权限

### 2. 数据权限配置
```python
# 数据权限范围配置
DATA_PERMISSION_SCOPES = {
    'all': '全部数据',
    'tenant': '租户数据', 
    'org': '本部门数据',
    'org_and_sub': '本部门及子部门数据',
    'self': '仅本人数据'
}
```

### 3. 字段权限配置
```python
# 字段权限配置示例
FIELD_PERMISSIONS = {
    'article': {
        'public_fields': ['id', 'title', 'category', 'created_at'],
        'restricted_fields': {
            'content': {'required_permission': 'article.view_content'},
            'author_id': {'required_permission': 'article.view_author'},
            'is_featured': {'required_permission': 'article.view_featured'}
        }
    }
}
```

## 🔍 故障排除

### 1. 常见问题

**Q: 权限检查失败，返回403错误**
A: 检查用户是否有对应权限，查看审计日志确认权限代码是否正确

**Q: 数据权限过滤不生效**
A: 确认 `enable_data_permissions=True` 且用户角色配置了正确的数据范围

**Q: 字段权限不生效**
A: 确认 `enable_field_permissions=True` 且在权限配置中定义了字段权限

### 2. 调试技巧

```python
# 启用调试日志
import logging
logging.getLogger('zhi_auth.permission').setLevel(logging.DEBUG)

# 检查用户权限
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager

user_permissions = unified_permission_manager.get_user_permissions(user)
print(f"用户权限: {user_permissions}")

# 检查权限检查结果
has_permission = unified_permission_manager.check_permission(
    user, 'article.view', resource=article
)
print(f"权限检查结果: {has_permission}")
```

## 📚 更多资源

- [权限管理系统架构文档](./permission_system_architecture.md)
- [API权限配置参考](./api_permission_reference.md)
- [审计日志分析指南](./audit_log_analysis.md)
- [性能优化建议](./performance_optimization.md)

## 🤝 支持

如有问题或建议，请联系开发团队或提交Issue。
