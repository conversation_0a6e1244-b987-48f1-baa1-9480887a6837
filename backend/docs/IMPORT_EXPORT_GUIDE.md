# 导入导出功能使用指南

## 📋 概述

本指南详细介绍如何在 `BaseModelService` CRUD基类中使用导入导出功能，支持CSV、Excel和JSON格式的数据导入导出。

## 🚀 快速开始

### 1. 基础配置

在你的服务类中配置导入导出设置：

```python
from zhi_common.zhi_api.zhi_crud import BaseModelService

class ProductService(BaseModelService):
    model = Product
    
    # 导出配置
    export_settings = {
        'enabled': True,  # 启用导出
        'max_rows': 10000,  # 最大导出行数
        'fields': ['name', 'code', 'price'],  # 允许导出的字段
        'field_mapping': {  # 字段中文映射
            'name': '产品名称',
            'code': '产品编码',
            'price': '价格'
        }
    }
    
    # 导入配置
    import_settings = {
        'enabled': True,  # 启用导入
        'max_rows': 1000,  # 最大导入行数
        'field_mapping': {  # 文件字段 -> 模型字段
            '产品名称': 'name',
            '产品编码': 'code',
            '价格': 'price'
        }
    }
```

### 2. 添加API端点

```python
from ninja_extra import http_get, http_post
from zhi_common.zhi_api.base_api import api_route

@api_route(http_get, "/export")
def export_data(self, request):
    """导出数据"""
    format_type = request.GET.get('format', 'excel')
    return self.export_data(format_type=format_type)

@api_route(http_post, "/import")
def import_data(self, request, file: UploadedFile):
    """导入数据"""
    return self.import_data(file=file)

@api_route(http_get, "/import/template")
def get_template(self, request):
    """下载导入模板"""
    format_type = request.GET.get('format', 'excel')
    return self.get_import_template(format_type=format_type)
```

## 📤 导出功能

### 支持的格式
- **CSV**: 逗号分隔值文件，支持中文
- **Excel**: .xlsx格式，带样式和自动列宽
- **JSON**: 标准JSON格式

### 导出配置选项

```python
export_settings = {
    'enabled': True,  # 是否启用导出功能
    'max_rows': 10000,  # 最大导出行数限制
    'fields': ['field1', 'field2'],  # 允许导出的字段列表，None表示所有字段
    'field_mapping': {  # 字段名映射（用于表头显示）
        'field_name': '中文字段名'
    }
}
```

### 使用示例

```python
# 导出所有数据为Excel
response = service.export_data(format_type='excel')

# 导出指定字段为CSV
response = service.export_data(
    format_type='csv',
    fields=['name', 'code', 'price'],
    filename='products_export'
)

# 带过滤条件的导出
response = service.export_data(
    format_type='excel',
    filters={'category': '电子产品', 'is_active': True}
)
```

## 📥 导入功能

### 支持的格式
- **CSV**: 逗号分隔值文件
- **Excel**: .xlsx和.xls格式

### 导入配置选项

```python
import_settings = {
    'enabled': True,  # 是否启用导入功能
    'max_rows': 1000,  # 最大导入行数限制
    'max_file_size': 5 * 1024 * 1024,  # 最大文件大小（字节）
    'sheet_name': None,  # Excel工作表名称，None表示活动工作表
    'field_mapping': {  # 文件字段名 -> 模型字段名
        '产品名称': 'name',
        '产品编码': 'code'
    },
    'field_validators': {  # 字段验证器
        'price': lambda x: float(x) if float(x) > 0 else ValueError("价格必须大于0")
    }
}
```

### 使用示例

```python
# 导入数据
result = service.import_data(file=uploaded_file)

# 预览导入（不实际导入）
preview = service.import_data(file=uploaded_file, preview_only=True)

# 自定义字段映射导入
result = service.import_data(
    file=uploaded_file,
    field_mapping={'Name': 'name', 'Code': 'code'}
)
```

## 🛠️ 高级功能

### 1. 自定义字段验证器

```python
def validate_price(value):
    """价格验证器"""
    try:
        price = float(value)
        if price <= 0:
            raise ValueError("价格必须大于0")
        return price
    except (ValueError, TypeError):
        raise ValueError("价格格式不正确")

import_settings = {
    'field_validators': {
        'price': validate_price,
        'email': lambda x: x if '@' in str(x) else ValueError("邮箱格式不正确")
    }
}
```

### 2. 数据预处理

```python
def clean_string(value):
    """清理字符串数据"""
    return value.strip().upper() if isinstance(value, str) else value

import_settings = {
    'field_validators': {
        'code': clean_string
    }
}
```

### 3. 获取可导出字段

```python
# 获取模型的所有可导出字段信息
fields_info = service.get_export_fields()
# 返回字段名、类型、是否必填、选择项等信息
```

## 🔧 API端点示例

### 完整的控制器实现

```python
@auto_crud_api(Product, prefix="Product", tags=["产品管理"])
class ProductAPI(BaseModelService):
    model = Product
    export_settings = {...}  # 导出配置
    import_settings = {...}  # 导入配置
    
    @api_route(http_get, "/export")
    def export_products(self, request):
        """导出产品数据"""
        return self.export_data(
            format_type=request.GET.get('format', 'excel'),
            filters=dict(request.GET.items())
        )
    
    @api_route(http_post, "/import")
    def import_products(self, request, file: UploadedFile):
        """导入产品数据"""
        preview_only = request.POST.get('preview_only', 'false') == 'true'
        return self.import_data(file=file, preview_only=preview_only)
    
    @api_route(http_get, "/import/template")
    def download_template(self, request):
        """下载导入模板"""
        return self.get_import_template(
            format_type=request.GET.get('format', 'excel')
        )
    
    @api_route(http_get, "/export/fields")
    def get_exportable_fields(self, request):
        """获取可导出字段列表"""
        return self.get_export_fields()
```

## 📝 前端调用示例

### JavaScript调用

```javascript
// 导出数据
const exportData = async (format = 'excel') => {
    const response = await fetch(`/api/products/export?format=${format}`);
    const blob = await response.blob();
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `products.${format === 'excel' ? 'xlsx' : format}`;
    a.click();
};

// 导入数据
const importData = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/products/import', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
};

// 预览导入
const previewImport = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('preview_only', 'true');
    
    const response = await fetch('/api/products/import', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
};
```

## ⚠️ 注意事项

1. **文件大小限制**: 默认限制为5MB，可通过`max_file_size`配置
2. **行数限制**: 导入默认限制1000行，导出默认限制10000行
3. **字段映射**: 确保字段映射正确，避免数据导入错误
4. **数据验证**: 使用字段验证器确保数据质量
5. **权限控制**: 导入导出功能会自动应用用户权限过滤
6. **错误处理**: 导入时会收集所有错误信息，便于用户修正

## 🔍 故障排除

### 常见问题

1. **导入失败**: 检查字段映射和数据格式
2. **文件格式不支持**: 确保使用支持的文件格式
3. **数据验证失败**: 检查字段验证器和数据内容
4. **权限不足**: 确保用户有相应的导入导出权限

### 调试技巧

1. 使用预览功能检查数据解析结果
2. 查看错误日志了解具体失败原因
3. 下载模板确保文件格式正确
4. 使用小数据集测试功能
