# OAuth2密码登录 + 统一认证集成指南

## 📋 概述

本指南介绍如何在zhi_oauth系统中实现密码登录，然后统一采用Authorization Code OAuth2认证的完整方案。

## 🎯 认证流程

### 整体架构
```
用户 → 密码登录 → 获取session_token → OAuth2授权 → 获取authorization_code → 换取access_token → 访问API
```

### 详细流程
1. **密码登录**: 用户使用用户名/密码登录，获得session_token
2. **应用选择**: 基于session_token获取可用的OAuth2应用列表
3. **OAuth2授权**: 选择应用进行授权，获得authorization_code
4. **令牌交换**: 使用authorization_code换取access_token
5. **API访问**: 使用access_token访问受保护的API
6. **令牌刷新**: 使用refresh_token刷新access_token
7. **登出**: 清除所有令牌和会话

## 🚀 快速开始

### 1. 数据库迁移

```bash
# 生成迁移文件
python manage.py makemigrations zhi_oauth

# 执行迁移
python manage.py migrate zhi_oauth
```

### 2. 创建OAuth2应用

```python
from zhi_oauth.models import OAuthApplication

# 创建OAuth2应用
app = OAuthApplication.objects.create(
    name="测试应用",
    client_id="test-client-id",
    client_secret="test-client-secret",
    app_type="web",
    redirect_uris=["http://localhost:3000/callback"],
    grant_types=["authorization_code", "refresh_token"],
    response_types=["code"],
    scope=["read", "write"],
    description="测试用OAuth2应用"
)
```

### 3. 配置URL路由

```python
# urls.py
from django.urls import path, include
from zhi_oauth.apis.auth_controller import AuthController

urlpatterns = [
    path('api/auth/', include(AuthController.urls)),
    # 其他路由...
    ]
```

### 4. 前端集成

```javascript
// 1. 密码登录
const loginResponse = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
        remember_me: false
    })
});

const loginData = await loginResponse.json();
const sessionToken = loginData.session_token;

// 2. OAuth2授权
const authorizeResponse = await fetch('/api/auth/oauth2/authorize', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        session_token: sessionToken,
        client_id: 'test-client-id',
        redirect_uri: 'http://localhost:3000/callback',
        scope: 'read write'
    })
});

const authorizeData = await authorizeResponse.json();
// 重定向到 authorizeData.redirect_url

// 3. 在回调页面换取令牌
const tokenResponse = await fetch('/api/auth/oauth2/token', {
    method: 'POST',
    headers: {'Content-Type': 'application/json'},
    body: JSON.stringify({
        grant_type: 'authorization_code',
        code: authorizationCode, // 从URL参数获取
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
        redirect_uri: 'http://localhost:3000/callback'
    })
});

const tokenData = await tokenResponse.json();
const accessToken = tokenData.access_token;

// 4. 使用访问令牌调用API
const apiResponse = await fetch('/api/user/profile', {
    headers: {
        'Authorization': `Bearer ${accessToken}`
    }
});
```

## 📊 API接口文档

### 1. 密码登录

**POST** `/api/auth/login`

```json
// 请求
{
    "username": "admin",
    "password": "admin123",
    "remember_me": false
}

// 响应
{
    "success": true,
    "session_token": "session_xxx",
    "user_info": {
        "id": "user_id",
        "username": "admin",
        "name": "管理员",
        "email": "<EMAIL>"
    },
    "expires_in": 3600,
    "message": "登录成功"
}
```

### 2. OAuth2授权

**POST** `/api/auth/oauth2/authorize`

```json
// 请求
{
    "session_token": "session_xxx",
    "client_id": "test-client-id",
    "redirect_uri": "http://localhost:3000/callback",
    "response_type": "code",
    "scope": "read write",
    "state": "random_state"
}

// 响应
{
    "success": true,
    "authorization_code": "auth_code_xxx",
    "redirect_url": "http://localhost:3000/callback?code=auth_code_xxx&state=random_state",
    "expires_in": 600,
    "message": "授权成功"
}
```

### 3. 令牌交换

**POST** `/api/auth/oauth2/token`

```json
// 请求（授权码模式）
{
    "grant_type": "authorization_code",
    "code": "auth_code_xxx",
    "client_id": "test-client-id",
    "client_secret": "test-client-secret",
    "redirect_uri": "http://localhost:3000/callback"
}

// 请求（刷新令牌模式）
{
    "grant_type": "refresh_token",
    "refresh_token": "refresh_token_xxx",
    "client_id": "test-client-id",
    "client_secret": "test-client-secret"
}

// 响应
{
    "success": true,
    "access_token": "access_token_xxx",
    "token_type": "Bearer",
    "expires_in": 3600,
    "refresh_token": "refresh_token_xxx",
    "scope": "read write",
    "message": "令牌获取成功"
}
```

### 4. 用户信息

**GET** `/api/auth/user/info?session_token=session_xxx`

```json
// 响应
{
    "success": true,
    "user_info": {
        "id": "user_id",
        "username": "admin",
        "name": "管理员",
        "email": "<EMAIL>",
        "roles": [
            {"id": "role_id", "code": "admin", "name": "管理员"}
        ]
    },
    "message": "获取用户信息成功"
}
```

### 5. 登出

**POST** `/api/auth/logout`

```json
// 请求
{
    "session_token": "session_xxx",
    "access_token": "access_token_xxx"
}

// 响应
{
    "success": true,
    "message": "登出成功"
}
```

## 🔧 配置选项

### 1. Django Settings

```python
# settings.py

# 认证配置
AUTH_SESSION_TIMEOUT = 3600  # 会话超时时间（秒）
AUTH_MAX_LOGIN_ATTEMPTS = 5  # 最大登录尝试次数
AUTH_LOCKOUT_DURATION = 1800  # 账户锁定时长（秒）

# OAuth2配置
OAUTH2_SETTINGS = {
    'ACCESS_TOKEN_EXPIRE_SECONDS': 3600,  # 访问令牌过期时间
    'REFRESH_TOKEN_EXPIRE_SECONDS': 3600 * 24 * 7,  # 刷新令牌过期时间
    'AUTHORIZATION_CODE_EXPIRE_SECONDS': 600,  # 授权码过期时间
    'ENABLE_PKCE': True,  # 启用PKCE
}

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

### 2. 环境变量

```bash
# .env
SECRET_KEY=your-secret-key
DATABASE_URL=postgresql://user:pass@localhost/dbname
REDIS_URL=redis://localhost:6379/1

# OAuth2配置
OAUTH2_ACCESS_TOKEN_LIFETIME=3600
OAUTH2_REFRESH_TOKEN_LIFETIME=604800
OAUTH2_AUTHORIZATION_CODE_LIFETIME=600
```

## 🛡️ 安全考虑

### 1. PKCE支持

```python
# 生成code_verifier和code_challenge
import base64
import hashlib
import secrets

def generate_pkce_pair():
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    code_challenge = base64.urlsafe_b64encode(
        hashlib.sha256(code_verifier.encode('utf-8')).digest()
    ).decode('utf-8').rstrip('=')
    return code_verifier, code_challenge

# 在授权请求中包含code_challenge
authorize_data = {
    "session_token": session_token,
    "client_id": client_id,
    "redirect_uri": redirect_uri,
    "code_challenge": code_challenge,
    "code_challenge_method": "S256"
}

# 在令牌交换时包含code_verifier
token_data = {
    "grant_type": "authorization_code",
    "code": authorization_code,
    "client_id": client_id,
    "client_secret": client_secret,
    "redirect_uri": redirect_uri,
    "code_verifier": code_verifier
}
```

### 2. 安全最佳实践

- **HTTPS**: 生产环境必须使用HTTPS
- **状态参数**: 使用state参数防止CSRF攻击
- **令牌过期**: 设置合理的令牌过期时间
- **刷新令牌**: 使用刷新令牌机制
- **客户端验证**: 严格验证客户端身份
- **重定向URI**: 严格验证重定向URI

## 🔍 故障排除

### 1. 常见问题

**Q: 登录失败，提示"用户名或密码错误"**
A: 检查用户名和密码是否正确，支持用户名、邮箱、手机号登录

**Q: OAuth2授权失败，提示"无效的OAuth2客户端"**
A: 检查client_id和redirect_uri是否正确配置

**Q: 令牌交换失败，提示"授权码无效或已过期"**
A: 授权码只能使用一次，且有效期为10分钟

**Q: API调用失败，提示"令牌无效"**
A: 检查access_token是否正确，是否已过期

### 2. 调试技巧

```python
# 启用调试日志
import logging

logging.getLogger('zhi_oauth').setLevel(logging.DEBUG)

# 检查会话状态
from zhi_oauth.controllers.auth_service import auth_service

user = auth_service._validate_session_token(session_token)
print(f"会话用户: {user}")

# 检查令牌状态
from zhi_oauth.models import OAuthAccessToken

token = OAuthAccessToken.objects.get(token=access_token)
print(f"令牌状态: valid={token.is_valid()}, expired={token.is_expired()}")
```

## 📚 更多资源

- [OAuth2 RFC 6749](https://tools.ietf.org/html/rfc6749)
- [PKCE RFC 7636](https://tools.ietf.org/html/rfc7636)
- [Django OAuth Toolkit](https://django-oauth-toolkit.readthedocs.io/)
- [前端集成示例](./frontend_integration_examples.md)

## 🤝 支持

如有问题或建议，请联系开发团队或提交Issue。
