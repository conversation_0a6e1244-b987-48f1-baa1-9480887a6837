# 文件管理系统使用指南

## 📋 概述

基于 django-ninja 和 django-ninja-extra 开发的完整文件管理系统，支持文件上传、下载、管理和统计功能。

## 🏗️ 系统架构

### 核心组件

1. **基础文件管理器** (`FileManager`)
   - 基础的文件上传下载功能
   - 文件验证和存储
   - 支持多种文件格式

2. **增强文件管理器** (`EnhancedFileManager`)
   - 集成数据库记录功能
   - 文件标签和描述管理
   - 访问日志和统计
   - 文件过期管理

3. **文件存储模型** (`FileStorage`)
   - 文件元数据存储
   - 访问统计
   - 标签和分类管理

4. **API控制器**
   - 基础文件API (`FileAPIController`)
   - 增强文件API (`EnhancedFileAPIController`)

## 🚀 快速开始

### 1. 基础配置

```python
from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig

# 创建配置
config = FileUploadConfig(
    max_file_size=10 * 1024 * 1024,  # 10MB
    allowed_types=['image', 'document', 'archive'],
    upload_path='uploads',
    organize_by_date=True,
    generate_unique_name=True
)

# 创建文件管理器
file_manager = FileManager(config)
```

### 2. 文件上传

```python
from django.core.files.uploadedfile import UploadedFile

# 上传单个文件
result = file_manager.upload_file(uploaded_file)
print(f"文件ID: {result.file_id}")
print(f"访问URL: {result.file_url}")

# 批量上传
results = file_manager.upload_multiple_files([file1, file2, file3])
```

### 3. 文件下载

```python
# 下载文件
response = file_manager.download_file(file_path, as_attachment=True)

# 获取文件信息
file_info = file_manager.get_file_info(file_path)
```

## 📤 API接口

### 基础文件API

#### 上传文件
```http
POST /api/files/upload
Content-Type: multipart/form-data

file: [文件]
custom_path: [自定义路径]
file_id: [自定义文件ID]
max_size: [最大文件大小]
allowed_types: [允许的文件类型]
```

#### 下载文件
```http
GET /api/files/download?file_path={path}&as_attachment=true
```

#### 获取文件信息
```http
GET /api/files/info?file_path={path}
```

#### 删除文件
```http
DELETE /api/files/delete?file_path={path}
```

#### 列出文件
```http
GET /api/files/list?directory={dir}&recursive=false
```

### 增强文件API

#### 上传文件（增强版）
```http
POST /api/enhanced-files/upload
Content-Type: multipart/form-data

file: [文件]
custom_path: [自定义路径]
file_id: [自定义文件ID]
tags: [标签，逗号分隔]
description: [文件描述]
expires_in_days: [过期天数]
max_size: [最大文件大小]
allowed_types: [允许的文件类型]
```

#### 下载文件（增强版）
```http
GET /api/enhanced-files/download?file_id={id}&as_attachment=true
```

#### 获取文件信息（增强版）
```http
GET /api/enhanced-files/info?file_id={id}
```

#### 删除文件（增强版）
```http
DELETE /api/enhanced-files/delete?file_id={id}&physical_delete=false
```

#### 列出文件（增强版）
```http
GET /api/enhanced-files/list?file_type=image&tags=test&page=1&page_size=20
```

## 🔧 配置选项

### FileUploadConfig 参数

```python
FileUploadConfig(
    max_file_size=10 * 1024 * 1024,  # 最大文件大小(字节)
    allowed_extensions=['.jpg', '.png'],  # 允许的扩展名
    allowed_types=['image', 'document'],  # 允许的文件类型
    upload_path='uploads',  # 上传路径
    organize_by_date=True,  # 按日期组织文件夹
    generate_unique_name=True  # 生成唯一文件名
)
```

### 支持的文件类型

- **image**: jpg, jpeg, png, gif, bmp, webp
- **document**: pdf, doc, docx, xls, xlsx, ppt, pptx, txt
- **archive**: zip, rar, 7z, tar, gz
- **video**: mp4, avi, mov, wmv, flv, mkv
- **audio**: mp3, wav, flac, aac, ogg

## 📊 数据库模型

### FileStorage 模型

```python
class FileStorage(CoreModel):
    file_id = CharField(max_length=64, unique=True)  # 文件唯一标识
    original_name = CharField(max_length=255)  # 原始文件名
    file_path = CharField(max_length=500)  # 文件路径
    file_url = URLField(max_length=500)  # 访问URL
    file_size = BigIntegerField()  # 文件大小
    file_type = CharField(max_length=20)  # 文件类型
    content_type = CharField(max_length=100)  # MIME类型
    file_hash = CharField(max_length=64)  # MD5哈希
    status = CharField(max_length=20)  # 状态
    download_count = PositiveIntegerField(default=0)  # 下载次数
    tags = JSONField(default=list)  # 标签
    description = TextField(blank=True)  # 描述
    expires_at = DateTimeField(null=True)  # 过期时间
```

### FileAccessLog 模型

```python
class FileAccessLog(CoreModel):
    file = ForeignKey(FileStorage)  # 关联文件
    action = CharField(max_length=20)  # 操作类型
    ip_address = GenericIPAddressField()  # IP地址
    user_agent = TextField()  # 用户代理
    extra_data = JSONField(default=dict)  # 额外数据
```

## 🛡️ 安全特性

1. **文件类型验证**: 严格的文件类型和扩展名检查
2. **文件大小限制**: 可配置的文件大小限制
3. **访问控制**: 基于用户权限的访问控制
4. **访问日志**: 完整的文件访问日志记录
5. **文件哈希**: MD5哈希值防止重复上传
6. **过期管理**: 支持文件自动过期

## 📝 使用示例

### JavaScript 前端调用

```javascript
// 上传文件
const uploadFile = async (file, tags = [], description = '') => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('tags', tags.join(','));
    formData.append('description', description);
    
    const response = await fetch('/api/enhanced-files/upload', {
        method: 'POST',
        body: formData,
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    
    return await response.json();
};

// 下载文件
const downloadFile = (fileId, filename) => {
    const url = `/api/enhanced-files/download?file_id=${fileId}&custom_filename=${filename}`;
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
};

// 获取文件列表
const getFileList = async (page = 1, fileType = null, tags = []) => {
    let url = `/api/enhanced-files/list?page=${page}&page_size=20`;
    if (fileType) url += `&file_type=${fileType}`;
    if (tags.length > 0) url += `&tags=${tags.join(',')}`;
    
    const response = await fetch(url, {
        headers: {
            'Authorization': `Bearer ${token}`
        }
    });
    
    return await response.json();
};
```

### Python 客户端调用

```python
import requests

# 上传文件
def upload_file(file_path, tags=None, description=''):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        data = {
            'tags': ','.join(tags) if tags else '',
            'description': description
        }
        response = requests.post(
            '/api/enhanced-files/upload',
            files=files,
            data=data,
            headers={'Authorization': f'Bearer {token}'}
        )
    return response.json()

# 下载文件
def download_file(file_id, save_path):
    response = requests.get(
        f'/api/enhanced-files/download?file_id={file_id}',
        headers={'Authorization': f'Bearer {token}'}
    )
    with open(save_path, 'wb') as f:
        f.write(response.content)
```

## 🔍 高级功能

### 1. 文件去重

系统自动计算文件MD5哈希值，可以实现文件去重功能：

```python
# 检查文件是否已存在
existing_file = FileStorage.objects.filter(file_hash=file_hash).first()
if existing_file:
    # 文件已存在，返回现有文件信息
    return existing_file
```

### 2. 文件过期管理

```python
# 设置文件过期时间
file_manager.upload_file(
    file=uploaded_file,
    expires_in_days=30  # 30天后过期
)

# 清理过期文件
cleanup_result = file_manager.cleanup_expired_files()
```

### 3. 文件统计

```python
# 获取文件统计信息
stats = file_manager.get_file_statistics()
print(f"总文件数: {stats['total_files']}")
print(f"总大小: {stats['total_size_human']}")
print(f"总下载次数: {stats['total_downloads']}")
```

### 4. 标签管理

```python
# 添加标签
file_record.add_tag('important')

# 移除标签
file_record.remove_tag('temporary')

# 按标签搜索
files = file_manager.list_files(tags=['important', 'document'])
```

## ⚠️ 注意事项

1. **存储空间**: 定期清理不需要的文件，避免存储空间不足
2. **权限控制**: 确保正确配置文件访问权限
3. **备份策略**: 重要文件需要定期备份
4. **监控告警**: 监控文件上传下载的异常情况
5. **性能优化**: 大文件上传建议使用分片上传

## 🎯 最佳实践

1. **文件命名**: 使用有意义的文件名和描述
2. **标签使用**: 合理使用标签进行文件分类
3. **定期清理**: 定期清理过期和无用文件
4. **访问控制**: 根据业务需求设置合适的访问权限
5. **监控日志**: 定期检查访问日志，发现异常行为

## 🔧 故障排除

### 常见问题

1. **上传失败**: 检查文件大小和类型限制
2. **下载失败**: 确认文件路径和权限
3. **存储空间不足**: 清理无用文件或扩容
4. **权限错误**: 检查用户权限配置

### 调试技巧

1. 查看日志文件了解详细错误信息
2. 使用测试脚本验证功能
3. 检查数据库记录的一致性
4. 验证文件系统权限设置
