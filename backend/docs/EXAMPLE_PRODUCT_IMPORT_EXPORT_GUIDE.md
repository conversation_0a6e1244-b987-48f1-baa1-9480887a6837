# ExampleProduct 导入导出功能使用指南

## 🎉 功能完成

已成功为 `zhi_oauth.apis.example_product.py` 中的 `ExampleProductControllerAPI` 添加了完整的导入导出功能。

## 📋 功能概述

### 支持的功能
- ✅ **数据导出**: 支持CSV、Excel、JSON三种格式
- ✅ **数据导入**: 支持CSV、Excel格式导入
- ✅ **导入预览**: 上传文件预览解析结果
- ✅ **模板下载**: 生成标准导入模板
- ✅ **字段信息**: 获取可导出字段列表
- ✅ **数据验证**: 自定义字段验证器
- ✅ **权限控制**: 自动应用用户权限过滤

## 🔧 配置详情

### 导出配置
```python
export_settings = {
    'enabled': True,  # 启用导出功能
    'max_rows': 5000,  # 最大导出行数
    'fields': ['name', 'description', 'created_at', 'updated_at'],  # 允许导出的字段
    'field_mapping': {  # 字段中文映射
        'name': '产品名称',
        'description': '产品描述',
        'created_at': '创建时间',
        'updated_at': '更新时间',
        'creator_name': '创建人',
        'modifier_name': '修改人'
    }
}
```

### 导入配置
```python
import_settings = {
    'enabled': True,  # 启用导入功能
    'max_rows': 1000,  # 最大导入行数
    'max_file_size': 2 * 1024 * 1024,  # 最大文件大小 2MB
    'field_mapping': {  # 字段映射：文件字段名 -> 模型字段名
        '产品名称': 'name',
        '产品描述': 'description',
        # 支持英文字段名
        'name': 'name',
        'description': 'description',
        'Name': 'name',
        'Description': 'description'
    },
    'field_validators': {  # 字段验证器
        'name': validate_name,
        'description': validate_description
    }
}
```

### 字段验证器
```python
def validate_name(value):
    """产品名称验证器"""
    if not value or not value.strip():
        raise ValueError("产品名称不能为空")
    if len(value.strip()) < 2:
        raise ValueError("产品名称至少需要2个字符")
    if len(value.strip()) > 100:
        raise ValueError("产品名称不能超过100个字符")
    return value.strip()

def validate_description(value):
    """产品描述验证器"""
    if value and len(value.strip()) > 1000:
        raise ValueError("产品描述不能超过1000个字符")
    return value.strip() if value else ""
```

## 🚀 API端点

### 1. 导出数据
```http
GET /api/ExampleProduct/export?format=excel&name=测试
```

**查询参数：**
- `format`: 导出格式 (csv/excel/json，默认excel)
- `filename`: 文件名（可选）
- `fields`: 要导出的字段列表（可选，逗号分隔）
- `name`: 按产品名称过滤（可选）

**响应：** 文件下载

### 2. 导入数据
```http
POST /api/ExampleProduct/import
Content-Type: multipart/form-data

file: [上传的文件]
preview_only: false
```

**表单参数：**
- `file`: 上传的文件 (CSV/Excel)
- `preview_only`: 是否仅预览（可选，默认false）

**响应：**
```json
{
  "code": 2000,
  "message": "ok",
  "success": true,
  "data": {
    "success": true,
    "total_rows": 3,
    "success_rows": 3,
    "error_rows": 0,
    "errors": [],
    "message": "导入完成，成功3条"
  }
}
```

### 3. 预览导入
```http
POST /api/ExampleProduct/import/preview
Content-Type: multipart/form-data

file: [上传的文件]
```

**响应：**
```json
{
  "code": 2000,
  "message": "ok",
  "success": true,
  "data": {
    "total_rows": 3,
    "sample_data": [...],
    "headers": ["产品名称", "产品描述"],
    "field_mapping": {...},
    "errors": []
  }
}
```

### 4. 下载模板
```http
GET /api/ExampleProduct/import/template?format=excel
```

**查询参数：**
- `format`: 模板格式 (csv/excel，默认excel)

**响应：** 模板文件下载

### 5. 获取字段信息
```http
GET /api/ExampleProduct/export/fields
```

**响应：**
```json
{
  "code": 2000,
  "message": "ok",
  "success": true,
  "data": [
    {
      "name": "name",
      "verbose_name": "产品名称",
      "type": "CharField",
      "required": true
    },
    {
      "name": "description",
      "verbose_name": "产品描述",
      "type": "TextField",
      "required": false
    }
  ]
}
```

## 📝 使用示例

### JavaScript 前端调用

```javascript
// 1. 导出数据
const exportData = async (format = 'excel') => {
    const response = await fetch(`/api/ExampleProduct/export?format=${format}`);
    const blob = await response.blob();
    
    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `example_products.${format === 'excel' ? 'xlsx' : format}`;
    a.click();
};

// 2. 导入数据
const importData = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/ExampleProduct/import', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
};

// 3. 预览导入
const previewImport = async (file) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/ExampleProduct/import/preview', {
        method: 'POST',
        body: formData
    });
    
    return await response.json();
};

// 4. 下载模板
const downloadTemplate = async (format = 'excel') => {
    const response = await fetch(`/api/ExampleProduct/import/template?format=${format}`);
    const blob = await response.blob();
    
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `example_product_template.${format === 'excel' ? 'xlsx' : 'csv'}`;
    a.click();
};
```

### Python 客户端调用

```python
import requests

# 1. 导出数据
def export_data(format_type='excel'):
    response = requests.get(f'/api/ExampleProduct/export?format={format_type}')
    with open(f'export.{format_type}', 'wb') as f:
        f.write(response.content)

# 2. 导入数据
def import_data(file_path):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post('/api/ExampleProduct/import', files=files)
    return response.json()

# 3. 预览导入
def preview_import(file_path):
    with open(file_path, 'rb') as f:
        files = {'file': f}
        response = requests.post('/api/ExampleProduct/import/preview', files=files)
    return response.json()
```

## 📊 测试结果

✅ **所有测试通过 (6/6)**
- 服务类实例化测试通过
- 示例数据创建测试通过
- 导出功能测试通过 (CSV/Excel/JSON)
- 导入功能测试通过 (预览和实际导入)
- 模板生成测试通过
- 字段信息获取测试通过

## 🔍 导入数据格式示例

### CSV格式
```csv
产品名称,产品描述
测试产品1,这是测试产品1的描述
测试产品2,这是测试产品2的描述
测试产品3,这是测试产品3的描述
```

### Excel格式
| 产品名称 | 产品描述 |
|---------|---------|
| 测试产品1 | 这是测试产品1的描述 |
| 测试产品2 | 这是测试产品2的描述 |
| 测试产品3 | 这是测试产品3的描述 |

## ⚠️ 注意事项

1. **文件大小限制**: 最大2MB
2. **行数限制**: 导入最大1000行，导出最大5000行
3. **字段验证**: 产品名称必填，长度2-100字符
4. **编码支持**: 支持UTF-8编码的CSV文件
5. **权限控制**: 自动应用用户权限过滤
6. **错误处理**: 导入时会收集所有错误信息

## 🎯 扩展建议

1. **添加更多字段**: 可以扩展ExampleProduct模型，添加更多业务字段
2. **自定义验证**: 根据业务需求添加更复杂的验证逻辑
3. **批量操作**: 可以添加批量更新、批量删除等功能
4. **异步处理**: 对于大数据量可以考虑异步处理
5. **审计日志**: 记录导入导出操作的审计日志

## 🎊 总结

ExampleProduct的导入导出功能已完全实现并通过测试，具备：
- 完整的CRUD操作支持
- 多格式导入导出
- 数据验证和错误处理
- 权限控制和安全保护
- 友好的API接口设计

该功能可以作为其他模型导入导出功能的参考模板！
