"""
ZhiAdmin 主 URL 配置
根据不同的Django设置模块加载对应的URL配置
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
import os

# 基础 URL 配置
urlpatterns = [
    path('admin/', admin.site.urls),
]

# 根据当前的Django设置模块动态加载URL配置
def load_urls_by_settings():
    """根据Django设置模块加载对应的URL配置"""
    settings_module = os.environ.get('DJANGO_SETTINGS_MODULE', '')

    try:
        if 'zhi_oauth' in settings_module:
            # OAuth项目URL配置
            from application.conf_urls.zhi_oauth import urlpatterns as oauth_urls
            urlpatterns.extend(oauth_urls)

        elif 'zhi_logger' in settings_module:
            # Logger项目URL配置
            from application.conf_urls.zhi_logger import urlpatterns as logger_urls
            urlpatterns.extend(logger_urls)

        elif 'interval_admin' in settings_module:
            # Admin项目URL配置
            from application.conf_urls.interval_admin import urlpatterns as admin_urls
            urlpatterns.extend(admin_urls)

        else:
            # 默认加载所有URL配置（开发模式）
            try:
                from application.conf_urls.zhi_oauth import urlpatterns as oauth_urls
                urlpatterns.extend(oauth_urls)
            except ImportError:
                pass

            try:
                from application.conf_urls.zhi_logger import urlpatterns as logger_urls
                urlpatterns.extend(logger_urls)
            except ImportError:
                pass

            try:
                from application.conf_urls.interval_admin import urlpatterns as admin_urls
                urlpatterns.extend(admin_urls)
            except ImportError:
                pass

    except ImportError as e:
        print(f"Warning: Failed to load URL config for {settings_module}: {e}")
    except Exception as e:
        print(f"Error loading URL config: {e}")

# 加载URL配置
load_urls_by_settings()

# 系统状态端点
def system_status_view(request):
    """系统状态查看"""
    settings_module = os.environ.get('DJANGO_SETTINGS_MODULE', 'unknown')

    return JsonResponse({
        'settings_module': settings_module,
        'debug': getattr(settings, 'DEBUG', False),
        'installed_apps': getattr(settings, 'INSTALLED_APPS', []),
        'url_patterns_count': len(urlpatterns),
    })

urlpatterns.append(path('api/system/status/', system_status_view, name='system_status'))

# 静态文件配置
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
