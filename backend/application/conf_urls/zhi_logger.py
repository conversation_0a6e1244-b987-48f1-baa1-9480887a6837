"""
ZhiLogger URL 配置
启动命令: python manage.py runserver --settings=application.settings.zhi_logger
"""
from application.conf_urls.base import *
from zhi_logger.api import api as logger_api
from django.urls import include, path

# Logger 相关路由
app_urls = [
    # Logger API 路由 - 临时禁用以避免命名空间冲突
    # path('api/logger/', logger_api.urls),

    # 日志管理相关的其他路由
    # path('logger/', include('zhi_logger.urls')),  # 如果有传统的 Django 视图

    # 可以添加其他日志相关的 URL 模式
    # path('logs/export/', logger_export_view),  # 日志导出
    # path('logs/dashboard/', logger_dashboard_view),  # 日志仪表板
]

urlpatterns += app_urls
