"""
ZhiCelery URL 配置
"""
from application.conf_urls.base import *
# from zhi_celery.api import api as oauth_api
from django.urls import include, path

# OAuth2 相关路由
interval_admin_urls = [
    # OAuth2 API 路由
    # path('api/oauth/', oauth_api.urls),

    # 兼容原有的OAuth客户端API路由
    # 这样原有的前端调用 /api/oauth_client/ 仍然可以工作
    # 新的API路径为: /api/oauth/oauth_client/

    # 可以添加其他 OAuth2 相关的 URL 模式
    # path('oauth2/', include('zhi_oauth.urls')),  # 如果有传统的 Django 视图
]

urlpatterns += interval_admin_urls
