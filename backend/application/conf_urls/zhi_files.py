"""
ZhiFiles URL配置
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

# 导入API实例
from zhi_files.api import api

# 导入简化API视图（作为备用）
from zhi_files.apis.simple_file_api import (
    SimpleFileUploadView,
    SimpleFileDownloadView,
    SimpleFileListView
)

urlpatterns = [
    path('admin/', admin.site.urls),

    # 主要API路由 - 使用ninja-extra的完整功能
    path('api/', api.urls),

    # 简化API路由（备用/兼容性）
    path('api/simple/files/upload/', SimpleFileUploadView.as_view(), name='simple-file-upload'),
    path('api/simple/files/download/<str:file_id>/', SimpleFileDownloadView.as_view(), name='simple-file-download'),
    path('api/simple/files/list/', SimpleFileListView.as_view(), name='simple-file-list'),
]

# 开发环境下提供媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
