"""
ZhiFiles URL配置
"""

from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static

from zhi_files.apis.simple_file_api import (
    SimpleFileUploadView,
    SimpleFileDownloadView,
    SimpleFileListView
)

urlpatterns = [
    path('admin/', admin.site.urls),

    # 简化API路由
    path('api/files/upload/', SimpleFileUploadView.as_view(), name='file-upload'),
    path('api/files/download/<str:file_id>/', SimpleFileDownloadView.as_view(), name='file-download'),
    path('api/files/list/', SimpleFileListView.as_view(), name='file-list'),
]

# 开发环境下提供媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
