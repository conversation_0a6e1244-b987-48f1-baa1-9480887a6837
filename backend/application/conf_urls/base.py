"""zhiadmin URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from zhi_common.core_doc_routers import render_login_docs
from zhi_oauth.api import api as oauth_api

urlpatterns = [
    # 多项目文档管理页
    path('docs/', render_login_docs, name='multi_project_docs'),
    path('api/docs/', render_login_docs, name='api_docs_portal'),
    # 向后兼容
    path('api/login-docs/', render_login_docs, name='login-docs'),
]
