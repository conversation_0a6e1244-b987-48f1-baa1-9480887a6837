

AUDIT_LOG_CONFIG = {
    # # 配置示例
    # 'demo.demo': {
    #     # 是否启用，主要针对于配置了需要关闭的
    #     'is_enabled': True,
    #     # 配置特殊字段获取可查看内容的数据如：枚举类key->提取value
    #     'using_fields': {
    #         'demo_field1': {
    #             # 查询其他数据表的配置，不一定必须是foreign_key才可使用，有关联性就可，只要是在本系统内皆可配置使用。
    #             'type': 'foreign_key',
    #             # 关联的目标数据表名称
    #             'relation_model': 'zhiadmin.system.Users',
    #             # 此关联的值在目标表中对应的 field 名称
    #             'key': 'id',
    #             # 此关联的值在目标表中对应想要展示值的 field 名称
    #             'value': 'name',
    #         },
    #         'demo_field2': {
    #             'type': 'dictionary',
    #             # 使用此系统的字典配置时，对应的key名称
    #             'dictionary_key': '',
    #             # 未获取到配置值，设置的默认值, 默认为原始值；修改为其他值时，则默认为该值。
    #             'default': 'self',
    #         },
    #         'demo_field3': {
    #             # 未使用此系统的字典配置时，无需使用字段配置时，使用 枚举类型
    #             'type': 'enum',
    #             # 枚举值的配置信息
    #             'enum_config': {
    #                 '2': "测试enum类型为2",
    #                 '1': "测试enum类型为1"
    #             },
    #             # 未获取到配置值，设置的默认值, 默认为原始值；修改为其他值时，则默认为该值。
    #             'default': 'self',
    #         }
    #     },
    # },
}
