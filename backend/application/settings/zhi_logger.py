"""
ZhiLogger 子项目配置
启动命令: python manage.py runserver --settings=application.settings.zhi_logger
"""

from application.settings.base import *
from loguru import logger

# APP 的 URL 配置
ROOT_URLCONF = 'application.conf_urls.zhi_logger'
LOGIN_URL = '/admin/login/'

# 日志子项目特定的应用（base.py中已包含zhi_logger和zhi_common，这里不需要重复添加）
# LOGGER_APPS = [
#     "zhi_logger",
#     "zhi_common",
# ]

# 合并应用列表
# INSTALLED_APPS += LOGGER_APPS

# ZhiLogger 特定配置
LOGGER_SETTINGS = {
    # WebSocket 配置
    'WEBSOCKET_ENABLED': True,
    'WEBSOCKET_MAX_CONNECTIONS': 100,
    'WEBSOCKET_HEARTBEAT_INTERVAL': 30,
    
    # 日志存储配置
    'LOG_RETENTION_DAYS': {
        'system': 30,
        'api': 7,
        'auth': 90,
        'database': 30,
        'business': 60,
        'security': 180,
        'performance': 7,
    },
    
    # 日志级别配置
    'LOG_LEVELS': {
        'DEBUG': {'enabled': True, 'websocket': False},
        'INFO': {'enabled': True, 'websocket': True},
        'WARNING': {'enabled': True, 'websocket': True},
        'ERROR': {'enabled': True, 'websocket': True},
        'CRITICAL': {'enabled': True, 'websocket': True},
    },
    
    # API日志配置
    'API_LOG_ENABLED': True,
    'API_LOG_METHODS': ['POST', 'PUT', 'PATCH', 'DELETE'],
    'API_LOG_SKIP_PATHS': ['/admin/', '/static/', '/media/', '/ws/'],
    'API_LOG_REQUEST_BODY': True,
    'API_LOG_RESPONSE_BODY': False,
    'API_LOG_MAX_BODY_SIZE': 10000,
    
    # 清理配置
    'AUTO_CLEANUP': True,
    'CLEANUP_INTERVAL_HOURS': 24,
    'DEFAULT_RETENTION_DAYS': 30,
    
    # 告警配置
    'ALERT_ERROR_RATE_THRESHOLD': 0.1,
    'ALERT_LOG_VOLUME_THRESHOLD': 10000,
    'ALERT_EMAIL_ENABLED': False,
    'ALERT_WEBHOOK_ENABLED': False,
}

# 将配置添加到全局设置
locals().update(LOGGER_SETTINGS)

# 配置 ZhiLogger SDK
def configure_zhi_logger_sdk():
    """配置 ZhiLogger SDK"""
    from zhi_common.zhi_logger import configure_logging
    import logging
    
    # 基础配置
    configure_logging(
        level=logging.INFO,
        format_type="json",
        enable_console=True
    )
    
    # 模块特定配置
    configure_module_logging("zhi_logger", logging.DEBUG)
    configure_module_logging("api_service", logging.INFO)
    configure_module_logging("websocket", logging.WARNING)

# 在应用就绪时配置SDK
from django.apps import AppConfig

class ZhiLoggerConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'zhi_logger'
    
    def ready(self):
        configure_zhi_logger_sdk()
        # 导入信号处理器
        from zhi_common.zhi_logger import signals

# 中间件配置（日志子项目特定）
# 暂时注释掉，避免导入错误
# MIDDLEWARE += [
#     'zhi_common.zhi_logger.middleware.SystemLogMiddleware',
#     'zhi_common.zhi_logger.middleware.EnhancedApiLoggingMiddleware',
# ]

# Celery 任务配置（日志相关）
# 确保 CELERY_BEAT_SCHEDULE 存在
if 'CELERY_BEAT_SCHEDULE' not in locals():
    CELERY_BEAT_SCHEDULE = {}

CELERY_BEAT_SCHEDULE.update({
    'cleanup-old-logs': {
        'task': 'zhi_logger.tasks.cleanup_old_logs_task',
        'schedule': 60 * 60 * 24,  # 24小时
        'args': (30,),
    },
    'generate-log-report': {
        'task': 'zhi_logger.tasks.generate_log_report_task',
        'schedule': 60 * 60 * 24 * 7,  # 7天
        'args': (7,),
    },
    'log-health-check': {
        'task': 'zhi_logger.tasks.log_health_check_task',
        'schedule': 60 * 60,  # 1小时
    },
})

logger.debug(f"ZhiLogger 子项目配置加载完成: {INSTALLED_APPS}")
logger.debug(f"日志配置: {LOGGER_SETTINGS}")
