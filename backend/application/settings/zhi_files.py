"""
ZhiFiles 文件管理子项目配置
"""

from .base import *

# 应用配置
INSTALLED_APPS = [
    # Django 核心应用
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    
    # 第三方应用
    'ninja_extra',
    
    # 公共模块 - 仅加载必要的模块
    'zhi_common.zhi_response',
    'zhi_common.zhi_logger',
    
    # ZhiFiles 文件管理应用
    'zhi_files',
]

# 中间件配置
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'zhi_common.zhi_middleware.request_middleware.RequestMiddleware',
]

# URL配置
ROOT_URLCONF = 'application.conf_urls.zhi_files'

# 数据库配置（继承自base.py）
# 如果需要独立数据库，可以在这里重新配置
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'zhi_files.sqlite3',
#     }
# }

# 文件存储配置
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# 静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
FILE_UPLOAD_PERMISSIONS = 0o644

# ZhiFiles 特定配置
ZHI_FILES_CONFIG = {
    # 默认文件上传配置
    'DEFAULT_MAX_FILE_SIZE': 50 * 1024 * 1024,  # 50MB
    'DEFAULT_ALLOWED_TYPES': ['image', 'document', 'archive', 'video', 'audio'],
    'DEFAULT_UPLOAD_PATH': 'zhi_files_uploads',
    'ORGANIZE_BY_DATE': True,
    'GENERATE_UNIQUE_NAME': True,
    
    # 存储配置
    'STORAGE_BACKEND': 'django.core.files.storage.FileSystemStorage',
    'STORAGE_OPTIONS': {},
    
    # 安全配置
    'ENABLE_ACCESS_LOG': True,
    'ENABLE_DOWNLOAD_STATS': True,
    'ENABLE_FILE_EXPIRY': True,
    
    # 清理配置
    'AUTO_CLEANUP_EXPIRED': True,
    'CLEANUP_INTERVAL_HOURS': 24,
    
    # 缓存配置
    'ENABLE_FILE_CACHE': False,
    'CACHE_TIMEOUT': 3600,
}

# CORS配置已移除，如需要请安装 django-cors-headers

# API配置
API_TITLE = "ZhiFiles 文件管理API"
API_VERSION = "1.0.0"
API_DESCRIPTION = "ZhiAdmin 文件管理系统API接口"

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'zhi_files.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'zhi_files': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'zhi_common': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# 确保日志目录存在
import os
log_dir = BASE_DIR / 'logs'
os.makedirs(log_dir, exist_ok=True)

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'zhi_files_cache',
    }
}

# 会话配置
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24小时

# 安全配置
if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'

# 国际化配置
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# 默认主键字段类型
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
