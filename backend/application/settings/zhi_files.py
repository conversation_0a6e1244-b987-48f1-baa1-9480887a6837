"""
ZhiFiles 文件管理子项目配置
启动命令: python manage.py runserver --settings=application.settings.zhi_files
"""

from application.settings.base import *
from loguru import logger

# 文件管理子项目特定的应用
FILES_APPS = [
    'zhi_files',  # 文件管理应用
]

# 移除Celery相关应用，避免依赖问题（与其他子项目保持一致）
INSTALLED_APPS = [app for app in INSTALLED_APPS if not app.startswith('django_celery')]
INSTALLED_APPS = [app for app in INSTALLED_APPS if app != 'zhi_celery']

# 合并应用列表 - base.py中已包含基础应用，这里只添加特定应用
INSTALLED_APPS += FILES_APPS

# APP 的 URL 配置
ROOT_URLCONF = 'application.conf_urls.zhi_files'

logger.debug(f"ZhiFiles INSTALLED_APPS: {INSTALLED_APPS}")

# 数据库配置（继承自base.py）
# 如果需要独立数据库，可以在这里重新配置
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': BASE_DIR / 'zhi_files.sqlite3',
#     }
# }

# 文件存储配置
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# 静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 50 * 1024 * 1024  # 50MB
FILE_UPLOAD_PERMISSIONS = 0o644

# ZhiFiles 特定配置
ZHI_FILES_CONFIG = {
    # 默认文件上传配置
    'DEFAULT_MAX_FILE_SIZE': 100 * 1024 * 1024,  # 100MB
    'DEFAULT_ALLOWED_TYPES': ['image', 'document', 'archive', 'video', 'audio'],
    'DEFAULT_UPLOAD_PATH': 'zhi_files_uploads',
    'ORGANIZE_BY_DATE': True,
    'GENERATE_UNIQUE_NAME': True,

    # 存储配置
    'STORAGE_BACKEND': 'django.core.files.storage.FileSystemStorage',
    'STORAGE_OPTIONS': {},

    # 安全配置
    'ENABLE_ACCESS_LOG': True,
    'ENABLE_DOWNLOAD_STATS': True,
    'ENABLE_FILE_EXPIRY': True,
    'ENABLE_PERMISSION_CHECK': True,

    # 清理配置
    'AUTO_CLEANUP_EXPIRED': True,
    'CLEANUP_INTERVAL_HOURS': 24,
    'KEEP_DELETED_FILES_DAYS': 30,

    # 缓存配置
    'ENABLE_FILE_CACHE': True,
    'CACHE_TIMEOUT': 3600,
    'CACHE_KEY_PREFIX': 'zhi_files',

    # 微服务配置
    'MICROSERVICE_MODE': False,  # 是否启用微服务模式
    'SERVICE_NAME': 'zhi_files',
    'SERVICE_VERSION': '2.0.0',

    # 性能配置
    'ENABLE_ASYNC_UPLOAD': True,
    'CHUNK_UPLOAD_SIZE': 5 * 1024 * 1024,  # 5MB 分片大小
    'MAX_CONCURRENT_UPLOADS': 3,

    # 预览配置
    'ENABLE_FILE_PREVIEW': True,
    'PREVIEW_IMAGE_SIZES': [(150, 150), (300, 300), (800, 600)],
    'PREVIEW_CACHE_TIMEOUT': 7200,
}

# 数据库路由配置
if ZHI_FILES_CONFIG.get('MICROSERVICE_MODE', False):
    DATABASE_ROUTERS = ['zhi_files.database_router.ZhiFilesRouter']

    # 微服务模式下的数据库配置
    DATABASES.update({
        'files_db': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'zhi_files',
            'USER': DATABASES['default']['USER'],
            'PASSWORD': DATABASES['default']['PASSWORD'],
            'HOST': DATABASES['default']['HOST'],
            'PORT': DATABASES['default']['PORT'],
            'OPTIONS': DATABASES['default'].get('OPTIONS', {}),
        }
    })

# CORS配置已移除，如需要请安装 django-cors-headers

# API配置
API_TITLE = "ZhiFiles 文件管理API"
API_VERSION = "2.0.0"
API_DESCRIPTION = "ZhiAdmin 文件管理系统API接口 - 增强版"

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'zhi_files.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'zhi_files': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'zhi_common': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# 确保日志目录存在
import os
log_dir = BASE_DIR / 'logs'
os.makedirs(log_dir, exist_ok=True)

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'zhi_files_cache',
    }
}

# 会话配置
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 86400  # 24小时

# 安全配置
if not DEBUG:
    SECURE_BROWSER_XSS_FILTER = True
    SECURE_CONTENT_TYPE_NOSNIFF = True
    X_FRAME_OPTIONS = 'DENY'

# 国际化配置
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# 默认主键字段类型
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
