"""
<AUTHOR>
@Date    ：2024/10/29
"""

from application.settings.base import *
from loguru import logger

# APP 的 URL 配置
ROOT_URLCONF = 'application.conf_urls.zhi_oauth'

# 移除Celery相关应用，避免依赖问题
INSTALLED_APPS = [app for app in INSTALLED_APPS if not app.startswith('django_celery')]
INSTALLED_APPS = [app for app in INSTALLED_APPS if app != 'zhi_celery']

ADMIN_APPS = [
    "oauth2_provider",
    # "zhi_oauth",  # 已在base.py中添加，避免重复
    ]

# OAuth2 配置
try:
    OAUTH2_PROVIDER.update(
        {
            'SCOPES': {
                'read': 'Read scope',
                'write': 'Write scope',
                'groups': 'Access to your groups'
                }
            }
        )
except NameError:
    # 如果OAUTH2_PROVIDER未定义，创建它
    OAUTH2_PROVIDER = {
        'SCOPES': {
            'read': 'Read scope',
            'write': 'Write scope',
            'groups': 'Access to your groups'
        }
    }

INSTALLED_APPS += ADMIN_APPS
logger.debug(f"ZhiOAuth INSTALLED_APPS: {INSTALLED_APPS}")
