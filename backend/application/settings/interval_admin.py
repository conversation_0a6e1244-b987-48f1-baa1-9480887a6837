"""
<AUTHOR>
@Date    ：2024/10/29
"""

from application.settings.base import *
from loguru import logger


# APP 的 URL 配置
ROOT_URLCONF = 'application.conf_urls.interval_admin'


# 移除Celery相关应用，避免依赖问题
INSTALLED_APPS = [app for app in INSTALLED_APPS if not app.startswith('django_celery')]
INSTALLED_APPS = [app for app in INSTALLED_APPS if app != 'zhi_celery']

# 主项目特定的应用配置
INTERVAL_ADMIN_APPS = [
    "oauth2_provider",  # OAuth2提供者
    # 移除不存在的'system'应用
]

# 确保所有子项目应用都被加载（去重）
ALL_SUBPROJECT_APPS = [
    'zhi_oauth',    # OAuth认证系统
    'zhi_logger',   # 日志系统
    'zhi_common',   # 通用组件
]

# 合并应用，避免重复
for app in INTERVAL_ADMIN_APPS + ALL_SUBPROJECT_APPS:
    if app not in INSTALLED_APPS:
        INSTALLED_APPS.append(app)

# 主项目数据库配置 - 使用独立的数据库
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db_interval_admin.sqlite3',
    }
}

# 主项目特定设置
INTERVAL_ADMIN_SETTINGS = {
    'TITLE': 'ZhiAdmin 统一管理后台',
    'DESCRIPTION': '集成所有子项目的统一管理界面',
    'VERSION': '1.0.0',
    'ENABLE_ALL_SUBPROJECTS': True,
    'DEFAULT_LOAD_AUTH': True,  # 默认加载认证模块
}

logger.info("Interval Admin 主项目设置加载完成")
logger.info(f"已加载的应用: {len(INSTALLED_APPS)} 个")

print(f"🚀 Interval Admin 主项目设置加载完成")
print(f"📦 已加载 {len(INSTALLED_APPS)} 个应用")
print(f"🔐 默认加载认证模块: {INTERVAL_ADMIN_SETTINGS['DEFAULT_LOAD_AUTH']}")
print(f"🌐 集成所有子项目: {INTERVAL_ADMIN_SETTINGS['ENABLE_ALL_SUBPROJECTS']}")
