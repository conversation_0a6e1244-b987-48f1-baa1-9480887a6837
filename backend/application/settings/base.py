"""
Django settings for zhiadmin project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""
import sys
from pathlib import Path
import pymysql

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# [Important] 【下行调用请勿修改调用位置】，以下文件中需要用到上面的 【WORKER_ENV】 参数
from conf.env import *

# #####################################################################
# *********************** ZhiAdmin日志配置 ****************************#
# #####################################################################
import logging
from logging import Handler
# # 导入自定义 loguru 日志配置
from loguru import logger
from zhi_common.core_context import get_trace_id

SERVER_LOGS_FILE = os.path.join(BASE_DIR, "zhi_system_logs", "server.log")
ERROR_LOGS_FILE = os.path.join(BASE_DIR, "zhi_system_logs", "error.log")
LOGS_FILE = os.path.join(BASE_DIR, "zhi_system_logs")
if not os.path.exists(os.path.join(BASE_DIR, "zhi_system_logs")):
    os.makedirs(os.path.join(BASE_DIR, "zhi_system_logs"))

# 修改 LOGURU_CONFIG 配置
LOGURU_CONFIG = {
    "handlers": [
        {
            "sink": sys.stdout,
            "format": "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>trace_id={"
                      "extra[trace_id]}</cyan> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
                      "<level>{message}</level>",
            "level": "DEBUG",
            "colorize": True,
            "enqueue": True
            },
        {
            "sink": SERVER_LOGS_FILE,
            "rotation": "500 MB" if DEBUG else "00:00",
            "retention": "7 days",
            "enqueue": True,
            "level": "INFO",
            "format": "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | trace_id={extra[trace_id]} | {name}:{"
                      "function}:{line} - {message}",
            "catch": True
            },
        {
            "sink": ERROR_LOGS_FILE,
            "rotation": "100 MB",
            "retention": "30 days",
            "level": "ERROR",
            "enqueue": True,
            "format": "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | trace_id={extra[trace_id]} | {name}:{"
                      "function}:{line} - {message}"
            }
        ],
    "extra": {"user": None, "trace_id": None}  # 添加 trace_id 支持
    }


class InterceptHandler(Handler):
    def emit(self, record):
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno

        frame, depth = logging.currentframe(), 2
        while frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1

            # 获取当前 trace_id 或使用默认值
        trace_id = get_trace_id()

        logger.opt(depth=depth, exception=record.exc_info).bind(
            trace_id=trace_id
            ).log(level, record.getMessage())

    # 禁用 Django 默认的日志配置


LOGGING_CONFIG = None

# Loguru 配置
logger.remove()  # 移除所有默认处理器

# 应用配置
logger.configure(**LOGURU_CONFIG)
# 拦截 Django 日志
logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
for name in ["django", "django.request", "django.server", "django.db.backends"]:
    logging.getLogger(name).setLevel(logging.WARNING if not DEBUG else logging.INFO)

# 基础 Django 应用
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'ninja',
    'ninja_extra',
    # Celery 相关应用（需要安装相应包）
    'django_celery_beat',
    'django_celery_results',
    # ZhiAdmin 通用组件
    'zhi_common',
    'zhi_celery',  # 异步任务管理
    'zhi_oauth',   # OAuth认证系统（提供User模型）
    'zhi_logger',  # 统一日志系统
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    # zhiadmin 自定义中间件
    'zhi_common.zhi_middlewares.core_req_preprocessor.RequestPreprocessorMiddleware',
    'zhi_common.zhi_middlewares.core_api_logger.ApiLoggingMiddleware',
    # OAuth2 全局权限验证中间件
    'zhi_oauth.middleware.global_permission_middleware.GlobalPermissionMiddleware',
    ]

ROOT_URLCONF = 'application.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                ],
            },
        },
    ]

WSGI_APPLICATION = 'application.wsgi.application'

# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases
# ================================================= #
# *************** mysql数据库 配置  *************** #
# ================================================= #
pymysql.install_as_MySQLdb()
# 表前缀
TABLE_PREFIX = "zhi_"
DATABASE_ENGINE = "django.db.backends.mysql"
DATABASES = {
    "default": {
        "ENGINE": DATABASE_ENGINE,
        "NAME": DATABASE_NAME,
        "USER": DATABASE_USER,
        "PASSWORD": DATABASE_PASSWORD,
        "HOST": DATABASE_HOST,
        "PORT": DATABASE_PORT,
        'ATOMIC_REQUESTS': True,
        'OPTIONS': {'charset': 'utf8mb4'},
        },
    }

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'
# ########################### zhiadmin 配置 ########################################
# 用户模型配置
AUTH_USER_MODEL = 'zhi_oauth.User'  # 使用自定义User模型
USERNAME_FIELD = "username"

# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
        },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
        },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
        },
    ]

# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = "Asia/Shanghai"

USE_I18N = True

USE_L10N = True

USE_TZ = False

# ==================== 全局权限配置 ====================

# 全局权限验证配置
GLOBAL_PERMISSION_CONFIG = {
    # API 白名单 - 不需要认证的接口
    'WHITELIST_PATTERNS': [
        # OAuth2 认证相关
        r'^/api/oauth/login/?$',
        r'^/api/oauth/authorize/?$',  # OAuth2 授权端点需要 session_token，不需要 access_token
        r'^/api/oauth/token/?$',
        r'^/api/oauth/refresh/?$',

        # 系统健康检查
        r'^/health/?$',
        r'^/ping/?$',
        r'^/api/health/?$',

        # Django 管理后台
        r'^/admin/.*',

        # 静态文件和媒体文件
        r'^/static/.*',
        r'^/media/.*',

        # API 文档 - 使用正则表达式匹配所有子项目
        r'^/docs/?$',                                    # 项目文档入口
        r'^/api/docs/?$',                                # 通用文档入口
        r'^/api/(?P<project>\w+)/docs/?$',               # 子项目文档: /api/{project}/docs
        r'^/api/(?P<project>\w+)/openapi\.json/?$',      # 子项目OpenAPI: /api/{project}/openapi.json
        r'^/api/(?P<project>\w+)/redoc/?$',              # 子项目ReDoc: /api/{project}/redoc
        r'^/api/openapi\.json/?$',                       # 通用 OpenAPI 规范
        r'^/api/redoc/?$',                               # 通用 ReDoc 文档

        # 开发调试（仅开发环境）
        r'^/__debug__/.*' if DEBUG else None,
    ],

    # 权限检查配置
    'PERMISSION_CHECK': {
        'ENABLE_GLOBAL_CHECK': True,           # 启用全局权限检查
        'FALLBACK_TO_CONTROLLER': True,        # 中间件无法确定权限时，回退到控制器层面检查
    },

    # 超级管理员配置
    'SUPERUSER_CONFIG': {
        'BYPASS_PERMISSION_CHECK': True,       # 超级管理员绕过权限检查
        'LOG_SUPERUSER_ACCESS': True,          # 记录超级管理员访问日志
    },

    # 审计配置
    'AUDIT_CONFIG': {
        'ENABLE_ACCESS_LOG': True,             # 启用访问日志
        'ENABLE_PERMISSION_LOG': DEBUG,        # 开发环境启用权限检查日志
        'LOG_FAILED_ATTEMPTS': True,           # 记录失败的访问尝试
    },
}

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = "/static/"
# # 设置django的静态文件目录
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
    ]

MEDIA_ROOT = "media"  # 项目下的目录
MEDIA_URL = "/media/"  # 跟STATIC_URL类似，指定用户可以通过这个url找到文件

# 添加以下代码以后就不用写{% load staticfiles %}，可以直接引用
STATICFILES_FINDERS = (
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder"
    )

# 收集静态文件，必须将 MEDIA_ROOT,STATICFILES_DIRS先注释
# python manage.py collectstatic
# STATIC_ROOT=os.path.join(BASE_DIR,'static')


REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
CELERY_BROKER_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:{REDIS_PORT}/{CELERY_BROKER_DB}'

# ================================================= #
# ****************** Celery 配置  ******************* #
# ================================================= #
DJANGO_CELERY_BEAT_TZ_AWARE = False
CELERY_ENABLE_UTC = False
CELERY_WORKER_CONCURRENCY = 2  # 并发数
CELERY_MAX_TASKS_PER_CHILD = 5  # 每个worker最多执行5个任务便自我销毁释放内存
CELERY_TIMEZONE = TIME_ZONE  # celery 时区问题
CELERY_RESULT_BACKEND = 'django-db'  # celery结果存储到数据库中
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'  # Backend数据库

# ================================================= #
# ****************** 其他 配置  ******************* #
# ================================================= #
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/
ALLOWED_HOSTS = ["*"]
# 列权限中排除App应用
COLUMN_EXCLUDE_APPS = []

# token 有效时间 时 分 秒
TOKEN_LIFETIME = 12 * 60 * 60

# 全部允许配置
CORS_ORIGIN_ALLOW_ALL = True
# 允许cookie
CORS_ALLOW_CREDENTIALS = True  # 指明在跨域访问中，后端是否支持对cookie的操作

# ===================================================== #
# ********************* channels配置 ******************* #
# ===================================================== #
ASGI_APPLICATION = 'application.asgi.application'

# Channel Layers 配置 - 支持 WebSocket 日志推送
# 优先使用 channels_redis，如果不可用则回退到内存层
try:
    # 尝试导入 channels_redis
    import channels_redis

    # 使用 Redis Channel Layer (推荐用于生产环境)
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                "hosts": [REDIS_URL],
                "capacity": 1500,  # 每个频道的最大消息数
                "expiry": 60,      # 消息过期时间（秒）
            },
        },
    }

except ImportError:
    # channels_redis 未安装，使用内存 Channel Layer
    # 注意：内存层不支持多进程，仅适用于开发环境
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels.layers.InMemoryChannelLayer',
        },
    }

    # 在开发环境下提示安装 channels_redis
    if DEBUG:
        import warnings
        warnings.warn(
            "使用内存 Channel Layer。生产环境建议安装 channels_redis: "
            "pip install channels_redis",
            UserWarning
        )

# ================================================= #
# ********************* 缓存配置 ******************* #
# ================================================= #
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": REDIS_URL,
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            "PASSWORD": '',
            "SOCKET_CONNECT_TIMEOUT": 5,  # socket 建立连接超时设置
            "SOCKET_TIMEOUT": 5,  # 连接建立后的读写操作超时设置
            # "COMPRESSOR": "django_redis.compressors.zlib.ZlibCompressor",  # 压缩
            "CONNECTION_POOL_KWARGS": {"max_connections": 30},  # 设置连接池的最大连接数
            "SERIALIZER": "django_redis.serializers.json.JSONSerializer",  # 默认使用pickle序列化，这里设置为json
            }
        }
    }

# 接口白名单，不需要授权直接访问
WHITE_LIST = ['/api/system/userinfo', '/api/system/permCode', '/api/system/menu/route/tree', '/api/system/user/*',
              '/api/system/user/set/repassword', '/api/system/login']

# 接口日志记录
API_LOG_ENABLE = True
API_LOG_METHODS = ['POST', 'DELETE', 'PUT', 'PATCH']
API_MODEL_MAP = {}

# DATETIME 时间类型 默认时间格式
DEFAULT_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S'

# 初始化需要执行的列表，用来初始化后执行
INITIALIZE_RESET_LIST = []

ALL_MODELS_OBJECTS = []  # 所有app models 对象

# ================================================= #
# ******** NINJA_EXTRA 配置  *********************** #
# ================================================= #
NINJA_EXTRA = {
    'PAGINATION_CLASS': "ninja_extra.pagination.PageNumberPaginationExtra",
    'AUTO_CRUD_ENABLED': False,  # 完全禁用自动CRUD路由
    'INJECT_DECORATOR': False,
    'PAGINATION_PER_PAGE': 20,
    'INJECTOR_MODULES': [],
    'THROTTLE_CLASSES': [
        "ninja_extra.throttling.AnonRateThrottle",
        "ninja_extra.throttling.UserRateThrottle",
        ],
    'THROTTLE_RATES': {
        'user': '1000/day',
        'anon': '100/day',
        },
    'NUM_PROXIES': None,
    'ORDERING_CLASS': "ninja_extra.ordering.Ordering",
    'SEARCHING_CLASS': "ninja_extra.searching.Search",
    }


# OAuth2配置
OAUTH2_PROVIDER = {
    'ACCESS_TOKEN_EXPIRE_SECONDS': 3600 * 12,
    'REFRESH_TOKEN_EXPIRE_SECONDS': 3600 * 24 * 7,
    'AUTH_CODE_EXPIRE_SECONDS': 600,
    'TOKEN_PREFIX': 'oauth2_access',
    'REFRESH_TOKEN_PREFIX': 'oauth2_refresh',
    'ISSUER': 'https://auth.zhiadmin.com',
    # 'OAUTH2_BACKEND_CLASS': 'oauth2_provider.oauth2_backends.JSONOAuthLibCore',
    }


# OAuth2 认证后端
AUTHENTICATION_BACKENDS = [
    'zhi_oauth.auth.oauth_backend.OAuth2Backend',
    'django.contrib.auth.backends.ModelBackend',
]

# API 配置
API_PREFIX = '/api/'

# 限流配置
OAUTH2_RATE_LIMIT = 1000  # 每小时请求数
OAUTH2_RATE_WINDOW = 3600  # 时间窗口（秒）


# ============================================================================
# Celery 扩展配置
# ============================================================================

# 任务结果后端
CELERY_RESULT_BACKEND = 'django-db'
CELERY_CACHE_BACKEND = 'django-cache'

# 任务序列化
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_ACCEPT_CONTENT = ['json']

# 时区配置
CELERY_TIMEZONE = TIME_ZONE
CELERY_ENABLE_UTC = False

# 任务路由配置
CELERY_TASK_ROUTES = {
    'zhi_celery.tasks.logger_tasks.*': {'queue': 'logger'},
    'zhi_celery.tasks.*': {'queue': 'default'},
}

# 任务结果过期时间
CELERY_RESULT_EXPIRES = 3600

# Worker配置
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_WORKER_MAX_TASKS_PER_CHILD = 1000
CELERY_TASK_ACKS_LATE = True
CELERY_TASK_REJECT_ON_WORKER_LOST = True

# Beat调度器
CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers:DatabaseScheduler'

# 队列配置
CELERY_TASK_DEFAULT_QUEUE = 'default'
