"""
测试外键关联修复

验证所有User外键都正确关联到id字段而不是seq字段
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from django.db import models
from zhi_oauth.models import (
    User, OAuthAccessToken, OAuthRefreshToken, OAuthAuthorizationCode,
    UserThirdParty, Tenant, Organization, UserRole
)


def test_foreign_key_relationships():
    """测试外键关联关系"""
    print("🧪 测试外键关联关系...")
    
    models_to_test = [
        (OAuthAccessToken, 'user'),
        (OAuthAuthorizationCode, 'user'),
        (UserThirdParty, 'user'),
        (Tenant, 'admin_user'),
        (Organization, 'leader_user'),
        (UserRole, 'user'),
    ]
    
    results = []
    
    for model_class, field_name in models_to_test:
        try:
            # 获取字段对象
            field = model_class._meta.get_field(field_name)
            
            # 检查是否是ForeignKey
            is_foreign_key = isinstance(field, models.ForeignKey)
            
            # 检查to_field属性
            to_field = getattr(field, 'to_field', None)
            
            # 检查关联的模型
            related_model = field.related_model if hasattr(field, 'related_model') else None
            
            result = {
                'model': model_class.__name__,
                'field': field_name,
                'is_foreign_key': is_foreign_key,
                'to_field': to_field,
                'related_model': related_model.__name__ if related_model else None,
                'status': 'correct' if to_field == 'id' else 'needs_fix'
            }
            
            results.append(result)
            
            status_icon = "✅" if result['status'] == 'correct' else "❌"
            print(f"{status_icon} {model_class.__name__}.{field_name}:")
            print(f"   关联模型: {result['related_model']}")
            print(f"   to_field: {to_field or 'default(seq)'}")
            print(f"   状态: {result['status']}")
            
        except Exception as e:
            print(f"❌ 检查 {model_class.__name__}.{field_name} 失败: {e}")
            results.append({
                'model': model_class.__name__,
                'field': field_name,
                'status': 'error',
                'error': str(e)
            })
    
    return results


def test_user_model_fields():
    """测试User模型的字段结构"""
    print("\n🧪 测试User模型的字段结构...")
    
    try:
        # 检查User模型的主键字段
        user_meta = User._meta
        
        # 获取主键字段
        pk_field = user_meta.pk
        print(f"✅ 主键字段: {pk_field.name} ({pk_field.__class__.__name__})")
        
        # 检查id字段
        id_field = user_meta.get_field('id')
        print(f"✅ id字段: {id_field.name} ({id_field.__class__.__name__})")
        print(f"   unique: {id_field.unique}")
        print(f"   db_index: {id_field.db_index}")
        
        # 检查seq字段
        seq_field = user_meta.get_field('seq')
        print(f"✅ seq字段: {seq_field.name} ({seq_field.__class__.__name__})")
        print(f"   primary_key: {seq_field.primary_key}")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查User模型字段失败: {e}")
        return False


def test_token_creation_with_fixed_fk():
    """测试修复外键后的令牌创建"""
    print("\n🧪 测试修复外键后的令牌创建...")
    
    try:
        from django.test import RequestFactory
        from zhi_oauth.apis.auth import AuthControllerAPI, PasswordLoginSchema
        
        # 确保有测试用户
        test_user, created = User.objects.get_or_create(
            username='fk_test_user',
            defaults={
                'email': '<EMAIL>',
                'name': 'FK测试用户',
                'is_active': True
            }
        )
        
        if created:
            test_user.set_password('test_password123')
            test_user.save()
            print("✅ 创建FK测试用户成功")
        
        print(f"✅ 测试用户ID: {test_user.id} (类型: {type(test_user.id)})")
        print(f"✅ 测试用户SEQ: {test_user.seq} (类型: {type(test_user.seq)})")
        
        # 尝试登录
        factory = RequestFactory()
        auth_controller = AuthControllerAPI()
        
        login_data = PasswordLoginSchema(
            username='fk_test_user',
            password='test_password123',
            remember_me=True
        )
        
        request = factory.post('/api/auth/login')
        request.META['HTTP_USER_AGENT'] = 'FK Test Client'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        # 执行登录
        response = auth_controller.password_login(request, login_data)
        
        # 检查响应
        if hasattr(response, 'dict'):
            response_data = response.dict()
        elif hasattr(response, 'model_dump'):
            response_data = response.model_dump()
        else:
            response_data = response
        
        success = response_data.get('success', False)
        print(f"✅ 登录成功: {success}")
        
        if success:
            # 检查数据库中的令牌记录
            access_tokens = OAuthAccessToken.objects.filter(user=test_user)
            print(f"✅ 访问令牌记录数: {access_tokens.count()}")
            
            if access_tokens.exists():
                latest_token = access_tokens.first()
                print(f"✅ 令牌关联用户ID: {latest_token.user.id}")
                print(f"✅ 令牌关联用户SEQ: {latest_token.user.seq}")
                print(f"✅ 外键关联正确: {latest_token.user.id == test_user.id}")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试令牌创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_database_queries():
    """测试数据库查询"""
    print("\n🧪 测试数据库查询...")
    
    try:
        # 测试通过id字段查询
        user_by_id = User.objects.filter(username='fk_test_user').first()
        if user_by_id:
            print(f"✅ 通过用户名查询到用户: {user_by_id.id}")
            
            # 测试反向查询
            tokens = user_by_id.oauth_access_tokens.all()
            print(f"✅ 用户的访问令牌数量: {tokens.count()}")
            
            # 测试正向查询
            if tokens.exists():
                token = tokens.first()
                print(f"✅ 令牌关联的用户: {token.user.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试外键关联修复...")
    print("🎯 目标：验证所有User外键都正确关联到id字段")
    
    tests = [
        test_user_model_fields,
        test_foreign_key_relationships,
        test_token_creation_with_fixed_fk,
        test_database_queries
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！外键关联修复成功。")
        print("\n✅ 修复内容:")
        print("   - OAuthAccessToken.user → User.id")
        print("   - OAuthAuthorizationCode.user → User.id")
        print("   - UserThirdParty.user → User.id")
        print("   - Tenant.admin_user → User.id")
        print("   - Organization.leader_user → User.id")
        print("   - UserRole.user → User.id")
        print("\n💡 现在所有外键都正确关联到User的id字段（CharField）而不是seq字段（BigAutoField）")
        return True
    else:
        print("⚠️  部分测试失败，可能需要数据库迁移。")
        print("\n🔧 下一步操作:")
        print("   1. 生成数据库迁移: python manage.py makemigrations")
        print("   2. 应用迁移: python manage.py migrate")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
