# 全局权限验证系统

## 📋 系统概述

全局权限验证系统是一个基于中间件的统一权限管理解决方案，为所有 API 提供统一的认证和权限验证。

## 🏗️ 系统架构

### 核心组件

1. **GlobalPermissionMiddleware** - 全局权限验证中间件
2. **PermissionConfigManager** - 权限配置管理器
3. **统一权限管理器** - 权限检查和审计
4. **OAuth2 认证器** - Token 验证

### 工作流程

```
请求 → 白名单检查 → Token 提取 → Token 验证 → 权限检查 → 审计日志 → 响应
```

## ⚙️ 配置说明

### 1. 中间件配置

在 `settings/base.py` 中启用：

```python
MIDDLEWARE = [
    # ... 其他中间件
    'zhi_oauth.middleware.global_permission_middleware.GlobalPermissionMiddleware',
]
```

### 2. 权限配置

```python
GLOBAL_PERMISSION_CONFIG = {
    # API 白名单 - 不需要认证的接口
    'WHITELIST_PATTERNS': [
        r'^/api/oauth/login/?$',
        r'^/api/oauth/authorize/?$',
        r'^/api/oauth/token/?$',
        r'^/api/docs/?$',
        # ... 更多白名单
    ],
    
    # 权限检查配置
    'PERMISSION_CHECK': {
        'ENABLE_GLOBAL_CHECK': True,
        'FALLBACK_TO_CONTROLLER': True,
    },
    
    # 超级管理员配置
    'SUPERUSER_CONFIG': {
        'BYPASS_PERMISSION_CHECK': True,
        'LOG_SUPERUSER_ACCESS': True,
    },
    
    # 审计配置
    'AUDIT_CONFIG': {
        'ENABLE_ACCESS_LOG': True,
        'ENABLE_PERMISSION_LOG': DEBUG,
        'LOG_FAILED_ATTEMPTS': True,
    },
}
```

## 🔐 权限验证流程

### 1. 白名单检查

系统首先检查请求路径是否在白名单中：

- **OAuth2 认证端点**：`/api/oauth/login`、`/api/oauth/authorize`、`/api/oauth/token`
- **系统端点**：`/health`、`/ping`、`/api/docs`
- **静态文件**：`/static/*`、`/media/*`
- **管理后台**：`/admin/*`

### 2. Token 验证

对于非白名单请求，系统会：

1. 从 `Authorization: Bearer <token>` 头部提取 Token
2. 验证 Token 的有效性和过期时间
3. 获取 Token 对应的用户信息

### 3. 权限检查

系统支持多层权限检查：

1. **超级管理员绕过**：`is_superuser` 用户自动通过
2. **动态权限策略**：基于配置的动态权限规则
3. **基础权限检查**：基于角色和权限的传统检查
4. **控制器层面回退**：中间件无法确定权限时，由控制器处理

### 4. 审计日志

系统记录所有访问和权限检查：

- **访问日志**：记录所有 API 访问
- **权限日志**：记录权限检查结果
- **失败尝试**：记录认证和权限失败的尝试

## 🎯 使用示例

### 1. API 调用示例

```bash
# 白名单 API（无需 Token）
curl -X POST http://127.0.0.1:8001/api/oauth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "user", "password": "pass"}'

# 受保护 API（需要 Token）
curl -X GET http://127.0.0.1:8001/api/v1/example-products/list_pagination \
  -H "Authorization: Bearer oauth2_access-xxx..."
```

### 2. 权限配置示例

```python
# 在控制器中使用权限装饰器
from zhi_common.zhi_auth.unified_permission_manager import require_permission

@require_permission('example_product.view')
def list_products(self, request):
    # 控制器逻辑
    pass
```

## 🧪 测试验证

### 运行测试脚本

```bash
cd backend
python test_global_permission.py
```

### 测试覆盖

1. **白名单 API 测试**：验证白名单 API 可以正常访问
2. **无 Token 测试**：验证受保护 API 正确返回 401
3. **无效 Token 测试**：验证无效 Token 被正确拒绝
4. **有效 Token 测试**：验证有效 Token 可以访问受保护 API

## 📊 监控和维护

### 1. 日志监控

```bash
# 查看权限验证日志
tail -f logs/oauth2.log | grep "权限检查"

# 查看访问日志
tail -f logs/api_access.log
```

### 2. 性能监控

- **中间件响应时间**：监控权限检查的性能影响
- **缓存命中率**：监控权限缓存的效果
- **Token 验证频率**：监控 Token 验证的频率

### 3. 安全监控

- **失败尝试统计**：监控认证失败的频率
- **异常访问模式**：检测异常的访问行为
- **权限提升尝试**：监控权限提升的尝试

## 🔧 故障排除

### 常见问题

1. **Token 验证失败**
   - 检查 Token 格式是否正确
   - 确认 Token 未过期
   - 验证 OAuth2 应用配置

2. **权限检查失败**
   - 确认用户有相应权限
   - 检查权限配置是否正确
   - 验证权限系统是否正常工作

3. **白名单不生效**
   - 检查正则表达式是否正确
   - 确认中间件配置正确
   - 验证请求路径匹配

### 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('zhi_oauth').setLevel(logging.DEBUG)

# 检查权限配置
from zhi_oauth.middleware.permission_config import permission_config_manager
print(permission_config_manager.get_whitelist_patterns())

# 测试权限检查
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
result = unified_permission_manager.check_permission(user, 'example_product.view')
```

## 🚀 扩展开发

### 1. 添加新的白名单规则

```python
# 在 settings.py 中添加
GLOBAL_PERMISSION_CONFIG['WHITELIST_PATTERNS'].append(
    r'^/api/public/.*'
)
```

### 2. 自定义权限检查逻辑

```python
# 继承并扩展权限中间件
class CustomPermissionMiddleware(GlobalPermissionMiddleware):
    def _check_permissions(self, request):
        # 自定义权限检查逻辑
        return super()._check_permissions(request)
```

### 3. 集成第三方认证

```python
# 扩展 Token 验证逻辑
def custom_token_validator(token):
    # 自定义 Token 验证逻辑
    pass
```

## 📈 性能优化

### 1. 缓存优化

- **权限结果缓存**：缓存权限检查结果
- **用户信息缓存**：缓存用户基本信息
- **配置缓存**：缓存权限配置

### 2. 数据库优化

- **索引优化**：为 Token 查询添加索引
- **查询优化**：优化权限查询语句
- **连接池**：使用数据库连接池

### 3. 中间件优化

- **早期返回**：尽早返回白名单请求
- **异步处理**：异步处理审计日志
- **批量操作**：批量处理权限检查

## 🔒 安全考虑

### 1. Token 安全

- **Token 过期**：设置合理的过期时间
- **Token 撤销**：支持 Token 主动撤销
- **Token 刷新**：支持 Token 自动刷新

### 2. 权限安全

- **最小权限原则**：用户只获得必要权限
- **权限审计**：记录所有权限变更
- **权限分离**：敏感操作需要额外权限

### 3. 系统安全

- **防暴力破解**：限制登录尝试次数
- **IP 白名单**：支持 IP 访问控制
- **请求限流**：防止 API 滥用

## 📚 相关文档

- [OAuth2 认证系统](README.md)
- [权限管理系统](../config/permission_settings.py)
- [API 文档](EXAMPLE_PRODUCT_API.md)
- [迁移指南](MIGRATION_GUIDE.md)
