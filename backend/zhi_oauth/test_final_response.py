"""
最终响应测试 - 验证修复后的完整响应
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

import json
from django.test import RequestFactory
from zhi_oauth.apis.auth import AuthControllerAPI, PasswordLoginSchema


def test_final_login_response():
    """测试最终的登录响应"""
    print("🧪 测试最终的登录响应...")
    
    try:
        factory = RequestFactory()
        auth_controller = AuthControllerAPI()
        
        # 使用现有用户
        login_data = PasswordLoginSchema(
            username='admin',  # 使用admin用户
            password='admin123',
            remember_me=True
        )
        
        request = factory.post('/api/auth/login')
        request.META['HTTP_USER_AGENT'] = 'Final Test Client'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        # 执行登录
        response = auth_controller.password_login(request, login_data)
        
        # 检查响应格式
        if hasattr(response, 'dict'):
            response_data = response.dict()
        elif hasattr(response, 'model_dump'):
            response_data = response.model_dump()
        else:
            response_data = response
        
        print("📋 最终响应结构:")
        print(json.dumps(response_data, indent=2, ensure_ascii=False, default=str))
        
        # 检查响应成功性
        success = response_data.get('success', False)
        print(f"\n✅ 登录成功: {success}")
        
        if success:
            data = response_data.get('data', {})
            
            # 检查关键字段
            key_fields = ['access_token', 'refresh_token', 'token_type', 'expires_in', 'user_info']
            found_fields = [field for field in key_fields if field in data]
            missing_fields = [field for field in key_fields if field not in data]
            
            print(f"✅ 包含关键字段: {found_fields}")
            if missing_fields:
                print(f"⚠️  缺少字段: {missing_fields}")
            
            # 检查令牌类型
            access_token = data.get('access_token', '')
            refresh_token = data.get('refresh_token', '')
            
            is_real_access = access_token.startswith('access-')
            is_real_refresh = refresh_token.startswith('refresh-')
            
            print(f"✅ 访问令牌类型: {'真实令牌' if is_real_access else '备用令牌'}")
            print(f"✅ 刷新令牌类型: {'真实令牌' if is_real_refresh else '备用令牌'}")
            
            # 检查过期时间
            expires_in = data.get('expires_in', 0)
            expected_long = 3600 * 24 * 7  # 7天
            
            if expires_in >= expected_long:
                print(f"✅ 记住登录生效: {expires_in // 3600} 小时")
            else:
                print(f"⚠️  过期时间较短: {expires_in // 3600} 小时")
            
            # 检查用户信息
            user_info = data.get('user_info', {})
            if user_info:
                print(f"✅ 用户信息: {user_info.get('username')} ({user_info.get('name', 'N/A')})")
            
            return True
        else:
            print(f"❌ 登录失败: {response_data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 最终响应测试")
    print("🎯 验证修复后的完整响应格式")
    
    success = test_final_login_response()
    
    if success:
        print("\n🎉 最终测试成功！")
        print("✅ 数据结构重复问题已修复")
        print("✅ 真实令牌创建成功")
        print("✅ 响应格式完全正确")
    else:
        print("\n⚠️  最终测试失败")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
