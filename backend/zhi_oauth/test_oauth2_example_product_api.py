"""
ExampleProduct API OAuth2认证测试脚本
演示完整的OAuth2认证流程和API调用
"""

import os
import sys
import django
import requests
import json
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_oauth.models import ExampleProduct, OAuthApplication, Permission, Role, UserRole
from django.contrib.auth import get_user_model

User = get_user_model()


class OAuth2ExampleProductAPITest:
    """ExampleProduct API OAuth2认证测试类"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/auth"
        self.product_api_base = f"{base_url}/api/auth/v1/example-products"
        self.session_token = None
        self.access_token = None
        self.test_user = None
        self.test_app = None
    
    def setup_test_data(self):
        """设置测试数据"""
        print("=== 设置测试数据 ===")
        
        # 创建测试用户
        self.test_user, created = User.objects.get_or_create(
            username='test_product_user',
            defaults={
                'email': '<EMAIL>',
                'name': '测试用户',
                'is_active': True
            }
        )
        if created:
            self.test_user.set_password('test123456')
            self.test_user.save()
            print(f"✓ 创建测试用户: {self.test_user.username}")
        else:
            print(f"- 测试用户已存在: {self.test_user.username}")
        
        # 创建OAuth2应用
        self.test_app, created = OAuthApplication.objects.get_or_create(
            name='ExampleProduct测试应用',
            defaults={
                'client_id': 'test_example_product_client',
                'client_secret': 'test_secret_123456',
                'app_type': 'web',
                'redirect_uris': ['http://localhost:3000/callback'],
                'grant_types': ['authorization_code', 'refresh_token'],
                'allowed_scopes': ['read', 'write'],
                'is_active': True
            }
        )
        if created:
            print(f"✓ 创建OAuth2应用: {self.test_app.name}")
        else:
            print(f"- OAuth2应用已存在: {self.test_app.name}")
        
        # 创建测试产品
        test_products = [
            {'name': '测试产品A', 'description': '这是测试产品A的描述'},
            {'name': '测试产品B', 'description': '这是测试产品B的描述'},
            {'name': '示例产品C', 'description': '这是示例产品C的描述'},
        ]
        
        for product_data in test_products:
            product, created = ExampleProduct.objects.get_or_create(
                name=product_data['name'],
                defaults={
                    'description': product_data['description'],
                    'creator_id': self.test_user.id,
                    'creator_name': self.test_user.username
                }
            )
            if created:
                print(f"✓ 创建测试产品: {product.name}")
        
        print("测试数据设置完成\n")
    
    def step1_password_login(self):
        """步骤1: 密码登录"""
        print("=== 步骤1: 密码登录 ===")

        url = f"{self.api_base}/auth/login"
        data = {
            "username": self.test_user.username,
            "password": "test123456",
            "remember_me": False
        }
        
        try:
            response = requests.post(url, json=data)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if result.get('success'):
                self.session_token = result['session_token']
                print(f"✅ 登录成功，获得session_token: {self.session_token[:20]}...")
                return True
            else:
                print(f"❌ 登录失败: {result.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def step2_oauth2_authorize(self):
        """步骤2: OAuth2授权"""
        print("\n=== 步骤2: OAuth2授权 ===")
        
        if not self.session_token:
            print("❌ 需要先登录获取session_token")
            return False
        
        url = f"{self.api_base}/auth/oauth2/authorize"
        data = {
            "session_token": self.session_token,
            "client_id": self.test_app.client_id,
            "redirect_uri": "http://localhost:3000/callback",
            "response_type": "code",
            "scope": "read write"
        }
        
        try:
            response = requests.post(url, json=data)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if result.get('success'):
                auth_code = result['authorization_code']
                print(f"✅ 授权成功，获得authorization_code: {auth_code[:20]}...")
                return auth_code
            else:
                print(f"❌ 授权失败: {result.get('message')}")
                return None
        except Exception as e:
            print(f"❌ 授权异常: {e}")
            return None
    
    def step3_exchange_token(self, authorization_code):
        """步骤3: 授权码换取访问令牌"""
        print("\n=== 步骤3: 授权码换取访问令牌 ===")
        
        url = f"{self.api_base}/auth/oauth2/token"
        data = {
            "grant_type": "authorization_code",
            "code": authorization_code,
            "client_id": self.test_app.client_id,
            "client_secret": self.test_app.client_secret,
            "redirect_uri": "http://localhost:3000/callback"
        }
        
        try:
            response = requests.post(url, json=data)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if result.get('success'):
                self.access_token = result['access_token']
                print(f"✅ 令牌交换成功，获得access_token: {self.access_token[:20]}...")
                return True
            else:
                print(f"❌ 令牌交换失败: {result.get('message')}")
                return False
        except Exception as e:
            print(f"❌ 令牌交换异常: {e}")
            return False
    
    def step4_test_product_apis(self):
        """步骤4: 测试产品API"""
        print("\n=== 步骤4: 测试产品API ===")
        
        if not self.access_token:
            print("❌ 需要先获取access_token")
            return False
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # 测试获取产品列表
        print("\n4.1 测试获取产品列表")
        try:
            url = f"{self.product_api_base}/list_pagination?page=1&page_size=10"
            response = requests.get(url, headers=headers)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ 获取产品列表成功，共 {len(result.get('data', []))} 个产品")
            else:
                print(f"❌ 获取产品列表失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 获取产品列表异常: {e}")
        
        # 测试创建产品
        print("\n4.2 测试创建产品")
        try:
            url = f"{self.product_api_base}/"
            data = {
                "name": f"API测试产品_{datetime.now().strftime('%H%M%S')}",
                "description": "通过API创建的测试产品"
            }
            response = requests.post(url, json=data, headers=headers)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if response.status_code == 200 and result.get('success'):
                created_product_id = result['data']['id']
                print(f"✅ 创建产品成功，产品ID: {created_product_id}")
                
                # 测试获取产品详情
                print("\n4.3 测试获取产品详情")
                detail_url = f"{self.product_api_base}/{created_product_id}"
                detail_response = requests.get(detail_url, headers=headers)
                detail_result = detail_response.json()
                
                if detail_response.status_code == 200:
                    print(f"✅ 获取产品详情成功: {detail_result['data']['name']}")
                else:
                    print(f"❌ 获取产品详情失败: {detail_result.get('message')}")
                
                return created_product_id
            else:
                print(f"❌ 创建产品失败: {result.get('message')}")
                return None
        except Exception as e:
            print(f"❌ 创建产品异常: {e}")
            return None
    
    def step5_test_advanced_apis(self):
        """步骤5: 测试高级API功能"""
        print("\n=== 步骤5: 测试高级API功能 ===")
        
        if not self.access_token:
            print("❌ 需要先获取access_token")
            return
        
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # 测试获取统计信息
        print("\n5.1 测试获取统计信息")
        try:
            url = f"{self.product_api_base}/stats"
            response = requests.get(url, headers=headers)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                stats = result.get('data', {})
                print(f"✅ 获取统计信息成功:")
                print(f"  - 总产品数: {stats.get('total_products', 0)}")
                print(f"  - 最近产品数: {len(stats.get('recent_products', []))}")
            else:
                print(f"❌ 获取统计信息失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 获取统计信息异常: {e}")
        
        # 测试搜索产品
        print("\n5.2 测试搜索产品")
        try:
            url = f"{self.product_api_base}/search?keyword=测试&limit=5"
            response = requests.get(url, headers=headers)
            result = response.json()
            
            print(f"请求URL: {url}")
            print(f"响应状态: {response.status_code}")
            
            if response.status_code == 200:
                search_results = result.get('data', [])
                print(f"✅ 搜索产品成功，找到 {len(search_results)} 个结果")
                for product in search_results[:3]:  # 只显示前3个
                    print(f"  - {product['name']}")
            else:
                print(f"❌ 搜索产品失败: {result.get('message')}")
        except Exception as e:
            print(f"❌ 搜索产品异常: {e}")
    
    def run_complete_test(self):
        """运行完整测试流程"""
        print("🚀 开始ExampleProduct API OAuth2认证测试")
        print("=" * 60)
        
        try:
            # 设置测试数据
            self.setup_test_data()
            
            # 步骤1: 密码登录
            if not self.step1_password_login():
                return
            
            # 步骤2: OAuth2授权
            auth_code = self.step2_oauth2_authorize()
            if not auth_code:
                return
            
            # 步骤3: 授权码换取访问令牌
            if not self.step3_exchange_token(auth_code):
                return
            
            # 步骤4: 测试产品API
            created_product_id = self.step4_test_product_apis()
            
            # 步骤5: 测试高级API功能
            self.step5_test_advanced_apis()
            
            print("\n" + "=" * 60)
            print("🎉 ExampleProduct API OAuth2认证测试完成！")
            
            print("\n📝 测试总结:")
            print("✅ 密码登录 → 获取session_token")
            print("✅ OAuth2授权 → 获取authorization_code")
            print("✅ 令牌交换 → 获取access_token")
            print("✅ API调用 → 使用access_token访问受保护的API")
            print("✅ 权限控制 → 集成权限管理系统")
            print("✅ 审计日志 → 记录所有操作")
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现异常: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ExampleProduct API OAuth2认证测试')
    parser.add_argument('--base-url', default='http://localhost:8000', help='API基础URL')
    
    args = parser.parse_args()
    
    # 运行测试
    test = OAuth2ExampleProductAPITest(args.base_url)
    test.run_complete_test()


if __name__ == '__main__':
    main()
