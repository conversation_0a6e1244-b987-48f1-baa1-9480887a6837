"""
ZhiOAuth - OAuth2 认证授权模块

基于 Django 的 OAuth2 认证授权模块，集成了 zhi_common.zhi_auth.core_access 的 token 生成方式，
提供完整的 OAuth2 认证流程和 RBAC 权限管理。
"""

default_app_config = 'zhi_oauth.apps.ZhiOauthConfig'

__version__ = '1.0.0'

# 延迟导入，避免在模块导入时就初始化
def get_oauth2_service():
    """获取OAuth2服务实例"""
    from .controllers import oauth2_service
    return oauth2_service

def get_token_manager():
    """获取Token管理器实例"""
    from zhi_oauth.utils.token_manager import oauth2_token_manager
    return oauth2_token_manager

__all__ = [
    'get_oauth2_service',
    'get_token_manager',
]