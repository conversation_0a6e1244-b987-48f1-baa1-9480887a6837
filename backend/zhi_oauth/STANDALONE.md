# ZhiOAuth 独立运行指南

本文档说明如何独立运行和测试 ZhiOAuth 模块，无需依赖完整的 ZhiAdmin 系统。

## 1. 独立运行模式

ZhiOAuth 模块支持两种运行模式：

1. **集成模式**：作为 ZhiAdmin 系统的一部分运行
2. **独立模式**：作为独立的 Django 应用运行

在独立模式下，ZhiOAuth 会：
- 使用自己的设置文件 (`zhi_oauth/settings.py`)
- 使用自己的 URL 配置 (`zhi_oauth/urls.py`)
- 使用自己的数据库 (默认为 SQLite)
- 如果可用，会集成 zhi_logger 进行日志记录

## 2. 独立运行步骤

### 2.1 测试独立运行

运行测试脚本检查是否可以独立运行：

```bash
cd backend
python zhi_oauth/test_standalone.py
```

如果测试通过，说明 ZhiOAuth 可以独立运行。

### 2.2 初始化数据库

```bash
cd backend
python zhi_oauth/manage.py migrate
```

### 2.3 创建超级用户

```bash
cd backend
python zhi_oauth/manage.py createsuperuser
```

### 2.4 运行开发服务器

```bash
cd backend
python zhi_oauth/manage.py runserver 8001
```

现在可以访问 http://localhost:8001/admin/ 进入管理界面，或访问 http://localhost:8001/api/ 查看 API 文档。

## 3. 日志配置

ZhiOAuth 支持两种日志记录方式：

1. **使用 zhi_logger**：如果 zhi_logger 模块可用，日志会统一记录到 zhi_logger
2. **使用标准日志**：如果 zhi_logger 不可用，会使用 Django 的标准日志系统

### 3.1 查看日志

在独立运行模式下，日志会记录到：
- 控制台
- `backend/oauth.log` 文件

### 3.2 集成 zhi_logger

如果要使用 zhi_logger 进行日志记录，确保：

1. zhi_logger 模块已安装
2. 在 `settings.py` 中添加 zhi_logger 到 INSTALLED_APPS

## 4. API 文档

独立运行模式下，API 文档可以通过以下 URL 访问：

- API 文档：http://localhost:8001/oauth/docs
- OpenAPI 规范：http://localhost:8001/oauth/openapi.json

## 5. 测试 OAuth2 流程

### 5.1 创建 OAuth2 应用

通过管理界面或 API 创建 OAuth2 应用：

```python
from zhi_oauth.models import OAuthApplication

application = OAuthApplication.objects.create(
    name="测试应用",
    app_type="web",
    grant_types=["authorization_code", "refresh_token"],
    redirect_uris=["http://localhost:8000/callback"],
    allowed_scopes=["read", "write"],
)

print(f"Client ID: {application.client_id}")
print(f"Client Secret: {application.client_secret}")
```

### 5.2 授权码流程测试

1. 访问授权页面：
   ```
   http://localhost:8001/api/oauth/authorize?response_type=code&client_id=YOUR_CLIENT_ID&redirect_uri=http://localhost:8000/callback&scope=read
   ```

2. 用授权码换取令牌：
   ```bash
   curl -X POST http://localhost:8001/api/oauth/token \
     -d "grant_type=authorization_code" \
     -d "code=YOUR_AUTH_CODE" \
     -d "client_id=YOUR_CLIENT_ID" \
     -d "client_secret=YOUR_CLIENT_SECRET" \
     -d "redirect_uri=http://localhost:8000/callback"
   ```

3. 使用刷新令牌：
   ```bash
   curl -X POST http://localhost:8001/api/oauth/token \
     -d "grant_type=refresh_token" \
     -d "refresh_token=YOUR_REFRESH_TOKEN" \
     -d "client_id=YOUR_CLIENT_ID" \
     -d "client_secret=YOUR_CLIENT_SECRET"
   ```

## 6. 集成到其他项目

要将 ZhiOAuth 集成到其他 Django 项目中：

1. 将 zhi_oauth 目录复制到项目中
2. 在 settings.py 中添加：
   ```python
   INSTALLED_APPS = [
       # ...
       'zhi_oauth',
   ]
   ```
3. 在 urls.py 中添加：
   ```python
   from zhi_oauth.api import api as oauth_api
   
   urlpatterns = [
       # ...
       path('api/oauth/', oauth_api.urls),
   ]
   ```
4. 运行迁移：
   ```bash
   python manage.py migrate
   ```

## 7. 故障排除

### 7.1 导入错误

如果遇到导入错误，检查：
- Python 路径是否正确
- 所有依赖是否已安装
- 模块名称是否正确

### 7.2 数据库错误

如果遇到数据库错误：
- 检查数据库配置
- 确保已运行迁移
- 检查数据库权限

### 7.3 日志问题

如果日志不工作：
- 检查 LOGGING 配置
- 确保日志目录可写
- 如果使用 zhi_logger，确保它已正确配置
