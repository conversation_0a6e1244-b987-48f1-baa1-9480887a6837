# 增强后的认证API接口使用指南

## 概述

我已经为您完善了 `zhi_oauth.apis.auth.py` 中的登录接口，现在它能够返回完整的可认证token，包括OAuth2标准的access_token和refresh_token。

## 接口详情

### 登录接口

**端点**: `POST /api/auth/login`

**请求体**:
```json
{
    "username": "your_username",
    "password": "your_password", 
    "remember_me": true
}
```

**成功响应**:
```json
{
    "code": 200,
    "message": "登录成功，已获得完整认证令牌",
    "success": true,
    "data": {
        "session_token": "session_abc123...",
        "access_token": "access_xyz789...",
        "refresh_token": "refresh_def456...",
        "token_type": "Bearer",
        "expires_in": 604800,
        "scope": "read write",
        "user_info": {
            "id": 1,
            "username": "your_username",
            "name": "用户姓名",
            "email": "<EMAIL>",
            "avatar": "avatar_url",
            "is_superuser": false,
            "is_staff": false,
            "roles": [
                {
                    "id": 1,
                    "code": "admin",
                    "name": "管理员"
                }
            ]
        },
        "token_info": {
            "access_token_id": 123,
            "refresh_token_id": 456,
            "application": {
                "id": 1,
                "name": "默认认证应用",
                "client_id": "default_auth_client"
            }
        }
    },
    "trace_id": "uuid-string",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 令牌说明

### 1. Access Token (访问令牌)
- **用途**: 用于API认证
- **格式**: Bearer Token
- **过期时间**: 
  - 记住登录: 7天 (604800秒)
  - 普通登录: 2小时 (7200秒)
- **权限范围**: `read write`

### 2. Refresh Token (刷新令牌)
- **用途**: 用于刷新访问令牌
- **过期时间**: 30天
- **使用场景**: 当access_token过期时，使用refresh_token获取新的access_token

### 3. Session Token (会话令牌)
- **用途**: 用于会话管理和OAuth2授权流程
- **过期时间**: 与access_token相同

## 使用方式

### 1. 登录获取令牌

```javascript
// 前端登录请求
const loginResponse = await fetch('/api/auth/login', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        username: 'your_username',
        password: 'your_password',
        remember_me: true
    })
});

const result = await loginResponse.json();

if (result.success) {
    const { access_token, refresh_token, expires_in } = result.data;
    
    // 保存令牌到本地存储
    localStorage.setItem('access_token', access_token);
    localStorage.setItem('refresh_token', refresh_token);
    localStorage.setItem('token_expires_at', Date.now() + expires_in * 1000);
}
```

### 2. 使用Access Token进行API认证

```javascript
// 使用access_token调用受保护的API
const apiResponse = await fetch('/api/protected-endpoint', {
    method: 'GET',
    headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json',
    }
});
```

### 3. 令牌刷新（待实现）

```javascript
// 当access_token即将过期时，使用refresh_token刷新
const refreshResponse = await fetch('/api/auth/refresh', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        refresh_token: refresh_token
    })
});
```

## 技术实现

### 1. 默认OAuth2应用

系统会自动创建一个默认的OAuth2应用：
- **Client ID**: `default_auth_client`
- **应用名称**: `默认认证应用`
- **应用类型**: `confidential`
- **授权类型**: `authorization_code refresh_token password`
- **权限范围**: `read write`

### 2. 数据库存储

令牌信息会持久化存储到数据库：
- `OAuthAccessToken` - 访问令牌表
- `OAuthRefreshToken` - 刷新令牌表
- `OAuthApplication` - OAuth2应用表

### 3. 安全特性

- ✅ **令牌过期机制** - 访问令牌有明确的过期时间
- ✅ **刷新令牌** - 支持令牌刷新，无需重新登录
- ✅ **客户端信息记录** - 记录IP地址和User-Agent
- ✅ **用户状态验证** - 验证用户是否激活和未删除
- ✅ **应用关联** - 令牌与OAuth2应用关联
- ✅ **权限范围控制** - 支持scope权限控制

## 错误处理

### 登录失败响应

```json
{
    "code": 401,
    "message": "用户名或密码错误",
    "success": false,
    "data": {
        "error": "invalid_credentials"
    },
    "trace_id": "uuid-string",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 服务异常响应

```json
{
    "code": 500,
    "message": "登录服务异常，请稍后重试",
    "success": false,
    "data": {
        "error": "server_error"
    },
    "trace_id": "uuid-string",
    "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 后续扩展

### 1. 令牌刷新接口
可以添加 `/api/auth/refresh` 接口来支持令牌刷新：

```python
@http_post('/refresh')
def refresh_token(self, request: HttpRequest, data: RefreshTokenSchema):
    # 使用refresh_token获取新的access_token
    pass
```

### 2. 令牌撤销接口
可以添加 `/api/auth/revoke` 接口来撤销令牌：

```python
@http_post('/revoke')
def revoke_token(self, request: HttpRequest, data: RevokeTokenSchema):
    # 撤销指定的令牌
    pass
```

### 3. 令牌信息查询
可以添加 `/api/auth/token-info` 接口来查询令牌信息：

```python
@http_get('/token-info')
def token_info(self, request: HttpRequest):
    # 返回当前令牌的详细信息
    pass
```

## 总结

现在您的登录接口已经完全支持：

1. ✅ **完整的OAuth2令牌** - access_token、refresh_token
2. ✅ **标准的Bearer认证** - 符合OAuth2标准
3. ✅ **灵活的过期时间** - 支持记住登录
4. ✅ **完整的用户信息** - 包含角色信息
5. ✅ **数据库持久化** - 令牌安全存储
6. ✅ **统一响应格式** - 符合项目标准
7. ✅ **错误处理机制** - 完善的异常处理
8. ✅ **安全性保障** - 多重验证机制

您可以直接使用这个接口进行用户认证，获得的access_token可以用于所有需要认证的API调用！🎉
