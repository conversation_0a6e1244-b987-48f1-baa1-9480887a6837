"""
增强认证控制器 - 密码登录 + OAuth2统一认证
"""

from typing import Optional
from ninja_extra import api_controller, http_get, http_post
from ninja import Schema, Query
from django.http import HttpRequest, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from zhi_oauth.controllers.auth import auth_service
from zhi_common.zhi_logger import get_logger
from zhi_common.zhi_response.base import create_response, ZhiModelResponse
from zhi_common.zhi_consts.core_res_code import ResponseCode

logger = get_logger(module_name="auth_controller")


# ==================== Schema定义 ====================

class PasswordLoginSchema(Schema):
    """密码登录请求"""
    username: str
    password: str
    remember_me: bool = False


class PasswordLoginResponseSchema(Schema):
    """密码登录响应"""
    success: bool
    session_token: Optional[str] = None
    user_info: Optional[dict] = None
    expires_in: Optional[int] = None
    message: str
    error: Optional[str] = None


class OAuth2AuthorizeSchema(Schema):
    """OAuth2授权请求"""
    session_token: str
    client_id: str
    redirect_uri: str
    response_type: str = "code"
    scope: Optional[str] = None
    state: Optional[str] = None
    code_challenge: Optional[str] = None
    code_challenge_method: str = "S256"


class OAuth2AuthorizeResponseSchema(Schema):
    """OAuth2授权响应"""
    success: bool
    authorization_code: Optional[str] = None
    redirect_url: Optional[str] = None
    expires_in: Optional[int] = None
    message: str
    error: Optional[str] = None


class TokenExchangeSchema(Schema):
    """令牌交换请求"""
    grant_type: str
    code: Optional[str] = None
    client_id: str
    client_secret: str
    redirect_uri: Optional[str] = None
    code_verifier: Optional[str] = None
    refresh_token: Optional[str] = None


class TokenResponseSchema(Schema):
    """令牌响应"""
    success: bool
    access_token: Optional[str] = None
    token_type: str = "Bearer"
    expires_in: Optional[int] = None
    refresh_token: Optional[str] = None
    scope: Optional[str] = None
    message: str
    error: Optional[str] = None


class LogoutSchema(Schema):
    """登出请求"""
    session_token: Optional[str] = None
    access_token: Optional[str] = None


class LogoutResponseSchema(Schema):
    """登出响应"""
    success: bool
    message: str
    error: Optional[str] = None


class UserInfoResponseSchema(Schema):
    """用户信息响应"""
    success: bool
    user_info: Optional[dict] = None
    message: str
    error: Optional[str] = None


# ==================== 控制器定义 ====================

@api_controller( tags=['认证管理'])
class AuthControllerAPI:
    """认证控制器 - 密码登录 + OAuth2统一认证"""
    
    @method_decorator(csrf_exempt)
    @http_post('/login')
    def password_login(self, request: HttpRequest, data: PasswordLoginSchema):
        """
        密码登录

        支持用户名、邮箱、手机号登录
        登录成功后返回完整的认证token信息，包括access_token和refresh_token
        """
        try:
            # 获取客户端信息
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')

            # 执行密码登录
            result = auth_service.password_login(
                username=data.username,
                password=data.password,
                client_ip=client_ip,
                user_agent=user_agent,
                remember_me=data.remember_me
            )

            logger.info(f"密码登录请求: username={data.username}, success={result['success']}")

            if result['success']:
                # 登录成功，创建完整的OAuth2令牌
                token_info = self._create_complete_auth_tokens(
                    result=result,
                    client_ip=client_ip,
                    user_agent=user_agent,
                    remember_me=data.remember_me
                )

                # 正确合并数据：只取result['data']中的内容，避免重复
                result_data = result.get('data', {})
                complete_data = {
                    **result_data,  # 包含session_token和user_info
                    **token_info  # 包含access_token、refresh_token等
                }

                return create_response(
                    data=complete_data,
                    message='登录成功，已获得完整认证令牌'
                )
            else:
                return create_response(
                    data={'error': result['error']},
                    message=result['message'],
                    success=False,
                    code=ResponseCode.UNAUTHORIZED,
                    status_code=401
                )

        except Exception as e:
            logger.error(f"密码登录异常: {e}")
            return create_response(
                data={'error': 'server_error'},
                message='登录服务异常，请稍后重试',
                success=False,
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    @method_decorator(csrf_exempt)
    @http_post('/authorize')
    def oauth2_authorize(self, request: HttpRequest, data: OAuth2AuthorizeSchema):
        """
        OAuth2授权端点

        基于登录会话进行OAuth2授权，返回authorization_code
        """
        try:
            # 执行OAuth2授权
            result = auth_service.oauth2_authorize(
                session_token=data.session_token,
                client_id=data.client_id,
                redirect_uri=data.redirect_uri,
                scope=data.scope,
                state=data.state,
                code_challenge=data.code_challenge,
                code_challenge_method=data.code_challenge_method
            )

            logger.info(f"OAuth2授权请求: client_id={data.client_id}, success={result['success']}")

            if result['success']:
                return create_response(
                    data=result,
                    message='授权成功'
                )
            else:
                return create_response(
                    data={'error': result.get('error', 'authorization_failed')},
                    message=result.get('message', '授权失败'),
                    success=False,
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )

        except Exception as e:
            logger.error(f"OAuth2授权异常: {e}")
            return create_response(
                data={'error': 'server_error'},
                message='授权服务异常，请稍后重试',
                success=False,
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/token')
    def oauth2_token(self, request: HttpRequest, data: TokenExchangeSchema):
        """
        OAuth2令牌端点

        支持authorization_code和refresh_token两种grant_type
        """
        try:
            if data.grant_type == 'authorization_code':
                # 授权码换取令牌
                result = auth_service.exchange_code_for_token(
                    code=data.code,
                    client_id=data.client_id,
                    client_secret=data.client_secret,
                    redirect_uri=data.redirect_uri,
                    code_verifier=data.code_verifier
                )
            elif data.grant_type == 'refresh_token':
                # 刷新令牌
                result = auth_service.refresh_access_token(
                    refresh_token=data.refresh_token,
                    client_id=data.client_id,
                    client_secret=data.client_secret
                )
            else:
                return create_response(
                    data={'error': 'unsupported_grant_type'},
                    message=f'不支持的grant_type: {data.grant_type}',
                    success=False,
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )

            logger.info(f"令牌请求: grant_type={data.grant_type}, client_id={data.client_id}, success={result['success']}")

            if result['success']:
                return create_response(
                    data=result,
                    message='令牌获取成功'
                )
            else:
                return create_response(
                    data={'error': result.get('error', 'token_exchange_failed')},
                    message=result.get('message', '令牌交换失败'),
                    success=False,
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )

        except Exception as e:
            logger.error(f"令牌交换异常: {e}")
            return create_response(
                data={'error': 'server_error'},
                message='令牌交换异常',
                success=False,
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @method_decorator(csrf_exempt)
    @http_post('/logout', response=LogoutResponseSchema)
    def logout(self, request: HttpRequest, data: LogoutSchema):
        """
        登出
        
        清除登录会话和OAuth2令牌
        """
        try:
            result = auth_service.logout(
                session_token=data.session_token,
                access_token=data.access_token
            )
            
            logger.info("用户登出请求")
            
            return create_response(data=result)
            
        except Exception as e:
            logger.error(f"登出异常: {e}")
            return create_response(
                data={'error': 'server_error'},
                message='登出服务异常',
                success=False,
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('/user/info')
    def get_user_info(self, request: HttpRequest, session_token: str = Query(None)):
        """
        获取用户信息

        基于session_token获取当前登录用户信息
        """
        try:
            if not session_token:
                return create_response(
                    data={'error': 'missing_token'},
                    message='缺少session_token参数',
                    success=False,
                    code=ResponseCode.BAD_REQUEST,
                    status_code=400
                )
            # 验证会话令牌
            user = auth_service._validate_session_token(session_token)
            if not user:
                return create_response(
                    data={'error': 'invalid_session'},
                    message='登录会话无效或已过期',
                    success=False,
                    code=ResponseCode.UNAUTHORIZED,
                    status_code=401
                )

            # 返回用户信息
            user_info = {
                'id': user.id,
                'username': user.username,
                'name': user.name,
                'email': user.email,
                'mobile': user.mobile,
                'avatar': user.avatar,
                'is_superuser': user.is_superuser,
                'is_staff': user.is_staff,
                'date_joined': user.date_joined.isoformat(),
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'roles': [
                    {
                        'id': role.id,
                        'code': role.code,
                        'name': role.name
                    }
                    for role in user.roles.filter(is_active=True)
                ]
            }
            return create_response(data=user_info)
            
        except Exception as e:
            logger.error(f"获取用户信息异常: {e}")
            return create_response(
                data={'error': 'server_error'},
                message='获取用户信息失败',
                success=False,
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    @http_get('applications')
    def get_oauth_applications(self, request: HttpRequest, session_token: str = Query(...)):
        """
        获取用户可访问的OAuth2应用列表
        """
        try:
            # 验证会话令牌
            user = auth_service._validate_session_token(session_token)
            if not user:
                return create_response(
                    data={'error': 'invalid_session'},
                    message='登录会话无效或已过期',
                    success=False,
                    code=ResponseCode.UNAUTHORIZED,
                    status_code=401
                )
            
            # 获取应用列表（这里可以根据用户权限过滤）
            from zhi_oauth.models import OAuthApplication
            
            applications = OAuthApplication.objects.filter(
                is_active=True,
                is_deleted=False
            ).values(
                'id', 'name', 'client_id', 'app_type', 
                'description', 'logo_url', 'website_url'
            )
            return create_response(data=list(applications))
            
        except Exception as e:
            logger.error(f"获取应用列表异常: {e}")
            return create_response(success=False, message='获取应用列表失败', data=[])

    @http_get('/session/validate')
    def validate_session(self, request: HttpRequest, session_token: str = Query(...)):
        """
        验证登录会话
        """
        try:
            user = auth_service._validate_session_token(session_token)
            
            if user:
                return create_response(data={
                    'success': True,
                    'valid': True,
                    'user_id': user.id,
                    'username': user.username,
                    'message': '会话有效'
                })
            else:
                return create_response(
                    data={'valid': False},
                    message='会话无效或已过期',
                    success=False
                )
                
        except Exception as e:
            logger.error(f"会话验证异常: {e}")
            return create_response(
                data={'error': 'server_error'},
                message='会话验证失败',
                success=False,
                code=ResponseCode.INTERNAL_ERROR,
                status_code=500
            )
    
    # ==================== 私有方法 ====================
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '')
        return ip

    def _create_complete_auth_tokens(
        self,
        result: dict,
        client_ip: str,
        user_agent: str,
        remember_me: bool
    ) -> dict:
        """
        创建完整的认证令牌信息

        基于登录结果创建OAuth2访问令牌和刷新令牌
        """
        try:
            from zhi_oauth.models import User, OAuthApplication, OAuthAccessToken, OAuthRefreshToken
            from django.utils import timezone
            from datetime import timedelta
            from django.db import transaction

            # 从登录结果中获取用户信息
            result_data = result.get('data', {})
            user_info = result_data.get('user_info', {})
            user_id = user_info.get('id')

            if not user_id:
                logger.error(f"无法从登录结果中获取用户ID，登录结果: {result}")
                return self._create_fallback_tokens()

            # 获取用户对象
            try:
                user = User.objects.get(id=user_id, is_active=True, is_deleted=False)
            except User.DoesNotExist:
                logger.error(f"用户不存在: {user_id}")
                return self._create_fallback_tokens()

            # 获取或创建默认OAuth2应用
            default_app = self._get_or_create_default_oauth_app()

            # 设置令牌过期时间
            access_token_expires = 3600 * 24 * 7 if remember_me else 3600 * 2  # 记住登录7天，否则2小时
            refresh_token_expires = 3600 * 24 * 30  # 刷新令牌30天

            # 创建令牌字符串
            access_token = auth_service.token_manager.create_access_token(pre_fix='access')
            refresh_token = auth_service.token_manager.create_access_token(pre_fix='refresh')

            # 保存到数据库
            with transaction.atomic():
                # 创建访问令牌记录
                access_token_obj = OAuthAccessToken.objects.create(
                    token=access_token,
                    user=user,
                    application=default_app,
                    scope=['read', 'write'],
                    expires_at=timezone.now() + timedelta(seconds=access_token_expires),
                    ip_address=client_ip,
                    user_agent=user_agent
                )

                # 创建刷新令牌记录
                refresh_token_obj = OAuthRefreshToken.objects.create(
                    token=refresh_token,
                    access_token=access_token_obj,
                    expires_at=timezone.now() + timedelta(seconds=refresh_token_expires)
                )

            logger.info(f"创建完整认证令牌成功: user={user.username}, token={access_token[:10]}...")

            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'token_type': 'Bearer',
                'expires_in': access_token_expires,
                'scope': 'read write',
                'token_info': {
                    'access_token_id': access_token_obj.id,
                    'refresh_token_id': refresh_token_obj.id,
                    'application': {
                        'id': default_app.id,
                        'name': default_app.name,
                        'client_id': default_app.client_id
                    }
                }
            }

        except Exception as e:
            logger.error(f"创建完整认证令牌失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return self._create_fallback_tokens()

    def _create_fallback_tokens(self) -> dict:
        """创建备用令牌（当主要流程失败时）"""
        return {
            'access_token': auth_service.token_manager.create_access_token(pre_fix='fallback'),
            'refresh_token': auth_service.token_manager.create_access_token(pre_fix='fallback_refresh'),
            'token_type': 'Bearer',
            'expires_in': 3600,
            'scope': 'read',
            'token_info': {
                'note': '备用令牌，功能受限'
            }
        }

    def _get_or_create_default_oauth_app(self):
        """获取或创建默认OAuth2应用"""
        try:
            from zhi_oauth.models import OAuthApplication

            # 尝试获取默认应用
            default_app = OAuthApplication.objects.filter(
                client_id='default_auth_client',
                is_active=True,
                is_deleted=False
            ).first()

            if not default_app:
                # 创建默认应用
                default_app = OAuthApplication.objects.create(
                    name='默认认证应用',
                    client_id='default_auth_client',
                    client_secret=auth_service.token_manager.create_access_token(pre_fix='secret'),
                    app_type='confidential',
                    grant_types='authorization_code refresh_token password',
                    redirect_uris='http://localhost:3000/callback',
                    allowed_scopes='read write',
                    description='系统默认OAuth2应用，用于密码登录后的直接认证',
                    is_active=True
                )
                logger.info(f"创建默认OAuth2应用: {default_app.client_id}")

            return default_app

        except Exception as e:
            logger.error(f"获取或创建默认OAuth2应用失败: {e}")
            raise
