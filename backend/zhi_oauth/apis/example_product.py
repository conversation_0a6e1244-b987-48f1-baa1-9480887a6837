from pydantic import BaseModel
from typing import List, Any, Dict
from ninja import Field, ModelSchema
from ninja_extra import http_get, http_post, route
from django.http import HttpResponse
from django.core.files.uploadedfile import UploadedFile
from zhi_common.zhi_response.base import ZhiResponse
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_response.schemas.base import (
    BaseResponseSchema,
    ListIDValueMappingSchema,
    ExportRequestSchema,
    ImportResultSchema,
    ImportPreviewSchema
)
from zhi_common.zhi_api.base_api import auto_crud_api, api_route
from zhi_common.zhi_api.base_config import BASE_SCHEMA_IN_EXCLUDE_FIELDS
from zhi_common.zhi_api.zhi_crud import (
    BaseModelService,
)
from zhi_oauth.models import ExampleProduct


class ExampleProductSchemaIn(ModelSchema):
    """产品创建/更新Schema"""
    class Meta:
        model = ExampleProduct
        exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS


class ExampleProductSchemaOut(ModelSchema):
    class Meta:
        model = ExampleProduct
        fields = '__all__'


# 自定义字段验证器
def validate_name(value):
    """产品名称验证器"""
    if not value or not value.strip():
        raise ValueError("产品名称不能为空")
    if len(value.strip()) < 2:
        raise ValueError("产品名称至少需要2个字符")
    if len(value.strip()) > 100:
        raise ValueError("产品名称不能超过100个字符")
    return value.strip()


def validate_description(value):
    """产品描述验证器"""
    if value and len(value.strip()) > 1000:
        raise ValueError("产品描述不能超过1000个字符")
    return value.strip() if value else ""


@auto_crud_api(
    ExampleProduct,
    prefix="ExampleProduct",
    tags=["示例产品"],
    schema_in=ExampleProductSchemaIn,
    schema_out=ExampleProductSchemaOut,
    exclude=[]  # 排除自动生成的，使用显式定义的
    )
class ExampleProductControllerAPI(BaseModelService):
    """示例产品服务 - 支持导入导出功能"""
    model = ExampleProduct
    model_exclude = BASE_SCHEMA_IN_EXCLUDE_FIELDS

    # ID映射配置
    mappings_settings = {
        "id_key": 'id',
        "id_value": "name",
        "is_enabled": True,
    }

    # 导出配置
    export_settings = {
        'enabled': True,  # 启用导出功能
        'max_rows': 5000,  # 最大导出行数
        'fields': ['name', 'description', 'created_at', 'updated_at'],  # 允许导出的字段
        'field_mapping': {  # 字段中文映射
            'name': '产品名称',
            'description': '产品描述',
            'created_at': '创建时间',
            'updated_at': '更新时间',
            'creator_name': '创建人',
            'modifier_name': '修改人'
        }
    }

    # 导入配置
    import_settings = {
        'enabled': True,  # 启用导入功能
        'max_rows': 1000,  # 最大导入行数
        'max_file_size': 2 * 1024 * 1024,  # 最大文件大小 2MB
        'sheet_name': None,  # Excel工作表名称，None表示使用活动工作表
        'field_mapping': {  # 字段映射：文件字段名 -> 模型字段名
            '产品名称': 'name',
            '产品描述': 'description',
            # 支持英文字段名
            'name': 'name',
            'description': 'description',
            'Name': 'name',
            'Description': 'description'
        },
        'field_validators': {  # 字段验证器
            'name': validate_name,
            'description': validate_description
        }
    }

    @api_route(http_get, "/v1/custom_method1", response={200: BaseResponseSchema[List[ExampleProductSchemaOut]]})
    def user_custom_method(self, request, code: str):
        """
        自定义方法
        """
        model = getattr(self, 'model', None)
        items = list(model.objects.filter(code=code).values())
        return ZhiResponse(
            data=items,
            message="查询成功"
            )

    @api_route(http_get, "/export", response=None)
    def export_products(self, request):
        """
        导出示例产品数据

        支持的格式：csv, excel, json
        查询参数：
        - format: 导出格式 (csv/excel/json，默认excel)
        - filename: 文件名（可选）
        - fields: 要导出的字段列表（可选，逗号分隔）
        - name: 按产品名称过滤（可选）
        """
        # 从查询参数获取导出配置
        format_type = request.GET.get('format', 'excel')
        filename = request.GET.get('filename', None)
        fields_param = request.GET.get('fields', None)
        fields = fields_param.split(',') if fields_param else None

        # 构建过滤条件
        filters = {}
        if request.GET.get('name'):
            filters['name__icontains'] = request.GET.get('name')

        return self.export_data(
            format_type=format_type,
            filename=filename,
            filters=filters,
            fields=fields
        )

    @api_route(http_post, "/import", response={200: BaseResponseSchema[ImportResultSchema]})
    def import_products(self, request):
        """
        导入示例产品数据

        支持的格式：csv, excel
        表单参数：
        - file: 上传的文件
        - preview_only: 是否仅预览（可选，默认false）
        """
        # 从request.FILES获取上传的文件
        file = request.FILES.get('file')
        if not file:
            return ZhiResponse(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message="请上传文件",
                success=False
            )

        preview_only = request.POST.get('preview_only', 'false').lower() == 'true'

        return self.import_data(
            file=file,
            preview_only=preview_only
        )

    @api_route(http_get, "/import/template", response=None)
    def get_import_template(self, request):
        """
        下载导入模板

        支持的格式：csv, excel
        查询参数：
        - format: 模板格式 (csv/excel，默认excel)
        """
        format_type = request.GET.get('format', 'excel')
        return self.get_import_template(format_type=format_type)

    @api_route(http_get, "/export/fields", response={200: BaseResponseSchema[List[Dict[str, Any]]]})
    def get_export_fields(self, request):
        """
        获取可导出的字段列表

        返回字段信息包括：
        - name: 字段名
        - verbose_name: 字段显示名
        - type: 字段类型
        - required: 是否必填
        """
        return self.get_export_fields()

    @api_route(http_post, "/import/preview", response={200: BaseResponseSchema[ImportPreviewSchema]})
    def preview_import(self, request):
        """
        预览导入数据

        上传文件并预览解析结果，不实际导入数据
        表单参数：
        - file: 上传的文件
        """
        # 从request.FILES获取上传的文件
        file = request.FILES.get('file')
        if not file:
            return ZhiResponse(
                data=None,
                code=ResponseCode.BAD_REQUEST,
                message="请上传文件",
                success=False
            )

        return self.import_data(file=file, preview_only=True)
