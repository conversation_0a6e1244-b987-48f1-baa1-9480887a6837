"""
增强型OAuth2认证系统 - 基于旧项目经验重构
集成缓存、权限管理、数据过滤等功能
"""

import re
from typing import Optional, Dict, Any
from ninja.security import HttpBearer 
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model

# 导入标准化响应和异常
from zhi_common.zhi_consts.core_res_code import ResponseCode
from zhi_common.zhi_logger.core_logger import zhi_logger

# 导入权限管理 - 使用正确路径
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
from zhi_common.zhi_auth.permission_audit_system import permission_audit_logger

PERMISSION_SYSTEM_AVAILABLE = True

# 导入模型
from ..models import OAuthAccessToken

User = get_user_model()

# HTTP方法映射 - 基于旧项目
HTTP_METHODS = {
    'GET': 0,
    'POST': 1,
    'PUT': 2,
    'DELETE': 3,
    'PATCH': 4,
    'HEAD': 5,
    'OPTIONS': 6,
}

# UUID模式匹配 - 基于旧项目
UUID_PATTERN = re.compile(r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')

# Token缓存配置
TOKEN_CACHE_PREFIX = 'oauth_token:'
TOKEN_CACHE_TTL = 3600  # 1小时


class ZhiTokenException(Exception):
    """Token异常类"""
    def __init__(self, message: str, code: int = ResponseCode.UNAUTHORIZED):
        self.message = message
        self.code = code
        super().__init__(message)


class DataPermissionService:
    """数据权限服务 - 基于旧项目经验"""
    def __init__(self, user):
        self.user = user
    
    def get_data_filters(self) -> Dict[str, Any]:
        """获取数据过滤条件"""
        filters = {'is_deleted': False}
        
        # 基于用户角色和组织的数据过滤
        if hasattr(self.user, 'org_id') and self.user.org_id:
            filters['org_id'] = self.user.org_id
        
        # 如果不是超级用户，添加创建者过滤
        if not self.user.is_superuser:
            filters['creator_id'] = str(self.user.id)
        
        return filters


class EnhancedGlobalOAuth2(HttpBearer):
    """增强型全局OAuth2认证 - 基于旧项目经验重构"""
    
    def __init__(self, enable_cache: bool = True, cache_ttl: int = TOKEN_CACHE_TTL):
        super().__init__()
        self.enable_cache = enable_cache
        self.cache_ttl = cache_ttl
        zhi_logger.info(f"EnhancedGlobalOAuth2 初始化完成，缓存: {enable_cache}")
    
    def authenticate(self, request, token: str) -> Optional[Dict[str, Any]]:
        """
        增强型认证方法 - 基于旧项目经验优化
        - 支持token缓存
        - 集成权限管理
        - 自动数据权限过滤
        """
        try:
            zhi_logger.debug(f"开始认证 token: {token[:20] if token else 'None'}...")
            
            # 验证token格式 - 基于旧项目逻辑
            if not self._validate_token_format(token):
                raise ZhiTokenException(message='无效Token格式')
            
            # 尝试从缓存获取用户信息
            user_info = None
            if self.enable_cache:
                user_info = self._get_cached_user_info(token)
            
            # 缓存未命中，从数据库获取
            if not user_info:
                user_info = self._authenticate_from_database(token)
                
                # 缓存用户信息
                if self.enable_cache and user_info:
                    self._cache_user_info(token, user_info)
            
            # 设置请求上下文 - 基于旧项目方式
            self._setup_request_context(request, user_info, token)
            
            zhi_logger.debug(f"认证成功，用户: {user_info.get('username', 'unknown')}")
            return user_info
            
        except ZhiTokenException:
            raise
        except Exception as e:
            zhi_logger.error(f"认证过程异常: {str(e)}")
            raise ZhiTokenException(message='认证服务异常')
    
    def _validate_token_format(self, token: str) -> bool:
        """验证token格式 - 基于旧项目逻辑"""
        if not token or len(token) < 10:
            return False
        
        # 检查是否包含oauth2标识 - 保持旧项目兼容性
        if 'oauth2' not in token.lower():
            return False
        
        return True
    
    def _get_cached_user_info(self, token: str) -> Optional[Dict[str, Any]]:
        """从缓存获取用户信息"""
        try:
            cache_key = f"{TOKEN_CACHE_PREFIX}{token}"
            user_info = cache.get(cache_key)
            
            if user_info:
                zhi_logger.debug("从缓存获取用户信息成功")
                return user_info
                
        except Exception as e:
            zhi_logger.warning(f"缓存获取失败: {str(e)}")
        
        return None
    
    def _authenticate_from_database(self, token: str) -> Dict[str, Any]:
        """从数据库认证token - 基于旧项目逻辑"""
        try:
            # 获取token对象 - 保持旧项目逻辑
            token_obj = OAuthAccessToken.objects.select_related('user').get(token=token)
            
            if not token_obj:
                raise ZhiTokenException(message='无效Token')
            
            # 检查token是否过期 - 保持旧项目逻辑
            now_time = timezone.now()
            if token_obj.expires <= now_time:
                raise ZhiTokenException(message='token已过期')
            
            # 检查用户状态
            user = token_obj.user
            if not user.is_active:
                raise ZhiTokenException(message='用户已被禁用')
            
            # 构建用户信息 - 基于旧项目的to_dict方式
            user_info = self._build_user_info(user, token_obj)
            
            return user_info
            
        except OAuthAccessToken.DoesNotExist:
            raise ZhiTokenException(message='无效Token')
    
    def _build_user_info(self, user: User, token_obj: OAuthAccessToken) -> Dict[str, Any]:
        """构建用户信息字典 - 基于旧项目的to_dict方式"""
        # 优先使用用户模型的to_dict方法 - 保持旧项目兼容性
        if hasattr(user, 'to_dict'):
            try:
                user_info = user.to_dict
                if isinstance(user_info, dict):
                    # 添加token相关信息
                    user_info.update({
                        'token_expires': token_obj.expires.isoformat(),
                        'token': token_obj.token,
                    })
                    return user_info
            except Exception as e:
                zhi_logger.warning(f"获取用户to_dict失败: {str(e)}")
        
        # 回退到基础用户信息
        user_info = {
            'id': str(user.id),
            'username': user.username,
            'email': getattr(user, 'email', ''),
            'is_active': user.is_active,
            'is_staff': user.is_staff,
            'is_superuser': user.is_superuser,
            'expires_at': token_obj.expires_at.isoformat(),
            'token': token_obj.token,
            'last_login': user.last_login.isoformat() if user.last_login else None,
        }
        
        return user_info
    
    def _cache_user_info(self, token: str, user_info: Dict[str, Any]):
        """缓存用户信息"""
        try:
            cache_key = f"{TOKEN_CACHE_PREFIX}{token}"
            cache.set(cache_key, user_info, self.cache_ttl)
            zhi_logger.debug("用户信息缓存成功")
        except Exception as e:
            zhi_logger.warning(f"缓存设置失败: {str(e)}")
    
    def _setup_request_context(self, request, user_info: Dict[str, Any], token: str):
        """设置请求上下文 - 基于旧项目方式"""
        # 设置用户对象 - 保持旧项目兼容性
        try:
            user = User.objects.get(id=user_info['id'])
            request.user = user  # 基于旧项目方式
            request.oauth_user = user
        except User.DoesNotExist:
            zhi_logger.error(f"用户不存在: {user_info['id']}")
            raise ZhiTokenException(message='用户不存在')
        
        # 设置用户信息 - 基于旧项目方式
        request.user_info = user_info
        
        # 初始化数据权限服务 - 基于旧项目经验
        data_perm_service = DataPermissionService(user)
        request.data_permission_service = data_perm_service
        request.data_filters = data_perm_service.get_data_filters()
        
        # 设置权限上下文
        request.permission_context = {
            'user_id': user_info['id'],
            'username': user_info['username'],
            'is_superuser': user_info.get('is_superuser', False),
            'method': request.method,
            'path': request.path,
            'token': token,
        }
    
    @staticmethod
    def clear_token_cache(token: str):
        """清除token缓存"""
        try:
            cache_key = f"{TOKEN_CACHE_PREFIX}{token}"
            cache.delete(cache_key)
            zhi_logger.debug(f"Token缓存已清除: {token[:20]}...")
        except Exception as e:
            zhi_logger.warning(f"清除缓存失败: {str(e)}")


# 创建全局认证实例 - 基于旧项目方式
enhanced_global_oauth2 = EnhancedGlobalOAuth2(enable_cache=True)

# 保持旧项目兼容性
GlobalOAuth2 = EnhancedGlobalOAuth2
