"""
ZhiOAuth 子项目审计配置

OAuth2认证系统的审计日志配置
重点关注用户认证、授权和令牌管理的安全审计
"""

# ZhiOAuth 子项目的审计日志配置
AUDIT_LOG_CONFIG = {
    # User 模型配置
    'User': {
        'is_enabled': True,
        'using_fields': {
            'username': {
                'field_name': '用户名',
                'is_enabled': True,
                'is_important': True,
            },
            'email': {
                'field_name': '邮箱',
                'is_enabled': True,
                'is_important': True,
            },
            'first_name': {
                'field_name': '名字',
                'is_enabled': True,
                'is_important': False,
            },
            'last_name': {
                'field_name': '姓氏',
                'is_enabled': True,
                'is_important': False,
            },
            'is_active': {
                'field_name': '是否激活',
                'is_enabled': True,
                'is_important': True,
            },
            'is_staff': {
                'field_name': '是否员工',
                'is_enabled': True,
                'is_important': True,
            },
            'is_superuser': {
                'field_name': '是否超级用户',
                'is_enabled': True,
                'is_important': True,
            },
            'last_login': {
                'field_name': '最后登录',
                'is_enabled': False,
                'is_important': False,
            },
            'date_joined': {
                'field_name': '注册时间',
                'is_enabled': True,
                'is_important': False,
            },
            # 敏感字段不记录
            'password': {
                'field_name': '密码',
                'is_enabled': False,  # 密码变更不记录具体值
                'is_important': False,
            },
        }
    },
    
    # Application 模型配置 (OAuth2应用)
    'Application': {
        'is_enabled': True,
        'using_fields': {
            'name': {
                'field_name': '应用名称',
                'is_enabled': True,
                'is_important': True,
            },
            'client_id': {
                'field_name': '客户端ID',
                'is_enabled': True,
                'is_important': True,
            },
            'client_type': {
                'field_name': '客户端类型',
                'is_enabled': True,
                'is_important': True,
            },
            'authorization_grant_type': {
                'field_name': '授权类型',
                'is_enabled': True,
                'is_important': True,
            },
            'client_secret': {
                'field_name': '客户端密钥',
                'is_enabled': False,  # 密钥不记录具体值
                'is_important': True,
            },
            'redirect_uris': {
                'field_name': '重定向URI',
                'is_enabled': True,
                'is_important': True,
            },
            'post_logout_redirect_uris': {
                'field_name': '登出重定向URI',
                'is_enabled': True,
                'is_important': False,
            },
            'algorithm': {
                'field_name': '算法',
                'is_enabled': True,
                'is_important': True,
            },
        }
    },
    
    # AccessToken 模型配置
    'AccessToken': {
        'is_enabled': True,
        'using_fields': {
            'user': {
                'field_name': '用户',
                'is_enabled': True,
                'is_important': True,
            },
            'application': {
                'field_name': '应用',
                'is_enabled': True,
                'is_important': True,
            },
            'token': {
                'field_name': '令牌',
                'is_enabled': False,  # 令牌值不记录
                'is_important': True,
            },
            'expires': {
                'field_name': '过期时间',
                'is_enabled': True,
                'is_important': True,
            },
            'scope': {
                'field_name': '权限范围',
                'is_enabled': True,
                'is_important': True,
            },
            'created': {
                'field_name': '创建时间',
                'is_enabled': True,
                'is_important': False,
            },
            'updated': {
                'field_name': '更新时间',
                'is_enabled': True,
                'is_important': False,
            },
        }
    },
    
    # RefreshToken 模型配置
    'RefreshToken': {
        'is_enabled': True,
        'using_fields': {
            'user': {
                'field_name': '用户',
                'is_enabled': True,
                'is_important': True,
            },
            'application': {
                'field_name': '应用',
                'is_enabled': True,
                'is_important': True,
            },
            'token': {
                'field_name': '刷新令牌',
                'is_enabled': False,  # 令牌值不记录
                'is_important': True,
            },
            'access_token': {
                'field_name': '访问令牌',
                'is_enabled': True,
                'is_important': True,
            },
            'created': {
                'field_name': '创建时间',
                'is_enabled': True,
                'is_important': False,
            },
            'updated': {
                'field_name': '更新时间',
                'is_enabled': True,
                'is_important': False,
            },
            'revoked': {
                'field_name': '撤销时间',
                'is_enabled': True,
                'is_important': True,
            },
        }
    },
    
    # Grant 模型配置
    'Grant': {
        'is_enabled': True,
        'using_fields': {
            'user': {
                'field_name': '用户',
                'is_enabled': True,
                'is_important': True,
            },
            'application': {
                'field_name': '应用',
                'is_enabled': True,
                'is_important': True,
            },
            'code': {
                'field_name': '授权码',
                'is_enabled': False,  # 授权码不记录
                'is_important': True,
            },
            'expires': {
                'field_name': '过期时间',
                'is_enabled': True,
                'is_important': True,
            },
            'redirect_uri': {
                'field_name': '重定向URI',
                'is_enabled': True,
                'is_important': True,
            },
            'scope': {
                'field_name': '权限范围',
                'is_enabled': True,
                'is_important': True,
            },
            'created': {
                'field_name': '创建时间',
                'is_enabled': True,
                'is_important': False,
            },
            'updated': {
                'field_name': '更新时间',
                'is_enabled': True,
                'is_important': False,
            },
        }
    },
}

# OAuth子项目审计配置的元数据
AUDIT_CONFIG_META = {
    'version': '1.0.0',
    'project': 'zhi_oauth',
    'description': 'ZhiOAuth 子项目审计配置',
    'last_updated': '2025-07-22',
    'maintainer': 'ZhiAdmin Team',
    
    # OAuth特定配置选项
    'options': {
        # 是否记录令牌操作
        'log_token_operations': True,
        
        # 是否记录授权操作
        'log_authorization_operations': True,
        
        # 是否记录用户登录/登出
        'log_user_sessions': True,
        
        # 是否记录应用管理操作
        'log_application_management': True,
        
        # 审计日志保留天数（安全相关保留更久）
        'audit_retention_days': 180,
        
        # 是否异步记录审计日志
        'async_audit': True,
        
        # 是否使用Celery任务
        'use_celery': True,
        
        # 审计日志级别
        'audit_log_level': 'INFO',
        
        # 是否记录敏感字段变更（OAuth中很重要）
        'log_sensitive_fields': True,
        
        # OAuth敏感字段列表
        'sensitive_fields': [
            'password',
            'client_secret',
            'token',
            'code',
            'refresh_token',
            'access_token',
        ],
        
        # 是否启用安全事件特殊处理
        'enable_security_events': True,
        
        # 安全事件类型
        'security_events': [
            'user_login_failed',
            'user_locked',
            'token_revoked',
            'application_created',
            'application_deleted',
            'permission_changed',
        ],
    }
}

# OAuth特定的审计函数
def log_oauth_security_event(event_type: str, user_id: str = None, application_id: str = None, details: dict = None):
    """
    记录OAuth安全事件
    
    Args:
        event_type: 事件类型
        user_id: 用户ID
        application_id: 应用ID
        details: 事件详情
    """
    from zhi_common.zhi_logger import get_logger
    
    logger = get_logger(__name__, module_name="oauth_security")
    
    logger.warning(
        f"OAuth安全事件: {event_type}",
        category="security",
        user_id=user_id,
        extra_data={
            'event_type': event_type,
            'application_id': application_id,
            'details': details or {},
            'project': 'zhi_oauth'
        }
    )


def log_token_operation(operation: str, user_id: str, application_id: str, token_type: str = 'access_token'):
    """
    记录令牌操作
    
    Args:
        operation: 操作类型 (create, refresh, revoke)
        user_id: 用户ID
        application_id: 应用ID
        token_type: 令牌类型
    """
    from zhi_common.zhi_logger import get_logger
    
    logger = get_logger(__name__, module_name="oauth_token")
    
    logger.info(
        f"令牌操作: {operation}",
        category="auth",
        user_id=user_id,
        extra_data={
            'operation': operation,
            'application_id': application_id,
            'token_type': token_type,
            'project': 'zhi_oauth'
        }
    )


def log_authorization_event(event: str, user_id: str, application_id: str, scope: str = None):
    """
    记录授权事件
    
    Args:
        event: 事件类型 (grant, deny, revoke)
        user_id: 用户ID
        application_id: 应用ID
        scope: 权限范围
    """
    from zhi_common.zhi_logger import get_logger
    
    logger = get_logger(__name__, module_name="oauth_auth")
    
    logger.info(
        f"授权事件: {event}",
        category="auth",
        user_id=user_id,
        extra_data={
            'event': event,
            'application_id': application_id,
            'scope': scope,
            'project': 'zhi_oauth'
        }
    )
