from django.apps import AppConfig


class ZhiOauthConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'zhi_oauth'
    verbose_name = '<PERSON><PERSON><PERSON><PERSON><PERSON> OAuth2认证'

    def ready(self):
        """应用准备就绪时的初始化"""
        # 导入信号处理器
        try:
            from . import signals  # 如果有信号处理器
        except ImportError:
            pass

        # 注册日志记录
        try:
            from zhi_common.zhi_logger import get_logger
            logger = get_logger(module_name="zhi_oauth")
            logger.info("ZhiOAuth 模块已加载")
        except ImportError:
            pass
