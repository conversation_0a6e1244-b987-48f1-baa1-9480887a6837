# ExampleProduct 重构后使用指南

## 概述

ExampleProduct已完全重构，采用增强的APIService，提供标准化响应和完整的权限管理。

## 重构亮点

### ✅ 完全采用EnhancedModelService
- 继承自`zhi_common.zhi_services.enhanced_model_service.EnhancedModelService`
- 自动集成权限管理、审计日志、数据序列化等功能

### ✅ 标准化响应格式
- 所有API返回统一的响应格式：`{code, message, success, trace_id, timestamp, data}`
- 使用`ResponseCode`枚举，符合zhi_oauth标准

### ✅ 权限管理集成
- 自动进行权限检查和数据过滤
- 支持字段级权限控制
- 完整的审计日志记录

## API端点

### 1. 分页列表查询
```http
GET /api/v1/example-products/list_pagination?page=1&page_size=10&name=test&ordering=-created_at
```

**响应格式：**
```json
{
  "code": 2000,
  "message": "ok",
  "success": true,
  "trace_id": "xxx-xxx-xxx",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": {
    "items": [...],
    "total": 100,
    "page": 1,
    "page_size": 10,
    "pages": 10
  }
}
```

### 2. 创建产品
```http
POST /api/v1/example-products/
Content-Type: application/json

{
  "name": "新产品",
  "description": "产品描述"
}
```

### 3. 详情查询
```http
GET /api/v1/example-products/{id}
```

### 4. 更新产品
```http
PUT /api/v1/example-products/{id}
Content-Type: application/json

{
  "name": "更新后的名称",
  "description": "更新后的描述"
}
```

### 5. 删除产品
```http
DELETE /api/v1/example-products/{id}
```

### 6. 统计信息
```http
GET /api/v1/example-products/stats?name_filter=test
```

### 7. 搜索产品
```http
GET /api/v1/example-products/search?keyword=关键词&limit=10
```

### 8. 批量更新
```http
POST /api/v1/example-products/bulk-update-names
Content-Type: application/json

{
  "updates": [
    {"id": "1", "name": "新名称1"},
    {"id": "2", "name": "新名称2"}
  ]
}
```

### 9. 映射列表
```http
GET /api/v1/example-products/mappings?is_all=true
```

## 服务层使用

### 直接使用服务

```python
from zhi_oauth.controllers.example_product import ExampleProductService

# 初始化服务
service = ExampleProductService()

# 设置用户上下文
service.set_user_context(user)

# 调用方法
response = service.list_with_pagination(
    user=user,
    filters={'name__icontains': 'test'},
    page=1,
    page_size=10
    )

# 响应是标准化的ZhiResponse对象
print(response.content)  # 获取响应内容
```

### 权限配置
服务已预配置以下权限：
- `example_product.view` - 查看权限
- `example_product.create` - 创建权限
- `example_product.update` - 更新权限
- `example_product.delete` - 删除权限
- `example_product.export` - 导出权限
- `example_product.statistics` - 统计权限
- `example_product.search` - 搜索权限
- `example_product.batch_update` - 批量更新权限

## 错误处理

所有错误都返回标准格式：
```json
{
  "code": 4003,
  "message": "权限不足",
  "success": false,
  "trace_id": "xxx-xxx-xxx",
  "timestamp": "2024-01-01T00:00:00Z",
  "data": null
}
```

常见错误码：
- `2000` - 成功
- `4001` - 未授权
- `4003` - 权限不足
- `4004` - 资源不存在
- `4022` - 数据验证失败
- `5000` - 内部服务器错误

## 审计日志

所有操作都会自动记录审计日志，包括：
- 用户信息
- 操作类型
- 操作结果
- 上下文信息
- 时间戳

## 扩展开发

### 添加新的业务方法
```python
def custom_business_method(self, user: User, **kwargs) -> Union[ZhiResponse, ZhiModelResponse]:
    """自定义业务方法"""
    try:
        # 设置用户上下文
        self.set_user_context(user)
        
        # 检查权限
        if not self.check_permission('custom_action', user=user):
            return self._handle_response(
                data=None,
                code=ResponseCode.FORBIDDEN,
                message="权限不足",
                success=False
            )
        
        # 业务逻辑
        # ...
        
        # 记录审计日志
        self.log_audit_event('custom_action', True, user=user)
        
        return self._handle_response(data=result_data, message="ok")
        
    except Exception as e:
        zhi_logger.error(f"自定义方法失败: {e}")
        return self._handle_response(
            data=None,
            code=ResponseCode.INTERNAL_ERROR,
            message="操作失败",
            success=False
        )
```

### 添加新的API端点
```python
@http_get("/custom-endpoint")
def custom_endpoint(self, request: HttpRequest):
    """自定义端点"""
    user = self._get_user_from_request(request)
    if not user:
        return self._handle_service_response({
            'success': False, 
            'message': '需要登录'
        })
    
    service_response = self.service.custom_business_method(user=user)
    return self._handle_service_response(service_response)
```

## 总结

重构后的ExampleProduct提供了：
1. **标准化响应** - 符合zhi_oauth规范
2. **完整权限管理** - 数据和字段级权限控制
3. **审计日志** - 完整的操作记录
4. **错误处理** - 统一的错误响应格式
5. **易于扩展** - 基于增强服务的架构

这为其他模块的重构提供了最佳实践参考。
