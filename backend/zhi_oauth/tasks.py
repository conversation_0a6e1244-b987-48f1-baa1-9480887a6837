"""
OAuth2 相关的异步任务
任务已迁移到 zhi_celery 应用中统一管理

为了保持向后兼容性，这里提供任务的导入接口
"""

# 从 zhi_celery 导入OAuth任务
try:
    from zhi_celery.tasks.oauth_tasks import (
        cleanup_expired_tokens_task,
        generate_oauth_statistics_task,
        revoke_expired_tokens_task,
    )
    
    # 保持向后兼容的任务名称
    __all__ = [
        'cleanup_expired_tokens_task',
        'generate_oauth_statistics_task', 
        'revoke_expired_tokens_task',
    ]
    
except ImportError:
    # 如果 zhi_celery 不可用，提供空的导入
    __all__ = []

# 注意：任务实现已迁移到 zhi_celery.tasks.oauth_tasks
# 这里只保留导入接口以维持向后兼容性
