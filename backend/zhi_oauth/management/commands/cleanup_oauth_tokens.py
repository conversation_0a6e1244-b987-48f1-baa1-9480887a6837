"""
清理过期的OAuth2令牌管理命令
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from datetime import timedelta

from zhi_oauth.models import OAuthAccessToken, OAuthRefreshToken, OAuthAuthorizationCode, OAuthAuditLog

# 安全导入日志记录器
try:
    from zhi_common.zhi_logger import get_logger
    logger = get_logger(module_name="oauth_cleanup")
except ImportError:
    # 如果 zhi_logger 不可用，使用标准日志
    import logging
    logger = logging.getLogger("oauth_cleanup")


class Command(BaseCommand):
    help = '清理过期的OAuth2令牌和授权码'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='清理多少天前过期的令牌（默认7天）'
        )
        parser.add_argument(
            '--audit-days',
            type=int,
            default=90,
            help='清理多少天前的审计日志（默认90天）'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要删除的数量，不实际删除'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=1000,
            help='批量删除的大小（默认1000）'
        )

    def handle(self, *args, **options):
        days = options['days']
        audit_days = options['audit_days']
        dry_run = options['dry_run']
        batch_size = options['batch_size']

        cutoff_time = timezone.now() - timedelta(days=days)
        audit_cutoff_time = timezone.now() - timedelta(days=audit_days)

        self.stdout.write(
            self.style.SUCCESS(f'开始清理 {days} 天前过期的OAuth2令牌...')
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING('这是一次试运行，不会实际删除数据')
            )

        # 清理过期的访问令牌
        expired_access_tokens = OAuthAccessToken.objects.filter(
            expires_at__lt=cutoff_time,
            is_deleted=False
        )
        access_token_count = expired_access_tokens.count()

        if dry_run:
            self.stdout.write(f'将删除 {access_token_count} 个过期的访问令牌')
        else:
            deleted_count = self._batch_delete(expired_access_tokens, batch_size)
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_count} 个过期的访问令牌')
            )
            logger.info(f"清理过期访问令牌: {deleted_count} 个")

        # 清理过期的刷新令牌
        expired_refresh_tokens = OAuthRefreshToken.objects.filter(
            expires_at__lt=cutoff_time,
            is_deleted=False
        )
        refresh_token_count = expired_refresh_tokens.count()

        if dry_run:
            self.stdout.write(f'将删除 {refresh_token_count} 个过期的刷新令牌')
        else:
            deleted_count = self._batch_delete(expired_refresh_tokens, batch_size)
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_count} 个过期的刷新令牌')
            )
            logger.info(f"清理过期刷新令牌: {deleted_count} 个")

        # 清理过期的授权码
        expired_auth_codes = OAuthAuthorizationCode.objects.filter(
            expires_at__lt=cutoff_time,
            is_deleted=False
        )
        auth_code_count = expired_auth_codes.count()

        if dry_run:
            self.stdout.write(f'将删除 {auth_code_count} 个过期的授权码')
        else:
            deleted_count = self._batch_delete(expired_auth_codes, batch_size)
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_count} 个过期的授权码')
            )
            logger.info(f"清理过期授权码: {deleted_count} 个")

        # 清理已撤销的令牌
        revoked_access_tokens = OAuthAccessToken.objects.filter(
            is_revoked=True,
            updated_at__lt=cutoff_time,
            is_deleted=False
        )
        revoked_access_count = revoked_access_tokens.count()

        if dry_run:
            self.stdout.write(f'将删除 {revoked_access_count} 个已撤销的访问令牌')
        else:
            deleted_count = self._batch_delete(revoked_access_tokens, batch_size)
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_count} 个已撤销的访问令牌')
            )
            logger.info(f"清理已撤销访问令牌: {deleted_count} 个")

        # 清理已撤销的刷新令牌
        revoked_refresh_tokens = OAuthRefreshToken.objects.filter(
            is_revoked=True,
            updated_at__lt=cutoff_time,
            is_deleted=False
        )
        revoked_refresh_count = revoked_refresh_tokens.count()

        if dry_run:
            self.stdout.write(f'将删除 {revoked_refresh_count} 个已撤销的刷新令牌')
        else:
            deleted_count = self._batch_delete(revoked_refresh_tokens, batch_size)
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_count} 个已撤销的刷新令牌')
            )
            logger.info(f"清理已撤销刷新令牌: {deleted_count} 个")

        # 清理旧的审计日志
        old_audit_logs = OAuthAuditLog.objects.filter(
            created_at__lt=audit_cutoff_time,
            is_deleted=False
        )
        audit_log_count = old_audit_logs.count()

        if dry_run:
            self.stdout.write(f'将删除 {audit_log_count} 条旧的审计日志')
        else:
            deleted_count = self._batch_delete(old_audit_logs, batch_size)
            self.stdout.write(
                self.style.SUCCESS(f'已删除 {deleted_count} 条旧的审计日志')
            )
            logger.info(f"清理旧审计日志: {deleted_count} 条")

        # 统计信息
        total_count = (
            access_token_count + refresh_token_count + auth_code_count +
            revoked_access_count + revoked_refresh_count + audit_log_count
        )

        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'试运行完成，总共将删除 {total_count} 条记录')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'清理完成，总共删除了 {total_count} 条记录')
            )
            logger.info(f"OAuth2令牌清理完成，总共删除 {total_count} 条记录")

    def _batch_delete(self, queryset, batch_size):
        """批量删除数据"""
        total_deleted = 0
        
        while True:
            # 获取一批要删除的ID
            ids = list(queryset.values_list('id', flat=True)[:batch_size])
            
            if not ids:
                break
            
            # 批量软删除
            with transaction.atomic():
                deleted_count = queryset.filter(id__in=ids).update(
                    is_deleted=True,
                    deleted_at=timezone.now()
                )
                total_deleted += deleted_count
            
            self.stdout.write('.', ending='')
            
        self.stdout.write('')  # 换行
        return total_deleted
