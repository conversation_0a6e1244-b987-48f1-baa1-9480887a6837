"""
初始化ExampleProduct权限数据的管理命令
"""

from django.core.management.base import BaseCommand
from django.db import transaction
from zhi_oauth.models import Permission, Role, RolePermission


class Command(BaseCommand):
    help = '初始化ExampleProduct相关的权限数据'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新创建权限（删除已存在的权限）'
        )
    
    def handle(self, *args, **options):
        force = options.get('force', False)
        
        self.stdout.write(self.style.SUCCESS('开始初始化ExampleProduct权限数据...'))
        
        try:
            with transaction.atomic():
                # 如果强制模式，先删除已存在的权限
                if force:
                    self.stdout.write('删除已存在的ExampleProduct权限...')
                    Permission.objects.filter(code__startswith='example_product.').delete()
                
                # 创建权限
                permissions_data = [
                    {
                        'code': 'example_product.view',
                        'name': '查看示例产品',
                        'permission_type': 'api',
                        'description': '查看示例产品列表和详情的权限'
                    },
                    {
                        'code': 'example_product.create',
                        'name': '创建示例产品',
                        'permission_type': 'api',
                        'description': '创建新示例产品的权限'
                    },
                    {
                        'code': 'example_product.update',
                        'name': '更新示例产品',
                        'permission_type': 'api',
                        'description': '更新示例产品信息的权限'
                    },
                    {
                        'code': 'example_product.delete',
                        'name': '删除示例产品',
                        'permission_type': 'api',
                        'description': '删除示例产品的权限'
                    },
                    {
                        'code': 'example_product.export',
                        'name': '导出示例产品',
                        'permission_type': 'api',
                        'description': '导出示例产品数据的权限'
                    },
                    {
                        'code': 'example_product.statistics',
                        'name': '查看示例产品统计',
                        'permission_type': 'api',
                        'description': '查看示例产品统计信息的权限'
                    },
                    {
                        'code': 'example_product.batch_update',
                        'name': '批量更新示例产品',
                        'permission_type': 'api',
                        'description': '批量更新示例产品的权限'
                    },
                    # 字段级权限
                    {
                        'code': 'example_product.field.description',
                        'name': '查看产品描述字段',
                        'permission_type': 'field',
                        'description': '查看示例产品描述字段的权限'
                    },
                    {
                        'code': 'example_product.field.creator_info',
                        'name': '查看创建人信息字段',
                        'permission_type': 'field',
                        'description': '查看示例产品创建人信息字段的权限'
                    }
                ]
                
                created_permissions = []
                for perm_data in permissions_data:
                    permission, created = Permission.objects.get_or_create(
                        code=perm_data['code'],
                        defaults=perm_data
                    )
                    if created:
                        created_permissions.append(permission)
                        self.stdout.write(f'  ✓ 创建权限: {permission.code} - {permission.name}')
                    else:
                        self.stdout.write(f'  - 权限已存在: {permission.code}')
                
                # 创建角色
                roles_data = [
                    {
                        'code': 'example_product_viewer',
                        'name': '示例产品查看者',
                        'description': '只能查看示例产品信息',
                        'data_scope': 'org',
                        'permissions': [
                            'example_product.view',
                            'example_product.field.description'
                        ]
                    },
                    {
                        'code': 'example_product_editor',
                        'name': '示例产品编辑者',
                        'description': '可以查看、创建、更新示例产品',
                        'data_scope': 'org',
                        'permissions': [
                            'example_product.view',
                            'example_product.create',
                            'example_product.update',
                            'example_product.field.description',
                            'example_product.field.creator_info'
                        ]
                    },
                    {
                        'code': 'example_product_admin',
                        'name': '示例产品管理员',
                        'description': '拥有示例产品的所有权限',
                        'data_scope': 'all',
                        'permissions': [
                            'example_product.view',
                            'example_product.create',
                            'example_product.update',
                            'example_product.delete',
                            'example_product.export',
                            'example_product.statistics',
                            'example_product.batch_update',
                            'example_product.field.description',
                            'example_product.field.creator_info'
                        ]
                    }
                ]
                
                created_roles = []
                for role_data in roles_data:
                    permission_codes = role_data.pop('permissions')
                    role, created = Role.objects.get_or_create(
                        code=role_data['code'],
                        defaults=role_data
                    )
                    
                    if created:
                        created_roles.append(role)
                        self.stdout.write(f'  ✓ 创建角色: {role.code} - {role.name}')
                        
                        # 分配权限
                        for perm_code in permission_codes:
                            try:
                                permission = Permission.objects.get(code=perm_code)
                                RolePermission.objects.get_or_create(
                                    role=role,
                                    permission=permission
                                )
                                self.stdout.write(f'    - 分配权限: {perm_code}')
                            except Permission.DoesNotExist:
                                self.stdout.write(
                                    self.style.WARNING(f'    ! 权限不存在: {perm_code}')
                                )
                    else:
                        self.stdout.write(f'  - 角色已存在: {role.code}')
                
                # 输出统计信息
                self.stdout.write('\n' + '='*50)
                self.stdout.write(self.style.SUCCESS('权限初始化完成！'))
                self.stdout.write(f'创建权限数量: {len(created_permissions)}')
                self.stdout.write(f'创建角色数量: {len(created_roles)}')
                
                # 输出使用说明
                self.stdout.write('\n使用说明:')
                self.stdout.write('1. 为用户分配角色:')
                self.stdout.write('   python manage.py assign_user_role <username> example_product_viewer')
                self.stdout.write('2. 查看权限列表:')
                self.stdout.write('   python manage.py list_permissions --filter example_product')
                self.stdout.write('3. 测试权限:')
                self.stdout.write('   python manage.py test_user_permissions <username> example_product.view')
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'权限初始化失败: {str(e)}')
            )
            raise e
