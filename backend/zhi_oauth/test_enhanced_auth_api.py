"""
测试增强后的认证API接口

验证 zhi_oauth.apis.auth.py 中的登录接口是否返回完整的可认证token
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

import json
from django.test import RequestFactory
from zhi_oauth.apis.auth import AuthControllerAPI
from zhi_oauth.models import User, OAuthApplication, OAuthAccessToken, OAuthRefreshToken


def test_enhanced_login_api():
    """测试增强后的登录API"""
    print("\n🧪 测试增强后的登录API...")
    
    try:
        # 创建请求工厂和控制器实例
        factory = RequestFactory()
        auth_controller = AuthControllerAPI()
        
        # 创建测试用户（如果不存在）
        test_user, created = User.objects.get_or_create(
            username='api_test_user',
            defaults={
                'email': '<EMAIL>',
                'name': 'API测试用户',
                'is_active': True
            }
        )
        
        if created:
            test_user.set_password('test_password123')
            test_user.save()
            print("✅ 创建API测试用户成功")
        else:
            print("✅ 使用现有API测试用户")
        
        # 创建登录请求
        from zhi_oauth.apis.auth import PasswordLoginSchema
        
        login_data = PasswordLoginSchema(
            username='api_test_user',
            password='test_password123',
            remember_me=True
        )
        
        # 模拟HTTP请求
        request = factory.post('/api/auth/login', 
                              data=json.dumps({
                                  'username': 'api_test_user',
                                  'password': 'test_password123',
                                  'remember_me': True
                              }),
                              content_type='application/json')
        
        # 添加必要的请求头
        request.META['HTTP_USER_AGENT'] = 'Test Client 1.0'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        # 执行登录
        response = auth_controller.password_login(request, login_data)
        
        print(f"✅ API调用完成，响应类型: {type(response)}")
        
        # 检查响应内容
        if hasattr(response, 'dict'):
            response_data = response.dict()
        elif hasattr(response, 'model_dump'):
            response_data = response.model_dump()
        else:
            response_data = response
        
        print(f"✅ 响应成功: {response_data.get('success', False)}")
        
        if response_data.get('success'):
            data = response_data.get('data', {})
            
            # 检查完整认证令牌字段
            expected_fields = [
                'session_token', 'access_token', 'refresh_token', 
                'token_type', 'expires_in', 'scope', 'user_info', 'token_info'
            ]
            
            found_fields = []
            missing_fields = []
            
            for field in expected_fields:
                if field in data:
                    found_fields.append(field)
                else:
                    missing_fields.append(field)
            
            print(f"✅ 包含字段: {found_fields}")
            if missing_fields:
                print(f"⚠️  缺少字段: {missing_fields}")
            
            # 检查具体字段值
            if 'access_token' in data:
                print(f"✅ access_token: {data['access_token'][:20]}...")
            if 'refresh_token' in data:
                print(f"✅ refresh_token: {data['refresh_token'][:20]}...")
            if 'token_type' in data:
                print(f"✅ token_type: {data['token_type']}")
            if 'expires_in' in data:
                print(f"✅ expires_in: {data['expires_in']} 秒 ({data['expires_in'] // 3600} 小时)")
            if 'scope' in data:
                print(f"✅ scope: {data['scope']}")
            
            # 检查令牌信息
            token_info = data.get('token_info', {})
            if token_info:
                print(f"✅ 令牌信息: {list(token_info.keys())}")
                if 'application' in token_info:
                    app_info = token_info['application']
                    print(f"✅ 关联应用: {app_info.get('name')} ({app_info.get('client_id')})")
            
            # 检查用户信息
            user_info = data.get('user_info', {})
            if user_info:
                print(f"✅ 用户信息字段: {list(user_info.keys())}")
            
            return True
        else:
            print(f"❌ 登录失败: {response_data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_token_database_persistence():
    """测试令牌数据库持久化"""
    print("\n🧪 测试令牌数据库持久化...")
    
    try:
        # 检查访问令牌记录
        access_tokens = OAuthAccessToken.objects.filter(
            user__username='api_test_user'
        ).order_by('-created_at')[:3]
        
        print(f"✅ 访问令牌记录数: {access_tokens.count()}")
        
        if access_tokens.exists():
            latest_token = access_tokens.first()
            print(f"✅ 最新访问令牌: {latest_token.token[:20]}...")
            print(f"✅ 令牌范围: {latest_token.scope}")
            print(f"✅ 过期时间: {latest_token.expires_at}")
            print(f"✅ 客户端IP: {latest_token.client_ip}")
            print(f"✅ 用户代理: {latest_token.user_agent[:50]}...")
            print(f"✅ 关联应用: {latest_token.application.name}")
        
        # 检查刷新令牌记录
        refresh_tokens = OAuthRefreshToken.objects.filter(
            user__username='api_test_user'
        ).order_by('-created_at')[:3]
        
        print(f"✅ 刷新令牌记录数: {refresh_tokens.count()}")
        
        if refresh_tokens.exists():
            latest_refresh = refresh_tokens.first()
            print(f"✅ 最新刷新令牌: {latest_refresh.token[:20]}...")
            print(f"✅ 过期时间: {latest_refresh.expires_at}")
            print(f"✅ 关联访问令牌: {latest_refresh.access_token.token[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_default_oauth_app():
    """测试默认OAuth2应用"""
    print("\n🧪 测试默认OAuth2应用...")
    
    try:
        # 查找默认应用
        default_apps = OAuthApplication.objects.filter(
            client_id='default_auth_client',
            is_active=True,
            is_deleted=False
        )
        
        print(f"✅ 默认应用数量: {default_apps.count()}")
        
        if default_apps.exists():
            default_app = default_apps.first()
            print(f"✅ 应用名称: {default_app.name}")
            print(f"✅ Client ID: {default_app.client_id}")
            print(f"✅ 应用类型: {default_app.app_type}")
            print(f"✅ 授权类型: {default_app.grant_types}")
            print(f"✅ 允许范围: {default_app.allowed_scopes}")
            print(f"✅ 重定向URI: {default_app.redirect_uris}")
            print(f"✅ 描述: {default_app.description}")
            print(f"✅ 是否激活: {default_app.is_active}")
            
            # 检查关联的令牌
            associated_tokens = OAuthAccessToken.objects.filter(application=default_app).count()
            print(f"✅ 关联的访问令牌数: {associated_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_token_validation_flow():
    """测试令牌验证流程"""
    print("\n🧪 测试令牌验证流程...")
    
    try:
        # 获取最新的访问令牌
        latest_token = OAuthAccessToken.objects.filter(
            user__username='api_test_user',
            expires_at__gt=django.utils.timezone.now()
        ).order_by('-created_at').first()
        
        if not latest_token:
            print("⚠️  没有找到有效的访问令牌")
            return False
        
        print(f"✅ 找到有效令牌: {latest_token.token[:20]}...")
        
        # 验证令牌的各个属性
        checks = [
            ("令牌未过期", latest_token.expires_at > django.utils.timezone.now()),
            ("用户激活", latest_token.user.is_active),
            ("用户未删除", not latest_token.user.is_deleted),
            ("应用激活", latest_token.application.is_active),
            ("应用未删除", not latest_token.application.is_deleted),
            ("有效范围", latest_token.scope in ['read', 'write', 'read write']),
        ]
        
        all_valid = True
        for check_name, check_result in checks:
            status = "✅" if check_result else "❌"
            print(f"   {status} {check_name}: {check_result}")
            if not check_result:
                all_valid = False
        
        print(f"✅ 令牌整体有效性: {all_valid}")
        
        # 检查刷新令牌
        refresh_token = OAuthRefreshToken.objects.filter(
            access_token=latest_token,
            expires_at__gt=django.utils.timezone.now()
        ).first()
        
        if refresh_token:
            print(f"✅ 关联的刷新令牌有效: {refresh_token.token[:20]}...")
        else:
            print("⚠️  没有找到有效的刷新令牌")
        
        return all_valid
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_response_format():
    """测试API响应格式"""
    print("\n🧪 测试API响应格式...")
    
    try:
        factory = RequestFactory()
        auth_controller = AuthControllerAPI()
        
        from zhi_oauth.apis.auth import PasswordLoginSchema
        
        login_data = PasswordLoginSchema(
            username='api_test_user',
            password='test_password123',
            remember_me=False
        )
        
        request = factory.post('/api/auth/login')
        request.META['HTTP_USER_AGENT'] = 'Format Test Client'
        request.META['REMOTE_ADDR'] = '192.168.1.100'
        
        response = auth_controller.password_login(request, login_data)
        
        # 检查响应格式
        if hasattr(response, 'dict'):
            response_data = response.dict()
        elif hasattr(response, 'model_dump'):
            response_data = response.model_dump()
        else:
            response_data = response
        
        # 检查统一响应格式的必要字段
        required_fields = ['code', 'message', 'success', 'data', 'timestamp']
        found_required = [field for field in required_fields if field in response_data]
        missing_required = [field for field in required_fields if field not in response_data]
        
        print(f"✅ 统一响应格式字段: {found_required}")
        if missing_required:
            print(f"⚠️  缺少统一格式字段: {missing_required}")
        
        # 检查响应数据结构
        if response_data.get('success') and 'data' in response_data:
            data = response_data['data']
            
            # 检查认证相关字段
            auth_fields = ['access_token', 'refresh_token', 'token_type', 'expires_in']
            found_auth = [field for field in auth_fields if field in data]
            
            print(f"✅ 认证字段: {found_auth}")
            print(f"✅ 认证字段完整性: {len(found_auth)}/{len(auth_fields)}")
        
        return len(missing_required) == 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试增强后的认证API接口...")
    print("🎯 目标：验证 zhi_oauth.apis.auth.py 中的登录接口返回完整的可认证token")
    
    tests = [
        test_enhanced_login_api,
        test_token_database_persistence,
        test_default_oauth_app,
        test_token_validation_flow,
        test_api_response_format
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强后的认证API接口完美运行。")
        print("\n💡 现在登录接口 /api/auth/login 返回的完整认证信息：")
        print("   ✅ session_token - 用于会话管理")
        print("   ✅ access_token - 用于API认证（Bearer Token）")
        print("   ✅ refresh_token - 用于令牌刷新")
        print("   ✅ token_type - Bearer类型")
        print("   ✅ expires_in - 令牌过期时间（记住登录7天，否则2小时）")
        print("   ✅ scope - 令牌权限范围（read write）")
        print("   ✅ user_info - 完整用户信息")
        print("   ✅ token_info - 令牌详细信息（包含应用信息）")
        print("\n🔧 使用方式：")
        print("   POST /api/auth/login")
        print("   Authorization: Bearer {access_token}")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
