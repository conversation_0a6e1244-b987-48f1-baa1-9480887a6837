# ExampleProduct API 完整示例

## 概述

这是一个基于 ZhiAdmin 框架的完整 API 示例，展示了如何使用增强版 CRUD API 功能创建一个功能丰富的产品管理系统。该示例集成了 OAuth2 认证、权限管理、审计日志、缓存优化等企业级功能。

## 🌟 核心特性

- **OAuth2 认证**: 完整的密码登录 + Authorization Code OAuth2 认证流程
- **权限管理**: 多维度权限控制（API权限、数据权限、字段权限）
- **审计日志**: 详细的操作记录和安全审计
- **缓存优化**: Redis 缓存和内存缓存的智能组合
- **速率限制**: API 调用频率控制，防止滥用
- **数据验证**: 输入输出数据的严格验证
- **错误处理**: 统一的错误响应格式
- **自动文档**: 基于 OpenAPI 的自动 API 文档生成

## 📁 项目结构

```
zhi_oauth/
├── models.py                                    # ExampleProduct 模型定义
├── services/
│   └── example_product.py                     # 业务逻辑服务层（集成权限管理）
├── controllers/
│   └── example_product.py                     # API 控制器（OAuth2认证）
├── management/
│   └── commands/
│       └── init_example_product_permissions.py # 权限初始化命令
├── api.py                                      # API 注册配置
├── EXAMPLE_PRODUCT_API.md                     # API 使用文档
├── test_oauth2_example_product_api.py         # OAuth2认证测试脚本
├── example_usage.py                           # 功能演示脚本
└── EXAMPLE_PRODUCT_README.md                  # 本文件
```

## 🔄 认证流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Auth as 认证服务
    participant API as 产品API
    participant DB as 数据库

    Client->>Auth: 1. 密码登录
    Auth->>Client: 返回session_token

    Client->>Auth: 2. OAuth2授权请求
    Auth->>Client: 返回authorization_code

    Client->>Auth: 3. 授权码换取令牌
    Auth->>Client: 返回access_token

    Client->>API: 4. 使用access_token调用API
    API->>API: 权限验证
    API->>DB: 数据操作
    API->>Client: 返回结果
```

## 核心功能

### 1. 模型定义 (models.py)
- 基于 `ZhiCoreModel` 的示例产品模型
- 包含基础字段：name, description
- 继承了完整的审计字段和软删除功能

### 2. 服务层 (services/example_product.py)
- `ExampleProductService`: 继承自 `EnhancedModelService`
- 提供业务逻辑方法：
  - `get_product_stats()`: 获取产品统计信息（带缓存）
  - `search_products()`: 产品搜索功能
  - `bulk_update_names()`: 批量更新产品名称

### 3. 控制器 (controllers/example_product.py)
- `ExampleProductController`: 使用 `@enhanced_auto_crud_api` 装饰器
- 自动生成标准 CRUD 端点
- 自定义端点：
  - `GET /stats`: 获取统计信息
  - `GET /search`: 搜索产品
  - `POST /bulk-update-names`: 批量更新

### 4. Schema 定义
- `ExampleProductCreateSchema`: 创建请求 Schema
- `ExampleProductUpdateSchema`: 更新请求 Schema
- `ExampleProductResponseSchema`: 响应 Schema
- 其他专用 Schema：统计、搜索、批量操作

## 自动生成的 API 端点

使用 `@enhanced_auto_crud_api` 装饰器自动生成以下端点：

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/v1/example-products/list_pagination` | 分页列表 |
| GET | `/v1/example-products/list_all` | 全部列表 |
| GET | `/v1/example-products/{id}` | 获取详情 |
| POST | `/v1/example-products/` | 创建产品 |
| PUT | `/v1/example-products/{id}` | 更新产品 |
| DELETE | `/v1/example-products/{id}` | 删除产品 |
| POST | `/v1/example-products/batch_create` | 批量创建 |
| GET | `/v1/example-products/list_id_mappings` | ID映射 |

## 自定义端点

| 方法 | 端点 | 描述 |
|------|------|------|
| GET | `/v1/example-products/stats` | 获取统计信息 |
| GET | `/v1/example-products/search` | 搜索产品 |
| POST | `/v1/example-products/bulk-update-names` | 批量更新名称 |

## 高级特性

### 1. 缓存配置
```python
cache_config={
    'list_pagination': {
        'strategy': 'redis',
        'ttl': 300  # 5分钟
    },
    'retrieve': {
        'strategy': 'memory',
        'ttl': 600  # 10分钟
    }
}
```

### 2. 速率限制
```python
rate_limit_config={
    'create': {'requests': 10, 'window': 60},
    'update': {'requests': 20, 'window': 60},
    'delete': {'requests': 5, 'window': 60}
}
```

### 3. 审计配置
```python
audit_config={
    'enable': True,
    'sensitive_fields': ['name'],
    'async_logging': True
}
```

### 4. 查询过滤
支持以下查询参数：
- `name`: 按名称过滤
- `creator_id`: 按创建人过滤
- `page`: 页码
- `page_size`: 每页大小

## 🚀 快速开始

### 1. 初始化权限数据
```bash
cd backend
python manage.py init_example_product_permissions
```

### 2. 创建测试用户和OAuth2应用
```bash
# 创建超级用户
python manage.py createsuperuser

# 或运行测试脚本自动创建
python zhi_oauth/test_oauth2_example_product_api.py
```

### 3. 启动服务
```bash
# 启动Django服务
python manage.py runserver

# 访问API文档
http://localhost:8000/docs
```

### 4. 运行完整测试
```bash
# OAuth2认证流程测试
python zhi_oauth/test_oauth2_example_product_api.py

# 功能演示
python zhi_oauth/example_usage.py
```

## 使用示例

### Python 客户端
```python
import requests

# 创建产品
response = requests.post('http://localhost:8000/v1/example-products/', json={
    'name': '测试产品',
    'description': '产品描述'
})

# 获取产品列表
response = requests.get('http://localhost:8000/v1/example-products/list_pagination?page=1&page_size=10')

# 搜索产品
response = requests.get('http://localhost:8000/v1/example-products/search?keyword=测试&limit=5')

# 获取统计信息
response = requests.get('http://localhost:8000/v1/example-products/stats')
```

### cURL 示例
```bash
# 创建产品
curl -X POST http://localhost:8000/v1/example-products/ \
  -H "Content-Type: application/json" \
  -d '{"name": "测试产品", "description": "产品描述"}'

# 获取产品列表
curl http://localhost:8000/v1/example-products/list_pagination?page=1&page_size=10

# 搜索产品
curl "http://localhost:8000/v1/example-products/search?keyword=测试&limit=5"
```

## 扩展指南

### 1. 添加新字段
在 `ExampleProduct` 模型中添加字段，然后更新相应的 Schema。

### 2. 添加新的业务方法
在 `ExampleProductService` 中添加方法，然后在控制器中使用 `@versioned_api_route` 暴露。

### 3. 自定义验证
在服务层的 `validation_config` 中添加验证规则。

### 4. 权限控制
配置 `permission_config` 来启用权限管理功能。

## 最佳实践

1. **分层架构**: 模型 → 服务 → 控制器的清晰分层
2. **Schema 验证**: 使用 Ninja Schema 进行输入输出验证
3. **缓存策略**: 合理使用缓存提升性能
4. **错误处理**: 统一的错误响应格式
5. **文档完善**: 详细的 API 文档和使用示例
6. **测试覆盖**: 完整的测试用例

## 注意事项

1. 确保已正确配置 Django 设置
2. 需要 Redis 支持缓存功能
3. 权限功能需要相应的权限管理模块
4. 生产环境需要配置适当的认证和授权

## 相关文档

- [EXAMPLE_PRODUCT_API.md](./EXAMPLE_PRODUCT_API.md) - 详细的API使用文档
- [ZhiAdmin 框架文档](../README.md) - 框架整体文档
- [增强版API装饰器文档](../zhi_common/zhi_services/README.md) - 装饰器使用说明

## 支持

如有问题，请查看：
1. API 文档和示例代码
2. 测试脚本中的用法
3. 框架的整体文档
