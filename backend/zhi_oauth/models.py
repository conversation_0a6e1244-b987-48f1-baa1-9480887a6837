"""
Zhi<PERSON>dmin OAuth2 权限认证模型
实现完整的OAuth2认证和RBAC权限体系

包含所有业务模型：用户、租户、组织、角色、权限、OAuth2等
"""
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
import secrets
import string

from zhi_common.zhi_model.core_model import ZhiCoreModel

table_prefix = "zhi_oauth_"


# 延迟导入token管理器，避免循环导入
def get_token_manager():
    from zhi_oauth.utils.token_manager import oauth2_token_manager
    return oauth2_token_manager


# ================================
# 用户相关模型
# ================================

class User(AbstractUser, ZhiCoreModel):
    """
    自定义用户模型
    整合旧系统的Users模型设计
    """
    # 重写基础字段
    username = models.CharField(
        max_length=150,
        unique=True,
        db_index=True,
        verbose_name='用户账号',
        help_text="用户登录账号",
        db_comment="用户登录账号"
    )
    email = models.EmailField(
        max_length=255,
        verbose_name="邮箱",
        null=True,
        blank=True,
        help_text="用户邮箱",
        db_comment="用户邮箱"
    )

    # 扩展字段
    mobile = models.CharField(
        max_length=20,
        verbose_name="手机号",
        null=True,
        blank=True,
        help_text="用户手机号",
        db_comment="用户手机号",
        validators=[
            RegexValidator(
                regex=r'^1[3-9]\d{9}$',
                message='请输入有效的手机号码'
            )
        ]
    )
    avatar = models.TextField(
        verbose_name="头像",
        null=True,
        blank=True,
        help_text="用户头像URL",
        db_comment="用户头像URL"
    )
    name = models.CharField(
        max_length=40,
        verbose_name="真实姓名",
        null=True,
        blank=True,
        help_text="用户真实姓名",
        db_comment="用户真实姓名"
    )

    # 状态字段
    status = models.BooleanField(
        default=True,
        verbose_name="账户状态",
        help_text="账户是否正常",
        db_comment="账户是否正常"
    )

    # 性别选择
    GENDER_CHOICES = [
        (0, "女"),
        (1, "男"),
        (2, "未知"),
    ]
    gender = models.IntegerField(
        choices=GENDER_CHOICES,
        default=2,
        verbose_name="性别",
        null=True,
        blank=True,
        help_text="用户性别",
        db_comment="用户性别"
    )

    # 用户类型
    USER_TYPE_CHOICES = [
        (0, "后台用户"),
        (1, "前台用户"),
        (2, "API用户"),
        (3, "系统用户"),
    ]
    user_type = models.IntegerField(
        choices=USER_TYPE_CHOICES,
        default=0,
        verbose_name="用户类型",
        null=True,
        blank=True,
        help_text="用户类型分类",
        db_comment="用户类型分类"
    )

    # 主页路径
    home_path = models.CharField(
        max_length=150,
        blank=True,
        null=True,
        help_text="用户登录后的默认主页路径",
        db_comment="用户登录后的默认主页路径"
    )

    # 租户关联
    tenant = models.ForeignKey(
        'Tenant',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='users',
        verbose_name="所属租户",
        help_text="用户所属租户",
        db_comment="用户所属租户"
    )

    # 组织关联
    organization = models.ForeignKey(
        'Organization',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='users',
        verbose_name="所属组织",
        help_text="用户所属组织",
        db_comment="用户所属组织"
    )

    # OAuth2相关字段
    oauth_provider = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="OAuth提供商",
        help_text="第三方登录提供商",
        db_comment="第三方登录提供商"
    )
    oauth_uid = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="OAuth用户ID",
        help_text="第三方平台用户ID",
        db_comment="第三方平台用户ID"
    )

    # 安全字段
    login_failure_count = models.IntegerField(
        default=0,
        verbose_name="登录失败次数",
        help_text="连续登录失败次数",
        db_comment="连续登录失败次数"
    )
    locked_until = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="锁定到期时间",
        help_text="账户锁定到期时间",
        db_comment="账户锁定到期时间"
    )
    password_changed_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="密码修改时间",
        help_text="最后一次密码修改时间",
        db_comment="最后一次密码修改时间"
    )

    # 多对多关系
    roles = models.ManyToManyField(
        'Role',
        through='UserRole',
        through_fields=('user', 'role'),  # 指定通过字段，避免歧义
        related_name='users',
        verbose_name='用户角色',
        help_text="用户拥有的角色"
    )

    class Meta:
        db_table = table_prefix + "user"
        verbose_name = '用户'
        verbose_name_plural = verbose_name
        ordering = ['-date_joined']
        indexes = [
            models.Index(fields=['username']),
            models.Index(fields=['email']),
            models.Index(fields=['mobile']),
            models.Index(fields=['tenant']),
            models.Index(fields=['organization']),
        ]

    def __str__(self):
        return f"{self.name or self.username} ({self.username})"

    @property
    def display_name(self):
        """获取显示名称"""
        return self.name or self.username or self.email

    def is_locked(self):
        """检查账户是否被锁定"""
        if self.locked_until:
            return timezone.now() < self.locked_until
        return False

    def lock_account(self, duration_minutes=30):
        """锁定账户"""
        self.locked_until = timezone.now() + timezone.timedelta(minutes=duration_minutes)
        self.save()

    def unlock_account(self):
        """解锁账户"""
        self.locked_until = None
        self.login_failure_count = 0
        self.save()

    def increment_login_failure(self):
        """增加登录失败次数"""
        self.login_failure_count += 1
        # 连续失败5次后锁定账户30分钟
        if self.login_failure_count >= 5:
            self.lock_account(30)
        self.save()

    def reset_login_failure(self):
        """重置登录失败次数"""
        self.login_failure_count = 0
        self.save()

    def get_permissions(self):
        """获取用户所有权限"""
        permissions = set()
        for role in self.roles.filter(is_active=True):
            permissions.update(role.get_permissions())
        return permissions

    def has_permission(self, permission_code):
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        return permission_code in self.get_permissions()

    def get_data_scope_orgs(self):
        """获取用户数据权限范围内的组织"""
        org_ids = set()
        for role in self.roles.filter(is_active=True):
            org_ids.update(role.get_data_scope_orgs(self.organization))
        return list(org_ids)


class UserThirdParty(ZhiCoreModel):
    """
    用户第三方平台关联
    """
    PLATFORM_CHOICES = [
        ('wechat', '微信'),
        ('gitee', 'Gitee'),
        ('github', 'GitHub'),
        ('gitlab', 'GitLab'),
        ('weibo', '微博'),
        ('dingtalk', '钉钉'),
        ('baidu', '百度'),
        ('alipay', '支付宝'),
        ('qq', 'QQ'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        to_field='id',  # 关联到User的id字段而不是默认的主键seq
        related_name='third_party_accounts',
        verbose_name='关联用户',
        help_text="关联的用户账户"
    )
    platform = models.CharField(
        max_length=20,
        choices=PLATFORM_CHOICES,
        verbose_name="第三方平台",
        help_text="第三方登录平台",
        db_comment="第三方登录平台"
    )
    platform_uid = models.CharField(
        max_length=100,
        verbose_name="平台用户ID",
        help_text="第三方平台的用户唯一标识",
        db_comment="第三方平台的用户唯一标识"
    )
    platform_username = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="平台用户名",
        help_text="第三方平台的用户名",
        db_comment="第三方平台的用户名"
    )
    access_token = models.TextField(
        null=True,
        blank=True,
        verbose_name="访问令牌",
        help_text="第三方平台的访问令牌",
        db_comment="第三方平台的访问令牌"
    )
    refresh_token = models.TextField(
        null=True,
        blank=True,
        verbose_name="刷新令牌",
        help_text="第三方平台的刷新令牌",
        db_comment="第三方平台的刷新令牌"
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="令牌过期时间",
        help_text="第三方平台令牌过期时间",
        db_comment="第三方平台令牌过期时间"
    )
    extra_data = models.JSONField(
        default=dict,
        verbose_name="额外数据",
        help_text="第三方平台返回的额外用户信息",
        db_comment="第三方平台返回的额外用户信息"
    )

    class Meta:
        db_table = table_prefix + "user_third_party"
        verbose_name = '用户第三方账户'
        verbose_name_plural = verbose_name
        unique_together = [['platform', 'platform_uid']]
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['platform', 'platform_uid']),
        ]

    def __str__(self):
        return f"{self.user.username} - {self.get_platform_display()}"


# ================================
# 业务核心模型
# ================================

class Tenant(ZhiCoreModel):
    """
    租户模型 - 多租户数据隔离的核心
    """
    TENANT_TYPE_CHOICES = [
        ('normal', '普通租户'),
        ('super', '超级租户'),
        ('sub', '子租户'),
    ]

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="租户名称",
        help_text="租户显示名称",
        db_comment="租户显示名称"
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="租户编码",
        help_text="租户唯一标识码",
        db_comment="租户唯一标识码"
    )
    tenant_type = models.CharField(
        max_length=20,
        choices=TENANT_TYPE_CHOICES,
        default='normal',
        verbose_name="租户类型",
        help_text="租户类型分类",
        db_comment="租户类型分类"
    )

    # 企业信息
    city = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="城市",
        help_text="企业所在城市",
        db_comment="企业所在城市"
    )
    province = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="省份",
        help_text="企业所在省份",
        db_comment="企业所在省份"
    )
    district = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="区县",
        help_text="企业所在区县",
        db_comment="企业所在区县"
    )
    address = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name="详细地址",
        help_text="企业详细地址",
        db_comment="企业详细地址"
    )

    # 企业规模信息
    corp_size = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="企业规模",
        help_text="企业人员规模",
        db_comment="企业人员规模"
    )
    corp_years = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="成立年限",
        help_text="企业成立年限",
        db_comment="企业成立年限"
    )
    industry = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="所属行业",
        help_text="企业所属行业",
        db_comment="企业所属行业"
    )

    # 配置信息
    domain = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="专属域名",
        help_text="租户专属域名",
        db_comment="租户专属域名"
    )
    logo = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name="企业Logo",
        help_text="企业Logo图片URL",
        db_comment="企业Logo图片URL"
    )

    # 状态控制
    is_active = models.BooleanField(
        default=True,
        verbose_name="是否激活",
        help_text="租户是否正常使用",
        db_comment="租户是否正常使用"
    )
    max_users = models.IntegerField(
        default=100,
        verbose_name="最大用户数",
        help_text="租户允许的最大用户数量",
        db_comment="租户允许的最大用户数量"
    )
    expire_date = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="服务到期时间",
        help_text="租户服务到期时间",
        db_comment="租户服务到期时间"
    )

    # 管理员
    admin_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        to_field='id',  # 关联到User的id字段而不是默认的主键seq
        null=True,
        blank=True,
        related_name='managed_tenants',
        verbose_name="租户管理员",
        help_text="租户管理员用户",
        db_comment="租户管理员用户"
    )

    class Meta:
        db_table = table_prefix + "tenant"
        verbose_name = '租户'
        verbose_name_plural = verbose_name
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"


class Organization(ZhiCoreModel):
    """
    组织架构模型 - 支持树形结构的组织管理
    """
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name="上级组织",
        help_text="上级组织部门",
        db_comment="上级组织部门"
    )
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        related_name='organizations',
        verbose_name="所属租户",
        help_text="组织所属租户",
        db_comment="组织所属租户"
    )

    # 组织信息
    org_code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name="组织编码",
        help_text="组织唯一编码",
        db_comment="组织唯一编码"
    )
    org_name = models.CharField(
        max_length=100,
        verbose_name="组织名称",
        help_text="组织部门名称",
        db_comment="组织部门名称"
    )
    org_level = models.IntegerField(
        default=1,
        verbose_name="组织层级",
        help_text="组织在树形结构中的层级",
        db_comment="组织在树形结构中的层级"
    )
    org_sort = models.IntegerField(
        default=1,
        verbose_name="排序",
        help_text="同级组织的排序",
        db_comment="同级组织的排序"
    )

    # 负责人信息
    leader = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name="负责人",
        help_text="组织负责人姓名",
        db_comment="组织负责人姓名"
    )
    leader_user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        to_field='id',  # 关联到User的id字段而不是默认的主键seq
        null=True,
        blank=True,
        related_name='led_organizations',
        verbose_name="负责人用户",
        help_text="组织负责人用户账户",
        db_comment="组织负责人用户账户"
    )
    phone = models.CharField(
        max_length=20,
        null=True,
        blank=True,
        verbose_name="联系电话",
        help_text="组织联系电话",
        db_comment="组织联系电话"
    )
    email = models.EmailField(
        null=True,
        blank=True,
        verbose_name="联系邮箱",
        help_text="组织联系邮箱",
        db_comment="组织联系邮箱"
    )

    # 描述信息
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="组织描述",
        help_text="组织职能描述",
        db_comment="组织职能描述"
    )

    # 状态控制
    is_active = models.BooleanField(
        default=True,
        verbose_name="是否启用",
        help_text="组织是否启用",
        db_comment="组织是否启用"
    )

    class Meta:
        db_table = table_prefix + "organization"
        verbose_name = '组织架构'
        verbose_name_plural = verbose_name
        ordering = ['org_sort', 'org_name']
        indexes = [
            models.Index(fields=['tenant']),
            models.Index(fields=['parent']),
            models.Index(fields=['org_code']),
        ]

    def __str__(self):
        return f"{self.org_name} ({self.org_code})"

    def get_full_path(self):
        """获取组织完整路径"""
        path = [self.org_name]
        parent = self.parent
        while parent:
            path.insert(0, parent.org_name)
            parent = parent.parent
        return ' / '.join(path)

    def get_all_children(self, include_self=False):
        """获取所有子组织"""
        children = []
        if include_self:
            children.append(self)

        for child in self.children.filter(is_active=True):
            children.extend(child.get_all_children(include_self=True))

        return children

    def get_all_users(self, include_children=False):
        """获取组织下的所有用户"""
        if include_children:
            org_ids = [org.id for org in self.get_all_children(include_self=True)]
            return User.objects.filter(organization_id__in=org_ids, is_active=True)
        else:
            return self.users.filter(is_active=True)


class Permission(ZhiCoreModel):
    """
    权限模型 - RBAC权限体系的基础
    """
    PERMISSION_TYPE_CHOICES = [
        ('menu', '菜单权限'),
        ('button', '按钮权限'),
        ('api', 'API权限'),
        ('data', '数据权限'),
        ('field', '字段权限'),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name="权限名称",
        help_text="权限显示名称",
        db_comment="权限显示名称"
    )
    code = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="权限编码",
        help_text="权限唯一标识码",
        db_comment="权限唯一标识码"
    )
    permission_type = models.CharField(
        max_length=20,
        choices=PERMISSION_TYPE_CHOICES,
        default='menu',
        verbose_name="权限类型",
        help_text="权限分类类型",
        db_comment="权限分类类型"
    )

    # 层级关系
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name="上级权限",
        help_text="上级权限节点",
        db_comment="上级权限节点"
    )

    # 资源信息
    resource_path = models.CharField(
        max_length=200,
        null=True,
        blank=True,
        verbose_name="资源路径",
        help_text="API路径或菜单路径",
        db_comment="API路径或菜单路径"
    )
    http_method = models.CharField(
        max_length=10,
        null=True,
        blank=True,
        verbose_name="HTTP方法",
        help_text="API的HTTP请求方法",
        db_comment="API的HTTP请求方法"
    )

    # 描述信息
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="权限描述",
        help_text="权限功能描述",
        db_comment="权限功能描述"
    )

    # 状态控制
    is_active = models.BooleanField(
        default=True,
        verbose_name="是否启用",
        help_text="权限是否启用",
        db_comment="权限是否启用"
    )
    sort_order = models.IntegerField(
        default=1,
        verbose_name="排序",
        help_text="权限显示排序",
        db_comment="权限显示排序"
    )

    class Meta:
        db_table = table_prefix + "permission"
        verbose_name = '权限'
        verbose_name_plural = verbose_name
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['permission_type']),
            models.Index(fields=['parent']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"


class Role(ZhiCoreModel):
    """
    角色模型 - RBAC权限体系的核心
    """
    DATA_SCOPE_CHOICES = [
        ('all', '全部数据'),
        ('tenant', '租户数据'),
        ('org', '本部门数据'),
        ('org_and_sub', '本部门及子部门数据'),
        ('self', '仅本人数据'),
        ('custom', '自定义数据范围'),
    ]

    name = models.CharField(
        max_length=100,
        verbose_name="角色名称",
        help_text="角色显示名称",
        db_comment="角色显示名称"
    )
    code = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="角色编码",
        help_text="角色唯一标识码",
        db_comment="角色唯一标识码"
    )

    # 租户关联
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='roles',
        verbose_name="所属租户",
        help_text="角色所属租户",
        db_comment="角色所属租户"
    )

    # 角色属性
    is_system = models.BooleanField(
        default=False,
        verbose_name="是否系统角色",
        help_text="系统内置角色不可删除",
        db_comment="系统内置角色不可删除"
    )
    is_admin = models.BooleanField(
        default=False,
        verbose_name="是否管理员角色",
        help_text="管理员角色拥有更多权限",
        db_comment="管理员角色拥有更多权限"
    )

    # 数据权限范围
    data_scope = models.CharField(
        max_length=20,
        choices=DATA_SCOPE_CHOICES,
        default='org',
        verbose_name="数据权限范围",
        help_text="角色的数据访问范围",
        db_comment="角色的数据访问范围"
    )

    # 描述信息
    description = models.TextField(
        null=True,
        blank=True,
        verbose_name="角色描述",
        help_text="角色功能描述",
        db_comment="角色功能描述"
    )

    # 状态控制
    is_active = models.BooleanField(
        default=True,
        verbose_name="是否启用",
        help_text="角色是否启用",
        db_comment="角色是否启用"
    )
    sort_order = models.IntegerField(
        default=1,
        verbose_name="排序",
        help_text="角色显示排序",
        db_comment="角色显示排序"
    )

    # 权限关联
    permissions = models.ManyToManyField(
        Permission,
        through='RolePermission',
        related_name='roles',
        verbose_name="角色权限",
        help_text="角色拥有的权限"
    )

    class Meta:
        db_table = table_prefix + "role"
        verbose_name = '角色'
        verbose_name_plural = verbose_name
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['tenant']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"

    def get_permissions(self):
        """获取角色的所有权限编码"""
        return set(
            self.permissions.filter(is_active=True)
            .values_list('code', flat=True)
        )

    def get_data_scope_orgs(self, user_org=None):
        """根据数据权限范围获取可访问的组织ID列表"""
        if self.data_scope == 'all':
            return Organization.objects.filter(is_active=True).values_list('id', flat=True)
        elif self.data_scope == 'tenant' and self.tenant:
            return self.tenant.organizations.filter(is_active=True).values_list('id', flat=True)
        elif self.data_scope == 'org' and user_org:
            return [user_org.id]
        elif self.data_scope == 'org_and_sub' and user_org:
            return [org.id for org in user_org.get_all_children(include_self=True)]
        elif self.data_scope == 'self':
            return []  # 仅本人数据，不返回组织
        elif self.data_scope == 'custom':
            # 自定义数据范围，需要额外的配置表
            return []
        else:
            return []


# 关联表
class UserRole(ZhiCoreModel):
    """用户角色关联表"""
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        to_field='id',  # 关联到User的id字段而不是默认的主键seq
        related_name='user_roles',
        verbose_name="用户"
    )
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name='user_roles',
        verbose_name="角色"
    )
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_roles',
        verbose_name="授权人"
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="过期时间"
    )

    class Meta:
        db_table = table_prefix + "user_role"
        verbose_name = '用户角色关联'
        verbose_name_plural = verbose_name
        unique_together = [['user', 'role']]


class RolePermission(ZhiCoreModel):
    """角色权限关联表"""
    role = models.ForeignKey(
        Role,
        on_delete=models.CASCADE,
        related_name='role_permissions',
        verbose_name="角色"
    )
    permission = models.ForeignKey(
        Permission,
        on_delete=models.CASCADE,
        related_name='role_permissions',
        verbose_name="权限"
    )
    granted_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='granted_permissions',
        verbose_name="授权人"
    )

    class Meta:
        db_table = table_prefix + "role_permission"
        verbose_name = '角色权限关联'
        verbose_name_plural = verbose_name
        unique_together = [['role', 'permission']]


# ================================
# OAuth2相关模型
# ================================

class OAuthApplication(ZhiCoreModel):
    """
    OAuth2应用模型
    支持多种授权模式和租户隔离
    """
    APP_TYPE_CHOICES = [
        ('web', 'Web应用'),
        ('mobile', '移动应用'),
        ('desktop', '桌面应用'),
        ('service', '服务应用'),
        ('spa', '单页应用'),
    ]

    GRANT_TYPE_CHOICES = [
        ('authorization_code', '授权码模式'),
        ('client_credentials', '客户端凭证模式'),
        ('password', '密码模式'),
        ('refresh_token', '刷新令牌'),
        ('implicit', '隐式授权'),
    ]

    # 租户关联
    tenant = models.ForeignKey(
        Tenant,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='oauth_applications',
        verbose_name="所属租户",
        help_text="应用所属租户",
        db_comment="应用所属租户"
    )

    name = models.CharField(
        max_length=100,
        verbose_name="应用名称",
        help_text="OAuth2应用显示名称",
        db_comment="OAuth2应用显示名称"
    )
    description = models.TextField(
        blank=True,
        verbose_name="应用描述",
        help_text="应用功能描述",
        db_comment="应用功能描述"
    )
    client_id = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="客户端ID",
        help_text="OAuth2客户端标识",
        db_comment="OAuth2客户端标识"
    )
    client_secret = models.CharField(
        max_length=100,
        verbose_name="客户端密钥",
        help_text="OAuth2客户端密钥",
        db_comment="OAuth2客户端密钥"
    )
    app_type = models.CharField(
        max_length=20,
        choices=APP_TYPE_CHOICES,
        default='web',
        verbose_name="应用类型",
        help_text="应用类型分类",
        db_comment="应用类型分类"
    )
    grant_types = models.JSONField(
        default=list,
        verbose_name="支持的授权类型",
        help_text="应用支持的OAuth2授权模式",
        db_comment="应用支持的OAuth2授权模式"
    )
    redirect_uris = models.JSONField(
        default=list,
        verbose_name="重定向URI列表",
        help_text="OAuth2授权后的重定向地址",
        db_comment="OAuth2授权后的重定向地址"
    )
    allowed_scopes = models.JSONField(
        default=list,
        verbose_name="允许的权限范围",
        help_text="应用可申请的权限范围",
        db_comment="应用可申请的权限范围"
    )
    logo_url = models.URLField(
        blank=True,
        verbose_name="应用图标",
        help_text="应用图标URL",
        db_comment="应用图标URL"
    )
    home_url = models.URLField(
        blank=True,
        verbose_name="应用主页",
        help_text="应用主页URL",
        db_comment="应用主页URL"
    )
    terms_url = models.URLField(
        blank=True,
        verbose_name="服务条款URL",
        help_text="应用服务条款页面",
        db_comment="应用服务条款页面"
    )
    privacy_policy_url = models.URLField(
        blank=True,
        verbose_name="隐私政策URL",
        help_text="应用隐私政策页面",
        db_comment="应用隐私政策页面"
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name="是否激活",
        help_text="应用状态",
        db_comment="应用状态"
    )
    is_verified = models.BooleanField(
        default=False,
        verbose_name="是否已验证",
        help_text="应用是否通过验证",
        db_comment="应用是否通过验证"
    )
    require_consent = models.BooleanField(
        default=True,
        verbose_name="需要用户同意",
        help_text="是否需要用户明确同意授权",
        db_comment="是否需要用户明确同意授权"
    )
    reuse_consent = models.BooleanField(
        default=True,
        verbose_name="重用同意",
        help_text="是否可以重用之前的授权同意",
        db_comment="是否可以重用之前的授权同意"
    )
    skip_authorization = models.BooleanField(
        default=False,
        verbose_name="跳过授权页面",
        help_text="信任应用，跳过授权确认页面",
        db_comment="信任应用，跳过授权确认页面"
    )
    access_token_lifetime = models.IntegerField(
        default=3600,
        verbose_name="访问令牌有效期",
        help_text="访问令牌有效期（秒）",
        db_comment="访问令牌有效期（秒）"
    )
    refresh_token_lifetime = models.IntegerField(
        default=3600 * 24 * 7,
        verbose_name="刷新令牌有效期",
        help_text="刷新令牌有效期（秒）",
        db_comment="刷新令牌有效期（秒）"
    )

    def save(self, *args, **kwargs):
        if not self.client_id:
            self.client_id = self.generate_client_id()
        if not self.client_secret:
            self.client_secret = self.generate_client_secret()
        super().save(*args, **kwargs)

    def clean(self):
        """模型验证"""
        super().clean()

        # 验证重定向URI格式
        if self.redirect_uris:
            for uri in self.redirect_uris:
                if not uri.startswith(('http://', 'https://')):
                    raise ValidationError(f"Invalid redirect URI: {uri}")

    @staticmethod
    def generate_client_id():
        """生成客户端ID"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))

    @staticmethod
    def generate_client_secret():
        """生成客户端密钥"""
        return ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(64))

    def is_redirect_uri_valid(self, uri: str) -> bool:
        """验证重定向URI是否有效"""
        return uri in self.redirect_uris

    def supports_grant_type(self, grant_type: str) -> bool:
        """检查是否支持指定的授权类型"""
        return grant_type in self.grant_types

    class Meta:
        db_table = table_prefix + 'application'
        verbose_name = 'OAuth2应用'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

class OAuthAccessToken(ZhiCoreModel):
    """
    OAuth2访问令牌模型
    基于现有token模式优化，支持租户隔离
    """
    TOKEN_TYPE_CHOICES = [
        ('oauth2', 'OAuth2访问令牌'),
        ('bearer', 'Bearer令牌'),
        ('api_key', 'API密钥'),
    ]

    token = models.CharField(
        max_length=64,
        unique=True,
        db_index=True,
        verbose_name="访问令牌",
        help_text="OAuth2访问令牌字符串",
        db_comment="OAuth2访问令牌字符串"
    )
    token_type = models.CharField(
        max_length=20,
        choices=TOKEN_TYPE_CHOICES,
        default='oauth2',
        verbose_name="令牌类型",
        help_text="令牌类型标识",
        db_comment="令牌类型标识"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        to_field='id',  # 关联到User的id字段而不是默认的主键seq
        related_name='oauth_access_tokens',
        verbose_name="关联用户",
        help_text="令牌所属用户",
        db_comment="令牌所属用户"
    )
    application = models.ForeignKey(
        OAuthApplication,
        on_delete=models.CASCADE,
        related_name='access_tokens',
        verbose_name="关联应用",
        help_text="令牌所属应用",
        db_comment="令牌所属应用"
    )
    scope = models.JSONField(
        default=list,
        verbose_name="权限范围",
        help_text="令牌授权的权限范围",
        db_comment="令牌授权的权限范围"
    )
    expires_at = models.DateTimeField(
        verbose_name="过期时间",
        help_text="令牌过期时间",
        db_comment="令牌过期时间"
    )
    is_revoked = models.BooleanField(
        default=False,
        verbose_name="是否已撤销",
        help_text="令牌是否被撤销",
        db_comment="令牌是否被撤销"
    )
    last_used_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="最后使用时间",
        help_text="令牌最后使用时间",
        db_comment="令牌最后使用时间"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        verbose_name="IP地址",
        help_text="令牌创建时的IP地址",
        db_comment="令牌创建时的IP地址"
    )
    user_agent = models.TextField(
        blank=True,
        verbose_name="用户代理",
        help_text="令牌创建时的用户代理信息",
        db_comment="令牌创建时的用户代理信息"
    )

    def save(self, *args, **kwargs):
        token_manager = get_token_manager()
        if not self.token:
            self.token = token_manager.generate_access_token(self.application)
        if not self.expires_at:
            self.expires_at = token_manager.get_token_expires_at('access_token', self.application)
        super().save(*args, **kwargs)

    def is_expired(self) -> bool:
        """检查令牌是否过期"""
        return timezone.now() > self.expires_at

    def is_valid(self) -> bool:
        """检查令牌是否有效"""
        return not (self.is_expired() or self.is_revoked or self.is_deleted)

    def revoke(self):
        """撤销令牌"""
        self.is_revoked = True
        self.save()

    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used_at = timezone.now()
        self.save(update_fields=['last_used_at'])

    def get_token_info(self) -> dict:
        """获取令牌信息"""
        token_manager = get_token_manager()
        return {
            'token': self.token,
            'token_type': self.token_type,
            'expires_at': self.expires_at.isoformat(),
            'expires_in': int((self.expires_at - timezone.now()).total_seconds()),
            'scope': self.scope,
            'user_id': self.user.id if self.user else None,
            'client_id': self.application.client_id if self.application else None,
            'is_valid': self.is_valid(),
            'token_info': token_manager.extract_token_info(self.token)
        }

    def cache_token_info(self):
        """缓存令牌信息"""
        token_manager = get_token_manager()
        info = {
            'user_id': self.user.id if self.user else None,
            'client_id': self.application.client_id if self.application else None,
            'scope': self.scope,
            'expires_at': self.expires_at.isoformat(),
            'is_revoked': self.is_revoked,
        }
        expires_seconds = int((self.expires_at - timezone.now()).total_seconds())
        if expires_seconds > 0:
            token_manager.cache_token_info(self.token, info, expires_seconds)

    def revoke_cached_info(self):
        """撤销缓存的令牌信息"""
        token_manager = get_token_manager()
        token_manager.revoke_cached_token(self.token)

    class Meta:
        db_table = table_prefix + 'access_token'
        verbose_name = 'OAuth2访问令牌'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['token']),
            models.Index(fields=['user', 'application']),
            models.Index(fields=['expires_at']),
            models.Index(fields=['is_revoked']),
        ]


class OAuthRefreshToken(ZhiCoreModel):
    """
    OAuth2刷新令牌模型
    """
    token = models.CharField(
        max_length=64,
        unique=True,
        db_index=True,
        verbose_name="刷新令牌",
        help_text="OAuth2刷新令牌字符串",
        db_comment="OAuth2刷新令牌字符串"
    )
    access_token = models.OneToOneField(
        OAuthAccessToken,
        on_delete=models.CASCADE,
        related_name='refresh_token',
        verbose_name="关联访问令牌",
        help_text="对应的访问令牌",
        db_comment="对应的访问令牌"
    )
    expires_at = models.DateTimeField(
        verbose_name="过期时间",
        help_text="刷新令牌过期时间",
        db_comment="刷新令牌过期时间"
    )
    is_revoked = models.BooleanField(
        default=False,
        verbose_name="是否已撤销",
        help_text="刷新令牌是否被撤销",
        db_comment="刷新令牌是否被撤销"
    )

    def save(self, *args, **kwargs):
        token_manager = get_token_manager()
        if not self.token:
            application = self.access_token.application if self.access_token else None
            self.token = token_manager.generate_refresh_token(application)
        if not self.expires_at:
            application = self.access_token.application if self.access_token else None
            self.expires_at = token_manager.get_token_expires_at('refresh_token', application)
        super().save(*args, **kwargs)

    def is_expired(self) -> bool:
        """检查刷新令牌是否过期"""
        return timezone.now() > self.expires_at

    def is_valid(self) -> bool:
        """检查刷新令牌是否有效"""
        return not (self.is_expired() or self.is_revoked or self.is_deleted)

    def revoke(self):
        """撤销刷新令牌"""
        self.is_revoked = True
        self.save()

    class Meta:
        db_table = table_prefix + 'refresh_token'
        verbose_name = 'OAuth2刷新令牌'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']


class OAuthAuthorizationCode(ZhiCoreModel):
    """
    OAuth2授权码模型
    """
    code = models.CharField(
        max_length=64,
        unique=True,
        db_index=True,
        verbose_name="授权码",
        help_text="OAuth2授权码字符串",
        db_comment="OAuth2授权码字符串"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        to_field='id',  # 关联到User的id字段而不是默认的主键seq
        related_name='oauth_authorization_codes',
        verbose_name="关联用户",
        help_text="授权码所属用户",
        db_comment="授权码所属用户"
    )
    application = models.ForeignKey(
        OAuthApplication,
        on_delete=models.CASCADE,
        related_name='authorization_codes',
        verbose_name="关联应用",
        help_text="授权码所属应用",
        db_comment="授权码所属应用"
    )
    redirect_uri = models.URLField(
        verbose_name="重定向URI",
        help_text="授权后重定向地址",
        db_comment="授权后重定向地址"
    )
    scope = models.JSONField(
        default=list,
        verbose_name="权限范围",
        help_text="授权码对应的权限范围",
        db_comment="授权码对应的权限范围"
    )
    expires_at = models.DateTimeField(
        verbose_name="过期时间",
        help_text="授权码过期时间",
        db_comment="授权码过期时间"
    )
    is_used = models.BooleanField(
        default=False,
        verbose_name="是否已使用",
        help_text="授权码是否已被使用",
        db_comment="授权码是否已被使用"
    )
    challenge = models.CharField(
        max_length=128,
        blank=True,
        null=True,
        verbose_name="PKCE挑战码",
        help_text="PKCE代码挑战",
        db_comment="PKCE代码挑战"
    )
    challenge_method = models.CharField(
        max_length=10,
        blank=True,
        null=True,
        choices=[('plain', 'Plain'), ('S256', 'SHA256')],
        verbose_name="挑战方法",
        help_text="PKCE挑战方法",
        db_comment="PKCE挑战方法"
    )
    used_at = models.DateTimeField(
        null=True,
        blank=True,
        verbose_name="使用时间",
        help_text="授权码使用时间",
        db_comment="授权码使用时间"
    )

    def save(self, *args, **kwargs):
        token_manager = get_token_manager()
        if not self.code:
            self.code = token_manager.generate_authorization_code(self.application)
        if not self.expires_at:
            self.expires_at = token_manager.get_token_expires_at('authorization_code', self.application)
        super().save(*args, **kwargs)

    def is_expired(self) -> bool:
        """检查授权码是否过期"""
        return timezone.now() > self.expires_at

    def is_valid(self) -> bool:
        """检查授权码是否有效"""
        return not (self.is_expired() or self.is_used or self.is_deleted)

    def use(self):
        """标记授权码为已使用"""
        self.is_used = True
        self.save()

    def verify_pkce_challenge(self, code_verifier: str) -> bool:
        """
        验证PKCE代码挑战

        Args:
            code_verifier: 代码验证器

        Returns:
            bool: 验证是否通过
        """
        if not self.challenge or not self.challenge_method:
            return True  # 如果没有设置PKCE，则跳过验证

        token_manager = get_token_manager()

        if self.challenge_method == 'plain':
            return self.challenge == code_verifier
        elif self.challenge_method == 'S256':
            return token_manager.verify_pkce_challenge(code_verifier, self.challenge)

        return False

    def set_pkce_challenge(self, code_challenge: str, code_challenge_method: str = 'S256'):
        """
        设置PKCE代码挑战

        Args:
            code_challenge: 代码挑战
            code_challenge_method: 挑战方法 ('plain' 或 'S256')
        """
        if code_challenge_method not in ['plain', 'S256']:
            raise ValueError("Invalid code challenge method")

        self.challenge = code_challenge
        self.challenge_method = code_challenge_method

    class Meta:
        db_table = table_prefix + 'authorization_code'
        verbose_name = 'OAuth2授权码'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']


class OAuthScope(ZhiCoreModel):
    """
    OAuth2权限范围模型
    定义可用的权限范围
    """
    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name="权限范围名称",
        help_text="权限范围的唯一标识",
        db_comment="权限范围的唯一标识"
    )
    description = models.TextField(
        blank=True,
        verbose_name="权限范围描述",
        help_text="权限范围的详细描述",
        db_comment="权限范围的详细描述"
    )
    sort_order = models.IntegerField(
        default=1,
        verbose_name="排序",
        help_text="显示排序",
        db_comment="显示排序"
    )
    is_default = models.BooleanField(
        default=False,
        verbose_name="是否默认",
        help_text="是否为默认权限范围",
        db_comment="是否为默认权限范围"
    )
    is_public = models.BooleanField(
        default=True,
        verbose_name="是否公开",
        help_text="是否对所有应用公开",
        db_comment="是否对所有应用公开"
    )

    class Meta:
        db_table = table_prefix + 'scope'
        verbose_name = 'OAuth2权限范围'
        verbose_name_plural = verbose_name
        ordering = ['sort_order', 'name']


class ExampleProduct(ZhiCoreModel):
    """示例产品模型"""
    name = models.CharField(
        max_length=100,
        verbose_name="产品名称",
        help_text="产品显示名称",
        db_comment="产品显示名称"
    )
    description = models.TextField(
        blank=True,
        verbose_name="产品描述",
        help_text="产品详细描述",
        db_comment="产品详细描述"
    )

    class Meta:
        db_table = table_prefix + 'example_product'
        verbose_name = '示例产品'
        verbose_name_plural = verbose_name
        ordering = ['name']