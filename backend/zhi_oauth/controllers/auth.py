"""
增强认证服务 - 支持密码登录 + OAuth2统一认证
"""

import hashlib
import secrets
from typing import Optional, Dict, Any, Tuple, List
from datetime import datetime, timedelta
from django.contrib.auth import authenticate
from django.utils import timezone
from django.core.cache import cache
from django.conf import settings
from django.db import transaction, models

from zhi_oauth.models import User, OAuthApplication, OAuthAccessToken, OAuthRefreshToken, OAuthAuthorizationCode
from zhi_oauth.utils.token_manager import oauth2_token_manager
from zhi_common.zhi_logger import get_logger

logger = get_logger(module_name="auth_service")


class AuthenticationService:
    """认证服务 - 密码登录 + OAuth2统一认证"""
    
    def __init__(self):
        self.token_manager = oauth2_token_manager
        self.session_timeout = getattr(settings, 'AUTH_SESSION_TIMEOUT', 3600)  # 1小时
        self.max_login_attempts = getattr(settings, 'AUTH_MAX_LOGIN_ATTEMPTS', 5)
        self.lockout_duration = getattr(settings, 'AUTH_LOCKOUT_DURATION', 1800)  # 30分钟
    
    def password_login(
        self, 
        username: str, 
        password: str, 
        client_ip: str = None,
        user_agent: str = None,
        remember_me: bool = False
    ) -> Dict[str, Any]:
        """
        密码登录
        
        Args:
            username: 用户名（支持用户名、邮箱、手机号）
            password: 密码
            client_ip: 客户端IP
            user_agent: 用户代理
            remember_me: 是否记住登录
            
        Returns:
            dict: 登录结果，包含session_token和用户信息
        """
        try:
            # 1. 查找用户（支持多种登录方式）
            user = self._find_user_by_identifier(username)
            if not user:
                logger.warning(f"用户不存在: {username}")
                return {
                    'success': False,
                    'error': 'invalid_credentials',
                    'message': '用户名或密码错误'
                }
            
            # 2. 检查账户状态
            account_check = self._check_account_status(user)
            if not account_check['valid']:
                return {
                    'success': False,
                    'error': account_check['error'],
                    'message': account_check['message']
                }

            # 3. 验证密码
            if not user.check_password(password):
                self._handle_login_failure(user, client_ip)
                logger.warning(f"密码验证失败: {username}")
                return {
                    'success': False,
                    'error': 'invalid_credentials',
                    'message': '用户名或密码错误'
                    }

            # 4. 登录成功处理
            self._handle_login_success(user, client_ip, user_agent)
            
            # 5. 创建登录会话
            session_token = self._create_login_session(
                user, client_ip, user_agent, remember_me
            )

            # 6. 创建默认OAuth2应用的访问令牌（用于直接认证）
            access_token_info = self._create_default_access_token(user, client_ip, user_agent, remember_me)

            logger.info(f"密码登录成功: user={user.username}, ip={client_ip}")

            login_data = {
                'session_token': session_token,
                'access_token': access_token_info['access_token'],
                'refresh_token': access_token_info['refresh_token'],
                'token_type': 'Bearer',
                'expires_in': access_token_info['expires_in'],
                'user_info': {
                    'id': user.id,
                    'username': user.username,
                    'name': user.name,
                    'email': user.email,
                    'avatar': user.avatar,
                    'is_superuser': user.is_superuser,
                    'is_staff': user.is_staff,
                    'roles': [
                        {
                            'id': role.id,
                            'code': role.code,
                            'name': role.name
                        }
                        for role in user.roles.filter(is_active=True)
                    ]
                },
                'scope': 'read write'
            }
            return {
                'success': True,
                'message': '登录成功',
                'data': login_data
                }
            
        except Exception as e:
            logger.error(f"密码登录异常: {e}")
            return {
                'success': False,
                'error': 'server_error',
                'message': '登录服务异常，请稍后重试'
            }
    
    def oauth2_authorize(
        self,
        session_token: str,
        client_id: str,
        redirect_uri: str,
        scope: str = None,
        state: str = None,
        code_challenge: str = None,
        code_challenge_method: str = 'S256'
    ) -> Dict[str, Any]:
        """
        OAuth2授权 - 基于登录会话
        
        Args:
            session_token: 登录会话令牌
            client_id: OAuth2客户端ID
            redirect_uri: 重定向URI
            scope: 权限范围
            state: 状态参数
            code_challenge: PKCE代码挑战
            code_challenge_method: PKCE挑战方法
            
        Returns:
            dict: 授权结果，包含authorization_code
        """
        try:
            # 1. 验证登录会话
            user = self._validate_session_token(session_token)
            if not user:
                return {
                    'success': False,
                    'error': 'invalid_session',
                    'message': '登录会话无效或已过期'
                }
            
            # 2. 验证OAuth2客户端
            application = self._validate_oauth_client(client_id, redirect_uri)
            if not application:
                return {
                    'success': False,
                    'error': 'invalid_client',
                    'message': '无效的OAuth2客户端'
                }
            
            # 3. 检查用户权限
            if not self._check_user_oauth_permission(user, application, scope):
                return {
                    'success': False,
                    'error': 'access_denied',
                    'message': '用户无权限访问该应用'
                }
            
            # 4. 创建授权码
            auth_code = self._create_authorization_code(
                user=user,
                application=application,
                redirect_uri=redirect_uri,
                scope=scope.split(' ') if scope else [],
                code_challenge=code_challenge,
                code_challenge_method=code_challenge_method
            )
            
            # 5. 构建重定向URL
            redirect_url = f"{redirect_uri}?code={auth_code.code}"
            if state:
                redirect_url += f"&state={state}"
            
            logger.info(f"OAuth2授权成功: user={user.username}, client={client_id}")
            
            return {
                'success': True,
                'authorization_code': auth_code.code,
                'redirect_url': redirect_url,
                'expires_in': 600,  # 授权码10分钟有效
                'message': '授权成功'
            }
            
        except Exception as e:
            logger.error(f"OAuth2授权异常: {e}")
            return {
                'success': False,
                'error': 'server_error',
                'message': '授权服务异常，请稍后重试'
            }
    
    def exchange_code_for_token(
        self,
        code: str,
        client_id: str,
        client_secret: str,
        redirect_uri: str,
        code_verifier: str = None
    ) -> Dict[str, Any]:
        """
        授权码换取访问令牌
        
        Args:
            code: 授权码
            client_id: 客户端ID
            client_secret: 客户端密钥
            redirect_uri: 重定向URI
            code_verifier: PKCE代码验证器
            
        Returns:
            dict: 令牌信息
        """
        try:
            # 1. 验证客户端
            application = self._validate_oauth_client(client_id, redirect_uri, client_secret)
            if not application:
                return {
                    'success': False,
                    'error': 'invalid_client',
                    'message': '客户端验证失败'
                }
            
            # 2. 验证授权码
            auth_code = self._validate_authorization_code(code, application, redirect_uri)
            if not auth_code:
                return {
                    'success': False,
                    'error': 'invalid_grant',
                    'message': '授权码无效或已过期'
                }
            
            # 3. 验证PKCE（如果使用）
            if code_verifier and not auth_code.verify_pkce_challenge(code_verifier):
                return {
                    'success': False,
                    'error': 'invalid_grant',
                    'message': 'PKCE验证失败'
                }
            
            # 4. 生成访问令牌和刷新令牌
            with transaction.atomic():
                access_token, refresh_token = self._create_oauth_tokens(
                    user=auth_code.user,
                    application=application,
                    scope=auth_code.scope
                )
                
                # 5. 使授权码失效
                auth_code.is_used = True
                auth_code.used_at = timezone.now()
                auth_code.save()
            
            logger.info(f"令牌交换成功: user={auth_code.user.username}, client={client_id}")
            
            return {
                'success': True,
                'access_token': access_token.token,
                'token_type': 'Bearer',
                'expires_in': int((access_token.expires_at - timezone.now()).total_seconds()),
                'refresh_token': refresh_token.token if refresh_token else None,
                'scope': ' '.join(access_token.scope) if access_token.scope else None,
                'message': '令牌获取成功'
            }
            
        except Exception as e:
            logger.error(f"令牌交换异常: {e}")
            return {
                'success': False,
                'error': 'server_error',
                'message': '令牌服务异常，请稍后重试'
            }
    
    def refresh_access_token(
        self,
        refresh_token: str,
        client_id: str,
        client_secret: str
    ) -> Dict[str, Any]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            client_id: 客户端ID
            client_secret: 客户端密钥
            
        Returns:
            dict: 新的令牌信息
        """
        try:
            # 1. 验证客户端
            application = OAuthApplication.objects.filter(
                client_id=client_id,
                client_secret=client_secret,
                is_active=True,
                is_deleted=False
            ).first()
            
            if not application:
                return {
                    'success': False,
                    'error': 'invalid_client',
                    'message': '客户端验证失败'
                }
            
            # 2. 验证刷新令牌
            refresh_token_obj = OAuthRefreshToken.objects.filter(
                token=refresh_token,
                access_token__application=application,
                is_revoked=False,
                is_deleted=False
            ).select_related('access_token', 'access_token__user').first()
            
            if not refresh_token_obj or refresh_token_obj.is_expired():
                return {
                    'success': False,
                    'error': 'invalid_grant',
                    'message': '刷新令牌无效或已过期'
                }
            
            # 3. 生成新的访问令牌
            with transaction.atomic():
                # 撤销旧的访问令牌
                old_access_token = refresh_token_obj.access_token
                old_access_token.revoke()
                
                # 创建新的访问令牌
                new_access_token, new_refresh_token = self._create_oauth_tokens(
                    user=old_access_token.user,
                    application=application,
                    scope=old_access_token.scope
                )
                
                # 撤销旧的刷新令牌
                refresh_token_obj.is_revoked = True
                refresh_token_obj.save()
            
            logger.info(f"令牌刷新成功: user={old_access_token.user.username}, client={client_id}")
            
            return {
                'success': True,
                'access_token': new_access_token.token,
                'token_type': 'Bearer',
                'expires_in': int((new_access_token.expires_at - timezone.now()).total_seconds()),
                'refresh_token': new_refresh_token.token if new_refresh_token else None,
                'scope': ' '.join(new_access_token.scope) if new_access_token.scope else None,
                'message': '令牌刷新成功'
            }
            
        except Exception as e:
            logger.error(f"令牌刷新异常: {e}")
            return {
                'success': False,
                'error': 'server_error',
                'message': '令牌刷新服务异常，请稍后重试'
            }
    
    def logout(self, session_token: str = None, access_token: str = None) -> Dict[str, Any]:
        """
        登出 - 清除会话和令牌
        
        Args:
            session_token: 登录会话令牌
            access_token: OAuth2访问令牌
            
        Returns:
            dict: 登出结果
        """
        try:
            # 清除登录会话
            if session_token:
                self._revoke_session_token(session_token)
            
            # 撤销OAuth2令牌
            if access_token:
                self._revoke_access_token(access_token)
            
            logger.info("用户登出成功")
            
            return {
                'success': True,
                'message': '登出成功'
            }
            
        except Exception as e:
            logger.error(f"登出异常: {e}")
            return {
                'success': False,
                'error': 'server_error',
                'message': '登出服务异常'
            }
    
    # ==================== 私有方法 ====================
    
    def _find_user_by_identifier(self, identifier: str) -> Optional[User]:
        """根据标识符查找用户（支持用户名、邮箱、手机号）"""
        user = User.objects.filter(
            models.Q(username=identifier) |
            models.Q(email=identifier) |
            models.Q(mobile=identifier),
            is_deleted=False
        ).first()
        return user
    
    def _check_account_status(self, user: User) -> Dict[str, Any]:
        """检查账户状态"""
        if not user.is_active:
            return {
                'valid': False,
                'error': 'account_disabled',
                'message': '账户已被禁用'
            }
        
        if user.is_locked():
            return {
                'valid': False,
                'error': 'account_locked',
                'message': f'账户已被锁定，请稍后重试'
            }
        
        return {'valid': True}
    
    def _handle_login_failure(self, user: User, client_ip: str):
        """处理登录失败"""
        user.increment_login_failure()
        logger.warning(f"登录失败: user={user.username}, ip={client_ip}, failures={user.login_failure_count}")
    
    def _handle_login_success(self, user: User, client_ip: str, user_agent: str):
        """处理登录成功"""
        try:
            with transaction.atomic():
                user.reset_login_failure()
                user.last_login = timezone.now()
                user.save()

            # 记录登录日志
            # 这里可以添加登录日志记录
        except Exception as e:
            logger.warning(f"更新用户登录信息失败: {e}")
            # 不影响登录流程，继续执行
    
    def _create_login_session(
        self, 
        user: User, 
        client_ip: str, 
        user_agent: str, 
        remember_me: bool
    ) -> str:
        """创建登录会话"""
        session_token = self.token_manager.create_access_token(pre_fix='session')
        
        # 设置会话过期时间
        expires_in = 3600 * 24 * 30 if remember_me else self.session_timeout
        
        # 缓存会话信息
        session_data = {
            'user_id': user.id,
            'username': user.username,
            'client_ip': client_ip,
            'user_agent': user_agent,
            'created_at': timezone.now().isoformat(),
            'remember_me': remember_me
        }
        
        cache.set(f"session:{session_token}", session_data, expires_in)
        
        return session_token
    
    def _validate_session_token(self, session_token: str) -> Optional[User]:
        """验证登录会话令牌"""
        session_data = cache.get(f"session:{session_token}")
        if not session_data:
            return None

        try:
            user = User.objects.get(
                id=session_data['user_id'],
                is_active=True,
                is_deleted=False
            )
            return user
        except User.DoesNotExist:
            return None

    def _create_default_access_token(
        self,
        user: User,
        client_ip: str,
        user_agent: str,
        remember_me: bool
    ) -> Dict[str, Any]:
        """创建默认OAuth2访问令牌用于直接认证"""
        try:
            # 获取或创建默认OAuth2应用
            default_app = self._get_or_create_default_oauth_app()

            # 设置令牌过期时间
            access_token_expires = 3600 * 24 * 7 if remember_me else 3600 * 2  # 记住登录7天，否则2小时
            refresh_token_expires = 3600 * 24 * 30  # 刷新令牌30天

            # 创建访问令牌
            access_token = self.token_manager.create_access_token(pre_fix='access')
            refresh_token = self.token_manager.create_access_token(pre_fix='refresh')

            # 保存到数据库
            with transaction.atomic():
                # 创建访问令牌记录
                access_token_obj = OAuthAccessToken.objects.create(
                    token=access_token,
                    user=user,
                    application=default_app,
                    scope='read write',
                    expires_at=timezone.now() + timedelta(seconds=access_token_expires),
                    client_ip=client_ip,
                    user_agent=user_agent
                )

                # 创建刷新令牌记录
                refresh_token_obj = OAuthRefreshToken.objects.create(
                    token=refresh_token,
                    user=user,
                    application=default_app,
                    access_token=access_token_obj,
                    expires_at=timezone.now() + timedelta(seconds=refresh_token_expires)
                )

            logger.info(f"创建默认访问令牌成功: user={user.username}, token={access_token[:10]}...")

            return {
                'access_token': access_token,
                'refresh_token': refresh_token,
                'expires_in': access_token_expires,
                'scope': 'read write'
            }

        except Exception as e:
            logger.error(f"创建默认访问令牌失败: {e}")
            # 如果创建失败，返回基础令牌信息
            return {
                'access_token': self.token_manager.create_access_token(pre_fix='fallback'),
                'refresh_token': self.token_manager.create_access_token(pre_fix='fallback_refresh'),
                'expires_in': 3600,
                'scope': 'read'
            }

    def _get_or_create_default_oauth_app(self) -> OAuthApplication:
        """获取或创建默认OAuth2应用"""
        try:
            # 尝试获取默认应用
            default_app = OAuthApplication.objects.filter(
                client_id='default_client',
                is_active=True,
                is_deleted=False
            ).first()

            if not default_app:
                # 创建默认应用
                default_app = OAuthApplication.objects.create(
                    name='默认认证应用',
                    client_id='default_client',
                    client_secret=self.token_manager.create_access_token(pre_fix='secret'),
                    app_type='confidential',
                    grant_types='authorization_code refresh_token password',
                    redirect_uris='http://localhost:3000/callback',
                    allowed_scopes='read write',
                    description='系统默认OAuth2应用，用于密码登录后的直接认证',
                    is_active=True
                )
                logger.info(f"创建默认OAuth2应用: {default_app.client_id}")

            return default_app

        except Exception as e:
            logger.error(f"获取或创建默认OAuth2应用失败: {e}")
            raise
    
    def _validate_oauth_client(
        self,
        client_id: str,
        redirect_uri: str,
        client_secret: str = None
    ) -> Optional[OAuthApplication]:
        """验证OAuth2客户端"""
        try:
            query = OAuthApplication.objects.filter(
                client_id=client_id,
                is_active=True,
                is_deleted=False
            )

            if client_secret:
                query = query.filter(client_secret=client_secret)

            application = query.first()

            if not application:
                logger.warning(f"OAuth2客户端不存在或未激活: client_id={client_id}")
                return None

            # 验证重定向URI
            if redirect_uri not in application.redirect_uris:
                logger.warning(f"重定向URI不匹配: client_id={client_id}, redirect_uri={redirect_uri}, allowed_uris={application.redirect_uris}")
                return None

            logger.info(f"OAuth2客户端验证成功: client_id={client_id}")
            return application

        except Exception as e:
            logger.error(f"OAuth2客户端验证异常: {e}")
            return None
    
    def _check_user_oauth_permission(
        self, 
        user: User, 
        application: OAuthApplication, 
        scope: str
    ) -> bool:
        """检查用户OAuth权限"""
        # 超级管理员拥有所有权限
        if user.is_superuser:
            return True
        
        # 这里可以添加更复杂的权限检查逻辑
        # 例如检查用户角色是否有权限访问该应用
        
        return True  # 简化实现，允许所有活跃用户
    
    def _create_authorization_code(
        self,
        user: User,
        application: OAuthApplication,
        redirect_uri: str,
        scope: List[str],
        code_challenge: str = None,
        code_challenge_method: str = 'S256'
    ) -> OAuthAuthorizationCode:
        """创建授权码"""
        auth_code = OAuthAuthorizationCode.objects.create(
            user=user,
            application=application,
            redirect_uri=redirect_uri,
            scope=scope,
            challenge=code_challenge,
            challenge_method=code_challenge_method,
            expires_at=timezone.now() + timedelta(minutes=10)
        )
        return auth_code
    
    def _validate_authorization_code(
        self,
        code: str,
        application: OAuthApplication,
        redirect_uri: str
    ) -> Optional[OAuthAuthorizationCode]:
        """验证授权码"""
        try:
            auth_code = OAuthAuthorizationCode.objects.get(
                code=code,
                application=application,
                redirect_uri=redirect_uri,
                is_used=False,
                is_deleted=False
            )
            
            if auth_code.is_expired():
                return None
            
            return auth_code
            
        except OAuthAuthorizationCode.DoesNotExist:
            return None
    
    def _create_oauth_tokens(
        self,
        user: User,
        application: OAuthApplication,
        scope: List[str]
    ) -> Tuple[OAuthAccessToken, Optional[OAuthRefreshToken]]:
        """创建OAuth令牌"""
        # 创建访问令牌
        access_token = OAuthAccessToken.objects.create(
            user=user,
            application=application,
            scope=scope,
            expires_at=timezone.now() + timedelta(seconds=application.access_token_lifetime or 3600)
        )
        
        # 创建刷新令牌
        refresh_token = None
        if 'refresh_token' in application.grant_types:
            refresh_token = OAuthRefreshToken.objects.create(
                access_token=access_token,
                expires_at=timezone.now() + timedelta(seconds=application.refresh_token_lifetime or 3600 * 24 * 7)
            )
        
        # 缓存令牌信息
        access_token.cache_token_info()
        
        return access_token, refresh_token
    
    def _revoke_session_token(self, session_token: str):
        """撤销登录会话令牌"""
        cache.delete(f"session:{session_token}")
    
    def _revoke_access_token(self, access_token: str):
        """撤销访问令牌"""
        try:
            token_obj = OAuthAccessToken.objects.get(
                token=access_token,
                is_deleted=False
            )
            token_obj.revoke()
            token_obj.revoke_cached_info()
        except OAuthAccessToken.DoesNotExist:
            pass


# 创建全局实例
auth_service = AuthenticationService()
