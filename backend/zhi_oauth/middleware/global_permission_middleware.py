"""
全局权限验证中间件
"""

import json
import re
from typing import List, Dict, Any, Optional
from django.http import JsonResponse, HttpRequest, HttpResponse
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from django.urls import resolve, Resolver404

from .permission_config import permission_config_manager

try:
    from zhi_common.zhi_logger import zhi_logger
except ImportError:
    import logging
    zhi_logger = logging.getLogger(__name__)

try:
    from zhi_common.zhi_auth.core_auth import GlobalOAuth2
    from zhi_oauth.models import OAuthAccessToken
    OAUTH2_AVAILABLE = True
except ImportError:
    OAUTH2_AVAILABLE = False
    GlobalOAuth2 = None
    zhi_logger.warning("OAuth2 认证系统不可用")

try:
    from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
    PERMISSION_SYSTEM_AVAILABLE = True
except ImportError:
    PERMISSION_SYSTEM_AVAILABLE = False
    unified_permission_manager = None
    zhi_logger.warning("统一权限管理系统不可用")


class GlobalPermissionMiddleware(MiddlewareMixin):
    """
    全局权限验证中间件
    
    功能：
    1. API 白名单检查
    2. OAuth2 Token 验证
    3. 权限验证
    4. 审计日志记录
    """
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        self.get_response = get_response

        # 从配置管理器获取白名单
        self.whitelist_patterns = permission_config_manager.get_whitelist_patterns()

        # 编译正则表达式
        self.compiled_patterns = [re.compile(pattern) for pattern in self.whitelist_patterns]

        # OAuth2 认证器
        if OAUTH2_AVAILABLE:
            self.oauth2_auth = GlobalOAuth2()

        zhi_logger.info(f"全局权限中间件初始化完成，白名单规则数量: {len(self.compiled_patterns)}")
    
    def process_request(self, request: HttpRequest) -> Optional[HttpResponse]:
        """处理请求前的权限验证"""
        
        # 1. 检查是否在白名单中
        if self._is_whitelisted(request.path):
            zhi_logger.debug(f"请求在白名单中，跳过权限验证: {request.path}")
            return None
        
        # 2. 提取和验证 OAuth2 Token
        token = self._extract_token(request)
        if not token:
            return self._create_error_response(
                message="缺少访问令牌",
                code="MISSING_TOKEN",
                status=401
            )
        
        # 3. 验证 Token 并获取用户信息
        user_info = self._authenticate_token(request, token)
        if not user_info:
            return self._create_error_response(
                message="无效的访问令牌",
                code="INVALID_TOKEN", 
                status=401
            )
        
        # 4. 权限验证
        if not self._check_permissions(request):
            return self._create_error_response(
                message="权限不足",
                code="PERMISSION_DENIED",
                status=403
            )
        
        # 5. 记录审计日志
        self._log_access(request)
        
        return None
    
    def _is_whitelisted(self, path: str) -> bool:
        """检查路径是否在白名单中"""
        for pattern in self.compiled_patterns:
            if pattern.match(path):
                return True
        return False
    
    def _extract_token(self, request: HttpRequest) -> Optional[str]:
        """从请求中提取 OAuth2 Token"""
        # 从 Authorization header 提取
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            return auth_header[7:]  # 移除 'Bearer ' 前缀
        
        # 从查询参数提取（不推荐，但支持）
        return request.GET.get('access_token')
    
    def _authenticate_token(self, request: HttpRequest, token: str) -> Optional[Dict[str, Any]]:
        """验证 Token 并获取用户信息"""
        if not OAUTH2_AVAILABLE:
            zhi_logger.error("OAuth2 系统不可用，无法验证 Token")
            return None
        
        try:
            # 使用 GlobalOAuth2 进行认证
            user_info = self.oauth2_auth.authenticate(request, token)
            zhi_logger.debug(f"Token 验证成功: user={request.user.username}")
            return user_info
            
        except Exception as e:
            zhi_logger.warning(f"Token 验证失败: {e}")
            return None
    
    def _check_permissions(self, request: HttpRequest) -> bool:
        """检查用户权限"""
        # 检查是否启用全局权限检查
        if not permission_config_manager.is_global_check_enabled():
            return True

        if not PERMISSION_SYSTEM_AVAILABLE:
            # 如果权限系统不可用，只检查用户是否已认证
            return hasattr(request, 'user') and request.user.is_authenticated

        try:
            user = getattr(request, 'user', None)
            if not user:
                return False

            # 超级管理员绕过权限检查
            if user.is_superuser and permission_config_manager.is_superuser_bypass_enabled():
                if permission_config_manager.get_config('SUPERUSER_CONFIG.LOG_SUPERUSER_ACCESS'):
                    zhi_logger.info(f"超级管理员访问: user={user.username}, path={request.path}")
                return True

            # 获取当前请求的权限代码
            permission_code = self._get_permission_code(request)
            if not permission_code:
                # 如果无法确定权限代码，根据配置决定是否允许通过
                fallback_to_controller = permission_config_manager.get_config(
                    'PERMISSION_CHECK.FALLBACK_TO_CONTROLLER', True
                )
                return fallback_to_controller

            context = {
                'client_ip': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT'),
                'method': request.method,
                'path': request.path,
                'middleware': 'GlobalPermissionMiddleware'
            }

            has_permission = unified_permission_manager.check_permission(
                user, permission_code, context=context
            )

            if permission_config_manager.is_permission_log_enabled():
                zhi_logger.debug(f"权限检查结果: user={user.username}, permission={permission_code}, result={has_permission}")

            return has_permission

        except Exception as e:
            zhi_logger.error(f"权限检查异常: {e}")
            return False
    
    def _get_permission_code(self, request: HttpRequest) -> Optional[str]:
        """根据请求路径和方法获取权限代码"""
        try:
            # 首先尝试从配置的权限映射中获取
            permission_code = permission_config_manager.get_permission_for_path(
                request.path, request.method
            )

            if permission_code:
                return permission_code

            # 如果配置中没有找到，尝试从 URL 解析
            try:
                resolver_match = resolve(request.path)

                # 构建权限代码
                # 格式: app_name.view_name.method
                app_name = getattr(resolver_match, 'app_name', '')
                view_name = getattr(resolver_match, 'view_name', '')
                method = request.method.lower()

                if app_name and view_name:
                    return f"{app_name}.{view_name}.{method}"
                elif view_name:
                    return f"{view_name}.{method}"
                else:
                    # 无法确定权限代码
                    return None

            except Resolver404:
                # URL 无法解析
                return None

        except Exception as e:
            zhi_logger.warning(f"获取权限代码失败: {e}")
            return None
    
    def _log_access(self, request: HttpRequest):
        """记录访问审计日志"""
        try:
            user = getattr(request, 'user', None)
            log_data = {
                'user_id': user.id if user else None,
                'username': user.username if user else 'anonymous',
                'method': request.method,
                'path': request.path,
                'client_ip': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT'),
                'timestamp': None,  # 将由日志系统自动添加
            }
            
            zhi_logger.info(f"API访问: {json.dumps(log_data, ensure_ascii=False)}")
            
        except Exception as e:
            zhi_logger.error(f"记录访问日志失败: {e}")
    
    def _create_error_response(self, message: str, code: str, status: int) -> JsonResponse:
        """创建错误响应"""
        response_data = {
            'success': False,
            'message': message,
            'code': code,
            'data': None
        }
        
        return JsonResponse(response_data, status=status)
    
    def process_exception(self, request: HttpRequest, exception: Exception) -> Optional[HttpResponse]:
        """处理异常"""
        if isinstance(exception, PermissionError):
            return self._create_error_response(
                message="权限验证异常",
                code="PERMISSION_ERROR",
                status=403
            )
        
        # 其他异常交给 Django 处理
        return None
