"""
权限配置管理
"""

from typing import Dict, List, Optional
from django.conf import settings

# 默认权限配置
DEFAULT_PERMISSION_CONFIG = {
    # API 白名单 - 不需要认证的接口
    'WHITELIST_PATTERNS': [
        # OAuth2 认证相关
        r'^/api/oauth/login/?$',
        r'^/api/oauth/token/?$', 
        r'^/api/oauth/refresh/?$',
        r'/api/oauth/docs/?$',
        
        # 系统健康检查
        r'^/health/?$',
        r'^/ping/?$',
        r'^/api/health/?$',
        
        # Django 管理后台
        r'^/admin/.*',
        
        # 静态文件和媒体文件
        r'^/static/.*',
        r'^/media/.*',
        
        # API 文档 - 使用正则表达式匹配所有子项目
        r'^/docs/?$',                                    # 项目文档入口
        r'^/api/docs/?$',                                # 通用文档入口
        r'^/api/(?P<project>\w+)/docs/?$',               # 子项目文档: /api/{project}/docs
        r'^/api/(?P<project>\w+)/openapi\.json/?$',      # 子项目OpenAPI: /api/{project}/openapi.json
        r'^/api/(?P<project>\w+)/redoc/?$',              # 子项目ReDoc: /api/{project}/redoc
        r'^/api/openapi\.json/?$',                       # 通用 OpenAPI 规范
        r'^/api/redoc/?$',                               # 通用 ReDoc 文档
        
        # 开发调试（仅开发环境）
        r'^/__debug__/.*' if getattr(settings, 'DEBUG', False) else None,
    ],
    
    # API 权限映射 - 路径模式到权限代码的映射
    'PERMISSION_MAPPING': {
        # ExampleProduct API
        r'^/api/v1/example-products/list_pagination/?$': 'example_product.view',
        r'^/api/v1/example-products/create/?$': 'example_product.create',
        r'^/api/v1/example-products/(?P<id>[^/]+)/?$': {
            'GET': 'example_product.view',
            'PUT': 'example_product.update', 
            'PATCH': 'example_product.update',
            'DELETE': 'example_product.delete',
        },
        r'^/api/v1/example-products/(?P<id>[^/]+)/export/?$': 'example_product.export',
        
        # OAuth2 应用管理
        r'^/api/oauth/applications/?$': 'oauth.application.view',
        r'^/api/oauth/user/info/?$': 'oauth.user.info',
        
        # 通用权限模式
        r'^/api/v1/(?P<resource>\w+)/.*': '{resource}.{method}',
    },
    
    # 权限检查配置
    'PERMISSION_CHECK': {
        'ENABLE_GLOBAL_CHECK': True,           # 启用全局权限检查
        'ENABLE_METHOD_MAPPING': True,         # 启用HTTP方法到权限的映射
        'ENABLE_RESOURCE_MAPPING': True,       # 启用资源级权限映射
        'FALLBACK_TO_CONTROLLER': True,        # 中间件无法确定权限时，回退到控制器层面检查
    },
    
    # HTTP 方法到权限动作的映射
    'METHOD_PERMISSION_MAPPING': {
        'GET': 'view',
        'POST': 'create', 
        'PUT': 'update',
        'PATCH': 'update',
        'DELETE': 'delete',
        'HEAD': 'view',
        'OPTIONS': 'view',
    },
    
    # 超级管理员配置
    'SUPERUSER_CONFIG': {
        'BYPASS_PERMISSION_CHECK': True,       # 超级管理员绕过权限检查
        'LOG_SUPERUSER_ACCESS': True,          # 记录超级管理员访问日志
    },
    
    # 审计配置
    'AUDIT_CONFIG': {
        'ENABLE_ACCESS_LOG': True,             # 启用访问日志
        'ENABLE_PERMISSION_LOG': True,         # 启用权限检查日志
        'LOG_FAILED_ATTEMPTS': True,           # 记录失败的访问尝试
        'LOG_LEVEL': 'INFO',                   # 日志级别
    },
    
    # 安全配置
    'SECURITY_CONFIG': {
        'ENABLE_IP_WHITELIST': False,          # 启用IP白名单
        'IP_WHITELIST': [],                    # IP白名单列表
        'ENABLE_RATE_LIMITING': False,         # 启用访问频率限制
        'MAX_REQUESTS_PER_MINUTE': 60,         # 每分钟最大请求数
    }
}


class PermissionConfigManager:
    """权限配置管理器"""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> Dict:
        """加载权限配置"""
        # 从 Django settings 加载配置
        user_config = getattr(settings, 'GLOBAL_PERMISSION_CONFIG', {})
        
        # 合并默认配置和用户配置
        config = DEFAULT_PERMISSION_CONFIG.copy()
        config.update(user_config)
        
        # 过滤掉 None 值的白名单模式
        config['WHITELIST_PATTERNS'] = [
            pattern for pattern in config['WHITELIST_PATTERNS'] 
            if pattern is not None
        ]
        
        return config
    
    def get_whitelist_patterns(self) -> List[str]:
        """获取白名单模式列表"""
        return self.config['WHITELIST_PATTERNS']
    
    def get_permission_mapping(self) -> Dict:
        """获取权限映射配置"""
        return self.config['PERMISSION_MAPPING']
    
    def get_permission_for_path(self, path: str, method: str) -> Optional[str]:
        """根据路径和方法获取权限代码"""
        import re
        
        permission_mapping = self.get_permission_mapping()
        
        for pattern, permission in permission_mapping.items():
            if re.match(pattern, path):
                if isinstance(permission, dict):
                    # 方法特定的权限映射
                    return permission.get(method.upper())
                elif isinstance(permission, str):
                    if '{method}' in permission:
                        # 动态权限模式
                        method_action = self.config['METHOD_PERMISSION_MAPPING'].get(
                            method.upper(), method.lower()
                        )
                        return permission.format(method=method_action)
                    elif '{resource}' in permission:
                        # 资源动态权限模式
                        match = re.match(pattern, path)
                        if match:
                            groups = match.groupdict()
                            method_action = self.config['METHOD_PERMISSION_MAPPING'].get(
                                method.upper(), method.lower()
                            )
                            return permission.format(method=method_action, **groups)
                    else:
                        # 静态权限代码
                        return permission
        
        return None
    
    def is_global_check_enabled(self) -> bool:
        """是否启用全局权限检查"""
        return self.config['PERMISSION_CHECK']['ENABLE_GLOBAL_CHECK']
    
    def is_superuser_bypass_enabled(self) -> bool:
        """超级管理员是否绕过权限检查"""
        return self.config['SUPERUSER_CONFIG']['BYPASS_PERMISSION_CHECK']
    
    def is_access_log_enabled(self) -> bool:
        """是否启用访问日志"""
        return self.config['AUDIT_CONFIG']['ENABLE_ACCESS_LOG']
    
    def is_permission_log_enabled(self) -> bool:
        """是否启用权限检查日志"""
        return self.config['AUDIT_CONFIG']['ENABLE_PERMISSION_LOG']
    
    def get_config(self, key: str, default=None):
        """获取配置项"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value


# 全局权限配置管理器实例
permission_config_manager = PermissionConfigManager()


def get_permission_config() -> PermissionConfigManager:
    """获取权限配置管理器实例"""
    return permission_config_manager


def reload_permission_config():
    """重新加载权限配置"""
    global permission_config_manager
    permission_config_manager = PermissionConfigManager()
    return permission_config_manager
