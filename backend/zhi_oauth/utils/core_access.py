import secrets
from datetime import timedelta
from django.utils import timezone
from django.conf import settings


class UIDAccessTokenManager(object):
    @staticmethod
    def create_access_token(pre_fix='oauth2_access'):
        """
        创建访问令牌
        :param pre_fix: 令牌前缀 默认：oauth2_access, oauth2_refresh
        :return: 访问令牌
        """
        return f'{pre_fix}-{secrets.token_hex(25)}'[:63]

    @staticmethod
    def get_default_expires_in():
        """
        获取默认的令牌过期时间
        :return: 过期时间
        """
        default_expires_in = 3600*12
        oauth_provider_config = getattr(settings, 'OAUTH2_PROVIDER')
        if oauth_provider_config:
            default_expires_in = oauth_provider_config.get('ACCESS_TOKEN_EXPIRE_SECONDS', default_expires_in)
        return timezone.now() + timedelta(seconds=default_expires_in)

    @staticmethod
    def generate_uuid(width=16):
        """
        生成uuid
        :return: uuid
        """
        return secrets.token_hex(width)
