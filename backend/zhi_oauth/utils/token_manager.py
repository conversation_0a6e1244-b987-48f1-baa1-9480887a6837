"""
OAuth2 Token 管理器
基于 zhi_common.zhi_auth.core_access 的增强版本
"""
import secrets
import hashlib
import base64
from datetime import timedelta
from typing import Optional, Dict, Any
from django.utils import timezone
from django.conf import settings
from django.core.cache import cache

from zhi_oauth.utils.core_access import UIDAccessTokenManager


class OAuth2TokenManager(UIDAccessTokenManager):
    """
    OAuth2 Token 管理器
    继承并扩展基础的 UIDAccessTokenManager
    """
    
    # Token 前缀配置
    TOKEN_PREFIXES = {
        'access_token': 'oauth2_access',
        'refresh_token': 'oauth2_refresh', 
        'authorization_code': 'oauth2_code',
        'client_credentials': 'oauth2_client',
        'device_code': 'oauth2_device',
    }
    
    # 默认过期时间配置（秒）
    DEFAULT_EXPIRES = {
        'access_token': 3600,  # 1小时
        'refresh_token': 3600 * 24 * 7,  # 7天
        'authorization_code': 600,  # 10分钟
        'client_credentials': 3600 * 24,  # 24小时
        'device_code': 1800,  # 30分钟
    }
    
    @classmethod
    def generate_access_token(cls, application=None) -> str:
        """
        生成访问令牌

        Args:
            application: OAuth应用实例，用于获取自定义配置

        Returns:
            str: 访问令牌
        """
        prefix = cls.TOKEN_PREFIXES['access_token']

        # 如果有应用实例，可以根据应用配置自定义令牌长度或格式
        if application and hasattr(application, 'token_format_config'):
            # 预留：支持应用特定的令牌格式配置
            token_config = application.token_format_config
            if token_config.get('custom_prefix'):
                prefix = f"{prefix}_{application.client_id[:8]}"

        return cls.create_access_token(pre_fix=prefix)
    
    @classmethod
    def generate_refresh_token(cls, application=None) -> str:
        """
        生成刷新令牌

        Args:
            application: OAuth应用实例

        Returns:
            str: 刷新令牌
        """
        prefix = cls.TOKEN_PREFIXES['refresh_token']

        # 如果有应用实例，可以根据应用配置自定义令牌长度或格式
        if application and hasattr(application, 'token_format_config'):
            # 预留：支持应用特定的令牌格式配置
            token_config = application.token_format_config
            if token_config.get('custom_prefix'):
                prefix = f"{prefix}_{application.client_id[:8]}"

        return cls.create_access_token(pre_fix=prefix)
    
    @classmethod
    def generate_authorization_code(cls, application=None) -> str:
        """
        生成授权码

        Args:
            application: OAuth应用实例

        Returns:
            str: 授权码
        """
        prefix = cls.TOKEN_PREFIXES['authorization_code']

        # 如果有应用实例，可以根据应用配置自定义令牌长度或格式
        if application and hasattr(application, 'token_format_config'):
            # 预留：支持应用特定的令牌格式配置
            token_config = application.token_format_config
            if token_config.get('custom_prefix'):
                prefix = f"{prefix}_{application.client_id[:8]}"

        return cls.create_access_token(pre_fix=prefix)
    
    @classmethod
    def generate_client_credentials_token(cls, client_id: str, application=None) -> str:
        """
        生成客户端凭证令牌

        Args:
            client_id: 客户端ID
            application: OAuth应用实例（可选）

        Returns:
            str: 客户端凭证令牌
        """
        prefix = cls.TOKEN_PREFIXES['client_credentials']

        # 使用client_id自定义令牌前缀，便于识别和管理
        if client_id:
            # 取client_id前8位作为标识
            client_prefix = client_id[:8] if len(client_id) >= 8 else client_id
            prefix = f"{prefix}_{client_prefix}"

        # 如果有应用实例，进一步自定义
        if application and hasattr(application, 'token_format_config'):
            token_config = application.token_format_config
            if token_config.get('client_credentials_format'):
                prefix = token_config['client_credentials_format'].format(
                    client_id=client_id[:8],
                    app_name=application.name[:4] if application.name else 'app'
                )

        return cls.create_access_token(pre_fix=prefix)
    
    @classmethod
    def generate_device_code(cls) -> Dict[str, str]:
        """
        生成设备码和用户码（用于设备流）
        
        Returns:
            dict: 包含device_code和user_code的字典
        """
        device_code = cls.create_access_token(pre_fix=cls.TOKEN_PREFIXES['device_code'])
        user_code = cls._generate_user_code()
        
        return {
            'device_code': device_code,
            'user_code': user_code
        }
    
    @classmethod
    def _generate_user_code(cls, length: int = 8) -> str:
        """
        生成用户友好的设备授权码
        
        Args:
            length: 码长度
            
        Returns:
            str: 用户码（如：ABCD-1234）
        """
        # 生成易读的字符组合
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'  # 排除容易混淆的字符
        code = ''.join(secrets.choice(chars) for _ in range(length))
        
        # 添加分隔符提高可读性
        if length > 4:
            mid = length // 2
            code = f"{code[:mid]}-{code[mid:]}"
            
        return code
    
    @classmethod
    def get_token_expires_at(cls, token_type: str, application=None) -> timezone.datetime:
        """
        获取令牌过期时间
        
        Args:
            token_type: 令牌类型
            application: OAuth应用实例
            
        Returns:
            datetime: 过期时间
        """
        # 优先使用应用配置
        if application:
            if token_type == 'access_token' and hasattr(application, 'access_token_lifetime'):
                expires_seconds = application.access_token_lifetime
            elif token_type == 'refresh_token' and hasattr(application, 'refresh_token_lifetime'):
                expires_seconds = application.refresh_token_lifetime
            else:
                expires_seconds = cls.DEFAULT_EXPIRES.get(token_type, 3600)
        else:
            # 使用全局配置
            config_key = f'OAUTH2_{token_type.upper()}_EXPIRE_SECONDS'
            expires_seconds = getattr(settings, config_key, cls.DEFAULT_EXPIRES.get(token_type, 3600))
        
        return timezone.now() + timedelta(seconds=expires_seconds)
    
    @classmethod
    def validate_token_format(cls, token: str, token_type: str) -> bool:
        """
        验证令牌格式
        
        Args:
            token: 令牌字符串
            token_type: 令牌类型
            
        Returns:
            bool: 是否有效
        """
        if not token:
            return False
            
        expected_prefix = cls.TOKEN_PREFIXES.get(token_type)
        if not expected_prefix:
            return False
            
        return token.startswith(f"{expected_prefix}-")
    
    @classmethod
    def extract_token_info(cls, token: str) -> Dict[str, Any]:
        """
        从令牌中提取信息
        
        Args:
            token: 令牌字符串
            
        Returns:
            dict: 令牌信息
        """
        if not token or '-' not in token:
            return {}
            
        parts = token.split('-', 1)
        if len(parts) != 2:
            return {}
            
        prefix, token_part = parts
        
        # 确定令牌类型
        token_type = None
        for t_type, t_prefix in cls.TOKEN_PREFIXES.items():
            if prefix == t_prefix:
                token_type = t_type
                break
        
        return {
            'type': token_type,
            'prefix': prefix,
            'token_part': token_part,
            'length': len(token_part)
        }
    
    @classmethod
    def generate_pkce_challenge(cls, code_verifier: str) -> str:
        """
        生成PKCE代码挑战
        
        Args:
            code_verifier: 代码验证器
            
        Returns:
            str: 代码挑战
        """
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
    
    @classmethod
    def generate_code_verifier(cls, length: int = 128) -> str:
        """
        生成PKCE代码验证器
        
        Args:
            length: 验证器长度（43-128字符）
            
        Returns:
            str: 代码验证器
        """
        if length < 43 or length > 128:
            length = 128
            
        # 使用URL安全的字符
        chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~'
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    @classmethod
    def verify_pkce_challenge(cls, code_verifier: str, code_challenge: str) -> bool:
        """
        验证PKCE代码挑战
        
        Args:
            code_verifier: 代码验证器
            code_challenge: 代码挑战
            
        Returns:
            bool: 验证是否通过
        """
        expected_challenge = cls.generate_pkce_challenge(code_verifier)
        return expected_challenge == code_challenge
    
    @classmethod
    def cache_token_info(cls, token: str, info: Dict[str, Any], expires_seconds: int = None):
        """
        缓存令牌信息
        
        Args:
            token: 令牌
            info: 令牌信息
            expires_seconds: 缓存过期时间
        """
        if not expires_seconds:
            expires_seconds = 3600  # 默认1小时
            
        cache_key = f"oauth2_token:{token}"
        cache.set(cache_key, info, expires_seconds)
    
    @classmethod
    def get_cached_token_info(cls, token: str) -> Optional[Dict[str, Any]]:
        """
        获取缓存的令牌信息
        
        Args:
            token: 令牌
            
        Returns:
            dict: 令牌信息，如果不存在返回None
        """
        cache_key = f"oauth2_token:{token}"
        return cache.get(cache_key)
    
    @classmethod
    def revoke_cached_token(cls, token: str):
        """
        撤销缓存的令牌

        Args:
            token: 令牌
        """
        cache_key = f"oauth2_token:{token}"
        cache.delete(cache_key)

    @classmethod
    def get_application_token_stats(cls, application) -> Dict[str, int]:
        """
        获取应用的令牌统计信息

        Args:
            application: OAuth应用实例

        Returns:
            dict: 令牌统计信息
        """
        if not application:
            return {}

        from ..models import OAuthAccessToken, OAuthRefreshToken, OAuthAuthorizationCode

        stats = {
            'active_access_tokens': OAuthAccessToken.objects.filter(
                application=application,
                is_revoked=False,
                is_deleted=False,
                expires_at__gt=timezone.now()
            ).count(),
            'active_refresh_tokens': OAuthRefreshToken.objects.filter(
                access_token__application=application,
                is_revoked=False,
                is_deleted=False,
                expires_at__gt=timezone.now()
            ).count(),
            'unused_auth_codes': OAuthAuthorizationCode.objects.filter(
                application=application,
                is_used=False,
                is_deleted=False,
                expires_at__gt=timezone.now()
            ).count(),
        }

        return stats

    @classmethod
    def cleanup_expired_tokens(cls, application=None) -> Dict[str, int]:
        """
        清理过期的令牌

        Args:
            application: OAuth应用实例，如果为None则清理所有应用的过期令牌

        Returns:
            dict: 清理统计信息
        """
        from ..models import OAuthAccessToken, OAuthRefreshToken, OAuthAuthorizationCode

        now = timezone.now()

        # 构建查询条件
        access_token_filter = {'expires_at__lt': now, 'is_deleted': False}
        refresh_token_filter = {'expires_at__lt': now, 'is_deleted': False}
        auth_code_filter = {'expires_at__lt': now, 'is_deleted': False}

        if application:
            access_token_filter['application'] = application
            refresh_token_filter['access_token__application'] = application
            auth_code_filter['application'] = application

        # 执行清理
        access_tokens_deleted = OAuthAccessToken.objects.filter(**access_token_filter).update(is_deleted=True)
        refresh_tokens_deleted = OAuthRefreshToken.objects.filter(**refresh_token_filter).update(is_deleted=True)
        auth_codes_deleted = OAuthAuthorizationCode.objects.filter(**auth_code_filter).update(is_deleted=True)

        return {
            'access_tokens_deleted': access_tokens_deleted,
            'refresh_tokens_deleted': refresh_tokens_deleted,
            'auth_codes_deleted': auth_codes_deleted,
        }


# 创建全局实例
oauth2_token_manager = OAuth2TokenManager()
