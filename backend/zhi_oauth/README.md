# ZhiOAuth - OAuth2 认证模块

## 概述

ZhiOAuth 是基于 Django 的 OAuth2 认证授权模块，采用统一的基于 `session_token` 的认证流程，提供完整的 OAuth2 认证和 RBAC 权限管理。

> **重要更新**: 已移除旧的 `request.user.is_authenticated` 认证方式，统一使用基于 `session_token` 的新认证流程。详见 [迁移指南](MIGRATION_GUIDE.md)。

## 主要特性

- **完整的 OAuth2 支持**：支持授权码模式、客户端凭证模式、密码模式等
- **PKCE 支持**：增强安全性的代码交换证明密钥
- **多租户支持**：支持多租户数据隔离
- **令牌管理**：基于 `zhi_common.zhi_auth.core_access` 的增强令牌管理
- **权限控制**：完整的 RBAC 权限体系
- **审计日志**：详细的操作审计记录
- **自动清理**：定期清理过期令牌和日志

## 核心组件

### 1. 模型 (Models)

- **OAuthApplication**: OAuth2 应用管理
- **OAuthAccessToken**: 访问令牌
- **OAuthRefreshToken**: 刷新令牌
- **OAuthAuthorizationCode**: 授权码
- **Permission**: 权限定义
- **Role**: 角色管理
- **UserRole**: 用户角色关联

### 2. Token 管理器 (Token Manager)

```python
from zhi_oauth.utils.token_manager import oauth2_token_manager

# 生成访问令牌
access_token = oauth2_token_manager.generate_access_token(application)

# 生成刷新令牌
refresh_token = oauth2_token_manager.generate_refresh_token(application)

# 生成授权码
auth_code = oauth2_token_manager.generate_authorization_code(application)

# PKCE 支持
code_verifier = oauth2_token_manager.generate_code_verifier()
code_challenge = oauth2_token_manager.generate_pkce_challenge(code_verifier)
```

### 3. OAuth2 服务 (Service)

```python
from zhi_oauth.controllers import oauth2_service

# 验证客户端
application = oauth2_service.authenticate_client(client_id, client_secret)

# 创建授权码
auth_code = oauth2_service.create_authorization_code(
    user=user,
    application=application,
    redirect_uri=redirect_uri,
    scope=['read', 'write'],
    code_challenge=code_challenge
    )

# 交换令牌
access_token, refresh_token = oauth2_service.exchange_code_for_token(
    code=auth_code.code,
    application=application,
    redirect_uri=redirect_uri,
    code_verifier=code_verifier
    )

# 验证令牌
token_info = oauth2_service.validate_access_token(token)

# 刷新令牌
new_access_token, new_refresh_token = oauth2_service.refresh_access_token(
    refresh_token=refresh_token.token,
    application=application
    )
```

## 使用示例

### 1. 创建 OAuth2 应用

```python
from zhi_oauth.models import OAuthApplication

application = OAuthApplication.objects.create(
    name="我的应用",
    app_type="web",
    grant_types=["authorization_code", "refresh_token"],
    redirect_uris=["https://myapp.com/callback"],
    allowed_scopes=["read", "write"],
    access_token_lifetime=3600,  # 1小时
    refresh_token_lifetime=3600 * 24 * 7,  # 7天
)

print(f"Client ID: {application.client_id}")
print(f"Client Secret: {application.client_secret}")
```

### 2. 授权码流程

```python
# 1. 用户授权，创建授权码
auth_code = oauth2_service.create_authorization_code(
    user=request.user,
    application=application,
    redirect_uri="https://myapp.com/callback",
    scope=["read", "write"]
)

# 2. 客户端用授权码换取令牌
access_token, refresh_token = oauth2_service.exchange_code_for_token(
    code=auth_code.code,
    application=application,
    redirect_uri="https://myapp.com/callback"
)

# 3. 使用访问令牌
token_info = oauth2_service.validate_access_token(access_token.token)
if token_info:
    user_id = token_info['user_id']
    scopes = token_info['scope']
```

### 3. 权限管理

```python
from zhi_oauth.models import Permission, Role, UserRole

# 创建权限
permission = Permission.objects.create(
    name="查看用户",
    code="user.view",
    permission_type="api",
    action="read"
)

# 创建角色
role = Role.objects.create(
    name="管理员",
    code="admin",
    data_scope="all"
)

# 角色分配权限
role.permissions.add(permission)

# 用户分配角色
UserRole.objects.create(
    user=user,
    role=role
)
```

## 配置说明

### Django 设置

```python
# settings.py

# OAuth2 配置
OAUTH2_ACCESS_TOKEN_EXPIRE_SECONDS = 3600  # 访问令牌过期时间
OAUTH2_REFRESH_TOKEN_EXPIRE_SECONDS = 3600 * 24 * 7  # 刷新令牌过期时间
OAUTH2_AUTH_CODE_EXPIRE_SECONDS = 600  # 授权码过期时间

# 应用配置
INSTALLED_APPS = [
    # ...
    'zhi_oauth',
    'zhi_common',
]

# 认证后端
AUTHENTICATION_BACKENDS = [
    'zhi_oauth.auth.oauth_backend.OAuth2Backend',
    'django.contrib.auth.backends.ModelBackend',
]
```

### Celery 定时任务

```python
# 在 settings.py 中添加
from zhi_oauth.tasks import CELERY_BEAT_SCHEDULE

CELERY_BEAT_SCHEDULE.update(CELERY_BEAT_SCHEDULE)
```

## 管理命令

### 清理过期令牌

```bash
# 清理7天前过期的令牌
python manage.py cleanup_oauth_tokens --days 7

# 试运行（不实际删除）
python manage.py cleanup_oauth_tokens --dry-run

# 清理90天前的审计日志
python manage.py cleanup_oauth_tokens --audit-days 90
```

## API 集成

模块提供了完整的 API 接口，可以通过以下方式访问：

```python
# 在 urls.py 中
from zhi_oauth.api import api as oauth_api

urlpatterns = [
    path('api/oauth/', oauth_api.urls),
]
```

## 安全考虑

1. **PKCE 支持**：对于公共客户端，强烈建议使用 PKCE
2. **令牌过期**：合理设置令牌过期时间
3. **权限控制**：细粒度的权限控制
4. **审计日志**：完整的操作审计
5. **定期清理**：自动清理过期令牌

## 扩展性

模块设计支持：

- 自定义令牌生成策略
- 扩展权限类型
- 自定义审计日志
- 多租户数据隔离
- 缓存优化

## 监控和维护

- 使用 Celery 定时任务自动清理过期数据
- 审计日志记录所有重要操作
- 支持令牌使用统计和分析
- 提供管理命令进行维护操作
