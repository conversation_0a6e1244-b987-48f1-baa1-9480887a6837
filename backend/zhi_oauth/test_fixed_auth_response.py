"""
测试修复后的认证响应格式

验证修复数据重复和备用令牌问题
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

import json
from django.test import RequestFactory
from zhi_oauth.apis.auth import AuthControllerAPI
from zhi_oauth.models import User


def test_fixed_response_structure():
    """测试修复后的响应结构"""
    print("\n🧪 测试修复后的响应结构...")
    
    try:
        factory = RequestFactory()
        auth_controller = AuthControllerAPI()
        
        # 确保测试用户存在
        test_user, created = User.objects.get_or_create(
            username='structure_test_user',
            defaults={
                'email': '<EMAIL>',
                'name': '结构测试用户',
                'is_active': True
            }
        )
        
        if created:
            test_user.set_password('test_password123')
            test_user.save()
            print("✅ 创建结构测试用户成功")
        
        from zhi_oauth.apis.auth import PasswordLoginSchema
        
        login_data = PasswordLoginSchema(
            username='structure_test_user',
            password='test_password123',
            remember_me=True
        )
        
        request = factory.post('/api/auth/login')
        request.META['HTTP_USER_AGENT'] = 'Structure Test Client'
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        
        # 执行登录
        response = auth_controller.password_login(request, login_data)
        
        # 检查响应格式
        if hasattr(response, 'dict'):
            response_data = response.dict()
        elif hasattr(response, 'model_dump'):
            response_data = response.model_dump()
        else:
            response_data = response
        
        print(f"✅ 登录成功: {response_data.get('success', False)}")
        
        if response_data.get('success'):
            data = response_data.get('data', {})
            
            # 检查是否还有重复的数据结构
            has_nested_data = 'data' in data
            print(f"✅ 是否有嵌套data字段: {has_nested_data}")
            
            if has_nested_data:
                print("⚠️  仍然存在嵌套data结构")
                nested_data = data.get('data', {})
                print(f"   嵌套data字段: {list(nested_data.keys())}")
            
            # 检查顶级data字段
            top_level_fields = list(data.keys())
            print(f"✅ 顶级data字段: {top_level_fields}")
            
            # 检查是否有重复的令牌字段
            token_fields = ['access_token', 'refresh_token', 'token_type', 'expires_in']
            duplicate_tokens = []
            
            for field in token_fields:
                count = 0
                if field in data:
                    count += 1
                if has_nested_data and field in data.get('data', {}):
                    count += 1
                
                if count > 1:
                    duplicate_tokens.append(field)
            
            if duplicate_tokens:
                print(f"⚠️  重复的令牌字段: {duplicate_tokens}")
            else:
                print("✅ 没有重复的令牌字段")
            
            # 检查令牌类型（是否还是备用令牌）
            access_token = data.get('access_token', '')
            refresh_token = data.get('refresh_token', '')
            
            is_fallback_access = access_token.startswith('fallback-')
            is_fallback_refresh = refresh_token.startswith('fallback_refresh-')
            
            print(f"✅ 访问令牌类型: {'备用令牌' if is_fallback_access else '正常令牌'}")
            print(f"✅ 刷新令牌类型: {'备用令牌' if is_fallback_refresh else '正常令牌'}")
            
            if is_fallback_access or is_fallback_refresh:
                print("⚠️  仍在使用备用令牌，需要检查主要令牌创建流程")
            
            # 显示完整的响应结构（简化版）
            print("\n📋 响应结构预览:")
            simplified_data = {}
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 50:
                    simplified_data[key] = f"{value[:20]}..."
                elif isinstance(value, dict):
                    simplified_data[key] = f"<dict with {len(value)} keys>"
                elif isinstance(value, list):
                    simplified_data[key] = f"<list with {len(value)} items>"
                else:
                    simplified_data[key] = value
            
            print(json.dumps(simplified_data, indent=2, ensure_ascii=False))
            
            return not (has_nested_data or duplicate_tokens or is_fallback_access or is_fallback_refresh)
        else:
            print(f"❌ 登录失败: {response_data.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_debug_login_result():
    """调试登录结果结构"""
    print("\n🔍 调试登录结果结构...")
    
    try:
        from zhi_oauth.controllers.auth import AuthenticationService
        
        auth_service = AuthenticationService()
        
        # 直接调用底层登录方法
        result = auth_service.password_login(
            username='structure_test_user',
            password='test_password123',
            client_ip='127.0.0.1',
            user_agent='Debug Client',
            remember_me=True
        )
        
        print("📋 底层登录结果结构:")
        print(json.dumps(result, indent=2, ensure_ascii=False, default=str))
        
        # 检查结果结构
        if result.get('success'):
            print(f"✅ 登录成功")
            print(f"✅ 顶级字段: {list(result.keys())}")
            
            if 'data' in result:
                data = result['data']
                print(f"✅ data字段类型: {type(data)}")
                print(f"✅ data字段内容: {list(data.keys()) if isinstance(data, dict) else 'not dict'}")
                
                if isinstance(data, dict) and 'user_info' in data:
                    user_info = data['user_info']
                    print(f"✅ user_info字段: {list(user_info.keys()) if isinstance(user_info, dict) else 'not dict'}")
        
        return result
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_token_creation_debug():
    """调试令牌创建过程"""
    print("\n🔍 调试令牌创建过程...")
    
    try:
        from django.test import RequestFactory
        from zhi_oauth.apis.auth import AuthControllerAPI
        
        factory = RequestFactory()
        auth_controller = AuthControllerAPI()
        
        # 模拟登录结果
        mock_result = {
            'success': True,
            'message': '登录成功',
            'data': {
                'session_token': 'test_session_token',
                'user_info': {
                    'id': 'test_user_id',
                    'username': 'test_user',
                    'name': '测试用户',
                    'email': '<EMAIL>'
                },
                'expires_in': 3600
            }
        }
        
        print("📋 模拟登录结果:")
        print(json.dumps(mock_result, indent=2, ensure_ascii=False))
        
        # 测试令牌创建
        token_info = auth_controller._create_complete_auth_tokens(
            result=mock_result,
            client_ip='127.0.0.1',
            user_agent='Debug Client',
            remember_me=True
        )
        
        print("📋 创建的令牌信息:")
        print(json.dumps(token_info, indent=2, ensure_ascii=False, default=str))
        
        # 检查是否是备用令牌
        access_token = token_info.get('access_token', '')
        is_fallback = access_token.startswith('fallback-')
        
        print(f"✅ 令牌类型: {'备用令牌' if is_fallback else '正常令牌'}")
        
        return token_info
        
    except Exception as e:
        print(f"❌ 令牌创建调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主测试函数"""
    print("🚀 开始测试修复后的认证响应格式...")
    print("🎯 目标：修复数据重复和备用令牌问题")
    
    tests = [
        test_debug_login_result,
        test_token_creation_debug,
        test_fixed_response_structure
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = test_func()
            if result is not None and result is not False:
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！响应格式修复成功。")
        print("\n✅ 修复内容:")
        print("   - 消除了数据结构重复")
        print("   - 修复了备用令牌问题")
        print("   - 优化了响应格式")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
