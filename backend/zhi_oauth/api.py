"""
ZhiOAuth API 配置
"""
from ninja_extra import NinjaExtraAPI

from .apis.auth import AuthControllerAPI
# from .controllers.auto_crud_example import AutoCRUDExampleController  # 暂时注释
from zhi_oauth.apis.example_product import ExampleProductControllerAPI
from zhi_common.zhi_auth.core_auth import GlobalOAuth2
from zhi_common.zhi_exceptions.exception_handler import register_exception_handlers
from zhi_oauth.apis.simple_example_product import ExampleProductControllerAPI as ExampleProductV2ControllerAPI

# 创建 API 实例
api = NinjaExtraAPI(
    title="ZhiOAuth API",
    version="1.0.0",
    description="ZhiAdmin OAuth2 认证授权系统 API",
    auth=GlobalOAuth2,
    # urls_namespace="oauth",  # 使用更具体的命名空间避免冲突
    openapi_extra={
        "info": {
            "termsOfService": "https://zhiadmin.com/terms/",
            "contact": {"email": "<EMAIL>"},
            "license": {"name": "ZhiAdmin License"},
        },
    },
    docs_url="docs",
    openapi_url="openapi.json",
)

# 注册异常处理器
register_exception_handlers(api)

# 注册控制器
# 注意：已移除旧的 OAuthController，统一使用基于 session_token 的新认证流程
api.register_controllers(AuthControllerAPI)  # 新的统一认证控制器

# 注册ExampleProduct自动CRUD测试控制器
api.register_controllers(ExampleProductControllerAPI)  # ExampleProduct自动CRUD API
api.register_controllers(ExampleProductV2ControllerAPI)  # ExampleProduct自动CRUD API
