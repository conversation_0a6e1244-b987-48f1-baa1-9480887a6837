# ExampleProduct API 集成指南

## 📋 概述

本指南详细介绍如何集成和使用 ExampleProduct API，包括 OAuth2 认证、权限管理、API 调用等完整流程。

## 🔧 环境准备

### 1. 依赖检查

确保以下模块可用：
```python
# 核心依赖
from zhi_common.zhi_services.enhanced_expose import enhanced_auto_crud_api
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
from zhi_common.zhi_cache.api_cache import api_cache
from zhi_common.zhi_logger import zhi_logger

# 如果某些模块不可用，系统会自动降级到基础功能
```

### 2. 数据库迁移

```bash
# 生成迁移文件
python manage.py makemigrations zhi_oauth

# 执行迁移
python manage.py migrate zhi_oauth
```

### 3. 权限初始化

```bash
# 初始化ExampleProduct权限
python manage.py init_example_product_permissions

# 查看创建的权限
python manage.py shell
>>> from zhi_oauth.models import Permission
>>> Permission.objects.filter(code__startswith='example_product').values('code', 'name')
```

## 🔐 OAuth2 认证集成

### 1. 创建 OAuth2 应用

```python
from zhi_oauth.models import OAuthApplication

app = OAuthApplication.objects.create(
    name="我的应用",
    client_id="my_client_id",
    client_secret="my_client_secret",
    app_type="web",
    redirect_uris=["http://localhost:3000/callback"],
    grant_types=["authorization_code", "refresh_token"],
    response_types=["code"],
    allowed_scopes=["read", "write"],
    is_active=True
)
```

### 2. 前端认证流程

```javascript
class ExampleProductAPI {
    constructor(baseURL = 'http://localhost:8000') {
        this.baseURL = baseURL;
        this.accessToken = null;
    }
    
    // 步骤1: 密码登录
    async login(username, password) {
        const response = await fetch(`${this.baseURL}/api/auth/login`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({username, password, remember_me: false})
        });
        
        const result = await response.json();
        if (result.success) {
            this.sessionToken = result.session_token;
            return result;
        }
        throw new Error(result.message);
    }
    
    // 步骤2: OAuth2授权
    async authorize(clientId, redirectUri) {
        const response = await fetch(`${this.baseURL}/api/auth/oauth2/authorize`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                session_token: this.sessionToken,
                client_id: clientId,
                redirect_uri: redirectUri,
                response_type: 'code',
                scope: 'read write'
            })
        });
        
        const result = await response.json();
        if (result.success) {
            return result.authorization_code;
        }
        throw new Error(result.message);
    }
    
    // 步骤3: 获取访问令牌
    async getAccessToken(authCode, clientId, clientSecret, redirectUri) {
        const response = await fetch(`${this.baseURL}/api/auth/oauth2/token`, {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                grant_type: 'authorization_code',
                code: authCode,
                client_id: clientId,
                client_secret: clientSecret,
                redirect_uri: redirectUri
            })
        });
        
        const result = await response.json();
        if (result.success) {
            this.accessToken = result.access_token;
            return result;
        }
        throw new Error(result.message);
    }
    
    // API调用辅助方法
    async apiCall(endpoint, options = {}) {
        const url = `${this.baseURL}/v1/example-products${endpoint}`;
        const headers = {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json',
            ...options.headers
        };
        
        const response = await fetch(url, {
            ...options,
            headers
        });
        
        return response.json();
    }
    
    // 产品API方法
    async getProducts(page = 1, pageSize = 10, filters = {}) {
        const params = new URLSearchParams({
            page: page.toString(),
            page_size: pageSize.toString(),
            ...filters
        });
        
        return this.apiCall(`/list_pagination?${params}`);
    }
    
    async createProduct(productData) {
        return this.apiCall('/', {
            method: 'POST',
            body: JSON.stringify(productData)
        });
    }
    
    async getProductStats(nameFilter = null) {
        const params = nameFilter ? `?name_filter=${nameFilter}` : '';
        return this.apiCall(`/stats${params}`);
    }
    
    async searchProducts(keyword, limit = 10) {
        const params = new URLSearchParams({keyword, limit: limit.toString()});
        return this.apiCall(`/search?${params}`);
    }
}

// 使用示例
async function example() {
    const api = new ExampleProductAPI();
    
    try {
        // 1. 登录
        await api.login('username', 'password');
        
        // 2. 授权
        const authCode = await api.authorize('client_id', 'redirect_uri');
        
        // 3. 获取令牌
        await api.getAccessToken(authCode, 'client_id', 'client_secret', 'redirect_uri');
        
        // 4. 调用API
        const products = await api.getProducts();
        console.log('产品列表:', products);
        
        const stats = await api.getProductStats();
        console.log('统计信息:', stats);
        
    } catch (error) {
        console.error('API调用失败:', error);
    }
}
```

## 🛡️ 权限管理

### 1. 权限配置

ExampleProduct API 支持以下权限：

| 权限代码 | 权限名称 | 描述 |
|---------|---------|------|
| `example_product.view` | 查看示例产品 | 查看产品列表和详情 |
| `example_product.create` | 创建示例产品 | 创建新产品 |
| `example_product.update` | 更新示例产品 | 修改产品信息 |
| `example_product.delete` | 删除示例产品 | 删除产品 |
| `example_product.export` | 导出示例产品 | 导出产品数据 |
| `example_product.statistics` | 查看统计信息 | 查看产品统计 |
| `example_product.batch_update` | 批量更新 | 批量修改产品 |

### 2. 角色分配

```python
from zhi_oauth.models import User, Role, UserRole

# 获取用户和角色
user = User.objects.get(username='test_user')
role = Role.objects.get(code='example_product_editor')

# 分配角色
UserRole.objects.create(user=user, role=role)
```

### 3. 权限检查

```python
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager

# 检查用户权限
has_permission = unified_permission_manager.check_permission(
    user, 'example_product.view'
)

# 获取用户所有权限
user_permissions = unified_permission_manager.get_user_permissions(user)
```

## 📊 监控和维护

### 1. 审计日志

```python
# 查看审计日志
from zhi_oauth.models import AuditLog

logs = AuditLog.objects.filter(
    resource_type='ExampleProduct',
    created_at__gte=datetime.now() - timedelta(days=7)
).order_by('-created_at')

for log in logs:
    print(f"{log.created_at}: {log.user} {log.action} {log.resource_id}")
```

### 2. 缓存管理

```bash
# 清除缓存
python manage.py shell
>>> from django.core.cache import cache
>>> cache.clear()

# 预热权限缓存
>>> from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
>>> unified_permission_manager.warm_up_cache()
```

### 3. 性能监控

```python
# 查看API调用统计
from zhi_oauth.models import APICallLog

stats = APICallLog.objects.filter(
    endpoint__startswith='/v1/example-products',
    created_at__gte=datetime.now() - timedelta(hours=1)
).aggregate(
    total_calls=Count('id'),
    avg_response_time=Avg('response_time'),
    error_rate=Count('id', filter=Q(status_code__gte=400)) * 100.0 / Count('id')
)
```

## 🔍 故障排除

### 1. 常见问题

**Q: API返回401未授权错误**
A: 检查access_token是否有效，是否已过期

**Q: API返回403权限不足错误**
A: 检查用户是否有对应的权限，查看角色分配

**Q: 缓存不生效**
A: 检查Redis连接，确认缓存配置正确

### 2. 调试技巧

```python
# 启用详细日志
import logging
logging.getLogger('zhi_oauth').setLevel(logging.DEBUG)
logging.getLogger('zhi_auth').setLevel(logging.DEBUG)

# 检查令牌状态
from zhi_oauth.models import OAuthAccessToken
token = OAuthAccessToken.objects.get(token='your_token')
print(f"令牌有效: {token.is_valid()}")
print(f"令牌过期: {token.is_expired()}")

# 检查权限
from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
result = unified_permission_manager.check_permission(user, 'example_product.view')
print(f"权限检查结果: {result}")
```

## 📚 扩展开发

### 1. 添加新的API端点

```python
# 在控制器中添加新方法
@require_permission('example_product.custom_action')
@versioned_api_route(http_post, '/custom-action', version=APIVersion.V1)
def custom_action(self, request: HttpRequest, data: CustomSchema):
    """自定义操作"""
    user = request.user
    # 业务逻辑
    return {'success': True, 'message': '操作成功'}
```

### 2. 自定义权限策略

```python
from zhi_common.zhi_auth.dynamic_permission_engine import PermissionPolicy

# 创建时间限制策略
time_policy = PermissionPolicy(
    name="work_hours_only",
    description="只允许工作时间操作",
    conditions=[
        TimeBasedCondition(start_time="09:00", end_time="18:00")
    ],
    effect="allow"
)
```

## 🤝 支持

如有问题或建议，请：
1. 查看API文档：`http://localhost:8000/docs`
2. 检查日志文件
3. 联系开发团队
