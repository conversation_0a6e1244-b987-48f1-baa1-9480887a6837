"""
OAuth2 服务类
提供OAuth2认证和授权的核心业务逻辑
"""
from typing import Optional, Dict, Any, List, Tuple
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import transaction

from .models import (
    OAuthApplication, OAuthAccessToken, OAuthRefreshToken,
    OAuthAuthorizationCode
)
# 导入日志模型
from zhi_logger.models import OAuthAuditLog
from zhi_oauth.utils.token_manager import oauth2_token_manager
# 安全导入日志记录器
try:
    from zhi_common.zhi_logger import get_logger
    logger = get_logger(module_name="oauth2_service")
except ImportError:
    # 如果 zhi_logger 不可用，使用标准日志
    import logging
    logger = logging.getLogger("oauth2_service")

User = get_user_model()


class OAuth2Service:
    """OAuth2 核心服务类"""
    
    def __init__(self):
        self.token_manager = oauth2_token_manager
    
    def authenticate_client(self, client_id: str, client_secret: str = None) -> Optional[OAuthApplication]:
        """
        验证OAuth2客户端
        
        Args:
            client_id: 客户端ID
            client_secret: 客户端密钥（可选，用于机密客户端）
            
        Returns:
            OAuthApplication: 验证成功的应用实例，失败返回None
        """
        try:
            application = OAuthApplication.objects.get(
                client_id=client_id,
                is_active=True,
                is_deleted=False
            )
            
            # 如果提供了client_secret，则验证
            if client_secret is not None:
                if application.client_secret != client_secret:
                    logger.warning(f"客户端密钥验证失败: {client_id}")
                    return None
            
            return application
            
        except OAuthApplication.DoesNotExist:
            logger.warning(f"客户端不存在: {client_id}")
            return None
    
    def create_authorization_code(
        self, 
        user: User, 
        application: OAuthApplication,
        redirect_uri: str,
        scope: List[str] = None,
        code_challenge: str = None,
        code_challenge_method: str = 'S256'
    ) -> OAuthAuthorizationCode:
        """
        创建授权码
        
        Args:
            user: 用户实例
            application: OAuth应用实例
            redirect_uri: 重定向URI
            scope: 权限范围
            code_challenge: PKCE代码挑战
            code_challenge_method: PKCE挑战方法
            
        Returns:
            OAuthAuthorizationCode: 授权码实例
        """
        # 验证重定向URI
        if not application.is_redirect_uri_valid(redirect_uri):
            raise ValidationError(f"Invalid redirect URI: {redirect_uri}")
        
        # 创建授权码
        auth_code = OAuthAuthorizationCode.objects.create(
            user=user,
            application=application,
            redirect_uri=redirect_uri,
            scope=scope or [],
            tenant_id=getattr(user, 'tenant_id', None)
        )
        
        # 设置PKCE挑战
        if code_challenge:
            auth_code.set_pkce_challenge(code_challenge, code_challenge_method)
            auth_code.save()
        
        # 记录审计日志
        self._create_audit_log(
            user=user,
            application=application,
            action='authorize',
            resource_type='authorization_code',
            resource_id=auth_code.id,
            is_success=True
        )
        
        logger.info(f"创建授权码成功: user={user.id}, client={application.client_id}")
        return auth_code
    
    def exchange_code_for_token(
        self,
        code: str,
        application: OAuthApplication,
        redirect_uri: str,
        code_verifier: str = None
    ) -> Tuple[OAuthAccessToken, Optional[OAuthRefreshToken]]:
        """
        用授权码换取访问令牌
        
        Args:
            code: 授权码
            application: OAuth应用实例
            redirect_uri: 重定向URI
            code_verifier: PKCE代码验证器
            
        Returns:
            tuple: (访问令牌, 刷新令牌)
        """
        try:
            # 获取授权码
            auth_code = OAuthAuthorizationCode.objects.get(
                code=code,
                application=application,
                is_deleted=False
            )
            
            # 验证授权码
            if not auth_code.is_valid():
                raise ValidationError("Authorization code is invalid or expired")
            
            if auth_code.redirect_uri != redirect_uri:
                raise ValidationError("Redirect URI mismatch")
            
            # 验证PKCE
            if not auth_code.verify_pkce_challenge(code_verifier or ''):
                raise ValidationError("PKCE verification failed")
            
            with transaction.atomic():
                # 标记授权码为已使用
                auth_code.use()
                
                # 创建访问令牌
                access_token = OAuthAccessToken.objects.create(
                    user=auth_code.user,
                    application=application,
                    scope=auth_code.scope,
                    tenant_id=auth_code.tenant_id
                )
                
                # 创建刷新令牌（如果应用支持）
                refresh_token = None
                if 'refresh_token' in application.grant_types:
                    refresh_token = OAuthRefreshToken.objects.create(
                        access_token=access_token,
                        tenant_id=auth_code.tenant_id
                    )
                
                # 缓存令牌信息
                access_token.cache_token_info()
                
                # 记录审计日志
                self._create_audit_log(
                    user=auth_code.user,
                    application=application,
                    action='token_issued',
                    resource_type='access_token',
                    resource_id=access_token.id,
                    is_success=True
                )
                
                logger.info(f"令牌交换成功: user={auth_code.user.id}, client={application.client_id}")
                return access_token, refresh_token
                
        except OAuthAuthorizationCode.DoesNotExist:
            logger.warning(f"授权码不存在: {code}")
            raise ValidationError("Invalid authorization code")
    
    def refresh_access_token(
        self,
        refresh_token: str,
        application: OAuthApplication,
        scope: List[str] = None
    ) -> Tuple[OAuthAccessToken, OAuthRefreshToken]:
        """
        刷新访问令牌
        
        Args:
            refresh_token: 刷新令牌
            application: OAuth应用实例
            scope: 新的权限范围（可选）
            
        Returns:
            tuple: (新访问令牌, 新刷新令牌)
        """
        try:
            # 获取刷新令牌
            refresh_token_obj = OAuthRefreshToken.objects.select_related('access_token').get(
                token=refresh_token,
                access_token__application=application,
                is_deleted=False
            )
            
            # 验证刷新令牌
            if not refresh_token_obj.is_valid():
                raise ValidationError("Refresh token is invalid or expired")
            
            old_access_token = refresh_token_obj.access_token
            
            with transaction.atomic():
                # 撤销旧令牌
                old_access_token.revoke()
                old_access_token.revoke_cached_info()
                refresh_token_obj.revoke()
                
                # 创建新的访问令牌
                new_access_token = OAuthAccessToken.objects.create(
                    user=old_access_token.user,
                    application=application,
                    scope=scope or old_access_token.scope,
                    tenant_id=old_access_token.tenant_id
                )
                
                # 创建新的刷新令牌
                new_refresh_token = OAuthRefreshToken.objects.create(
                    access_token=new_access_token,
                    tenant_id=old_access_token.tenant_id
                )
                
                # 缓存新令牌信息
                new_access_token.cache_token_info()
                
                # 记录审计日志
                self._create_audit_log(
                    user=old_access_token.user,
                    application=application,
                    action='token_refreshed',
                    resource_type='access_token',
                    resource_id=new_access_token.id,
                    is_success=True
                )
                
                logger.info(f"令牌刷新成功: user={old_access_token.user.id}, client={application.client_id}")
                return new_access_token, new_refresh_token
                
        except OAuthRefreshToken.DoesNotExist:
            logger.warning(f"刷新令牌不存在: {refresh_token}")
            raise ValidationError("Invalid refresh token")
    
    def validate_access_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        验证访问令牌
        
        Args:
            token: 访问令牌
            
        Returns:
            dict: 令牌信息，无效返回None
        """
        # 先从缓存获取
        cached_info = self.token_manager.get_cached_token_info(token)
        if cached_info:
            # 检查是否过期
            expires_at = timezone.datetime.fromisoformat(cached_info['expires_at'])
            if timezone.now() < expires_at and not cached_info.get('is_revoked'):
                return cached_info
        
        # 从数据库查询
        try:
            access_token = OAuthAccessToken.objects.select_related('user', 'application').get(
                token=token,
                is_deleted=False
            )
            
            if access_token.is_valid():
                # 更新最后使用时间
                access_token.update_last_used()
                
                # 重新缓存
                access_token.cache_token_info()
                
                return access_token.get_token_info()
            
        except OAuthAccessToken.DoesNotExist:
            pass
        
        return None
    
    def revoke_token(self, token: str, token_type: str = 'access_token') -> bool:
        """
        撤销令牌
        
        Args:
            token: 令牌
            token_type: 令牌类型
            
        Returns:
            bool: 是否成功撤销
        """
        try:
            if token_type == 'access_token':
                token_obj = OAuthAccessToken.objects.get(token=token, is_deleted=False)
                token_obj.revoke()
                token_obj.revoke_cached_info()
                
                # 同时撤销关联的刷新令牌
                if hasattr(token_obj, 'refresh_token'):
                    token_obj.refresh_token.revoke()
                    
            elif token_type == 'refresh_token':
                token_obj = OAuthRefreshToken.objects.get(token=token, is_deleted=False)
                token_obj.revoke()
                
                # 同时撤销关联的访问令牌
                token_obj.access_token.revoke()
                token_obj.access_token.revoke_cached_info()
            
            # 记录审计日志
            self._create_audit_log(
                user=getattr(token_obj, 'user', None) or getattr(token_obj, 'access_token', {}).user,
                application=getattr(token_obj, 'application', None) or getattr(token_obj, 'access_token', {}).application,
                action='token_revoked',
                resource_type=token_type,
                resource_id=token_obj.id,
                is_success=True
            )
            
            logger.info(f"令牌撤销成功: token_type={token_type}")
            return True
            
        except (OAuthAccessToken.DoesNotExist, OAuthRefreshToken.DoesNotExist):
            logger.warning(f"令牌不存在: token_type={token_type}")
            return False
    
    def _create_audit_log(
        self,
        user: User = None,
        application: OAuthApplication = None,
        action: str = '',
        resource_type: str = '',
        resource_id: str = '',
        details: Dict[str, Any] = None,
        is_success: bool = True,
        error_message: str = ''
    ):
        """创建审计日志"""
        try:
            OAuthAuditLog.objects.create(
                user=user,
                application=application,
                action=action,
                resource_type=resource_type,
                resource_id=str(resource_id),
                details=details or {},
                is_success=is_success,
                error_message=error_message,
                tenant_id=getattr(user, 'tenant_id', None) if user else None
            )
        except Exception as e:
            logger.error(f"创建审计日志失败: {e}")


# 全局服务实例
oauth2_service = OAuth2Service()
