# Generated by Django 5.2 on 2025-07-24 08:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhi_oauth', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExampleProduct',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.Char<PERSON>ield(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.<PERSON>r<PERSON>ield(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='产品显示名称', help_text='产品显示名称', max_length=100, verbose_name='产品名称')),
                ('description', models.TextField(blank=True, db_comment='产品详细描述', help_text='产品详细描述', verbose_name='产品描述')),
            ],
            options={
                'verbose_name': '示例产品',
                'verbose_name_plural': '示例产品',
                'db_table': 'zhi_oauth_example_product',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='oauthauthorizationcode',
            name='used_at',
            field=models.DateTimeField(blank=True, db_comment='授权码使用时间', help_text='授权码使用时间', null=True, verbose_name='使用时间'),
        ),
    ]
