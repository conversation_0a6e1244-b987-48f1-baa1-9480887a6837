# Generated by Django 5.2 on 2025-07-27 14:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhi_oauth', '0004_1_clean_data_before_fk_change'),
    ]

    operations = [
        migrations.AlterField(
            model_name='oauthaccesstoken',
            name='user',
            field=models.ForeignKey(db_comment='令牌所属用户', help_text='令牌所属用户', on_delete=django.db.models.deletion.CASCADE, related_name='oauth_access_tokens', to=settings.AUTH_USER_MODEL, to_field='id', verbose_name='关联用户'),
        ),
        migrations.AlterField(
            model_name='oauthauthorizationcode',
            name='user',
            field=models.ForeignKey(db_comment='授权码所属用户', help_text='授权码所属用户', on_delete=django.db.models.deletion.CASCADE, related_name='oauth_authorization_codes', to=settings.AUTH_USER_MODEL, to_field='id', verbose_name='关联用户'),
        ),
        migrations.AlterField(
            model_name='organization',
            name='leader_user',
            field=models.ForeignKey(blank=True, db_comment='组织负责人用户账户', help_text='组织负责人用户账户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='led_organizations', to=settings.AUTH_USER_MODEL, to_field='id', verbose_name='负责人用户'),
        ),
        migrations.AlterField(
            model_name='tenant',
            name='admin_user',
            field=models.ForeignKey(blank=True, db_comment='租户管理员用户', help_text='租户管理员用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_tenants', to=settings.AUTH_USER_MODEL, to_field='id', verbose_name='租户管理员'),
        ),
        migrations.AlterField(
            model_name='userrole',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, to_field='id', verbose_name='用户'),
        ),
        migrations.AlterField(
            model_name='userthirdparty',
            name='user',
            field=models.ForeignKey(help_text='关联的用户账户', on_delete=django.db.models.deletion.CASCADE, related_name='third_party_accounts', to=settings.AUTH_USER_MODEL, to_field='id', verbose_name='关联用户'),
        ),
    ]
