# Generated by Django 5.2 on 2025-07-24 09:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('zhi_oauth', '0002_exampleproduct_oauthauthorizationcode_used_at'),
    ]

    operations = [
        migrations.AlterField(
            model_name='oauthauthorizationcode',
            name='challenge',
            field=models.CharField(blank=True, db_comment='PKCE代码挑战', help_text='PKCE代码挑战', max_length=128, null=True, verbose_name='PKCE挑战码'),
        ),
        migrations.AlterField(
            model_name='oauthauthorizationcode',
            name='challenge_method',
            field=models.CharField(blank=True, choices=[('plain', 'Plain'), ('S256', 'SHA256')], db_comment='PKCE挑战方法', help_text='PKCE挑战方法', max_length=10, null=True, verbose_name='挑战方法'),
        ),
    ]
