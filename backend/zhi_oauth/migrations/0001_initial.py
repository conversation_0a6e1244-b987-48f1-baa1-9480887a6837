# Generated by Django 5.2 on 2025-07-23 15:14

import django.contrib.auth.models
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='OAuthApplication',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='OAuth2应用显示名称', help_text='OAuth2应用显示名称', max_length=100, verbose_name='应用名称')),
                ('description', models.TextField(blank=True, db_comment='应用功能描述', help_text='应用功能描述', verbose_name='应用描述')),
                ('client_id', models.CharField(db_comment='OAuth2客户端标识', help_text='OAuth2客户端标识', max_length=100, unique=True, verbose_name='客户端ID')),
                ('client_secret', models.CharField(db_comment='OAuth2客户端密钥', help_text='OAuth2客户端密钥', max_length=100, verbose_name='客户端密钥')),
                ('app_type', models.CharField(choices=[('web', 'Web应用'), ('mobile', '移动应用'), ('desktop', '桌面应用'), ('service', '服务应用'), ('spa', '单页应用')], db_comment='应用类型分类', default='web', help_text='应用类型分类', max_length=20, verbose_name='应用类型')),
                ('grant_types', models.JSONField(db_comment='应用支持的OAuth2授权模式', default=list, help_text='应用支持的OAuth2授权模式', verbose_name='支持的授权类型')),
                ('redirect_uris', models.JSONField(db_comment='OAuth2授权后的重定向地址', default=list, help_text='OAuth2授权后的重定向地址', verbose_name='重定向URI列表')),
                ('allowed_scopes', models.JSONField(db_comment='应用可申请的权限范围', default=list, help_text='应用可申请的权限范围', verbose_name='允许的权限范围')),
                ('logo_url', models.URLField(blank=True, db_comment='应用图标URL', help_text='应用图标URL', verbose_name='应用图标')),
                ('home_url', models.URLField(blank=True, db_comment='应用主页URL', help_text='应用主页URL', verbose_name='应用主页')),
                ('terms_url', models.URLField(blank=True, db_comment='应用服务条款页面', help_text='应用服务条款页面', verbose_name='服务条款URL')),
                ('privacy_policy_url', models.URLField(blank=True, db_comment='应用隐私政策页面', help_text='应用隐私政策页面', verbose_name='隐私政策URL')),
                ('is_active', models.BooleanField(db_comment='应用状态', default=True, help_text='应用状态', verbose_name='是否激活')),
                ('is_verified', models.BooleanField(db_comment='应用是否通过验证', default=False, help_text='应用是否通过验证', verbose_name='是否已验证')),
                ('require_consent', models.BooleanField(db_comment='是否需要用户明确同意授权', default=True, help_text='是否需要用户明确同意授权', verbose_name='需要用户同意')),
                ('reuse_consent', models.BooleanField(db_comment='是否可以重用之前的授权同意', default=True, help_text='是否可以重用之前的授权同意', verbose_name='重用同意')),
                ('skip_authorization', models.BooleanField(db_comment='信任应用，跳过授权确认页面', default=False, help_text='信任应用，跳过授权确认页面', verbose_name='跳过授权页面')),
                ('access_token_lifetime', models.IntegerField(db_comment='访问令牌有效期（秒）', default=3600, help_text='访问令牌有效期（秒）', verbose_name='访问令牌有效期')),
                ('refresh_token_lifetime', models.IntegerField(db_comment='刷新令牌有效期（秒）', default=604800, help_text='刷新令牌有效期（秒）', verbose_name='刷新令牌有效期')),
            ],
            options={
                'verbose_name': 'OAuth2应用',
                'verbose_name_plural': 'OAuth2应用',
                'db_table': 'zhi_oauth_application',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OAuthScope',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='权限范围的唯一标识', help_text='权限范围的唯一标识', max_length=100, unique=True, verbose_name='权限范围名称')),
                ('description', models.TextField(blank=True, db_comment='权限范围的详细描述', help_text='权限范围的详细描述', verbose_name='权限范围描述')),
                ('sort_order', models.IntegerField(db_comment='显示排序', default=1, help_text='显示排序', verbose_name='排序')),
                ('is_default', models.BooleanField(db_comment='是否为默认权限范围', default=False, help_text='是否为默认权限范围', verbose_name='是否默认')),
                ('is_public', models.BooleanField(db_comment='是否对所有应用公开', default=True, help_text='是否对所有应用公开', verbose_name='是否公开')),
            ],
            options={
                'verbose_name': 'OAuth2权限范围',
                'verbose_name_plural': 'OAuth2权限范围',
                'db_table': 'zhi_oauth_scope',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='角色显示名称', help_text='角色显示名称', max_length=100, verbose_name='角色名称')),
                ('code', models.CharField(db_comment='角色唯一标识码', help_text='角色唯一标识码', max_length=100, unique=True, verbose_name='角色编码')),
                ('is_system', models.BooleanField(db_comment='系统内置角色不可删除', default=False, help_text='系统内置角色不可删除', verbose_name='是否系统角色')),
                ('is_admin', models.BooleanField(db_comment='管理员角色拥有更多权限', default=False, help_text='管理员角色拥有更多权限', verbose_name='是否管理员角色')),
                ('data_scope', models.CharField(choices=[('all', '全部数据'), ('tenant', '租户数据'), ('org', '本部门数据'), ('org_and_sub', '本部门及子部门数据'), ('self', '仅本人数据'), ('custom', '自定义数据范围')], db_comment='角色的数据访问范围', default='org', help_text='角色的数据访问范围', max_length=20, verbose_name='数据权限范围')),
                ('description', models.TextField(blank=True, db_comment='角色功能描述', help_text='角色功能描述', null=True, verbose_name='角色描述')),
                ('is_active', models.BooleanField(db_comment='角色是否启用', default=True, help_text='角色是否启用', verbose_name='是否启用')),
                ('sort_order', models.IntegerField(db_comment='角色显示排序', default=1, help_text='角色显示排序', verbose_name='排序')),
            ],
            options={
                'verbose_name': '角色',
                'verbose_name_plural': '角色',
                'db_table': 'zhi_oauth_role',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('username', models.CharField(db_comment='用户登录账号', db_index=True, help_text='用户登录账号', max_length=150, unique=True, verbose_name='用户账号')),
                ('email', models.EmailField(blank=True, db_comment='用户邮箱', help_text='用户邮箱', max_length=255, null=True, verbose_name='邮箱')),
                ('mobile', models.CharField(blank=True, db_comment='用户手机号', help_text='用户手机号', max_length=20, null=True, validators=[django.core.validators.RegexValidator(message='请输入有效的手机号码', regex='^1[3-9]\\d{9}$')], verbose_name='手机号')),
                ('avatar', models.TextField(blank=True, db_comment='用户头像URL', help_text='用户头像URL', null=True, verbose_name='头像')),
                ('name', models.CharField(blank=True, db_comment='用户真实姓名', help_text='用户真实姓名', max_length=40, null=True, verbose_name='真实姓名')),
                ('status', models.BooleanField(db_comment='账户是否正常', default=True, help_text='账户是否正常', verbose_name='账户状态')),
                ('gender', models.IntegerField(blank=True, choices=[(0, '女'), (1, '男'), (2, '未知')], db_comment='用户性别', default=2, help_text='用户性别', null=True, verbose_name='性别')),
                ('user_type', models.IntegerField(blank=True, choices=[(0, '后台用户'), (1, '前台用户'), (2, 'API用户'), (3, '系统用户')], db_comment='用户类型分类', default=0, help_text='用户类型分类', null=True, verbose_name='用户类型')),
                ('home_path', models.CharField(blank=True, db_comment='用户登录后的默认主页路径', help_text='用户登录后的默认主页路径', max_length=150, null=True)),
                ('oauth_provider', models.CharField(blank=True, db_comment='第三方登录提供商', help_text='第三方登录提供商', max_length=50, null=True, verbose_name='OAuth提供商')),
                ('oauth_uid', models.CharField(blank=True, db_comment='第三方平台用户ID', help_text='第三方平台用户ID', max_length=100, null=True, verbose_name='OAuth用户ID')),
                ('login_failure_count', models.IntegerField(db_comment='连续登录失败次数', default=0, help_text='连续登录失败次数', verbose_name='登录失败次数')),
                ('locked_until', models.DateTimeField(blank=True, db_comment='账户锁定到期时间', help_text='账户锁定到期时间', null=True, verbose_name='锁定到期时间')),
                ('password_changed_at', models.DateTimeField(blank=True, db_comment='最后一次密码修改时间', help_text='最后一次密码修改时间', null=True, verbose_name='密码修改时间')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'zhi_oauth_user',
                'ordering': ['-date_joined'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='OAuthAccessToken',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('token', models.CharField(db_comment='OAuth2访问令牌字符串', db_index=True, help_text='OAuth2访问令牌字符串', max_length=64, unique=True, verbose_name='访问令牌')),
                ('token_type', models.CharField(choices=[('oauth2', 'OAuth2访问令牌'), ('bearer', 'Bearer令牌'), ('api_key', 'API密钥')], db_comment='令牌类型标识', default='oauth2', help_text='令牌类型标识', max_length=20, verbose_name='令牌类型')),
                ('scope', models.JSONField(db_comment='令牌授权的权限范围', default=list, help_text='令牌授权的权限范围', verbose_name='权限范围')),
                ('expires_at', models.DateTimeField(db_comment='令牌过期时间', help_text='令牌过期时间', verbose_name='过期时间')),
                ('is_revoked', models.BooleanField(db_comment='令牌是否被撤销', default=False, help_text='令牌是否被撤销', verbose_name='是否已撤销')),
                ('last_used_at', models.DateTimeField(blank=True, db_comment='令牌最后使用时间', help_text='令牌最后使用时间', null=True, verbose_name='最后使用时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, db_comment='令牌创建时的IP地址', help_text='令牌创建时的IP地址', null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, db_comment='令牌创建时的用户代理信息', help_text='令牌创建时的用户代理信息', verbose_name='用户代理')),
                ('user', models.ForeignKey(db_comment='令牌所属用户', help_text='令牌所属用户', on_delete=django.db.models.deletion.CASCADE, related_name='oauth_access_tokens', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
                ('application', models.ForeignKey(db_comment='令牌所属应用', help_text='令牌所属应用', on_delete=django.db.models.deletion.CASCADE, related_name='access_tokens', to='zhi_oauth.oauthapplication', verbose_name='关联应用')),
            ],
            options={
                'verbose_name': 'OAuth2访问令牌',
                'verbose_name_plural': 'OAuth2访问令牌',
                'db_table': 'zhi_oauth_access_token',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OAuthAuthorizationCode',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('code', models.CharField(db_comment='OAuth2授权码字符串', db_index=True, help_text='OAuth2授权码字符串', max_length=64, unique=True, verbose_name='授权码')),
                ('redirect_uri', models.URLField(db_comment='授权后重定向地址', help_text='授权后重定向地址', verbose_name='重定向URI')),
                ('scope', models.JSONField(db_comment='授权码对应的权限范围', default=list, help_text='授权码对应的权限范围', verbose_name='权限范围')),
                ('expires_at', models.DateTimeField(db_comment='授权码过期时间', help_text='授权码过期时间', verbose_name='过期时间')),
                ('is_used', models.BooleanField(db_comment='授权码是否已被使用', default=False, help_text='授权码是否已被使用', verbose_name='是否已使用')),
                ('challenge', models.CharField(blank=True, db_comment='PKCE代码挑战', help_text='PKCE代码挑战', max_length=128, verbose_name='PKCE挑战码')),
                ('challenge_method', models.CharField(blank=True, choices=[('plain', 'Plain'), ('S256', 'SHA256')], db_comment='PKCE挑战方法', help_text='PKCE挑战方法', max_length=10, verbose_name='挑战方法')),
                ('application', models.ForeignKey(db_comment='授权码所属应用', help_text='授权码所属应用', on_delete=django.db.models.deletion.CASCADE, related_name='authorization_codes', to='zhi_oauth.oauthapplication', verbose_name='关联应用')),
                ('user', models.ForeignKey(db_comment='授权码所属用户', help_text='授权码所属用户', on_delete=django.db.models.deletion.CASCADE, related_name='oauth_authorization_codes', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': 'OAuth2授权码',
                'verbose_name_plural': 'OAuth2授权码',
                'db_table': 'zhi_oauth_authorization_code',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='OAuthRefreshToken',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('token', models.CharField(db_comment='OAuth2刷新令牌字符串', db_index=True, help_text='OAuth2刷新令牌字符串', max_length=64, unique=True, verbose_name='刷新令牌')),
                ('expires_at', models.DateTimeField(db_comment='刷新令牌过期时间', help_text='刷新令牌过期时间', verbose_name='过期时间')),
                ('is_revoked', models.BooleanField(db_comment='刷新令牌是否被撤销', default=False, help_text='刷新令牌是否被撤销', verbose_name='是否已撤销')),
                ('access_token', models.OneToOneField(db_comment='对应的访问令牌', help_text='对应的访问令牌', on_delete=django.db.models.deletion.CASCADE, related_name='refresh_token', to='zhi_oauth.oauthaccesstoken', verbose_name='关联访问令牌')),
            ],
            options={
                'verbose_name': 'OAuth2刷新令牌',
                'verbose_name_plural': 'OAuth2刷新令牌',
                'db_table': 'zhi_oauth_refresh_token',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('org_code', models.CharField(db_comment='组织唯一编码', help_text='组织唯一编码', max_length=50, unique=True, verbose_name='组织编码')),
                ('org_name', models.CharField(db_comment='组织部门名称', help_text='组织部门名称', max_length=100, verbose_name='组织名称')),
                ('org_level', models.IntegerField(db_comment='组织在树形结构中的层级', default=1, help_text='组织在树形结构中的层级', verbose_name='组织层级')),
                ('org_sort', models.IntegerField(db_comment='同级组织的排序', default=1, help_text='同级组织的排序', verbose_name='排序')),
                ('leader', models.CharField(blank=True, db_comment='组织负责人姓名', help_text='组织负责人姓名', max_length=50, null=True, verbose_name='负责人')),
                ('phone', models.CharField(blank=True, db_comment='组织联系电话', help_text='组织联系电话', max_length=20, null=True, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, db_comment='组织联系邮箱', help_text='组织联系邮箱', max_length=254, null=True, verbose_name='联系邮箱')),
                ('description', models.TextField(blank=True, db_comment='组织职能描述', help_text='组织职能描述', null=True, verbose_name='组织描述')),
                ('is_active', models.BooleanField(db_comment='组织是否启用', default=True, help_text='组织是否启用', verbose_name='是否启用')),
                ('leader_user', models.ForeignKey(blank=True, db_comment='组织负责人用户账户', help_text='组织负责人用户账户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='led_organizations', to=settings.AUTH_USER_MODEL, verbose_name='负责人用户')),
                ('parent', models.ForeignKey(blank=True, db_comment='上级组织部门', help_text='上级组织部门', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='zhi_oauth.organization', verbose_name='上级组织')),
            ],
            options={
                'verbose_name': '组织架构',
                'verbose_name_plural': '组织架构',
                'db_table': 'zhi_oauth_organization',
                'ordering': ['org_sort', 'org_name'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='organization',
            field=models.ForeignKey(blank=True, db_comment='用户所属组织', help_text='用户所属组织', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='zhi_oauth.organization', verbose_name='所属组织'),
        ),
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='权限显示名称', help_text='权限显示名称', max_length=100, verbose_name='权限名称')),
                ('code', models.CharField(db_comment='权限唯一标识码', help_text='权限唯一标识码', max_length=100, unique=True, verbose_name='权限编码')),
                ('permission_type', models.CharField(choices=[('menu', '菜单权限'), ('button', '按钮权限'), ('api', 'API权限'), ('data', '数据权限'), ('field', '字段权限')], db_comment='权限分类类型', default='menu', help_text='权限分类类型', max_length=20, verbose_name='权限类型')),
                ('resource_path', models.CharField(blank=True, db_comment='API路径或菜单路径', help_text='API路径或菜单路径', max_length=200, null=True, verbose_name='资源路径')),
                ('http_method', models.CharField(blank=True, db_comment='API的HTTP请求方法', help_text='API的HTTP请求方法', max_length=10, null=True, verbose_name='HTTP方法')),
                ('description', models.TextField(blank=True, db_comment='权限功能描述', help_text='权限功能描述', null=True, verbose_name='权限描述')),
                ('is_active', models.BooleanField(db_comment='权限是否启用', default=True, help_text='权限是否启用', verbose_name='是否启用')),
                ('sort_order', models.IntegerField(db_comment='权限显示排序', default=1, help_text='权限显示排序', verbose_name='排序')),
                ('parent', models.ForeignKey(blank=True, db_comment='上级权限节点', help_text='上级权限节点', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='zhi_oauth.permission', verbose_name='上级权限')),
            ],
            options={
                'verbose_name': '权限',
                'verbose_name_plural': '权限',
                'db_table': 'zhi_oauth_permission',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_permissions', to=settings.AUTH_USER_MODEL, verbose_name='授权人')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_permissions', to='zhi_oauth.permission', verbose_name='权限')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_permissions', to='zhi_oauth.role', verbose_name='角色')),
            ],
            options={
                'verbose_name': '角色权限关联',
                'verbose_name_plural': '角色权限关联',
                'db_table': 'zhi_oauth_role_permission',
            },
        ),
        migrations.AddField(
            model_name='role',
            name='permissions',
            field=models.ManyToManyField(help_text='角色拥有的权限', related_name='roles', through='zhi_oauth.RolePermission', to='zhi_oauth.permission', verbose_name='角色权限'),
        ),
        migrations.CreateModel(
            name='Tenant',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('name', models.CharField(db_comment='租户显示名称', help_text='租户显示名称', max_length=100, unique=True, verbose_name='租户名称')),
                ('code', models.CharField(db_comment='租户唯一标识码', help_text='租户唯一标识码', max_length=50, unique=True, verbose_name='租户编码')),
                ('tenant_type', models.CharField(choices=[('normal', '普通租户'), ('super', '超级租户'), ('sub', '子租户')], db_comment='租户类型分类', default='normal', help_text='租户类型分类', max_length=20, verbose_name='租户类型')),
                ('city', models.CharField(blank=True, db_comment='企业所在城市', help_text='企业所在城市', max_length=50, null=True, verbose_name='城市')),
                ('province', models.CharField(blank=True, db_comment='企业所在省份', help_text='企业所在省份', max_length=50, null=True, verbose_name='省份')),
                ('district', models.CharField(blank=True, db_comment='企业所在区县', help_text='企业所在区县', max_length=50, null=True, verbose_name='区县')),
                ('address', models.CharField(blank=True, db_comment='企业详细地址', help_text='企业详细地址', max_length=200, null=True, verbose_name='详细地址')),
                ('corp_size', models.CharField(blank=True, db_comment='企业人员规模', help_text='企业人员规模', max_length=50, null=True, verbose_name='企业规模')),
                ('corp_years', models.CharField(blank=True, db_comment='企业成立年限', help_text='企业成立年限', max_length=50, null=True, verbose_name='成立年限')),
                ('industry', models.CharField(blank=True, db_comment='企业所属行业', help_text='企业所属行业', max_length=100, null=True, verbose_name='所属行业')),
                ('domain', models.CharField(blank=True, db_comment='租户专属域名', help_text='租户专属域名', max_length=100, null=True, verbose_name='专属域名')),
                ('logo', models.CharField(blank=True, db_comment='企业Logo图片URL', help_text='企业Logo图片URL', max_length=200, null=True, verbose_name='企业Logo')),
                ('is_active', models.BooleanField(db_comment='租户是否正常使用', default=True, help_text='租户是否正常使用', verbose_name='是否激活')),
                ('max_users', models.IntegerField(db_comment='租户允许的最大用户数量', default=100, help_text='租户允许的最大用户数量', verbose_name='最大用户数')),
                ('expire_date', models.DateTimeField(blank=True, db_comment='租户服务到期时间', help_text='租户服务到期时间', null=True, verbose_name='服务到期时间')),
                ('admin_user', models.ForeignKey(blank=True, db_comment='租户管理员用户', help_text='租户管理员用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_tenants', to=settings.AUTH_USER_MODEL, verbose_name='租户管理员')),
            ],
            options={
                'verbose_name': '租户',
                'verbose_name_plural': '租户',
                'db_table': 'zhi_oauth_tenant',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='role',
            name='tenant',
            field=models.ForeignKey(blank=True, db_comment='角色所属租户', help_text='角色所属租户', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='roles', to='zhi_oauth.tenant', verbose_name='所属租户'),
        ),
        migrations.AddField(
            model_name='organization',
            name='tenant',
            field=models.ForeignKey(db_comment='组织所属租户', help_text='组织所属租户', on_delete=django.db.models.deletion.CASCADE, related_name='organizations', to='zhi_oauth.tenant', verbose_name='所属租户'),
        ),
        migrations.AddField(
            model_name='oauthapplication',
            name='tenant',
            field=models.ForeignKey(blank=True, db_comment='应用所属租户', help_text='应用所属租户', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='oauth_applications', to='zhi_oauth.tenant', verbose_name='所属租户'),
        ),
        migrations.AddField(
            model_name='user',
            name='tenant',
            field=models.ForeignKey(blank=True, db_comment='用户所属租户', help_text='用户所属租户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='zhi_oauth.tenant', verbose_name='所属租户'),
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='过期时间')),
                ('granted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='granted_roles', to=settings.AUTH_USER_MODEL, verbose_name='授权人')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='zhi_oauth.role', verbose_name='角色')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户角色关联',
                'verbose_name_plural': '用户角色关联',
                'db_table': 'zhi_oauth_user_role',
            },
        ),
        migrations.AddField(
            model_name='user',
            name='roles',
            field=models.ManyToManyField(help_text='用户拥有的角色', related_name='users', through='zhi_oauth.UserRole', through_fields=('user', 'role'), to='zhi_oauth.role', verbose_name='用户角色'),
        ),
        migrations.CreateModel(
            name='UserThirdParty',
            fields=[
                ('seq', models.BigAutoField(db_comment='主键Seq', help_text='seq', primary_key=True, serialize=False, verbose_name='seq')),
                ('id', models.CharField(db_comment='Id', db_index=True, default='', help_text='Id', max_length=63, unique=True, verbose_name='Id')),
                ('creator_id', models.CharField(blank=True, db_comment='创建人用户ID', db_index=True, help_text='创建人用户ID', max_length=63, null=True, verbose_name='创建人ID')),
                ('creator_name', models.CharField(blank=True, db_comment='创建人姓名', help_text='创建人姓名', max_length=255, null=True, verbose_name='创建人姓名')),
                ('modifier_id', models.CharField(blank=True, db_comment='修改人用户ID', db_index=True, help_text='修改人用户ID', max_length=63, null=True, verbose_name='修改人ID')),
                ('modifier_name', models.CharField(blank=True, db_comment='修改人姓名', help_text='修改人姓名', max_length=255, null=True, verbose_name='修改人姓名')),
                ('updated_at', models.DateTimeField(auto_now=True, db_comment='修改时间', help_text='修改时间', null=True, verbose_name='修改时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_comment='创建时间', help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_deleted', models.BooleanField(db_comment='是否软删除', db_index=True, default=False, help_text='是否软删除', verbose_name='是否软删除')),
                ('deleted_at', models.DateTimeField(blank=True, db_comment='删除时间', default=None, help_text='删除时间', null=True, verbose_name='删除时间')),
                ('org_id', models.CharField(blank=True, db_comment='数据归属部门', help_text='数据归属部门', max_length=63, null=True, verbose_name='数据归属部门')),
                ('platform', models.CharField(choices=[('wechat', '微信'), ('gitee', 'Gitee'), ('github', 'GitHub'), ('gitlab', 'GitLab'), ('weibo', '微博'), ('dingtalk', '钉钉'), ('baidu', '百度'), ('alipay', '支付宝'), ('qq', 'QQ')], db_comment='第三方登录平台', help_text='第三方登录平台', max_length=20, verbose_name='第三方平台')),
                ('platform_uid', models.CharField(db_comment='第三方平台的用户唯一标识', help_text='第三方平台的用户唯一标识', max_length=100, verbose_name='平台用户ID')),
                ('platform_username', models.CharField(blank=True, db_comment='第三方平台的用户名', help_text='第三方平台的用户名', max_length=100, null=True, verbose_name='平台用户名')),
                ('access_token', models.TextField(blank=True, db_comment='第三方平台的访问令牌', help_text='第三方平台的访问令牌', null=True, verbose_name='访问令牌')),
                ('refresh_token', models.TextField(blank=True, db_comment='第三方平台的刷新令牌', help_text='第三方平台的刷新令牌', null=True, verbose_name='刷新令牌')),
                ('expires_at', models.DateTimeField(blank=True, db_comment='第三方平台令牌过期时间', help_text='第三方平台令牌过期时间', null=True, verbose_name='令牌过期时间')),
                ('extra_data', models.JSONField(db_comment='第三方平台返回的额外用户信息', default=dict, help_text='第三方平台返回的额外用户信息', verbose_name='额外数据')),
                ('user', models.ForeignKey(help_text='关联的用户账户', on_delete=django.db.models.deletion.CASCADE, related_name='third_party_accounts', to=settings.AUTH_USER_MODEL, verbose_name='关联用户')),
            ],
            options={
                'verbose_name': '用户第三方账户',
                'verbose_name_plural': '用户第三方账户',
                'db_table': 'zhi_oauth_user_third_party',
            },
        ),
        migrations.AddIndex(
            model_name='oauthaccesstoken',
            index=models.Index(fields=['token'], name='zhi_oauth_a_token_f2619c_idx'),
        ),
        migrations.AddIndex(
            model_name='oauthaccesstoken',
            index=models.Index(fields=['user', 'application'], name='zhi_oauth_a_user_id_d89529_idx'),
        ),
        migrations.AddIndex(
            model_name='oauthaccesstoken',
            index=models.Index(fields=['expires_at'], name='zhi_oauth_a_expires_7ed97d_idx'),
        ),
        migrations.AddIndex(
            model_name='oauthaccesstoken',
            index=models.Index(fields=['is_revoked'], name='zhi_oauth_a_is_revo_3dfb67_idx'),
        ),
        migrations.AddIndex(
            model_name='permission',
            index=models.Index(fields=['code'], name='zhi_oauth_p_code_bc6d7a_idx'),
        ),
        migrations.AddIndex(
            model_name='permission',
            index=models.Index(fields=['permission_type'], name='zhi_oauth_p_permiss_b19fda_idx'),
        ),
        migrations.AddIndex(
            model_name='permission',
            index=models.Index(fields=['parent'], name='zhi_oauth_p_parent__ed1d4f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='rolepermission',
            unique_together={('role', 'permission')},
        ),
        migrations.AddIndex(
            model_name='role',
            index=models.Index(fields=['code'], name='zhi_oauth_r_code_c5d985_idx'),
        ),
        migrations.AddIndex(
            model_name='role',
            index=models.Index(fields=['tenant'], name='zhi_oauth_r_tenant__ade051_idx'),
        ),
        migrations.AddIndex(
            model_name='organization',
            index=models.Index(fields=['tenant'], name='zhi_oauth_o_tenant__fef21c_idx'),
        ),
        migrations.AddIndex(
            model_name='organization',
            index=models.Index(fields=['parent'], name='zhi_oauth_o_parent__b49cde_idx'),
        ),
        migrations.AddIndex(
            model_name='organization',
            index=models.Index(fields=['org_code'], name='zhi_oauth_o_org_cod_4ba443_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userrole',
            unique_together={('user', 'role')},
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['username'], name='zhi_oauth_u_usernam_e12658_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['email'], name='zhi_oauth_u_email_ca6b94_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['mobile'], name='zhi_oauth_u_mobile_bca5b7_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['tenant'], name='zhi_oauth_u_tenant__570720_idx'),
        ),
        migrations.AddIndex(
            model_name='user',
            index=models.Index(fields=['organization'], name='zhi_oauth_u_organiz_825357_idx'),
        ),
        migrations.AddIndex(
            model_name='userthirdparty',
            index=models.Index(fields=['user'], name='zhi_oauth_u_user_id_6566bc_idx'),
        ),
        migrations.AddIndex(
            model_name='userthirdparty',
            index=models.Index(fields=['platform', 'platform_uid'], name='zhi_oauth_u_platfor_fabd12_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userthirdparty',
            unique_together={('platform', 'platform_uid')},
        ),
    ]
