# Generated manually to clean data before foreign key changes

from django.db import migrations


def clean_oauth_data(apps, schema_editor):
    """清理OAuth相关数据，为外键修改做准备"""
    
    # 获取模型
    OAuthAccessToken = apps.get_model('zhi_oauth', 'OAuthAccessToken')
    OAuthRefreshToken = apps.get_model('zhi_oauth', 'OAuthRefreshToken')
    OAuthAuthorizationCode = apps.get_model('zhi_oauth', 'OAuthAuthorizationCode')
    UserThirdParty = apps.get_model('zhi_oauth', 'UserThirdParty')
    UserRole = apps.get_model('zhi_oauth', 'UserRole')
    
    # 清理OAuth访问令牌
    deleted_tokens = OAuthAccessToken.objects.all().delete()
    print(f"清理了 {deleted_tokens[0]} 个访问令牌记录")
    
    # 清理OAuth刷新令牌
    deleted_refresh = OAuthRefreshToken.objects.all().delete()
    print(f"清理了 {deleted_refresh[0]} 个刷新令牌记录")
    
    # 清理OAuth授权码
    deleted_codes = OAuthAuthorizationCode.objects.all().delete()
    print(f"清理了 {deleted_codes[0]} 个授权码记录")
    
    # 清理第三方账户关联（如果有数据不兼容）
    try:
        # 只清理那些user_id不是有效字符串格式的记录
        invalid_third_party = UserThirdParty.objects.exclude(
            user_id__regex=r'^[a-zA-Z0-9_-]+$'
        )
        deleted_third_party = invalid_third_party.delete()
        print(f"清理了 {deleted_third_party[0]} 个无效的第三方账户记录")
    except Exception as e:
        print(f"清理第三方账户时出错: {e}")
    
    # 清理用户角色关联（如果有数据不兼容）
    try:
        invalid_user_roles = UserRole.objects.exclude(
            user_id__regex=r'^[a-zA-Z0-9_-]+$'
        )
        deleted_user_roles = invalid_user_roles.delete()
        print(f"清理了 {deleted_user_roles[0]} 个无效的用户角色记录")
    except Exception as e:
        print(f"清理用户角色时出错: {e}")


def reverse_clean_oauth_data(apps, schema_editor):
    """反向迁移：不需要恢复数据，因为这些都是临时数据"""
    print("反向迁移：OAuth数据清理无需恢复")


class Migration(migrations.Migration):

    dependencies = [
        ('zhi_oauth', '0003_alter_oauthauthorizationcode_challenge_and_more'),
    ]

    operations = [
        migrations.RunPython(clean_oauth_data, reverse_clean_oauth_data),
    ]
