"""
测试增强后的登录接口

验证登录接口是否返回完整的可认证token
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_oauth.controllers.auth import AuthenticationService
from zhi_oauth.models import User, OAuthApplication, OAuthAccessToken, OAuthRefreshToken


def test_enhanced_login_response():
    """测试增强后的登录响应"""
    print("\n🧪 测试增强后的登录响应...")
    
    try:
        # 创建认证服务实例
        auth_service = AuthenticationService()
        
        # 创建测试用户（如果不存在）
        test_user, created = User.objects.get_or_create(
            username='test_user',
            defaults={
                'email': '<EMAIL>',
                'name': '测试用户',
                'is_active': True
            }
        )
        
        if created:
            test_user.set_password('test_password')
            test_user.save()
            print("✅ 创建测试用户成功")
        else:
            print("✅ 使用现有测试用户")
        
        # 执行登录
        result = auth_service.password_login(
            username='test_user',
            password='test_password',
            client_ip='127.0.0.1',
            user_agent='Test Agent',
            remember_me=False
        )
        
        print(f"✅ 登录结果: {result.get('success', False)}")
        
        if result.get('success'):
            data = result.get('data', {})
            
            # 检查返回的字段
            expected_fields = [
                'session_token', 'access_token', 'refresh_token', 
                'token_type', 'expires_in', 'user_info', 'scope'
            ]
            
            found_fields = []
            missing_fields = []
            
            for field in expected_fields:
                if field in data:
                    found_fields.append(field)
                else:
                    missing_fields.append(field)
            
            print(f"✅ 包含字段: {found_fields}")
            if missing_fields:
                print(f"⚠️  缺少字段: {missing_fields}")
            
            # 检查具体字段值
            if 'access_token' in data:
                print(f"✅ access_token: {data['access_token'][:20]}...")
            if 'refresh_token' in data:
                print(f"✅ refresh_token: {data['refresh_token'][:20]}...")
            if 'token_type' in data:
                print(f"✅ token_type: {data['token_type']}")
            if 'expires_in' in data:
                print(f"✅ expires_in: {data['expires_in']} 秒")
            if 'scope' in data:
                print(f"✅ scope: {data['scope']}")
            
            # 检查用户信息
            user_info = data.get('user_info', {})
            if user_info:
                print(f"✅ 用户信息包含字段: {list(user_info.keys())}")
                if 'roles' in user_info:
                    print(f"✅ 用户角色: {len(user_info['roles'])} 个")
            
            return True
        else:
            print(f"❌ 登录失败: {result.get('message', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_default_oauth_app_creation():
    """测试默认OAuth2应用创建"""
    print("\n🧪 测试默认OAuth2应用创建...")
    
    try:
        auth_service = AuthenticationService()
        
        # 测试获取或创建默认应用
        default_app = auth_service._get_or_create_default_oauth_app()
        
        print(f"✅ 默认应用ID: {default_app.id}")
        print(f"✅ 应用名称: {default_app.name}")
        print(f"✅ Client ID: {default_app.client_id}")
        print(f"✅ 应用类型: {default_app.app_type}")
        print(f"✅ 授权类型: {default_app.grant_types}")
        print(f"✅ 允许范围: {default_app.allowed_scopes}")
        print(f"✅ 是否激活: {default_app.is_active}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_token_database_storage():
    """测试令牌数据库存储"""
    print("\n🧪 测试令牌数据库存储...")
    
    try:
        # 检查访问令牌记录
        access_tokens = OAuthAccessToken.objects.filter(
            user__username='test_user'
        ).order_by('-created_at')[:5]
        
        print(f"✅ 访问令牌记录数: {access_tokens.count()}")
        
        if access_tokens.exists():
            latest_token = access_tokens.first()
            print(f"✅ 最新令牌: {latest_token.token[:20]}...")
            print(f"✅ 令牌范围: {latest_token.scope}")
            print(f"✅ 过期时间: {latest_token.expires_at}")
            print(f"✅ 关联应用: {latest_token.application.name}")
        
        # 检查刷新令牌记录
        refresh_tokens = OAuthRefreshToken.objects.filter(
            user__username='test_user'
        ).order_by('-created_at')[:5]
        
        print(f"✅ 刷新令牌记录数: {refresh_tokens.count()}")
        
        if refresh_tokens.exists():
            latest_refresh = refresh_tokens.first()
            print(f"✅ 最新刷新令牌: {latest_refresh.token[:20]}...")
            print(f"✅ 过期时间: {latest_refresh.expires_at}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_token_validation():
    """测试令牌验证"""
    print("\n🧪 测试令牌验证...")
    
    try:
        # 获取最新的访问令牌
        latest_token = OAuthAccessToken.objects.filter(
            user__username='test_user',
            expires_at__gt=django.utils.timezone.now()
        ).order_by('-created_at').first()
        
        if not latest_token:
            print("⚠️  没有找到有效的访问令牌")
            return False
        
        print(f"✅ 找到有效令牌: {latest_token.token[:20]}...")
        
        # 验证令牌是否可以用于认证
        auth_service = AuthenticationService()
        
        # 这里可以添加令牌验证逻辑
        # 例如：验证令牌是否未过期、是否属于正确的用户等
        
        is_valid = (
            latest_token.expires_at > django.utils.timezone.now() and
            latest_token.user.is_active and
            not latest_token.user.is_deleted
        )
        
        print(f"✅ 令牌有效性: {is_valid}")
        print(f"✅ 令牌用户: {latest_token.user.username}")
        print(f"✅ 令牌应用: {latest_token.application.name}")
        
        return is_valid
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_auth_flow():
    """测试完整的认证流程"""
    print("\n🧪 测试完整的认证流程...")
    
    try:
        auth_service = AuthenticationService()
        
        # 1. 密码登录获取令牌
        login_result = auth_service.password_login(
            username='test_user',
            password='test_password',
            client_ip='127.0.0.1',
            user_agent='Test Agent',
            remember_me=True  # 测试记住登录
        )
        
        if not login_result.get('success'):
            print(f"❌ 登录失败: {login_result.get('message')}")
            return False
        
        data = login_result.get('data', {})
        access_token = data.get('access_token')
        refresh_token = data.get('refresh_token')
        
        print(f"✅ 步骤1 - 登录成功，获得令牌")
        
        # 2. 使用访问令牌验证身份（模拟）
        if access_token:
            token_obj = OAuthAccessToken.objects.filter(token=access_token).first()
            if token_obj:
                print(f"✅ 步骤2 - 令牌验证成功，用户: {token_obj.user.username}")
            else:
                print(f"❌ 步骤2 - 令牌验证失败")
                return False
        
        # 3. 检查令牌过期时间（记住登录应该更长）
        expires_in = data.get('expires_in', 0)
        expected_long_expiry = 3600 * 24 * 7  # 7天
        
        if expires_in >= expected_long_expiry:
            print(f"✅ 步骤3 - 记住登录生效，过期时间: {expires_in // 3600} 小时")
        else:
            print(f"⚠️  步骤3 - 过期时间较短: {expires_in // 3600} 小时")
        
        # 4. 检查刷新令牌
        if refresh_token:
            refresh_obj = OAuthRefreshToken.objects.filter(token=refresh_token).first()
            if refresh_obj:
                print(f"✅ 步骤4 - 刷新令牌有效，过期时间: {refresh_obj.expires_at}")
            else:
                print(f"❌ 步骤4 - 刷新令牌无效")
                return False
        
        print("✅ 完整认证流程测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试增强后的登录接口...")
    print("🎯 目标：验证登录接口返回完整的可认证token")
    
    tests = [
        test_enhanced_login_response,
        test_default_oauth_app_creation,
        test_token_database_storage,
        test_token_validation,
        test_complete_auth_flow
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强后的登录接口完美运行。")
        print("\n💡 现在登录接口返回的完整认证信息：")
        print("   ✅ session_token - 用于会话管理")
        print("   ✅ access_token - 用于API认证")
        print("   ✅ refresh_token - 用于令牌刷新")
        print("   ✅ token_type - Bearer类型")
        print("   ✅ expires_in - 令牌过期时间")
        print("   ✅ scope - 令牌权限范围")
        print("   ✅ user_info - 完整用户信息（包含角色）")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
