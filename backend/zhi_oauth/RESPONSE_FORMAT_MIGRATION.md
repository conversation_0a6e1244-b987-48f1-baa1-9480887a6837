# OAuth模块响应格式统一化迁移文档

## 概述

本次迁移将zhi_oauth模块的所有API响应格式统一为`zhi_common.zhi_response.base.BaseResponse`标准格式，确保整个系统的API响应一致性。

## 统一响应格式

### 标准格式结构
```json
{
  "code": 2000,           // 响应码 (ResponseCode枚举)
  "message": "ok",        // 响应消息
  "success": true,        // 成功标志
  "trace_id": "uuid",     // 请求追踪ID
  "timestamp": "ISO时间",  // 响应时间戳
  "data": {}              // 实际数据
}
```

### 响应码说明
- `2000`: 成功
- `4000`: 请求参数错误
- `4001`: 未认证/认证失败
- `4003`: 权限不足
- `4004`: 资源不存在
- `5000`: 服务器内部错误

## 修改的文件

### 1. zhi_common/zhi_response/base.py
**新增功能:**
- `adapt_service_response()` 函数：用于将服务层响应适配为统一格式

**使用示例:**
```python
from zhi_common.zhi_response.base import adapt_service_response
from zhi_common.zhi_consts.core_res_code import ResponseCode

# 服务层返回的结果
service_result = {
    'success': True,
    'message': '登录成功',
    'session_token': 'abc123',
    'user_info': {'id': 1, 'username': 'test'}
}

# 适配为统一格式
response = adapt_service_response(
    service_result,
    success_code=ResponseCode.SUCCESS,
    error_code=ResponseCode.UNAUTHORIZED
)
```

### 2. zhi_oauth/controllers.py
**修改内容:**
- 导入统一响应格式相关模块
- 所有API方法的返回值适配为BaseResponse格式
- 错误处理统一化

**修改的方法:**
- `OAuthController.authorize()` - OAuth2授权端点
- `TokenController.token()` - 令牌获取端点
- `TokenController.revoke_token()` - 令牌撤销端点
- `TokenController.introspect_token()` - 令牌内省端点
- `ApplicationController.list_applications()` - 应用列表
- `ApplicationController.create_application()` - 创建应用

### 3. zhi_oauth/controllers/auth_controller.py
**修改内容:**
- 导入统一响应格式相关模块
- 使用`adapt_service_response()`简化响应适配

**修改的方法:**
- `AuthController.password_login()` - 密码登录
- `AuthController.oauth2_authorize()` - OAuth2授权
- `AuthController.oauth2_token()` - 令牌交换

## 兼容性处理

为确保向后兼容，所有修改都包含了降级处理：

```python
if UNIFIED_RESPONSE_AVAILABLE:
    return adapt_service_response(result)
else:
    return result  # 返回原始格式
```

## 响应格式对比

### 修改前 (不统一)
```json
// 成功响应
{
  "success": true,
  "session_token": "abc123",
  "message": "登录成功"
}

// 错误响应
{
  "error": "invalid_client",
  "error_description": "Invalid client_id"
}
```

### 修改后 (统一格式)
```json
// 成功响应
{
  "code": 2000,
  "message": "登录成功",
  "success": true,
  "trace_id": "trace-uuid",
  "timestamp": "2024-07-24T10:00:00Z",
  "data": {
    "session_token": "abc123"
  }
}

// 错误响应
{
  "code": 4001,
  "message": "客户端验证失败",
  "success": false,
  "trace_id": "trace-uuid",
  "timestamp": "2024-07-24T10:00:00Z",
  "data": {
    "error": "invalid_client",
    "error_description": "Invalid client_id"
  }
}
```

## 测试验证

创建了测试脚本 `test_oauth_response_format.py` 来验证：
1. 响应格式适配功能
2. 不同类型响应的一致性
3. JSON序列化兼容性

## 使用建议

### 前端调用示例
```javascript
// 统一的响应处理
function handleApiResponse(response) {
  if (response.success) {
    // 成功处理
    console.log('操作成功:', response.message);
    return response.data;
  } else {
    // 错误处理
    console.error('操作失败:', response.message);
    throw new Error(response.message);
  }
}

// OAuth登录
fetch('/api/oauth/auth/login', {
  method: 'POST',
  body: JSON.stringify({username: 'test', password: '123456'})
})
.then(res => res.json())
.then(handleApiResponse)
.then(data => {
  // 使用data.session_token等
});
```

### 后端开发建议
1. 新的API开发直接使用`adapt_service_response()`
2. 服务层保持现有返回格式不变
3. 控制器层负责响应格式适配
4. 错误处理统一使用ResponseCode枚举

## 注意事项

1. **向后兼容**: 现有客户端代码需要适配新的响应格式
2. **错误处理**: 统一使用`success`字段判断操作结果
3. **数据获取**: 实际数据在`data`字段中
4. **追踪调试**: 使用`trace_id`进行请求追踪

## 迁移完成状态

✅ zhi_oauth/controllers.py - 所有方法已适配
✅ zhi_oauth/controllers/auth_controller.py - 所有方法已适配  
✅ 响应格式工具函数已优化
✅ 测试验证通过
✅ 向后兼容处理完成

所有zhi_oauth模块的API现在都使用统一的BaseResponse格式！
