#!/usr/bin/env python
"""
导入导出功能测试脚本
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_import_export_utils():
    """测试导入导出工具类"""
    print("\n=== 测试导入导出工具类 ===")
    
    try:
        from zhi_common.zhi_tools.import_export_utils import ImportExportUtils
        print("✅ ImportExportUtils 导入成功")
        
        # 测试支持的格式
        print(f"支持的导出格式: {ImportExportUtils.SUPPORTED_EXPORT_FORMATS}")
        print(f"支持的导入格式: {ImportExportUtils.SUPPORTED_IMPORT_FORMATS}")
        
        # 测试文件格式识别
        test_files = [
            'test.csv',
            'test.xlsx', 
            'test.xls',
            'test.json',
            'test.txt'
        ]
        
        for filename in test_files:
            format_type = ImportExportUtils.get_file_format(filename)
            print(f"文件 {filename} -> 格式: {format_type}")
        
        print("✅ 工具类基础功能测试通过")
        
    except Exception as e:
        print(f"❌ 工具类测试失败: {e}")
        return False
    
    return True

def test_export_functionality():
    """测试导出功能"""
    print("\n=== 测试导出功能 ===")
    
    try:
        from zhi_common.zhi_tools.import_export_utils import ImportExportUtils
        
        # 准备测试数据
        test_data = [
            {'id': 1, 'name': '产品A', 'price': 99.99, 'category': '电子产品'},
            {'id': 2, 'name': '产品B', 'price': 199.99, 'category': '家居用品'},
            {'id': 3, 'name': '产品C', 'price': 299.99, 'category': '服装'}
        ]
        
        field_mapping = {
            'id': 'ID',
            'name': '产品名称',
            'price': '价格',
            'category': '分类'
        }
        
        # 测试JSON导出
        json_response = ImportExportUtils.export_to_json(test_data, 'test.json')
        print(f"✅ JSON导出测试通过 - 状态码: {json_response.status_code}")
        
        # 测试CSV导出
        csv_response = ImportExportUtils.export_to_csv(test_data, 'test.csv', field_mapping)
        print(f"✅ CSV导出测试通过 - 状态码: {csv_response.status_code}")
        
        # 测试Excel导出
        excel_response = ImportExportUtils.export_to_excel(test_data, 'test.xlsx', field_mapping)
        print(f"✅ Excel导出测试通过 - 状态码: {excel_response.status_code}")
        
        print("✅ 导出功能测试通过")
        
    except Exception as e:
        print(f"❌ 导出功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_crud_integration():
    """测试CRUD集成"""
    print("\n=== 测试CRUD集成 ===")
    
    try:
        from zhi_common.zhi_api.zhi_crud import BaseModelService
        print("✅ BaseModelService 导入成功")
        
        # 检查是否有导入导出方法
        methods = ['export_data', 'import_data', 'get_import_template', 'get_export_fields']
        for method in methods:
            if hasattr(BaseModelService, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
                return False
        
        print("✅ CRUD集成测试通过")
        
    except Exception as e:
        print(f"❌ CRUD集成测试失败: {e}")
        return False
    
    return True

def test_schema_integration():
    """测试Schema集成"""
    print("\n=== 测试Schema集成 ===")
    
    try:
        from zhi_common.zhi_response.schemas.base import (
            ExportRequestSchema,
            ImportResultSchema,
            ImportPreviewSchema,
            BatchOperationResultSchema
        )
        print("✅ 导入导出Schema导入成功")
        
        # 测试Schema实例化
        export_schema = ExportRequestSchema(format="excel", filename="test.xlsx")
        print(f"✅ ExportRequestSchema 实例化成功: {export_schema.format}")
        
        import_result = ImportResultSchema(
            success=True,
            total_rows=100,
            success_rows=95,
            error_rows=5,
            errors=["第6行: 价格格式错误"],
            message="导入完成"
        )
        print(f"✅ ImportResultSchema 实例化成功: {import_result.message}")
        
        print("✅ Schema集成测试通过")
        
    except Exception as e:
        print(f"❌ Schema集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始导入导出功能测试")
    
    tests = [
        test_import_export_utils,
        test_export_functionality,
        test_crud_integration,
        test_schema_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！导入导出功能已成功集成")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
