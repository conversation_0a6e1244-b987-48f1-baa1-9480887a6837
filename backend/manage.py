#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys
from loguru import logger


def main():
    """Run administrative tasks."""
    # Custom settings
    # 优先使用环境变量，如果没有则使用默认配置
    settings_module = os.environ.get('DJANGO_SETTINGS_MODULE', 'application.settings.base')

    # 解析命令行参数中的--settings选项
    if len(sys.argv) > 1 and sys.argv[1] == 'runserver':
        for (index, args) in enumerate(sys.argv):
            if '--settings' in args:
                t_args = args.split('=')
                if len(t_args) <= 1:
                    raise SystemExit('Please use `--settings=example` argv, example is settings file.')
                t_settings = args.split('=')[1]
                settings_module = f'application.settings.{t_settings}'
                del sys.argv[index]
                break

    # End Custom settings
    logger.debug(f'DJANGO_SETTINGS_MODULE: {settings_module}')
    # 设置 DJANGO_SETTINGS_MODULE 环境变量
    os.environ['DJANGO_SETTINGS_MODULE'] = settings_module
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == '__main__':
    from django.core.management.commands.runserver import Command as Runserver

    Runserver.default_addr = '0.0.0.0'  # 修改默认地址
    Runserver.default_port = '8001'  # 修改默认端口
    main()
