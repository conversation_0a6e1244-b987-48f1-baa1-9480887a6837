#!/usr/bin/env python
"""
文件管理功能测试脚本
"""

import os
import sys
import django
import tempfile
from pathlib import Path
from io import BytesIO

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')

try:
    django.setup()
    print("✅ Django环境初始化成功")
except Exception as e:
    print(f"❌ Django环境初始化失败: {e}")
    sys.exit(1)

def test_basic_file_manager():
    """测试基础文件管理器"""
    print("\n=== 测试基础文件管理器 ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        # 创建配置
        config = FileUploadConfig(
            max_file_size=1024 * 1024,  # 1MB
            allowed_types=['image', 'document'],
            upload_path='test_uploads'
        )
        
        # 创建文件管理器
        file_manager = FileManager(config)
        print("✅ 基础文件管理器创建成功")
        
        # 创建测试文件
        test_content = b"This is a test file content for file manager testing."
        test_file = SimpleUploadedFile(
            name="test_document.txt",
            content=test_content,
            content_type="text/plain"
        )
        
        # 测试文件上传
        upload_result = file_manager.upload_file(test_file)
        print(f"✅ 文件上传成功: {upload_result.filename}")
        print(f"   文件路径: {upload_result.file_path}")
        print(f"   文件大小: {upload_result.file_size} 字节")
        print(f"   文件哈希: {upload_result.file_hash}")
        
        # 测试获取文件信息
        file_info = file_manager.get_file_info(upload_result.file_path)
        print(f"✅ 获取文件信息成功: 存在={file_info.exists}")
        
        # 测试文件下载
        try:
            download_response = file_manager.download_file(upload_result.file_path)
            print(f"✅ 文件下载测试通过: {type(download_response).__name__}")
        except Exception as e:
            print(f"⚠️  文件下载测试跳过: {str(e)}")
        
        # 测试文件删除
        delete_success = file_manager.delete_file(upload_result.file_path)
        print(f"✅ 文件删除成功: {delete_success}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础文件管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_file_manager():
    """测试增强文件管理器"""
    print("\n=== 测试增强文件管理器 ===")
    
    try:
        from zhi_common.zhi_services.enhanced_file_manager import EnhancedFileManager, FileUploadConfig
        from django.core.files.uploadedfile import SimpleUploadedFile
        
        # 创建配置
        config = FileUploadConfig(
            max_file_size=2 * 1024 * 1024,  # 2MB
            allowed_types=['image', 'document', 'archive'],
            upload_path='enhanced_test_uploads'
        )
        
        # 创建增强文件管理器
        file_manager = EnhancedFileManager(config)
        print("✅ 增强文件管理器创建成功")
        
        # 创建测试文件
        test_content = b"This is an enhanced test file with metadata and tags."
        test_file = SimpleUploadedFile(
            name="enhanced_test.txt",
            content=test_content,
            content_type="text/plain"
        )
        
        # 测试增强文件上传
        upload_result = file_manager.upload_file(
            file=test_file,
            tags=['test', 'document', 'sample'],
            description="这是一个测试文档",
            expires_in_days=30
        )
        print(f"✅ 增强文件上传成功: {upload_result.filename}")
        print(f"   数据库ID: {upload_result.id}")
        print(f"   标签: {upload_result.tags}")
        print(f"   描述: {upload_result.description}")
        print(f"   过期时间: {upload_result.expires_at}")
        
        # 测试获取增强文件信息
        file_info = file_manager.get_file_info(file_id=upload_result.file_id)
        print(f"✅ 获取增强文件信息成功")
        print(f"   下载次数: {file_info.download_count}")
        print(f"   状态: {file_info.status}")
        
        # 测试文件列表
        file_list = file_manager.list_files(page=1, page_size=10)
        print(f"✅ 获取文件列表成功: 总数={file_list['total']}")
        
        # 测试文件删除（逻辑删除）
        delete_success = file_manager.delete_file(file_id=upload_result.file_id, physical_delete=False)
        print(f"✅ 文件逻辑删除成功: {delete_success}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强文件管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_file_api_controllers():
    """测试文件API控制器"""
    print("\n=== 测试文件API控制器 ===")
    
    try:
        from zhi_common.zhi_api.file_api import FileAPIController
        from zhi_common.zhi_api.enhanced_file_api import EnhancedFileAPIController
        
        # 测试基础文件API控制器
        basic_controller = FileAPIController()
        print("✅ 基础文件API控制器创建成功")
        
        # 测试增强文件API控制器
        enhanced_controller = EnhancedFileAPIController()
        print("✅ 增强文件API控制器创建成功")
        
        # 检查控制器方法
        basic_methods = ['upload_file', 'upload_multiple_files', 'download_file', 'get_file_info', 'delete_file', 'list_files']
        for method in basic_methods:
            if hasattr(basic_controller, method):
                print(f"✅ 基础控制器方法存在: {method}")
            else:
                print(f"❌ 基础控制器方法缺失: {method}")
        
        enhanced_methods = ['upload_file', 'upload_multiple_files', 'download_file', 'get_file_info', 'delete_file', 'list_files']
        for method in enhanced_methods:
            if hasattr(enhanced_controller, method):
                print(f"✅ 增强控制器方法存在: {method}")
            else:
                print(f"❌ 增强控制器方法缺失: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件API控制器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_file_models():
    """测试文件模型"""
    print("\n=== 测试文件模型 ===")
    
    try:
        from zhi_common.zhi_model.file_model import FileStorage, FileAccessLog
        
        print("✅ 文件模型导入成功")
        
        # 检查模型字段
        file_storage_fields = [
            'file_id', 'original_name', 'file_name', 'file_path', 'file_url',
            'file_size', 'file_type', 'content_type', 'file_extension', 'file_hash',
            'status', 'download_count', 'tags', 'metadata', 'description'
        ]
        
        for field_name in file_storage_fields:
            if hasattr(FileStorage, field_name):
                print(f"✅ FileStorage字段存在: {field_name}")
            else:
                print(f"❌ FileStorage字段缺失: {field_name}")
        
        # 检查模型方法
        file_storage_methods = ['file_size_human', 'is_image', 'is_document', 'is_active', 'increment_download_count']
        for method_name in file_storage_methods:
            if hasattr(FileStorage, method_name):
                print(f"✅ FileStorage方法存在: {method_name}")
            else:
                print(f"❌ FileStorage方法缺失: {method_name}")
        
        print("✅ 文件模型结构验证完成")
        return True
        
    except Exception as e:
        print(f"❌ 文件模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_file_configurations():
    """测试文件配置"""
    print("\n=== 测试文件配置 ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileUploadConfig
        
        # 测试默认配置
        default_config = FileUploadConfig()
        print(f"✅ 默认配置创建成功")
        print(f"   最大文件大小: {default_config.max_file_size / 1024 / 1024:.1f}MB")
        print(f"   上传路径: {default_config.upload_path}")
        print(f"   按日期组织: {default_config.organize_by_date}")
        print(f"   生成唯一名称: {default_config.generate_unique_name}")
        
        # 测试自定义配置
        custom_config = FileUploadConfig(
            max_file_size=5 * 1024 * 1024,
            allowed_types=['image', 'document'],
            upload_path='custom_uploads',
            organize_by_date=False,
            generate_unique_name=False
        )
        print(f"✅ 自定义配置创建成功")
        print(f"   允许的扩展名数量: {len(custom_config.allowed_extensions)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始文件管理功能测试")
    
    tests = [
        ("文件配置测试", test_file_configurations),
        ("文件模型测试", test_file_models),
        ("基础文件管理器测试", test_basic_file_manager),
        ("增强文件管理器测试", test_enhanced_file_manager),
        ("文件API控制器测试", test_file_api_controllers),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"执行测试: {test_name}")
            print(f"{'='*50}")
            
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"{'='*50}")
    
    if passed == total:
        print("🎉 所有测试通过！文件管理功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
