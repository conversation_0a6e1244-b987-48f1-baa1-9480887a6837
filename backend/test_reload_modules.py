"""
重新加载模块并测试patch方法
"""

import os
import sys
import django
from pathlib import Path
import importlib

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

def test_reload_and_patch():
    """重新加载模块并测试patch方法"""
    print("🧪 重新加载模块并测试patch方法...")
    
    # 重新加载基础服务模块
    print("📋 重新加载 BaseModelService...")
    import zhi_common.zhi_services.base_model_service
    importlib.reload(zhi_common.zhi_services.base_model_service)
    
    # 重新加载示例产品API模块
    print("📋 重新加载 ExampleProductControllerAPI...")
    import zhi_oauth.apis.example_product
    importlib.reload(zhi_oauth.apis.example_product)
    
    # 重新导入类
    from zhi_common.zhi_services.base_model_service import BaseModelService
    from zhi_oauth.apis.example_product import ExampleProductControllerAPI
    from zhi_oauth.models import ExampleProduct
    
    # 测试BaseModelService
    print("\n📋 测试BaseModelService:")
    base_service = BaseModelService(model_class=ExampleProduct)
    
    methods_to_check = ['update', 'patch', 'partial_update', '_should_update_field']
    for method_name in methods_to_check:
        if hasattr(base_service, method_name):
            print(f"   ✅ BaseModelService.{method_name}: 存在")
        else:
            print(f"   ❌ BaseModelService.{method_name}: 不存在")
    
    # 测试ExampleProductControllerAPI
    print("\n📋 测试ExampleProductControllerAPI:")
    try:
        example_service = ExampleProductControllerAPI(model_class=ExampleProduct)
        
        for method_name in methods_to_check:
            if hasattr(example_service, method_name):
                print(f"   ✅ ExampleProductControllerAPI.{method_name}: 存在")
            else:
                print(f"   ❌ ExampleProductControllerAPI.{method_name}: 不存在")
        
        # 检查所有公共方法
        all_methods = [name for name in dir(example_service) if not name.startswith('_')]
        patch_related = [name for name in all_methods if 'patch' in name.lower()]
        print(f"\n📋 ExampleProductControllerAPI的patch相关方法: {patch_related}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建ExampleProductControllerAPI实例失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始重新加载模块测试...")
    
    try:
        success = test_reload_and_patch()
        
        if success:
            print("\n✅ 重新加载测试完成")
        else:
            print("\n❌ 重新加载测试失败")
            
        return success
        
    except Exception as e:
        print(f"❌ 测试出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
