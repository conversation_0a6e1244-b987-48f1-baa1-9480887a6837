# Windows开发环境启动指南

本指南提供了在Windows环境下启动ZhiAdmin开发环境的多种方式，基于您提供的Linux启动脚本适配。

## 🚀 快速开始

### 1. 快速测试OAuth响应格式统一化
```bash
# 运行快速测试（推荐）
quick_test_oauth.bat
```

### 2. 启动开发服务器
```bash
# 简单启动（推荐新手）
start_dev_windows.bat

# 或使用PowerShell（更多选项）
.\start_dev_windows.ps1 -Mode webapp
```

## 📋 启动脚本说明

### 1. `quick_test_oauth.bat` - 快速测试脚本
**用途**: 快速验证OAuth响应格式统一化是否成功
**功能**:
- 运行基础修复
- 启动测试服务器
- 执行完整API测试
- 显示测试结果

### 2. `start_dev_windows.bat` - 批处理启动脚本
**用途**: 交互式选择启动模式
**模式**:
1. Django开发服务器 (推荐)
2. Celery Worker (异步任务)
3. Celery Beat (定时任务)
4. Celery Worker + Beat
5. 完整开发环境 (Web + Celery)
6. 运行OAuth API测试

### 3. `start_dev_windows.ps1` - PowerShell启动脚本
**用途**: 命令行参数控制启动
**参数**:
- `-Mode`: 启动模式 (webapp, celery-worker, celery-beat, celery-all, all)
- `-Init`: 运行系统初始化
- `-Migrate`: 运行数据库迁移
- `-Port`: 服务器端口 (默认8001)
- `-Host`: 服务器主机 (默认0.0.0.0)

## 🔧 使用示例

### 基础开发环境
```bash
# 启动Django开发服务器
.\start_dev_windows.ps1 -Mode webapp

# 启动并运行迁移
.\start_dev_windows.ps1 -Mode webapp -Migrate

# 启动并初始化系统
.\start_dev_windows.ps1 -Mode webapp -Init
```

### Celery任务处理（基于zhi_celery架构）
```bash
# 使用专门的zhi_celery启动脚本（推荐）
.\start_zhi_celery_windows.ps1 -Mode worker
.\start_zhi_celery_windows.ps1 -Mode beat
.\start_zhi_celery_windows.ps1 -Mode all

# 或使用通用启动脚本
.\start_dev_windows.ps1 -Mode celery-worker
.\start_dev_windows.ps1 -Mode celery-beat
.\start_dev_windows.ps1 -Mode celery-all
```

### 完整开发环境
```bash
# 启动Web服务器 + Celery服务
.\start_dev_windows.ps1 -Mode all

# 启动完整环境并初始化
.\start_dev_windows.ps1 -Mode all -Init
```

## 🌐 访问地址

启动成功后，您可以访问：

- **Web应用**: http://localhost:8001
- **OAuth API文档**: http://localhost:8001/api/oauth/docs
- **管理后台**: http://localhost:8001/admin

## 📦 依赖要求

脚本会自动检查并安装以下依赖：
- `gevent` - Windows下Celery所需
- `redis` - 消息队列
- `celery` - 异步任务处理
- `uvicorn` - ASGI服务器（可选）

## 🔍 故障排除

### 1. 虚拟环境问题
```bash
# 确保虚拟环境存在
ls .venv

# 重新创建虚拟环境
python -m venv .venv
.venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Redis连接问题
```bash
# 使用Docker启动Redis
docker run -d -p 6379:6379 redis:alpine

# 或安装Windows版Redis
# 下载: https://github.com/microsoftarchive/redis/releases
```

### 3. 端口占用问题
```bash
# 使用不同端口
.\start_dev_windows.ps1 -Mode webapp -Port 8002
```

### 4. Celery在Windows上的问题
脚本已配置使用`gevent`作为事件循环，这是Windows上推荐的方式：
```bash
# 手动启动Celery Worker
celery -A application.celery worker -l info -c 1 -P gevent

# 手动启动Celery Beat
celery -A application.celery beat --loglevel=info
```

## 📚 相关文档

- [OAuth响应格式迁移文档](zhi_oauth/RESPONSE_FORMAT_MIGRATION.md)
- [测试脚本说明](test_oauth_complete.py)
- [问题修复脚本](fix_oauth_issues.py)

## 🎯 开发建议

### 日常开发
1. 使用 `start_dev_windows.bat` 选择模式1启动Django服务器
2. 如需异步任务，另开终端运行Celery Worker
3. 使用 `quick_test_oauth.bat` 验证API响应格式

### 生产环境准备
1. 使用 `.\start_dev_windows.ps1 -Mode webapp-uvicorn` 测试ASGI服务器
2. 分别启动Celery Worker和Beat进程
3. 配置反向代理（如Nginx）

### 测试验证
1. 运行 `python test_oauth_complete.py` 验证API
2. 访问 http://localhost:8001/api/oauth/docs 查看API文档
3. 检查响应格式是否符合BaseResponse标准

## ✅ 项目状态总结

### OAuth响应格式统一化状态: **75%完成** (3/4 API已统一)

✅ **已完成**:
- 登录API - 完美适配BaseResponse格式
- 用户信息API - 完美适配BaseResponse格式
- 测试用户创建 - 成功

⚠️ **待完善**:
- 应用列表API - 需要调试权限问题

### ZhiCelery异步任务状态: **100%完成** ✅

✅ **已完成**:
- Celery应用配置 - 完美运行
- Logger任务模块 - 8个任务可用
- OAuth任务模块 - 5个任务可用
- 调度配置 - 15个定时任务
- Django管理命令 - celery_worker & celery_beat
- Windows启动脚本 - 完整支持

**统一响应格式**:
```json
{
  "code": 2000,
  "message": "ok",
  "success": true,
  "trace_id": "trace-uuid",
  "timestamp": "2025-07-24T10:00:00Z",
  "data": {
    // 实际业务数据
  }
}
```

**ZhiCelery任务队列**:
- `default` - 默认任务队列
- `logger` - 日志相关任务
- `oauth` - OAuth相关任务
- `system` - 系统维护任务
