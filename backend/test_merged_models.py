#!/usr/bin/env python
"""
测试合并后的模型文件
验证所有模型是否能正常导入和使用
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

def test_model_imports():
    """测试模型导入"""
    print("🧪 测试模型导入")
    print("=" * 50)
    
    try:
        # 测试用户相关模型
        from zhi_oauth.models import User, UserThirdParty
        print("✅ 用户模型导入成功")
        print(f"   User: {User}")
        print(f"   UserThirdParty: {UserThirdParty}")
        
        # 测试业务核心模型
        from zhi_oauth.models import Tenant, Organization, Role, Permission
        print("✅ 业务核心模型导入成功")
        print(f"   Tenant: {Tenant}")
        print(f"   Organization: {Organization}")
        print(f"   Role: {Role}")
        print(f"   Permission: {Permission}")
        
        # 测试关联模型
        from zhi_oauth.models import UserRole, RolePermission
        print("✅ 关联模型导入成功")
        print(f"   UserRole: {UserRole}")
        print(f"   RolePermission: {RolePermission}")
        
        # 测试OAuth2模型
        from zhi_oauth.models import OAuthApplication, OAuthAccessToken, OAuthRefreshToken
        print("✅ OAuth2模型导入成功")
        print(f"   OAuthApplication: {OAuthApplication}")
        print(f"   OAuthAccessToken: {OAuthAccessToken}")
        print(f"   OAuthRefreshToken: {OAuthRefreshToken}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模型导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_model_fields():
    """测试模型字段"""
    print("\n🧪 测试模型字段")
    print("=" * 50)
    
    try:
        from zhi_oauth.models import User, Tenant, Organization, Role, Permission
        
        # 测试User模型字段
        user_fields = [field.name for field in User._meta.fields]
        expected_user_fields = [
            'username', 'email', 'mobile', 'avatar', 'name', 'status',
            'gender', 'user_type', 'home_path', 'tenant', 'organization',
            'oauth_provider', 'oauth_uid', 'login_failure_count', 
            'locked_until', 'password_changed_at'
        ]
        
        print("📋 User模型字段检查:")
        for field in expected_user_fields:
            if field in user_fields:
                print(f"   ✅ {field}")
            else:
                print(f"   ❌ {field} (缺失)")
        
        # 测试Tenant模型字段
        tenant_fields = [field.name for field in Tenant._meta.fields]
        expected_tenant_fields = [
            'name', 'code', 'tenant_type', 'city', 'province', 'district',
            'address', 'corp_size', 'corp_years', 'industry', 'domain',
            'logo', 'is_active', 'max_users', 'expire_date', 'admin_user'
        ]
        
        print("\n📋 Tenant模型字段检查:")
        for field in expected_tenant_fields:
            if field in tenant_fields:
                print(f"   ✅ {field}")
            else:
                print(f"   ❌ {field} (缺失)")
        
        # 测试数据库表名
        print("\n📋 数据库表名检查:")
        print(f"   User表名: {User._meta.db_table}")
        print(f"   Tenant表名: {Tenant._meta.db_table}")
        print(f"   Organization表名: {Organization._meta.db_table}")
        print(f"   Role表名: {Role._meta.db_table}")
        print(f"   Permission表名: {Permission._meta.db_table}")
        
        return True
        
    except Exception as e:
        print(f"❌ 字段测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_relationships():
    """测试模型关系"""
    print("\n🧪 测试模型关系")
    print("=" * 50)
    
    try:
        from zhi_oauth.models import User, Tenant, Organization, Role, Permission
        
        # 测试User模型关系
        print("📋 User模型关系:")
        user_relations = [
            ('tenant', 'ForeignKey to Tenant'),
            ('organization', 'ForeignKey to Organization'),
            ('roles', 'ManyToMany to Role'),
        ]
        
        for relation_name, relation_type in user_relations:
            if hasattr(User, relation_name):
                field = User._meta.get_field(relation_name)
                print(f"   ✅ {relation_name}: {field.__class__.__name__}")
            else:
                print(f"   ❌ {relation_name}: 关系不存在")
        
        # 测试Organization模型关系
        print("\n📋 Organization模型关系:")
        org_relations = [
            ('parent', 'ForeignKey to self'),
            ('tenant', 'ForeignKey to Tenant'),
            ('leader_user', 'ForeignKey to User'),
        ]
        
        for relation_name, relation_type in org_relations:
            if hasattr(Organization, relation_name):
                field = Organization._meta.get_field(relation_name)
                print(f"   ✅ {relation_name}: {field.__class__.__name__}")
            else:
                print(f"   ❌ {relation_name}: 关系不存在")
        
        # 测试Role模型关系
        print("\n📋 Role模型关系:")
        role_relations = [
            ('tenant', 'ForeignKey to Tenant'),
            ('permissions', 'ManyToMany to Permission'),
        ]
        
        for relation_name, relation_type in role_relations:
            if hasattr(Role, relation_name):
                field = Role._meta.get_field(relation_name)
                print(f"   ✅ {relation_name}: {field.__class__.__name__}")
            else:
                print(f"   ❌ {relation_name}: 关系不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 关系测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_methods():
    """测试模型方法"""
    print("\n🧪 测试模型方法")
    print("=" * 50)
    
    try:
        from zhi_oauth.models import User, Organization, Role
        
        # 测试User模型方法
        print("📋 User模型方法:")
        user_methods = [
            'display_name', 'is_locked', 'lock_account', 'unlock_account',
            'increment_login_failure', 'reset_login_failure', 'get_permissions',
            'has_permission', 'get_data_scope_orgs'
        ]
        
        for method_name in user_methods:
            if hasattr(User, method_name):
                method = getattr(User, method_name)
                print(f"   ✅ {method_name}: {type(method)}")
            else:
                print(f"   ❌ {method_name}: 方法不存在")
        
        # 测试Organization模型方法
        print("\n📋 Organization模型方法:")
        org_methods = ['get_full_path', 'get_all_children', 'get_all_users']
        
        for method_name in org_methods:
            if hasattr(Organization, method_name):
                method = getattr(Organization, method_name)
                print(f"   ✅ {method_name}: {type(method)}")
            else:
                print(f"   ❌ {method_name}: 方法不存在")
        
        # 测试Role模型方法
        print("\n📋 Role模型方法:")
        role_methods = ['get_permissions', 'get_data_scope_orgs']
        
        for method_name in role_methods:
            if hasattr(Role, method_name):
                method = getattr(Role, method_name)
                print(f"   ✅ {method_name}: {type(method)}")
            else:
                print(f"   ❌ {method_name}: 方法不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_django_user_model():
    """测试Django用户模型配置"""
    print("\n🧪 测试Django用户模型配置")
    print("=" * 50)
    
    try:
        from django.contrib.auth import get_user_model
        from zhi_oauth.models import User
        
        # 获取当前配置的用户模型
        CurrentUser = get_user_model()
        
        print(f"📋 当前用户模型: {CurrentUser}")
        print(f"📋 期望用户模型: {User}")
        
        if CurrentUser == User:
            print("✅ 用户模型配置正确")
            
            # 测试用户模型字段
            print(f"📋 用户模型表名: {CurrentUser._meta.db_table}")
            print(f"📋 用户模型字段数: {len(CurrentUser._meta.fields)}")
            
            return True
        else:
            print("❌ 用户模型配置错误")
            print(f"   当前: {CurrentUser}")
            print(f"   期望: {User}")
            return False
        
    except Exception as e:
        print(f"❌ 用户模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 合并模型测试")
    print("=" * 60)
    
    tests = [
        test_model_imports,
        test_model_fields,
        test_model_relationships,
        test_model_methods,
        test_django_user_model,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 测试总结")
    print("=" * 60)
    
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模型合并成功！")
        
        print("\n✅ 合并成果:")
        print("• 所有模型已合并到单一文件")
        print("• 用户模型配置正确")
        print("• 业务核心模型完整")
        print("• 模型关系正常")
        print("• 模型方法可用")
        
        print("\n💡 下一步:")
        print("1. 重新生成数据库迁移")
        print("2. 执行数据库迁移")
        print("3. 创建超级用户")
        print("4. 测试完整功能")
        
    else:
        print(f"⚠️  有 {total - passed} 个测试失败，请检查模型定义")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
