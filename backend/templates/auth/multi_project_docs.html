<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZhiAdmin - 多项目文档管理</title>
    <style>
        :root {
            --primary-color: #1890ff;
            --success-color: #52c41a;
            --warning-color: #fa8c16;
            --error-color: #f5222d;
            --dev-tag-color: #ff4d4f;
            --border-color: #e8e8e8;
            --text-color: #333;
            --text-secondary: #666;
            --bg-color: #f5f5f5;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        .dev-tag {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--dev-tag-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .projects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .project-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .project-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 16px 48px rgba(0,0,0,0.15);
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--project-color, var(--primary-color));
        }

        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .project-icon {
            font-size: 2rem;
            margin-right: 15px;
        }

        .project-title {
            flex: 1;
        }

        .project-title h3 {
            font-size: 1.3rem;
            color: var(--text-color);
            margin-bottom: 5px;
        }

        .project-status {
            font-size: 0.85rem;
            padding: 4px 8px;
            border-radius: 12px;
            background: #f0f0f0;
            color: var(--text-secondary);
        }

        .project-description {
            color: var(--text-secondary);
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .auth-note {
            background: #f0f8ff;
            color: #1890ff;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.85rem;
            margin-bottom: 15px;
            border-left: 3px solid #1890ff;
        }

        .auth-center-badge {
            background: linear-gradient(45deg, #1890ff, #40a9ff);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            margin-left: 8px;
        }

        .project-features {
            margin-bottom: 25px;
        }

        .features-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .feature-tag {
            background: #f0f8ff;
            color: var(--primary-color);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #e6f7ff;
        }

        .project-actions {
            display: flex;
            gap: 12px;
        }

        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            text-align: center;
            display: inline-block;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        .login-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            z-index: 2000;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 16px;
            padding: 30px;
            width: 90%;
            max-width: 450px;
            position: relative;
        }

        .modal-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-title {
            flex: 1;
            font-size: 1.3rem;
            color: var(--text-color);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-secondary);
            padding: 5px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text-color);
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s;
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
            outline: none;
        }

        .result-section {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            display: none;
        }

        .result-content {
            max-height: 200px;
            overflow: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
            white-space: pre-wrap;
        }

        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }

        .footer a {
            color: white;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .projects-grid {
                grid-template-columns: 1fr;
            }
            
            .stats {
                flex-direction: column;
                gap: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="dev-tag">Development Mode</div>
    
    <div class="container">
        <div class="header">
            <h1>🚀 ZhiAdmin</h1>
            <p>多项目API文档管理中心 - 统一OAuth认证架构</p>

            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">{{ total_projects }}</span>
                    <span class="stat-label">项目模块</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">统一认证</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">75%</span>
                    <span class="stat-label">BaseResponse适配</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <span class="stat-label">ZhiCelery完成</span>
                </div>
            </div>
        </div>

        <div class="projects-grid">
            {% for project in projects %}
            <div class="project-card" style="--project-color: {{ project.color }}">
                <div class="project-header">
                    <div class="project-icon">{{ project.icon }}</div>
                    <div class="project-title">
                        <h3>
                            {{ project.name }}
                            {% if project.is_auth_center %}
                            <span class="auth-center-badge">认证中心</span>
                            {% endif %}
                        </h3>
                        <div class="project-status">{{ project.status }}</div>
                    </div>
                </div>
                
                <div class="project-description">
                    {{ project.description }}
                </div>

                {% if project.auth_note %}
                <div class="auth-note">
                    🔐 {{ project.auth_note }}
                </div>
                {% endif %}
                
                <div class="project-features">
                    <div class="features-list">
                        {% for feature in project.features %}
                        <span class="feature-tag">{{ feature }}</span>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="project-actions">
                    <button class="btn btn-primary" onclick="openLoginModal('{{ project.name }}', '{{ project.api_url }}', '{{ project.docs_url }}')">
                        🔐 登录测试
                    </button>
                    <a href="{{ project.docs_url }}" class="btn btn-outline" target="_blank">
                        📚 查看文档
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="footer">
            <p>
                <a href="?project=all">查看所有项目</a> | 
                <a href="?project=oauth">仅OAuth</a> | 
                <a href="?project=logger">仅Logger</a> | 
                <a href="?project=celery">仅Celery</a>
            </p>
            <p style="margin-top: 10px; font-size: 0.9rem;">
                ZhiAdmin © 2025 - 统一API响应格式 & 多项目架构
            </p>
        </div>
    </div>

    <!-- 登录模态框 -->
    <div id="loginModal" class="login-modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">项目登录</div>
                <button class="close-btn" onclick="closeLoginModal()">&times;</button>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" value="admin" required>
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" value="123456" required>
                </div>
                
                <button type="submit" class="btn btn-primary" style="width: 100%;">
                    登录并测试
                </button>
            </form>
            
            <div id="resultSection" class="result-section">
                <h4>响应结果:</h4>
                <div id="resultContent" class="result-content"></div>
                <div style="margin-top: 10px;">
                    <button id="goToDocsBtn" class="btn btn-outline" style="width: 100%;">
                        前往API文档
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentApiUrl = '';
        let currentDocsUrl = '';

        function openLoginModal(projectName, apiUrl, docsUrl) {
            document.getElementById('modalTitle').textContent = projectName + ' - 登录测试';
            document.getElementById('loginModal').style.display = 'flex';
            document.getElementById('resultSection').style.display = 'none';
            currentApiUrl = apiUrl;
            currentDocsUrl = docsUrl;
            
            // 聚焦到密码框（用户名已预填）
            document.getElementById('password').focus();
        }

        function closeLoginModal() {
            document.getElementById('loginModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        document.getElementById('loginModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLoginModal();
            }
        });

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');
            const goToDocsBtn = document.getElementById('goToDocsBtn');
            
            resultContent.textContent = '正在发送请求...';
            resultSection.style.display = 'block';
            
            try {
                const response = await fetch(currentApiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password,
                        remember_me: false
                    })
                });
                
                const data = await response.json();
                resultContent.textContent = JSON.stringify(data, null, 2);
                
                // 设置前往文档按钮
                goToDocsBtn.onclick = function() {
                    window.open(currentDocsUrl, '_blank');
                };
                
                if (data.success) {
                    goToDocsBtn.style.background = 'var(--success-color)';
                    goToDocsBtn.style.borderColor = 'var(--success-color)';
                    goToDocsBtn.textContent = '✅ 登录成功 - 前往API文档';
                } else {
                    goToDocsBtn.style.background = 'var(--error-color)';
                    goToDocsBtn.style.borderColor = 'var(--error-color)';
                    goToDocsBtn.textContent = '❌ 登录失败 - 仍可查看文档';
                }
                
            } catch (error) {
                resultContent.textContent = `请求失败: ${error.message}`;
                goToDocsBtn.onclick = function() {
                    window.open(currentDocsUrl, '_blank');
                };
                goToDocsBtn.textContent = '查看API文档';
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLoginModal();
            }
        });
    </script>
</body>
</html>
