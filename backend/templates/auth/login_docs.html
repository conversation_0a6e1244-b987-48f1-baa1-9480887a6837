<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统登录测试</title>
    <style>
        :root {
            --primary-color: #1890ff;
            --error-color: #f5222d;
            --success-color: #52c41a;
            --dev-tag-color: #ff4d4f;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .login-container {
            width: 100%;
            max-width: 450px; /* 登录框宽度 */
            position: relative;
        }
        .dev-tag {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: var(--dev-tag-color);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .result-container {
            width: 100%;
            max-width: 850px; /* 结果框比登录框宽400px */
            margin-top: 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            width: 100%;
            box-sizing: border-box;
        }
        h1 {
            color: var(--primary-color);
            text-align: center;
            margin: 0 0 25px 0;
            font-size: 1.5rem;
        }
        h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2rem;
        }
        .form-group {
            margin-bottom: 18px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.95rem;
            transition: all 0.2s;
            box-sizing: border-box;
        }
        input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            outline: none;
        }
        .submit-btn {
            width: 100%;
            padding: 12px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            margin-top: 10px;
            transition: all 0.2s;
        }
        .submit-btn:hover {
            background: #40a9ff;
        }
        .json-result {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
            max-height: 300px;
            overflow: auto;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
            font-family: Consolas, Monaco, 'Andale Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        .token-section {
            margin-top: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 4px;
            border-left: 3px solid var(--primary-color);
        }
        .token-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .token-value {
            background: #fff;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin-bottom: 10px;
            overflow-x: auto;
        }
        .copy-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s;
            white-space: nowrap;
        }
        .copy-btn:hover {
            background: #40a9ff;
        }
        .copy-success {
            color: var(--success-color);
            font-size: 0.9rem;
            margin-left: 10px;
            opacity: 0;
            transition: opacity 0.2s;
            white-space: nowrap;
        }
        .show-success {
            opacity: 1;
        }
        .docs-link {
            display: block;
            text-align: center;
            color: var(--primary-color);
            margin-top: 20px;
            text-decoration: none;
            font-weight: 500;
        }
        .docs-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
         <div class="dev-tag">Development</div>
        <div class="card">
            <h1>系统登录</h1>

            <form id="loginForm">
                <div class="form-group">
                    <label for="username">用户名</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <button type="submit" class="submit-btn">登录</button>
            </form>

            <a href="{{ docs_url }}" class="docs-link">查看完整API文档 →</a>
        </div>
    </div>

    <div class="result-container">
        <div id="result" class="card" style="display: none;">
            <h2>结果</h2>

            <div class="json-result">
                <div id="resultContent"></div>
            </div>

            <div id="tokenInfo" class="token-section" style="display: none;">
                <div class="token-header">
                    <h3>认证令牌</h3>
                    <div style="display: flex; align-items: center;">
                        <button id="copyTokenBtn" class="copy-btn">复制Token</button>
                        <span id="copySuccess" class="copy-success">✓ 已复制</span>
                    </div>
                </div>
                <div class="token-value" id="tokenDisplay"></div>
                <p>请在后续文档请求头中添加: <code>Authorization: [上面的Token]</code></p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit',  async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultPanel = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            const tokenSection = document.getElementById('tokenInfo');
            const tokenDisplay = document.getElementById('tokenDisplay');
            const copyBtn = document.getElementById('copyTokenBtn');
            const copySuccess = document.getElementById('copySuccess');

            // 重置状态
            contentDiv.innerHTML  = '<p>正在发送请求...</p>';
            tokenSection.style.display  = 'none';
            copySuccess.classList.remove('show-success');
            resultPanel.style.display  = 'block';

            try {
                const response = await fetch('{{ api_url }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                const data = await response.json();
                contentDiv.innerHTML  = `<pre>${JSON.stringify(data,  null, 2)}</pre>`;

                if(data.data.token)  {
                    tokenDisplay.textContent  = data.data.token;
                    tokenSection.style.display  = 'block';

                    copyBtn.onclick  = function() {
                        navigator.clipboard.writeText(data.data.token).then(()  => {
                            copySuccess.classList.add('show-success');
                            setTimeout(() => {
                                copySuccess.classList.remove('show-success');
                            }, 2000);
                        });
                    };
                }
            } catch(error) {
                contentDiv.innerHTML  = `<p style="color: var(--error-color)">请求失败: ${error.message}</p>`;
            }
        });

        // 自动填充用户名
        const urlParams = new URLSearchParams(window.location.search);
        if(urlParams.has('username'))  {
            document.getElementById('username').value  = urlParams.get('username');
            document.getElementById('password').focus();
        }
    </script>
</body>
</html>