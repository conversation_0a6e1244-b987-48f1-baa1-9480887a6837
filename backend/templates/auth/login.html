<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZhiAdmin - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #333;
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .login-header p {
            color: #666;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .form-group input.error {
            border-color: #e74c3c;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .checkbox-group input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .checkbox-group label {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        
        .login-button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .login-button:hover {
            transform: translateY(-2px);
        }
        
        .login-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            background: #fee;
            color: #e74c3c;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        .success-message {
            background: #efe;
            color: #27ae60;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .oauth-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
            display: none;
        }
        
        .oauth-apps {
            display: grid;
            gap: 10px;
        }
        
        .oauth-app {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        
        .oauth-app:hover {
            border-color: #667eea;
        }
        
        .oauth-app img {
            width: 32px;
            height: 32px;
            margin-right: 12px;
            border-radius: 4px;
        }
        
        .oauth-app-info h4 {
            margin: 0;
            color: #333;
            font-size: 14px;
        }
        
        .oauth-app-info p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>ZhiAdmin</h1>
            <p>智能管理系统</p>
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名/邮箱/手机号</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="checkbox-group">
                <input type="checkbox" id="rememberMe" name="rememberMe">
                <label for="rememberMe">记住登录状态</label>
            </div>
            
            <button type="submit" class="login-button" id="loginButton">
                登录
            </button>
        </form>
        
        <div class="oauth-section" id="oauthSection">
            <h3>选择应用</h3>
            <div class="oauth-apps" id="oauthApps">
                <!-- OAuth应用列表将在这里动态加载 -->
            </div>
        </div>
    </div>

    <script>
        class AuthManager {
            constructor() {
                this.apiBase = '/api/auth';
                this.sessionToken = null;
                this.init();
            }
            
            init() {
                this.bindEvents();
                this.checkExistingSession();
            }
            
            bindEvents() {
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });
            }
            
            async checkExistingSession() {
                const sessionToken = localStorage.getItem('session_token');
                if (sessionToken) {
                    try {
                        const response = await this.apiCall('/session/validate', 'GET', null, {
                            session_token: sessionToken
                        });
                        
                        if (response.success && response.valid) {
                            this.sessionToken = sessionToken;
                            this.showOAuthApps();
                        } else {
                            localStorage.removeItem('session_token');
                        }
                    } catch (error) {
                        localStorage.removeItem('session_token');
                    }
                }
            }
            
            async handleLogin() {
                const form = document.getElementById('loginForm');
                const button = document.getElementById('loginButton');
                const formData = new FormData(form);
                
                const loginData = {
                    username: formData.get('username'),
                    password: formData.get('password'),
                    remember_me: formData.get('rememberMe') === 'on'
                };
                
                this.setLoading(true);
                this.hideMessages();
                
                try {
                    const response = await this.apiCall('/login', 'POST', loginData);
                    
                    if (response.success) {
                        this.sessionToken = response.session_token;
                        localStorage.setItem('session_token', this.sessionToken);
                        
                        this.showSuccess('登录成功！正在加载应用列表...');
                        
                        // 显示OAuth应用选择
                        setTimeout(() => {
                            this.showOAuthApps();
                        }, 1000);
                        
                    } else {
                        this.showError(response.message || '登录失败');
                    }
                } catch (error) {
                    this.showError('网络错误，请稍后重试');
                } finally {
                    this.setLoading(false);
                }
            }
            
            async showOAuthApps() {
                try {
                    const response = await this.apiCall('/oauth2/applications', 'GET', null, {
                        session_token: this.sessionToken
                    });
                    
                    if (response.success) {
                        this.renderOAuthApps(response.applications);
                        document.getElementById('oauthSection').style.display = 'block';
                        document.getElementById('loginForm').style.display = 'none';
                    } else {
                        this.showError('获取应用列表失败');
                    }
                } catch (error) {
                    this.showError('获取应用列表失败');
                }
            }
            
            renderOAuthApps(applications) {
                const container = document.getElementById('oauthApps');
                container.innerHTML = '';
                
                applications.forEach(app => {
                    const appElement = document.createElement('div');
                    appElement.className = 'oauth-app';
                    appElement.innerHTML = `
                        <img src="${app.logo_url || '/static/images/default-app.png'}" alt="${app.name}">
                        <div class="oauth-app-info">
                            <h4>${app.name}</h4>
                            <p>${app.description || '应用描述'}</p>
                        </div>
                    `;
                    
                    appElement.addEventListener('click', () => {
                        this.authorizeApp(app);
                    });
                    
                    container.appendChild(appElement);
                });
            }
            
            async authorizeApp(app) {
                // 这里可以从URL参数或配置中获取
                const redirectUri = new URLSearchParams(window.location.search).get('redirect_uri') || 
                                   app.redirect_uris?.[0] || 
                                   'http://localhost:3000/callback';
                
                const state = new URLSearchParams(window.location.search).get('state') || 
                             this.generateState();
                
                const authorizeData = {
                    session_token: this.sessionToken,
                    client_id: app.client_id,
                    redirect_uri: redirectUri,
                    response_type: 'code',
                    scope: 'read write',
                    state: state
                };
                
                try {
                    const response = await this.apiCall('/oauth2/authorize', 'POST', authorizeData);
                    
                    if (response.success) {
                        // 重定向到授权回调地址
                        window.location.href = response.redirect_url;
                    } else {
                        this.showError(response.message || '授权失败');
                    }
                } catch (error) {
                    this.showError('授权请求失败');
                }
            }
            
            generateState() {
                return Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
            }
            
            async apiCall(endpoint, method = 'GET', data = null, params = null) {
                let url = this.apiBase + endpoint;
                
                if (params) {
                    const searchParams = new URLSearchParams(params);
                    url += '?' + searchParams.toString();
                }
                
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                return await response.json();
            }
            
            setLoading(loading) {
                const button = document.getElementById('loginButton');
                if (loading) {
                    button.disabled = true;
                    button.innerHTML = '<span class="loading"></span> 登录中...';
                } else {
                    button.disabled = false;
                    button.innerHTML = '登录';
                }
            }
            
            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }
            
            showSuccess(message) {
                const successDiv = document.getElementById('successMessage');
                successDiv.textContent = message;
                successDiv.style.display = 'block';
            }
            
            hideMessages() {
                document.getElementById('errorMessage').style.display = 'none';
                document.getElementById('successMessage').style.display = 'none';
            }
        }
        
        // 初始化认证管理器
        new AuthManager();
    </script>
</body>
</html>
