#!/usr/bin/env python
"""
ZhiFiles API 功能测试脚本
测试文件上传、下载、列表等功能
"""

import requests
import json
import tempfile
import os
from pathlib import Path

# API 基础URL
BASE_URL = "http://127.0.0.1:8000/api/files"

def create_test_file():
    """创建测试文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("这是一个测试文件\n")
        f.write("用于测试ZhiFiles文件管理系统\n")
        f.write("创建时间: 2025-08-04\n")
        return f.name

def test_file_upload():
    """测试文件上传"""
    print("\n=== 测试文件上传 ===")
    
    # 创建测试文件
    test_file_path = create_test_file()
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            data = {'description': '这是一个测试文件'}
            
            response = requests.post(f"{BASE_URL}/upload/", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ 文件上传成功")
                    file_data = result.get('data', {})
                    print(f"   文件ID: {file_data.get('file_id')}")
                    print(f"   文件名: {file_data.get('original_name')}")
                    print(f"   文件大小: {file_data.get('file_size')} 字节")
                    print(f"   文件类型: {file_data.get('file_type')}")
                    return file_data.get('file_id')
                else:
                    print(f"❌ 上传失败: {result.get('message')}")
            else:
                print(f"❌ 上传失败: HTTP {response.status_code}")
                print(f"   响应内容: {response.text}")
    
    except Exception as e:
        print(f"❌ 上传异常: {e}")
    
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
        except:
            pass
    
    return None

def test_file_list():
    """测试文件列表"""
    print("\n=== 测试文件列表 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/list/")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ 获取文件列表成功")
                data = result.get('data', {})
                files = data.get('files', [])
                pagination = data.get('pagination', {})
                
                print(f"   总文件数: {pagination.get('total', 0)}")
                print(f"   当前页: {pagination.get('page', 1)}")
                print(f"   每页数量: {pagination.get('page_size', 20)}")
                
                if files:
                    print("   文件列表:")
                    for i, file_info in enumerate(files[:5], 1):  # 只显示前5个
                        print(f"     {i}. {file_info.get('original_name')} "
                              f"({file_info.get('file_size')} 字节)")
                else:
                    print("   暂无文件")
                
                return files
            else:
                print(f"❌ 获取列表失败: {result.get('message')}")
        else:
            print(f"❌ 获取列表失败: HTTP {response.status_code}")
    
    except Exception as e:
        print(f"❌ 获取列表异常: {e}")
    
    return []

def test_file_download(file_id):
    """测试文件下载"""
    print(f"\n=== 测试文件下载 (ID: {file_id}) ===")
    
    if not file_id:
        print("❌ 没有可下载的文件ID")
        return False
    
    try:
        response = requests.get(f"{BASE_URL}/download/{file_id}/")
        
        if response.status_code == 200:
            # 检查是否是文件下载响应
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                # 可能是错误响应
                result = response.json()
                print(f"❌ 下载失败: {result.get('message')}")
                return False
            else:
                print("✅ 文件下载成功")
                print(f"   内容类型: {content_type}")
                print(f"   文件大小: {len(response.content)} 字节")
                
                # 保存到临时文件验证
                with tempfile.NamedTemporaryFile(delete=False) as f:
                    f.write(response.content)
                    temp_path = f.name
                
                print(f"   已保存到: {temp_path}")
                
                # 清理临时文件
                try:
                    os.unlink(temp_path)
                except:
                    pass
                
                return True
        else:
            print(f"❌ 下载失败: HTTP {response.status_code}")
    
    except Exception as e:
        print(f"❌ 下载异常: {e}")
    
    return False

def test_server_connection():
    """测试服务器连接"""
    print("=== 测试服务器连接 ===")
    
    try:
        response = requests.get(f"{BASE_URL}/list/", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器连接正常")
            return True
        else:
            print(f"❌ 服务器响应异常: HTTP {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("   请确保服务器已启动: python zhi_scripts/start_files.py")
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
    except Exception as e:
        print(f"❌ 连接异常: {e}")
    
    return False

def main():
    """主测试函数"""
    print("🚀 开始ZhiFiles API功能测试")
    print("=" * 50)
    
    # 测试服务器连接
    if not test_server_connection():
        print("\n❌ 服务器连接失败，测试终止")
        return
    
    # 测试文件列表（初始状态）
    initial_files = test_file_list()
    
    # 测试文件上传
    uploaded_file_id = test_file_upload()
    
    # 测试文件列表（上传后）
    if uploaded_file_id:
        test_file_list()
        
        # 测试文件下载
        test_file_download(uploaded_file_id)
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    
    if uploaded_file_id:
        print("✅ 文件上传功能正常")
        print("✅ 文件列表功能正常")
        print("✅ 文件下载功能正常")
        print("\n🎉 所有核心功能测试通过！")
        print("\n💡 可以开始使用ZhiFiles文件管理系统了")
        print("   - 上传接口: POST /api/files/upload/")
        print("   - 列表接口: GET /api/files/list/")
        print("   - 下载接口: GET /api/files/download/{file_id}/")
    else:
        print("⚠️  部分功能可能存在问题")
        print("   请检查服务器日志获取详细信息")

if __name__ == '__main__':
    main()
