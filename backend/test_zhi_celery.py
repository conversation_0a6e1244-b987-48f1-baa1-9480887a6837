#!/usr/bin/env python
"""
测试ZhiCelery配置
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')
django.setup()


def test_celery_app():
    """测试Celery应用配置"""
    print("🔄 测试Celery应用配置...")
    
    try:
        from zhi_celery.celery_app import app
        print(f"✅ Celery应用名称: {app.main}")
        print(f"✅ 配置来源: Django设置")
        print(f"✅ 任务自动发现: 已启用")
        return True
    except Exception as e:
        print(f"❌ Celery应用配置失败: {e}")
        return False


def test_task_imports():
    """测试任务导入"""
    print("\n🔄 测试任务导入...")
    
    success_count = 0
    total_count = 0
    
    # 测试Logger任务
    try:
        from zhi_celery.tasks.logger_tasks import log_to_database_task
        print("✅ Logger任务导入成功")
        success_count += 1
    except ImportError as e:
        print(f"⚠️  Logger任务导入失败: {e}")
    total_count += 1
    
    # 测试OAuth任务
    try:
        from zhi_celery.tasks.oauth_tasks import cleanup_expired_tokens_task
        print("✅ OAuth任务导入成功")
        success_count += 1
    except ImportError as e:
        print(f"⚠️  OAuth任务导入失败: {e}")
    total_count += 1
    
    return success_count == total_count


def test_schedules():
    """测试调度配置"""
    print("\n🔄 测试调度配置...")
    
    try:
        from zhi_celery.schedules import CELERY_BEAT_SCHEDULE
        print(f"✅ 调度配置加载成功，包含 {len(CELERY_BEAT_SCHEDULE)} 个任务")
        for task_name in CELERY_BEAT_SCHEDULE.keys():
            print(f"   - {task_name}")
        return True
    except ImportError as e:
        print(f"⚠️  调度配置加载失败: {e}")
        return False


def test_redis_connection():
    """测试Redis连接"""
    print("\n🔄 测试Redis连接...")
    
    try:
        import redis
        r = redis.Redis()
        r.ping()
        print("✅ Redis连接正常")
        return True
    except Exception as e:
        print(f"⚠️  Redis连接失败: {e}")
        print("   请确保Redis服务正在运行")
        print("   可以使用: docker run -d -p 6379:6379 redis:alpine")
        return False


def test_django_commands():
    """测试Django管理命令"""
    print("\n🔄 测试Django管理命令...")
    
    try:
        from django.core.management import get_commands
        commands = get_commands()
        
        if 'celery_worker' in commands:
            print("✅ celery_worker 命令可用")
        else:
            print("❌ celery_worker 命令不可用")
            return False
            
        if 'celery_beat' in commands:
            print("✅ celery_beat 命令可用")
        else:
            print("❌ celery_beat 命令不可用")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Django管理命令测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始ZhiCelery配置测试...")
    print("=" * 50)
    
    results = []
    
    # 1. 测试Celery应用配置
    results.append(test_celery_app())
    
    # 2. 测试任务导入
    results.append(test_task_imports())
    
    # 3. 测试调度配置
    results.append(test_schedules())
    
    # 4. 测试Redis连接
    results.append(test_redis_connection())
    
    # 5. 测试Django管理命令
    results.append(test_django_commands())
    
    # 总结
    print("\n" + "=" * 50)
    success_count = sum(results)
    total_count = len(results)
    
    print(f"📊 测试结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！ZhiCelery配置正常！")
        print("\n✅ 可以使用以下命令启动:")
        print("   python manage.py celery_worker --pool=solo --loglevel=info")
        print("   python manage.py celery_beat --loglevel=info")
        print("   或使用启动脚本:")
        print("   powershell -File start_zhi_celery_windows.ps1 -Mode worker")
    else:
        print("⚠️  部分测试失败，请检查配置")
        
    return success_count == total_count


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
