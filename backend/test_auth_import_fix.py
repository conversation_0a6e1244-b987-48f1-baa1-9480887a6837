"""
测试认证导入路径修复 - 验证🔒锁标识显示问题
"""

def test_auth_import_fix():
    """测试认证导入路径修复"""
    
    print("=== 认证导入路径修复验证 ===")
    
    # 测试核心认证模块导入
    print("\n1. 测试核心认证模块导入...")
    try:
        from zhi_common.zhi_auth.core_auth import GlobalOAuth2
        print("✅ GlobalOAuth2 导入成功")
    except ImportError as e:
        print(f"❌ GlobalOAuth2 导入失败: {e}")
        return False
    
    # 测试统一权限管理器导入
    print("\n2. 测试统一权限管理器导入...")
    try:
        from zhi_common.zhi_auth.unified_permission_manager import unified_permission_manager
        print("✅ unified_permission_manager 导入成功")
    except ImportError as e:
        print(f"❌ unified_permission_manager 导入失败: {e}")
        return False
    
    # 测试权限审计系统导入
    print("\n3. 测试权限审计系统导入...")
    try:
        from zhi_common.zhi_auth.permission_audit_system import permission_audit_logger
        print("✅ permission_audit_logger 导入成功")
    except ImportError as e:
        print(f"❌ permission_audit_logger 导入失败: {e}")
        return False
    
    # 测试增强型API暴露装饰器导入
    print("\n4. 测试增强型API暴露装饰器导入...")
    try:
        from zhi_common.zhi_services.enhanced_api_expose import (
            enhanced_auto_crud_api,
            enhanced_api_route,
            require_permission
        )
        print("✅ enhanced_api_expose 模块导入成功")
    except ImportError as e:
        print(f"❌ enhanced_api_expose 模块导入失败: {e}")
        return False
    
    # 测试增强型模型服务导入
    print("\n5. 测试增强型模型服务导入...")
    try:
        from zhi_common.zhi_services.enhanced_model_service import EnhancedModelService
        print("✅ EnhancedModelService 导入成功")
    except ImportError as e:
        print(f"❌ EnhancedModelService 导入失败: {e}")
        return False
    
    # 测试权限系统可用性
    print("\n6. 测试权限系统可用性...")
    try:
        from zhi_common.zhi_services.enhanced_api_expose import PERMISSION_SYSTEM_AVAILABLE
        from zhi_common.zhi_services.enhanced_model_service import PERMISSION_SYSTEM_AVAILABLE as MODEL_PERM_AVAILABLE
        
        print(f"API暴露装饰器权限系统: {'✅ 可用' if PERMISSION_SYSTEM_AVAILABLE else '❌ 不可用'}")
        print(f"模型服务权限系统: {'✅ 可用' if MODEL_PERM_AVAILABLE else '❌ 不可用'}")
        
        if not (PERMISSION_SYSTEM_AVAILABLE and MODEL_PERM_AVAILABLE):
            print("⚠️  权限系统未完全启用，可能影响🔒锁标识显示")
            return False
            
    except ImportError as e:
        print(f"❌ 权限系统可用性检查失败: {e}")
        return False
    
    # 测试require_permission装饰器
    print("\n7. 测试require_permission装饰器...")
    try:
        @require_permission('test.permission')
        def test_function():
            """测试函数 🔒"""
            pass
        
        # 检查权限标记
        if hasattr(test_function, '_permission_required'):
            print(f"✅ 权限装饰器工作正常，权限码: {test_function._permission_required}")
        else:
            print("❌ 权限装饰器未正确设置权限标记")
            return False
            
    except Exception as e:
        print(f"❌ require_permission装饰器测试失败: {e}")
        return False
    
    # 测试GlobalOAuth2实例化
    print("\n8. 测试GlobalOAuth2实例化...")
    try:
        oauth2_instance = GlobalOAuth2()
        print("✅ GlobalOAuth2 实例化成功")
        print(f"   实例类型: {type(oauth2_instance)}")
    except Exception as e:
        print(f"❌ GlobalOAuth2 实例化失败: {e}")
        return False
    
    # 检查文件中的导入修复
    print("\n9. 检查文件中的导入修复...")
    
    files_to_check = [
        ("zhi_common/zhi_services/enhanced_api_expose.py", "API暴露装饰器"),
        ("zhi_common/zhi_services/enhanced_model_service.py", "增强型模型服务"),
        ("zhi_oauth/services/example_product.py", "示例产品服务"),
        ("zhi_oauth/controllers/example_product.py", "示例产品控制器"),
    ]
    
    import os
    for file_path, desc in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否使用了正确的导入路径
                if 'from zhi_common.zhi_auth.core_auth import GlobalOAuth2' in content:
                    print(f"✅ {desc}: 使用正确的导入路径")
                else:
                    print(f"⚠️  {desc}: 可能未使用正确的导入路径")
                
                # 检查是否移除了try-except兼容性代码
                if 'PERMISSION_SYSTEM_AVAILABLE = True' in content:
                    print(f"✅ {desc}: 权限系统已启用")
                else:
                    print(f"⚠️  {desc}: 权限系统可能未启用")
                    
            except Exception as e:
                print(f"❌ {desc}: 文件检查失败 - {e}")
        else:
            print(f"❌ {desc}: 文件不存在 - {file_path}")
    
    print("\n=== 修复效果预期 ===")
    print("✅ 权限管理系统正确导入")
    print("✅ PERMISSION_SYSTEM_AVAILABLE = True")
    print("✅ require_permission装饰器正常工作")
    print("✅ API文档中应该显示🔒锁标识")
    print("✅ 权限检查功能正常")
    
    print("\n=== 总结 ===")
    print("🎉 认证导入路径修复完成！")
    print("📝 主要修复内容:")
    print("   - 移除了try-except兼容性代码")
    print("   - 使用正确的导入路径: zhi_common.zhi_auth.core_auth")
    print("   - 确保PERMISSION_SYSTEM_AVAILABLE = True")
    print("   - 修复了所有相关文件的导入")
    
    print("\n🔧 下一步操作:")
    print("1. 重启Django服务")
    print("2. 访问 http://127.0.0.1:8000/api/docs")
    print("3. 检查API端点是否显示🔒锁标识")
    print("4. 测试权限检查功能是否正常")
    
    return True

if __name__ == "__main__":
    success = test_auth_import_fix()
    if success:
        print("\n✅ 验证通过 - 认证导入路径修复成功")
        print("🔒 API文档中的锁标识问题应该已解决")
    else:
        print("\n❌ 验证失败 - 需要进一步检查导入问题")
