#!/usr/bin/env python
"""
ZhiFiles 项目测试脚本
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_files')

def setup_django():
    """设置Django环境"""
    try:
        django.setup()
        print("✅ Django环境初始化成功")
        return True
    except Exception as e:
        print(f"❌ Django环境初始化失败: {e}")
        return False

def test_zhi_files_models():
    """测试ZhiFiles模型"""
    print("\n=== 测试ZhiFiles模型 ===")
    
    try:
        from zhi_files.models import FileStorage, FileAccessLog
        
        print("✅ FileStorage模型导入成功")
        print("✅ FileAccessLog模型导入成功")
        
        # 检查模型字段
        file_storage_fields = [
            'file_id', 'original_name', 'file_name', 'file_path', 'file_url',
            'file_size', 'file_type', 'content_type', 'file_extension', 'file_hash',
            'status', 'download_count', 'tags', 'metadata', 'description'
        ]
        
        for field_name in file_storage_fields:
            if hasattr(FileStorage, field_name):
                print(f"✅ FileStorage字段存在: {field_name}")
            else:
                print(f"❌ FileStorage字段缺失: {field_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ ZhiFiles模型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_zhi_files_api():
    """测试ZhiFiles API"""
    print("\n=== 测试ZhiFiles API ===")
    
    try:
        from zhi_files.api import api
        from zhi_files.apis.file_management import FileStorageControllerAPI
        
        print("✅ ZhiFiles API导入成功")
        print("✅ FileStorageControllerAPI导入成功")
        
        # 检查API配置
        print(f"API标题: {api.title}")
        print(f"API版本: {api.version}")
        print(f"API描述: {api.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ ZhiFiles API测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_zhi_files_controller():
    """测试ZhiFiles控制器"""
    print("\n=== 测试ZhiFiles控制器 ===")
    
    try:
        from zhi_files.apis.file_management import FileStorageControllerAPI
        
        # 创建控制器实例
        controller = FileStorageControllerAPI()
        print("✅ FileStorageControllerAPI实例化成功")
        
        # 检查控制器方法
        controller_methods = ['upload_file', 'download_file', 'get_file_info']
        for method in controller_methods:
            if hasattr(controller, method):
                print(f"✅ 控制器方法存在: {method}")
            else:
                print(f"❌ 控制器方法缺失: {method}")
        
        return True
        
    except Exception as e:
        print(f"❌ ZhiFiles控制器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_file_manager_integration():
    """测试文件管理器集成"""
    print("\n=== 测试文件管理器集成 ===")
    
    try:
        from zhi_common.zhi_services.file_manager import FileManager, FileUploadConfig
        
        # 创建文件管理器
        config = FileUploadConfig(
            max_file_size=10 * 1024 * 1024,
            allowed_types=['image', 'document'],
            upload_path='zhi_files_test'
        )
        
        file_manager = FileManager(config)
        print("✅ 文件管理器创建成功")
        print(f"   最大文件大小: {config.max_file_size / 1024 / 1024:.1f}MB")
        print(f"   上传路径: {config.upload_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件管理器集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_settings_configuration():
    """测试设置配置"""
    print("\n=== 测试设置配置 ===")
    
    try:
        from django.conf import settings
        
        print("✅ Django设置加载成功")
        print(f"   数据库引擎: {settings.DATABASES['default']['ENGINE']}")
        print(f"   媒体根目录: {settings.MEDIA_ROOT}")
        print(f"   媒体URL: {settings.MEDIA_URL}")
        print(f"   ROOT_URLCONF: {settings.ROOT_URLCONF}")
        
        # 检查ZhiFiles特定配置
        if hasattr(settings, 'ZHI_FILES_CONFIG'):
            zhi_files_config = settings.ZHI_FILES_CONFIG
            print(f"   ZhiFiles配置存在")
            print(f"   默认最大文件大小: {zhi_files_config.get('DEFAULT_MAX_FILE_SIZE', 0) / 1024 / 1024:.1f}MB")
            print(f"   默认上传路径: {zhi_files_config.get('DEFAULT_UPLOAD_PATH', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 设置配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始ZhiFiles项目测试")

    # 首先设置Django环境
    if not setup_django():
        print("❌ Django环境设置失败，退出测试")
        return False
    
    tests = [
        ("设置配置测试", test_settings_configuration),
        ("ZhiFiles模型测试", test_zhi_files_models),
        ("ZhiFiles API测试", test_zhi_files_api),
        ("ZhiFiles控制器测试", test_zhi_files_controller),
        ("文件管理器集成测试", test_file_manager_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*50}")
            print(f"执行测试: {test_name}")
            print(f"{'='*50}")
            
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 测试结果: {passed}/{total} 通过")
    print(f"{'='*50}")
    
    if passed >= 4:  # 至少4个测试通过就算成功
        print("🎉 ZhiFiles项目主要功能测试通过！")
        print("💡 提示:")
        print("   - 运行 'python zhi_scripts/start_files.py' 启动服务")
        print("   - 访问 http://127.0.0.1:8000/api/docs/ 查看API文档")
        print("   - 访问 http://127.0.0.1:8000/admin/ 进入管理后台")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关配置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
