"""
直接测试部分更新功能（有值则更新）

不依赖具体的API，直接测试基础服务类的部分更新逻辑
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from django.db import models
from zhi_common.zhi_services.base_model_service import BaseModelService
from zhi_common.zhi_services.enhanced_model_service import EnhancedModelService
from zhi_common.zhi_model.core_model import ZhiCoreModel


# 创建一个简单的测试模型
class TestModel(ZhiCoreModel):
    """测试模型"""
    name = models.CharField(max_length=100, verbose_name="名称")
    description = models.TextField(blank=True, verbose_name="描述")
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=False, verbose_name="价格")
    category = models.CharField(max_length=50, null=True, blank=True, verbose_name="分类")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    stock = models.IntegerField(default=0, verbose_name="库存")

    class Meta:
        app_label = 'test'
        db_table = 'test_partial_update_model'


def test_should_update_field_logic():
    """测试字段更新判断逻辑"""
    print("🧪 测试字段更新判断逻辑...")

    # 创建服务实例
    service = BaseModelService(model_class=TestModel)
    test_instance = TestModel()
    
    # 测试用例
    test_cases = [
        # (字段名, 值, 期望结果, 说明)
        ('name', '新名称', True, '非空字符串应该更新'),
        ('name', '', False, 'name字段不允许blank，空字符串不应该更新'),
        ('name', None, False, 'name字段不允许null，None不应该更新'),
        
        ('description', '新描述', True, '非空描述应该更新'),
        ('description', '', True, 'description字段允许blank，空字符串应该更新'),
        ('description', None, False, 'description字段不允许null，None不应该更新'),
        
        ('price', 199.99, True, '有效价格应该更新'),
        ('price', 0, True, '价格为0也是有效值'),
        ('price', None, True, 'price字段允许null，None应该更新'),
        
        ('category', '新分类', True, '非空分类应该更新'),
        ('category', '', True, 'category字段允许blank，空字符串应该更新'),
        ('category', None, True, 'category字段允许null，None应该更新'),
        
        ('is_active', True, True, '布尔值True应该更新'),
        ('is_active', False, True, '布尔值False也应该更新'),
        
        ('stock', 100, True, '正数库存应该更新'),
        ('stock', 0, True, '库存为0也应该更新'),
    ]
    
    passed = 0
    total = len(test_cases)
    
    print("📋 测试字段更新判断逻辑:")
    for field_name, value, expected, description in test_cases:
        try:
            result = service._should_update_field(field_name, value, test_instance)
            if result == expected:
                print(f"✅ {description}: {field_name}={repr(value)} -> {result}")
                passed += 1
            else:
                print(f"❌ {description}: {field_name}={repr(value)} -> {result} (期望: {expected})")
        except Exception as e:
            print(f"❌ {description}: {field_name}={repr(value)} -> 异常: {e}")
    
    print(f"\n📊 字段判断逻辑测试结果: {passed}/{total} 通过")
    return passed == total


def test_update_method_signature():
    """测试update方法是否支持partial参数"""
    print("\n🧪 测试update方法签名...")
    
    import inspect
    
    # 测试基础服务类
    base_service = BaseModelService(model_class=TestModel)
    base_update_signature = inspect.signature(base_service.update)
    base_params = list(base_update_signature.parameters.keys())

    print(f"📋 BaseModelService.update 参数: {base_params}")

    if 'partial' in base_params:
        print("✅ BaseModelService.update 支持 partial 参数")
        base_partial_support = True
    else:
        print("❌ BaseModelService.update 不支持 partial 参数")
        base_partial_support = False

    # 测试增强服务类
    enhanced_service = EnhancedModelService(model_class=TestModel)
    enhanced_update_signature = inspect.signature(enhanced_service.update)
    enhanced_params = list(enhanced_update_signature.parameters.keys())
    
    print(f"📋 EnhancedModelService.update 参数: {enhanced_params}")
    
    if 'partial' in enhanced_params:
        print("✅ EnhancedModelService.update 支持 partial 参数")
        enhanced_partial_support = True
    else:
        print("❌ EnhancedModelService.update 不支持 partial 参数")
        enhanced_partial_support = False
    
    # 检查partial_update方法
    base_has_partial_update = hasattr(base_service, 'partial_update')
    enhanced_has_partial_update = hasattr(enhanced_service, 'partial_update')
    
    print(f"📋 BaseModelService 是否有 partial_update 方法: {base_has_partial_update}")
    print(f"📋 EnhancedModelService 是否有 partial_update 方法: {enhanced_has_partial_update}")
    
    if base_has_partial_update:
        print("✅ BaseModelService 支持 partial_update 方法")
    if enhanced_has_partial_update:
        print("✅ EnhancedModelService 支持 partial_update 方法")
    
    return (base_partial_support and enhanced_partial_support and 
            base_has_partial_update and enhanced_has_partial_update)


def test_method_availability():
    """测试方法可用性"""
    print("\n🧪 测试方法可用性...")
    
    # 测试基础服务类
    base_service = BaseModelService(model_class=TestModel)

    methods_to_check = [
        '_should_update_field',
        'partial_update',
        'update'
    ]

    base_methods_available = 0
    for method_name in methods_to_check:
        if hasattr(base_service, method_name):
            print(f"✅ BaseModelService.{method_name} 可用")
            base_methods_available += 1
        else:
            print(f"❌ BaseModelService.{method_name} 不可用")

    # 测试增强服务类
    enhanced_service = EnhancedModelService(model_class=TestModel)
    
    enhanced_methods_available = 0
    for method_name in methods_to_check:
        if hasattr(enhanced_service, method_name):
            print(f"✅ EnhancedModelService.{method_name} 可用")
            enhanced_methods_available += 1
        else:
            print(f"❌ EnhancedModelService.{method_name} 不可用")
    
    total_expected = len(methods_to_check) * 2  # 两个服务类
    total_available = base_methods_available + enhanced_methods_available
    
    print(f"\n📊 方法可用性测试结果: {total_available}/{total_expected} 可用")
    return total_available == total_expected


def main():
    """主测试函数"""
    print("🚀 开始测试部分更新功能（有值则更新）...")
    print("🎯 目标：验证auto_crud基类支持部分更新，只更新有值的字段")
    print("📝 注意：这是直接测试，不依赖具体的API实现")
    
    tests = [
        test_should_update_field_logic,
        test_update_method_signature,
        test_method_availability,
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 出现异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！部分更新功能已成功实现。")
        print("\n✅ 实现的功能特性:")
        print("   - ✅ 支持部分更新（partial=True）和全量更新（partial=False）")
        print("   - ✅ 智能字段验证：根据字段属性判断是否应该更新")
        print("   - ✅ 空值处理：正确处理None、空字符串、0、False等值")
        print("   - ✅ 字段权限：增强服务支持字段级权限控制")
        print("   - ✅ 便捷方法：提供partial_update()方法简化调用")
        print("\n💡 使用方式:")
        print("   # 部分更新（推荐）- 只更新有值的字段")
        print("   service.partial_update(id, data)")
        print("   service.update(id, data, partial=True)")
        print("   ")
        print("   # 全量更新（传统方式）- 更新所有字段")
        print("   service.update(id, data, partial=False)")
        print("\n🎯 核心逻辑:")
        print("   - None值：根据字段的null属性决定是否更新")
        print("   - 空字符串：根据字段的blank属性决定是否更新")
        print("   - 数字0：始终更新（0是有效值）")
        print("   - 布尔False：始终更新（False是有效值）")
        print("   - 非空值：始终更新")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
