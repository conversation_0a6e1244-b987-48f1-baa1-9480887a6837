"""
直接测试ExampleProductControllerAPI实例
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_oauth.apis.example_product import ExampleProductControllerAPI
from zhi_oauth.models import ExampleProduct


def test_direct_example_service():
    """直接测试ExampleProductControllerAPI实例"""
    print("🧪 直接测试ExampleProductControllerAPI实例...")
    
    # 直接创建ExampleProductControllerAPI实例
    service = ExampleProductControllerAPI(model_class=ExampleProduct)
    
    # 检查方法
    methods_to_check = [
        'create',
        'list',
        'retrieve', 
        'update',
        'patch',
        'delete',
        'partial_update',
        '_should_update_field'
    ]
    
    print("📋 检查ExampleProductControllerAPI实例方法:")
    available_methods = []
    for method_name in methods_to_check:
        if hasattr(service, method_name):
            method = getattr(service, method_name)
            print(f"   ✅ {method_name}: {type(method)} - {method}")
            available_methods.append(method_name)
        else:
            print(f"   ❌ {method_name}: 不存在")
    
    print(f"\n📊 可用方法: {len(available_methods)}/{len(methods_to_check)}")
    
    # 检查类继承关系
    print(f"\n📋 类继承关系:")
    print(f"   类名: {service.__class__.__name__}")
    print(f"   基类: {[cls.__name__ for cls in service.__class__.__mro__]}")
    
    # 检查所有公共方法
    print(f"\n📋 ExampleProductControllerAPI的所有公共方法:")
    all_methods = [name for name in dir(service) if not name.startswith('_')]
    print(f"   {all_methods}")
    
    # 特别检查patch相关方法
    patch_related = [name for name in all_methods if 'patch' in name.lower()]
    print(f"\n📋 patch相关方法: {patch_related}")
    
    # 尝试调用patch方法
    if hasattr(service, 'patch'):
        print(f"\n📋 尝试调用patch方法:")
        try:
            # 这里不实际调用，只是检查方法签名
            import inspect
            patch_signature = inspect.signature(service.patch)
            print(f"   patch方法签名: {patch_signature}")
            print(f"   ✅ patch方法可调用")
        except Exception as e:
            print(f"   ❌ patch方法调用失败: {e}")
    
    return 'patch' in available_methods


def main():
    """主测试函数"""
    print("🚀 开始直接测试ExampleProductControllerAPI...")
    
    try:
        success = test_direct_example_service()
        
        if success:
            print("\n✅ ExampleProductControllerAPI有patch方法")
        else:
            print("\n❌ ExampleProductControllerAPI没有patch方法")
            
        return success
        
    except Exception as e:
        print(f"❌ 测试出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
