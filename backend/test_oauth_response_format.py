#!/usr/bin/env python
"""
测试OAuth模块响应格式统一性
"""
import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_common.zhi_response.base import ZhiModelResponse, adapt_service_response
from zhi_common.zhi_consts.core_res_code import ResponseCode
import json


def test_adapt_service_response():
    """测试服务响应适配函数"""
    print("=== 测试服务响应适配函数 ===")
    
    # 测试成功响应
    success_result = {
        'success': True,
        'message': '登录成功',
        'session_token': 'abc123',
        'user_info': {'id': 1, 'username': 'test'},
        'expires_in': 3600
    }
    
    adapted = adapt_service_response(success_result)
    print("成功响应适配结果:")
    print(f"  code: {adapted.code}")
    print(f"  message: {adapted.message}")
    print(f"  success: {adapted.success}")
    print(f"  data: {adapted.data}")
    print()
    
    # 测试失败响应
    error_result = {
        'success': False,
        'message': '用户名或密码错误',
        'error': 'invalid_credentials'
    }
    
    adapted_error = adapt_service_response(error_result, error_code=ResponseCode.UNAUTHORIZED)
    print("失败响应适配结果:")
    print(f"  code: {adapted_error.code}")
    print(f"  message: {adapted_error.message}")
    print(f"  success: {adapted_error.success}")
    print(f"  data: {adapted_error.data}")
    print()


def test_response_format_consistency():
    """测试响应格式一致性"""
    print("=== 测试响应格式一致性 ===")
    
    # 模拟不同类型的响应
    responses = [
        # 登录成功
        {
            'success': True,
            'message': '登录成功',
            'session_token': 'token123',
            'user_info': {'id': 1, 'username': 'test'}
        },
        # OAuth授权成功
        {
            'success': True,
            'message': '授权成功',
            'authorization_code': 'code123',
            'redirect_url': 'https://example.com/callback?code=code123'
        },
        # 令牌获取成功
        {
            'success': True,
            'message': '令牌获取成功',
            'access_token': 'access123',
            'token_type': 'Bearer',
            'expires_in': 3600,
            'refresh_token': 'refresh123'
        },
        # 错误响应
        {
            'success': False,
            'message': '客户端验证失败',
            'error': 'invalid_client',
            'error_description': 'Invalid client_id'
        }
    ]
    
    for i, response in enumerate(responses, 1):
        adapted = adapt_service_response(response)
        print(f"响应 {i}:")
        print(f"  原始: {response}")
        print(f"  适配后: code={adapted.code}, success={adapted.success}, message='{adapted.message}'")
        print(f"  数据: {adapted.data}")
        print()


def test_json_serialization():
    """测试JSON序列化"""
    print("=== 测试JSON序列化 ===")
    
    response = {
        'success': True,
        'message': '操作成功',
        'data': {'id': 1, 'name': 'test'},
        'timestamp': '2024-07-24T10:00:00Z'
    }
    
    adapted = adapt_service_response(response)
    
    # 测试序列化
    try:
        json_str = json.dumps(adapted.dict(), ensure_ascii=False, indent=2)
        print("JSON序列化成功:")
        print(json_str)
    except Exception as e:
        print(f"JSON序列化失败: {e}")


if __name__ == '__main__':
    print("开始测试OAuth响应格式统一性...")
    print()
    
    test_adapt_service_response()
    test_response_format_consistency()
    test_json_serialization()
    
    print("测试完成！")
