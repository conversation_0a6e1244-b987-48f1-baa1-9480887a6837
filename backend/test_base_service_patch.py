"""
直接测试BaseModelService是否有patch方法
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.zhi_oauth')
django.setup()

from zhi_common.zhi_services.base_model_service import BaseModelService
from zhi_oauth.models import ExampleProduct


def test_base_service_patch():
    """测试BaseModelService是否有patch方法"""
    print("🧪 测试BaseModelService是否有patch方法...")
    
    # 创建BaseModelService实例
    service = BaseModelService(model_class=ExampleProduct)
    
    # 检查方法
    methods_to_check = [
        'create',
        'list',
        'retrieve', 
        'update',
        'patch',
        'delete',
        'partial_update',
        '_should_update_field'
    ]
    
    print("📋 检查BaseModelService方法:")
    available_methods = []
    for method_name in methods_to_check:
        if hasattr(service, method_name):
            method = getattr(service, method_name)
            print(f"   ✅ {method_name}: {type(method)} - {method}")
            available_methods.append(method_name)
        else:
            print(f"   ❌ {method_name}: 不存在")
    
    print(f"\n📊 可用方法: {len(available_methods)}/{len(methods_to_check)}")
    
    # 检查所有公共方法
    print(f"\n📋 BaseModelService的所有公共方法:")
    all_methods = [name for name in dir(service) if not name.startswith('_')]
    print(f"   {all_methods}")
    
    # 特别检查patch相关方法
    patch_related = [name for name in all_methods if 'patch' in name.lower()]
    print(f"\n📋 patch相关方法: {patch_related}")
    
    return 'patch' in available_methods


def main():
    """主测试函数"""
    print("🚀 开始测试BaseModelService...")
    
    try:
        success = test_base_service_patch()
        
        if success:
            print("\n✅ BaseModelService有patch方法")
        else:
            print("\n❌ BaseModelService没有patch方法")
            
        return success
        
    except Exception as e:
        print(f"❌ 测试出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
