"""
Celery Worker 管理命令
"""

import os
import sys
from django.core.management.base import BaseCommand
from django.conf import settings


class Command(BaseCommand):
    help = '启动Celery Worker'

    def add_arguments(self, parser):
        parser.add_argument(
            '--loglevel',
            default='info',
            help='日志级别 (默认: info)'
        )
        parser.add_argument(
            '--concurrency',
            type=int,
            default=2,
            help='并发数 (默认: 2)'
        )
        parser.add_argument(
            '--queues',
            default='default,logger',
            help='队列名称，用逗号分隔 (默认: default,logger)'
        )
        parser.add_argument(
            '--pool',
            default='prefork',
            help='进程池类型 (默认: prefork, Windows建议使用solo)'
        )
        parser.add_argument(
            '--autoscale',
            help='自动扩缩容 (格式: max,min 例如: 10,3)'
        )

    def handle(self, *args, **options):
        from zhi_celery.celery_app import app
        
        # 构建启动参数
        worker_args = [
            'worker',
            f'--loglevel={options["loglevel"]}',
            f'--concurrency={options["concurrency"]}',
            f'--queues={options["queues"]}',
            f'--pool={options["pool"]}',
        ]
        
        if options['autoscale']:
            worker_args.append(f'--autoscale={options["autoscale"]}')
        
        # Windows平台优化
        if sys.platform == 'win32':
            worker_args.append('--pool=solo')
            self.stdout.write(
                self.style.WARNING('Windows平台自动使用solo进程池')
            )
        
        self.stdout.write(
            self.style.SUCCESS(f'启动Celery Worker...')
        )
        self.stdout.write(f'参数: {" ".join(worker_args)}')
        
        # 启动worker
        app.worker_main(worker_args)
