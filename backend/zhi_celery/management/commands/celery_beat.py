"""
Celery Beat 管理命令
"""

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    help = '启动Celery Beat调度器'

    def add_arguments(self, parser):
        parser.add_argument(
            '--loglevel',
            default='info',
            help='日志级别 (默认: info)'
        )
        parser.add_argument(
            '--scheduler',
            default='django_celery_beat.schedulers:DatabaseScheduler',
            help='调度器类型 (默认: DatabaseScheduler)'
        )

    def handle(self, *args, **options):
        from zhi_celery.celery_app import app
        
        # 构建启动参数
        beat_args = [
            'beat',
            f'--loglevel={options["loglevel"]}',
            f'--scheduler={options["scheduler"]}',
        ]
        
        self.stdout.write(
            self.style.SUCCESS(f'启动Celery Beat调度器...')
        )
        self.stdout.write(f'参数: {" ".join(beat_args)}')
        
        # 启动beat
        app.start(beat_args)
