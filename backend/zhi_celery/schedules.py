"""
ZhiCelery 调度配置

统一管理所有定时任务的调度配置
"""

from celery.schedules import crontab
from kombu import Queue

# 任务队列配置
CELERY_TASK_QUEUES = [
    Queue('default', routing_key='default'),
    Queue('logger', routing_key='logger'),
    Queue('oauth', routing_key='oauth'),
    Queue('system', routing_key='system'),
]

# 任务路由配置
CELERY_TASK_ROUTES = {
    # Logger任务路由
    'zhi_celery.tasks.logger_tasks.log_to_database_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.log_api_access_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.log_audit_event_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.websocket_log_push_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.cleanup_old_logs_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.generate_log_statistics_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.log_health_check_task': {'queue': 'logger'},
    'zhi_celery.tasks.logger_tasks.generate_log_report_task': {'queue': 'logger'},
    
    # OAuth任务路由
    'zhi_celery.tasks.oauth_tasks.cleanup_expired_tokens_task': {'queue': 'oauth'},
    'zhi_celery.tasks.oauth_tasks.generate_oauth_statistics_task': {'queue': 'oauth'},
    'zhi_celery.tasks.oauth_tasks.revoke_expired_tokens_task': {'queue': 'oauth'},
    'zhi_celery.tasks.oauth_tasks.sync_oauth_user_permissions_task': {'queue': 'oauth'},
    'zhi_celery.tasks.oauth_tasks.oauth_health_check_task': {'queue': 'oauth'},
    
    # 默认路由
    'zhi_celery.tasks.*': {'queue': 'default'},
}

# Beat调度配置
CELERY_BEAT_SCHEDULE = {
    # ==================== Logger任务调度 ====================
    'cleanup-old-logs-daily': {
        'task': 'zhi_celery.tasks.logger_tasks.cleanup_old_logs_task',
        'schedule': crontab(hour=2, minute=0),  # 每天凌晨2点执行
        'args': (30,),  # 清理30天前的日志
        'options': {'queue': 'logger'}
    },
    
    'generate-log-statistics-hourly': {
        'task': 'zhi_celery.tasks.logger_tasks.generate_log_statistics_task',
        'schedule': crontab(minute=0),  # 每小时执行
        'options': {'queue': 'logger'}
    },
    
    'log-health-check': {
        'task': 'zhi_celery.tasks.logger_tasks.log_health_check_task',
        'schedule': crontab(minute='*/15'),  # 每15分钟执行
        'options': {'queue': 'logger'}
    },
    
    'generate-daily-log-report': {
        'task': 'zhi_celery.tasks.logger_tasks.generate_log_report_task',
        'schedule': crontab(hour=1, minute=0),  # 每天凌晨1点执行
        'args': ('daily',),
        'options': {'queue': 'logger'}
    },
    
    'generate-weekly-log-report': {
        'task': 'zhi_celery.tasks.logger_tasks.generate_log_report_task',
        'schedule': crontab(hour=1, minute=30, day_of_week=1),  # 每周一凌晨1:30执行
        'args': ('weekly',),
        'options': {'queue': 'logger'}
    },
    
    # ==================== OAuth任务调度 ====================
    'cleanup-expired-oauth-tokens': {
        'task': 'zhi_celery.tasks.oauth_tasks.cleanup_expired_tokens_task',
        'schedule': crontab(hour=3, minute=0),  # 每天凌晨3点执行
        'options': {'queue': 'oauth'}
    },
    
    'generate-oauth-statistics-daily': {
        'task': 'zhi_celery.tasks.oauth_tasks.generate_oauth_statistics_task',
        'schedule': crontab(hour=1, minute=15),  # 每天凌晨1:15执行
        'args': (7,),  # 生成7天统计
        'options': {'queue': 'oauth'}
    },
    
    'oauth-health-check': {
        'task': 'zhi_celery.tasks.oauth_tasks.oauth_health_check_task',
        'schedule': crontab(minute='*/30'),  # 每30分钟执行
        'options': {'queue': 'oauth'}
    },
    
    # ==================== 系统维护任务 ====================
    'system-health-check': {
        'task': 'zhi_celery.tasks.system_tasks.system_health_check_task',
        'schedule': crontab(minute='*/10'),  # 每10分钟执行
        'options': {'queue': 'system'}
    },
}

# 开发环境调度配置（频率更高，用于测试）
CELERY_BEAT_SCHEDULE_DEV = {
    # Logger任务（开发环境）
    'cleanup-old-logs-dev': {
        'task': 'zhi_celery.tasks.logger_tasks.cleanup_old_logs_task',
        'schedule': crontab(minute='*/30'),  # 每30分钟执行
        'args': (1,),  # 清理1天前的日志
        'options': {'queue': 'logger'}
    },
    
    'generate-log-statistics-dev': {
        'task': 'zhi_celery.tasks.logger_tasks.generate_log_statistics_task',
        'schedule': crontab(minute='*/10'),  # 每10分钟执行
        'options': {'queue': 'logger'}
    },
    
    'log-health-check-dev': {
        'task': 'zhi_celery.tasks.logger_tasks.log_health_check_task',
        'schedule': crontab(minute='*/5'),  # 每5分钟执行
        'options': {'queue': 'logger'}
    },
    
    # OAuth任务（开发环境）
    'cleanup-expired-oauth-tokens-dev': {
        'task': 'zhi_celery.tasks.oauth_tasks.cleanup_expired_tokens_task',
        'schedule': crontab(minute='*/20'),  # 每20分钟执行
        'options': {'queue': 'oauth'}
    },
    
    'generate-oauth-statistics-dev': {
        'task': 'zhi_celery.tasks.oauth_tasks.generate_oauth_statistics_task',
        'schedule': crontab(minute='*/15'),  # 每15分钟执行
        'args': (1,),  # 生成1天统计
        'options': {'queue': 'oauth'}
    },
    
    'oauth-health-check-dev': {
        'task': 'zhi_celery.tasks.oauth_tasks.oauth_health_check_task',
        'schedule': crontab(minute='*/5'),  # 每5分钟执行
        'options': {'queue': 'oauth'}
    },
}

# 根据环境选择调度配置
import os
from django.conf import settings

if hasattr(settings, 'DEBUG') and settings.DEBUG:
    # 开发环境使用开发调度配置
    CELERY_BEAT_SCHEDULE.update(CELERY_BEAT_SCHEDULE_DEV)
    print("✅ 使用开发环境Celery调度配置")
else:
    print("✅ 使用生产环境Celery调度配置")

# 导出配置
__all__ = [
    'CELERY_BEAT_SCHEDULE',
    'CELERY_TASK_ROUTES', 
    'CELERY_TASK_QUEUES',
    'CELERY_BEAT_SCHEDULE_DEV',
]
