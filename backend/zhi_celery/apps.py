from django.apps import AppConfig


class ZhiCeleryConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'zhi_celery'
    verbose_name = 'ZhiCelery 异步任务管理'

    def ready(self):
        """应用就绪时的初始化"""
        # 导入Celery应用，确保任务被发现
        from . import celery_app

        # 导入信号处理器（如果有的话）
        try:
            from . import signals
        except ImportError:
            pass
