"""
ZhiCelery - 日志相关异步任务

为zhi_logger提供异步任务支持，提高日志系统性能
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from django.db import models

from loguru import logger


@shared_task(bind=True, max_retries=3, queue='logger')
def log_to_database_task(self, level: str, message: str, extra_data: Dict[str, Any] = None):
    """
    异步写入日志到数据库

    Args:
        level: 日志级别
        message: 日志消息
        extra_data: 额外数据
    """
    try:
        from zhi_logger.models import SystemLog, LogLevel, LogCategory

        # 防止循环日志：跳过日志系统内部的日志
        if extra_data and extra_data.get('module') == 'zhi_common.zhi_logger.loguru_logger':
            return {
                'success': True,
                'message': 'Skipped logging system internal log to prevent loop'
            }

        # 创建系统日志记录
        SystemLog.objects.create(
            level=getattr(LogLevel, level, LogLevel.INFO),
            message=message,
            category=extra_data.get('category', LogCategory.SYSTEM) if extra_data else LogCategory.SYSTEM,
            module_name=extra_data.get('module', 'unknown') if extra_data else 'unknown',
            trace_id=extra_data.get('trace_id', '') if extra_data else '',
            user_id=extra_data.get('user_id', '') if extra_data else '',
            ip_address=extra_data.get('ip_address', '') if extra_data else '',
            user_agent=extra_data.get('user_agent', '') if extra_data else '',
            extra_data=extra_data or {}
        )

        return {
            'success': True,
            'message': 'Log saved to database successfully'
        }

    except Exception as exc:
        # 静默处理错误，避免循环日志
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task(bind=True, max_retries=3, queue='logger')
def log_api_access_task(self, request_data: Dict[str, Any]):
    """
    异步记录API访问日志
    
    Args:
        request_data: API请求数据
    """
    try:
        from zhi_logger.models import ApiAccessLog
        
        ApiAccessLog.objects.create(
            method=request_data.get('method', 'GET'),
            path=request_data.get('path', ''),
            status_code=request_data.get('status_code', 200),
            request_body=request_data.get('request_body', ''),
            response_body=request_data.get('response_body', ''),
            response_time=request_data.get('response_time'),
            ip_address=request_data.get('ip_address', ''),
            user_agent=request_data.get('user_agent', ''),
            trace_id=request_data.get('trace_id', ''),
            user_id=request_data.get('user_id', ''),
            extra_data=request_data.get('extra_data', {})
        )
        
        return {
            'success': True,
            'message': 'API access log saved successfully'
        }
        
    except Exception as exc:
        logger.error(f"API访问日志写入失败: {exc}")
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task(bind=True, max_retries=3, queue='logger')
def log_audit_event_task(self, audit_data: Dict[str, Any]):
    """
    异步记录审计日志
    
    Args:
        audit_data: 审计数据
    """
    try:
        from zhi_logger.models import AuditLog
        
        AuditLog.objects.create(
            action=audit_data.get('action', 'unknown'),
            resource_type=audit_data.get('resource_type', ''),
            resource_id=audit_data.get('resource_id', ''),
            user_id=audit_data.get('user_id', ''),
            ip_address=audit_data.get('ip_address', ''),
            user_agent=audit_data.get('user_agent', ''),
            trace_id=audit_data.get('trace_id', ''),
            changes=audit_data.get('changes', {}),
            extra_data=audit_data.get('extra_data', {})
        )
        
        return {
            'success': True,
            'message': 'Audit log saved successfully'
        }
        
    except Exception as exc:
        logger.error(f"审计日志写入失败: {exc}")
        
        # 重试任务
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task(bind=True, max_retries=3, queue='logger')
def websocket_log_push_task(self, log_data: Dict[str, Any]):
    """
    异步推送日志到WebSocket

    Args:
        log_data: 日志数据
    """
    try:
        # 防止循环日志：跳过日志系统内部的日志
        if log_data and log_data.get('extra', {}).get('module') == 'zhi_common.zhi_logger.loguru_logger':
            return {
                'success': True,
                'message': 'Skipped logging system internal log to prevent loop'
            }

        from channels.layers import get_channel_layer

        channel_layer = get_channel_layer()
        if not channel_layer:
            return {
                'success': False,
                'message': 'Channel layer not configured'
            }

        # 使用同步方式推送，避免AsyncToSync问题
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def push_message():
                await channel_layer.group_send(
                    "logs",
                    {
                        "type": "log_message",
                        "level": log_data.get('level', 'INFO'),
                        "message": log_data.get('message', ''),
                        "extra": log_data.get('extra', {}),
                        "timestamp": timezone.now().isoformat()
                    }
                )

            loop.run_until_complete(push_message())
            loop.close()

        except Exception as async_exc:
            # 如果异步方式失败，静默处理
            pass

        return {
            'success': True,
            'message': 'Log pushed to WebSocket successfully'
        }

    except Exception as exc:
        # 静默处理错误，避免循环日志
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task(queue='logger')
def cleanup_old_logs_task(days: int = 30):
    """
    清理旧日志任务
    
    Args:
        days: 保留天数
    """
    try:
        from zhi_logger.models import SystemLog, ApiAccessLog, AuditLog
        
        cutoff_date = timezone.now() - timedelta(days=days)
        
        # 清理系统日志
        system_logs_deleted = SystemLog.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        # 清理API访问日志
        api_logs_deleted = ApiAccessLog.objects.filter(
            created_at__lt=cutoff_date
        ).delete()[0]
        
        # 清理审计日志（保留更长时间）
        audit_cutoff_date = timezone.now() - timedelta(days=days * 2)
        audit_logs_deleted = AuditLog.objects.filter(
            created_at__lt=audit_cutoff_date
        ).delete()[0]
        
        result = {
            'system_logs_deleted': system_logs_deleted,
            'api_logs_deleted': api_logs_deleted,
            'audit_logs_deleted': audit_logs_deleted,
            'cutoff_date': cutoff_date.isoformat()
        }
        
        logger.info(f"日志清理完成: {result}")
        
        return {
            'success': True,
            'result': result
        }
        
    except Exception as exc:
        logger.error(f"日志清理任务失败: {exc}")
        
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task(queue='logger')
def generate_log_statistics_task(days: int = 7):
    """
    生成日志统计报告任务
    
    Args:
        days: 统计天数
    """
    try:
        from zhi_logger.models import SystemLog, ApiAccessLog, LogLevel
        from django.db.models import Count, Q, Avg
        
        start_date = timezone.now() - timedelta(days=days)
        
        # 系统日志统计
        system_log_stats = SystemLog.objects.filter(
            created_at__gte=start_date
        ).values('level').annotate(count=Count('id'))
        
        # API访问统计
        api_log_stats = ApiAccessLog.objects.filter(
            created_at__gte=start_date
        ).aggregate(
            total_requests=Count('id'),
            error_requests=Count('id', filter=Q(status_code__gte=400)),
            avg_response_time=Avg('response_time')
        )
        
        # 模块日志统计
        module_stats = SystemLog.objects.filter(
            created_at__gte=start_date
        ).values('module_name').annotate(count=Count('id')).order_by('-count')[:10]
        
        statistics = {
            'period_days': days,
            'start_date': start_date.isoformat(),
            'end_date': timezone.now().isoformat(),
            'system_logs': {item['level']: item['count'] for item in system_log_stats},
            'api_logs': api_log_stats,
            'top_modules': list(module_stats),
            'generated_at': timezone.now().isoformat()
        }
        
        logger.info(f"日志统计报告生成完成: {statistics}")
        
        return {
            'success': True,
            'statistics': statistics
        }
        
    except Exception as exc:
        logger.error(f"日志统计任务失败: {exc}")
        
        return {
            'success': False,
            'error': str(exc)
        }


@shared_task(queue='logger')
def log_health_check_task():
    """
    日志系统健康检查任务
    """
    try:
        from zhi_logger.models import SystemLog
        from django.db import connection
        
        health_status = {
            'timestamp': timezone.now().isoformat(),
            'database_connection': True,
            'recent_log_count': 0,
            'error_rate': 0.0,
            'warnings': []
        }
        
        # 检查数据库连接
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
        except Exception as e:
            health_status['database_connection'] = False
            health_status['warnings'].append(f'数据库连接失败: {e}')
        
        # 检查最近1小时的日志量
        recent_time = timezone.now() - timedelta(hours=1)
        recent_logs = SystemLog.objects.filter(created_at__gte=recent_time)
        health_status['recent_log_count'] = recent_logs.count()
        
        # 计算错误率
        error_logs = recent_logs.filter(level__in=['ERROR', 'CRITICAL']).count()
        if health_status['recent_log_count'] > 0:
            health_status['error_rate'] = error_logs / health_status['recent_log_count']
        
        # 检查异常情况
        if health_status['error_rate'] > 0.1:
            health_status['warnings'].append('错误率过高')
        
        if health_status['recent_log_count'] > 10000:
            health_status['warnings'].append('日志量异常')
        
        logger.info(f"日志系统健康检查完成: {health_status}")
        
        return {
            'success': True,
            'health_status': health_status
        }
        
    except Exception as exc:
        logger.error(f"日志健康检查失败: {exc}")
        
        return {
            'success': False,
            'error': str(exc)
        }
