"""
ZhiCelery 任务模块

统一管理所有异步任务
"""

# 导入Logger任务
try:
    from .logger_tasks import (
        log_to_database_task,
        log_api_access_task,
        log_audit_event_task,
        websocket_log_push_task,
        cleanup_old_logs_task,
        generate_log_statistics_task,
        log_health_check_task,
        generate_log_report_task,
    )
    LOGGER_TASKS_AVAILABLE = True
except ImportError:
    LOGGER_TASKS_AVAILABLE = False

# 导入OAuth任务（如果存在）
try:
    from .oauth_tasks import (
        cleanup_expired_tokens_task,
        generate_oauth_statistics_task,
        revoke_expired_tokens_task,
    )
    OAUTH_TASKS_AVAILABLE = True
except ImportError:
    OAUTH_TASKS_AVAILABLE = False

# 构建__all__列表
__all__ = []

if LOGGER_TASKS_AVAILABLE:
    __all__.extend([
        'log_to_database_task',
        'log_api_access_task', 
        'log_audit_event_task',
        'websocket_log_push_task',
        'cleanup_old_logs_task',
        'generate_log_statistics_task',
        'log_health_check_task',
        'generate_log_report_task',
    ])

if OAUTH_TASKS_AVAILABLE:
    __all__.extend([
        'cleanup_expired_tokens_task',
        'generate_oauth_statistics_task',
        'revoke_expired_tokens_task',
    ])
