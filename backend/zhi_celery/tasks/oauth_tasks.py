"""
ZhiCelery - OAuth相关异步任务

为zhi_oauth提供异步任务支持，处理令牌清理、统计等任务
"""

from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from celery import shared_task
from django.utils import timezone
from django.conf import settings
from django.db import models

from loguru import logger


@shared_task(bind=True, max_retries=3, queue='oauth')
def cleanup_expired_tokens_task(self):
    """
    清理过期的OAuth令牌
    
    定期清理过期的access_token和refresh_token，释放数据库空间
    """
    try:
        from zhi_oauth.models import OAuthAccessToken, OAuthRefreshToken
        
        # 清理过期的访问令牌
        expired_access_tokens = OAuthAccessToken.objects.filter(
            expires_at__lt=timezone.now()
        )
        access_count = expired_access_tokens.count()
        expired_access_tokens.delete()
        
        # 清理过期的刷新令牌
        expired_refresh_tokens = OAuthRefreshToken.objects.filter(
            expires_at__lt=timezone.now()
        )
        refresh_count = expired_refresh_tokens.count()
        expired_refresh_tokens.delete()
        
        logger.info(f"清理过期令牌完成: access_token={access_count}, refresh_token={refresh_count}")
        
        return {
            'success': True,
            'access_tokens_cleaned': access_count,
            'refresh_tokens_cleaned': refresh_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"清理过期令牌失败: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        return {
            'success': False,
            'error': str(exc),
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, max_retries=3, queue='oauth')
def generate_oauth_statistics_task(self, days: int = 7):
    """
    生成OAuth统计报告
    
    Args:
        days: 统计天数，默认7天
    """
    try:
        from zhi_oauth.models import OAuthAccessToken, OAuthApplication, User
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        # 统计活跃令牌数
        active_tokens = OAuthAccessToken.objects.filter(
            expires_at__gt=timezone.now()
        ).count()
        
        # 统计新增令牌数
        new_tokens = OAuthAccessToken.objects.filter(
            created_at__gte=start_date,
            created_at__lt=end_date
        ).count()
        
        # 统计活跃应用数
        active_apps = OAuthApplication.objects.filter(
            is_active=True,
            is_deleted=False
        ).count()
        
        # 统计活跃用户数（有有效令牌的用户）
        active_users = User.objects.filter(
            oauthaccesstoken__expires_at__gt=timezone.now()
        ).distinct().count()
        
        statistics = {
            'period': f'{days}天',
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'active_tokens': active_tokens,
            'new_tokens': new_tokens,
            'active_applications': active_apps,
            'active_users': active_users,
            'generated_at': timezone.now().isoformat()
        }
        
        logger.info(f"OAuth统计报告生成完成: {statistics}")
        
        return {
            'success': True,
            'statistics': statistics,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"生成OAuth统计报告失败: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        return {
            'success': False,
            'error': str(exc),
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, max_retries=3, queue='oauth')
def revoke_expired_tokens_task(self, user_id: Optional[int] = None):
    """
    撤销指定用户或所有用户的过期令牌
    
    Args:
        user_id: 用户ID，如果为None则处理所有用户
    """
    try:
        from zhi_oauth.models import OAuthAccessToken, OAuthRefreshToken
        
        # 构建查询条件
        query_filter = {'expires_at__lt': timezone.now()}
        if user_id:
            query_filter['user_id'] = user_id
        
        # 撤销过期的访问令牌
        expired_access_tokens = OAuthAccessToken.objects.filter(**query_filter)
        access_count = expired_access_tokens.count()
        
        for token in expired_access_tokens:
            token.is_revoked = True
            token.revoked_at = timezone.now()
            token.save()
        
        # 撤销过期的刷新令牌
        expired_refresh_tokens = OAuthRefreshToken.objects.filter(**query_filter)
        refresh_count = expired_refresh_tokens.count()
        
        for token in expired_refresh_tokens:
            token.is_revoked = True
            token.revoked_at = timezone.now()
            token.save()
        
        user_info = f"用户{user_id}" if user_id else "所有用户"
        logger.info(f"撤销{user_info}过期令牌完成: access_token={access_count}, refresh_token={refresh_count}")
        
        return {
            'success': True,
            'user_id': user_id,
            'access_tokens_revoked': access_count,
            'refresh_tokens_revoked': refresh_count,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"撤销过期令牌失败: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        return {
            'success': False,
            'error': str(exc),
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, max_retries=3, queue='oauth')
def sync_oauth_user_permissions_task(self, user_id: int):
    """
    同步OAuth用户权限
    
    Args:
        user_id: 用户ID
    """
    try:
        from zhi_oauth.models import User
        
        user = User.objects.get(id=user_id)
        
        # 这里可以添加权限同步逻辑
        # 例如：同步用户角色、权限等
        
        logger.info(f"同步用户权限完成: user_id={user_id}, username={user.username}")
        
        return {
            'success': True,
            'user_id': user_id,
            'username': user.username,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"同步用户权限失败: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        return {
            'success': False,
            'error': str(exc),
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, max_retries=3, queue='oauth')
def oauth_health_check_task(self):
    """
    OAuth系统健康检查
    """
    try:
        from zhi_oauth.models import OAuthApplication, OAuthAccessToken
        
        # 检查应用状态
        total_apps = OAuthApplication.objects.count()
        active_apps = OAuthApplication.objects.filter(is_active=True, is_deleted=False).count()
        
        # 检查令牌状态
        total_tokens = OAuthAccessToken.objects.count()
        active_tokens = OAuthAccessToken.objects.filter(
            expires_at__gt=timezone.now(),
            is_revoked=False
        ).count()
        
        health_status = {
            'applications': {
                'total': total_apps,
                'active': active_apps,
                'health': 'good' if active_apps > 0 else 'warning'
            },
            'tokens': {
                'total': total_tokens,
                'active': active_tokens,
                'health': 'good' if active_tokens > 0 else 'warning'
            },
            'overall_health': 'good',
            'checked_at': timezone.now().isoformat()
        }
        
        logger.info(f"OAuth健康检查完成: {health_status}")
        
        return {
            'success': True,
            'health_status': health_status,
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"OAuth健康检查失败: {exc}")
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (self.request.retries + 1))
        return {
            'success': False,
            'error': str(exc),
            'timestamp': timezone.now().isoformat()
        }
