"""
ZhiCelery - 异步任务管理子应用

提供统一的异步任务处理能力，支持：
- 定时任务管理
- 异步任务队列
- 任务监控和管理
- 分布式任务调度
- 日志异步处理
"""

# 这将确保应用在Django启动时被加载
default_app_config = 'zhi_celery.apps.ZhiCeleryConfig'

# 导入Celery应用实例（暂时注释，等Celery安装完成后启用）
# from .celery_app import app as celery_app
try:
    from .celery_app import app as celery_app
except ImportError:
    celery_app = None

# 导入所有任务模块
try:
    # Logger任务
    from .tasks.logger_tasks import (
        log_to_database_task,
        log_api_access_task,
        log_audit_event_task,
        websocket_log_push_task,
        cleanup_old_logs_task,
        generate_log_statistics_task,
        log_health_check_task,
        generate_log_report_task,
    )

    # OAuth任务
    from .tasks.oauth_tasks import (
        cleanup_expired_tokens_task,
        generate_oauth_statistics_task,
        revoke_expired_tokens_task,
    )

    # 调度配置
    from .schedules import CELERY_BEAT_SCHEDULE, CELERY_TASK_ROUTES

    __all__ = [
        'celery_app',
        # Logger任务
        'log_to_database_task',
        'log_api_access_task',
        'log_audit_event_task',
        'websocket_log_push_task',
        'cleanup_old_logs_task',
        'generate_log_statistics_task',
        'log_health_check_task',
        'generate_log_report_task',
        # OAuth任务
        'cleanup_expired_tokens_task',
        'generate_oauth_statistics_task',
        'revoke_expired_tokens_task',
        # 配置
        'CELERY_BEAT_SCHEDULE',
        'CELERY_TASK_ROUTES',
    ]
except ImportError:
    # 如果任务模块不可用，提供基本的__all__
    __all__ = []