"""
ZhiCelery - Celery应用配置

统一的Celery配置，支持多子项目环境
"""

import os
from celery import Celery
from django.conf import settings

# 设置默认Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'application.settings.base')

# 创建Celery应用实例
app = Celery('zhi_celery')

# 使用Django设置配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动发现任务
app.autodiscover_tasks()

# 导入统一的调度配置
try:
    from .schedules import CELERY_BEAT_SCHEDULE, CELERY_TASK_ROUTES, CELERY_TASK_QUEUES

    # 配置任务路由
    app.conf.task_routes = CELERY_TASK_ROUTES

    # 配置Beat调度
    app.conf.beat_schedule = CELERY_BEAT_SCHEDULE

    # 配置队列
    app.conf.task_queues = CELERY_TASK_QUEUES

except ImportError:
    # 如果调度配置不可用，使用基本配置
    app.conf.task_routes = {
        'zhi_celery.tasks.logger_tasks.*': {'queue': 'logger'},
        'zhi_celery.tasks.oauth_tasks.*': {'queue': 'oauth'},
        'zhi_celery.tasks.*': {'queue': 'default'},
    }

# 配置队列
app.conf.task_default_queue = 'default'

# 配置任务序列化
app.conf.task_serializer = 'json'
app.conf.result_serializer = 'json'
app.conf.accept_content = ['json']

# 配置时区
app.conf.timezone = 'Asia/Shanghai'
app.conf.enable_utc = False

# 配置任务结果过期时间
app.conf.result_expires = 3600

# 配置任务重试
app.conf.task_acks_late = True
app.conf.task_reject_on_worker_lost = True

# 配置worker
app.conf.worker_prefetch_multiplier = 1
app.conf.worker_max_tasks_per_child = 1000

# 配置beat调度器
app.conf.beat_scheduler = 'django_celery_beat.schedulers:DatabaseScheduler'

@app.task(bind=True)
def debug_task(self):
    """调试任务"""
    print(f'Request: {self.request!r}')

@app.task
def health_check():
    """健康检查任务"""
    from django.utils import timezone
    return {
        'status': 'healthy',
        'timestamp': str(timezone.now()),
        'worker_id': os.getpid()
    }

# 配置错误处理
@app.task(bind=True)
def error_handler(self, uuid, err, traceback):
    """错误处理任务"""
    print(f'Task {uuid} raised exception: {err}\n{traceback}')

if __name__ == '__main__':
    app.start()
